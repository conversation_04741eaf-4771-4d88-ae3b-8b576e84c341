import json
import logging
from typing import Dict, Any
from kafka import KafkaProducer
from kafka.errors import KafkaError
from datetime import datetime

from app.config import settings

logger = logging.getLogger(__name__)

_producer = None

def get_kafka_producer() -> KafkaProducer:
    """
    Получение экземпляра Kafka Producer в режиме Singleton
    
    Returns:
        KafkaProducer: Экземпляр Kafka Producer
    """
    global _producer
    if _producer is None:
        _producer = KafkaProducer(
            bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
            value_serializer=lambda v: json.dumps(v).encode('utf-8')
        )
    return _producer

def send_transaction_event(user_id: int, amount: float, currency: str, tx_hash: str, status: str) -> bool:
    """
    Отправка события о транзакции в Kafka
    
    Args:
        user_id: ID пользователя
        amount: Сумма транзакции
        currency: Валюта транзакции
        tx_hash: Хеш транзакции
        status: Статус транзакции
        
    Returns:
        bool: True если сообщение успешно отправлено, иначе False
    """
    try:
        producer = get_kafka_producer()
        
        message = {
            "event_type": "transaction_update",
            "user_id": user_id,
            "amount": amount,
            "currency": currency,
            "tx_hash": tx_hash,
            "status": status,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        future = producer.send(settings.KAFKA_TRANSACTION_TOPIC, message)
        # Блокирующий вызов для получения результата
        future.get(timeout=10)
        
        logger.info(f"Событие о транзакции отправлено: user_id={user_id}, currency={currency}, amount={amount}, status={status}")
        return True
        
        
    except KafkaError as e:
        logger.error(f"Ошибка отправки события в Kafka: {str(e)}")
        return False
    

def send_deposit_confirmed_event(user_id: int, amount: float, currency: str, wallet_address: str, tx_hash: str) -> bool:
    """
    Отправка события о подтвержденном депозите в Treasury Service через Kafka
    
    Args:
        user_id: ID пользователя
        amount: Сумма депозита
        currency: Валюта депозита (с указанием сети для токенов, например USDT-TRC20)
        wallet_address: Адрес кошелька, на который поступил депозит
        tx_hash: Хеш транзакции
        
    Returns:
        bool: True если сообщение успешно отправлено, иначе False
    """
    try:
        producer = get_kafka_producer()
        
        message = {
            "event_type": "deposit_confirmed",
            "user_id": user_id,
            "amount": str(amount),  # Передаем как строку для точности
            "currency": currency,
            "wallet_address": wallet_address,
            "tx_hash": tx_hash,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Отправляем в специальный топик для Treasury Service
        future = producer.send(settings.KAFKA_BALANCE_UPDATES_TOPIC, message)
        # Блокирующий вызов для получения результата
        future.get(timeout=10)
        
        logger.info(f"Событие о подтвержденном депозите отправлено в Treasury Service: user_id={user_id}, currency={currency}, amount={amount}")
        return True
        
    except KafkaError as e:
        logger.error(f"Ошибка отправки события в Kafka для Treasury Service: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Непредвиденная ошибка при отправке события в Treasury Service: {str(e)}")
        return False