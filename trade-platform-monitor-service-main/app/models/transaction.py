from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, ForeignKey
from sqlalchemy.dialects.postgresql import UUID
import uuid
from datetime import datetime

from app.database import Base

class Transaction(Base):
    """Модель для хранения информации о транзакциях"""
    __tablename__ = "transactions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, nullable=False, index=True)
    wallet_address = Column(String, nullable=False, index=True)
    tx_hash = Column(String, nullable=False, unique=True, index=True)
    currency = Column(String, nullable=False)
    network = Column(String, nullable=False)
    amount = Column(Float, nullable=False)
    confirmations = Column(Integer, default=0)
    status = Column(String, default="pending")  # pending, confirmed, failed
    is_processed = Column(Boolean, default=False)  # Flag for internal processing
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    def __repr__(self):
        return f"Transaction(id={self.id}, tx_hash={self.tx_hash}, currency={self.currency}, amount={self.amount}, status={self.status})"