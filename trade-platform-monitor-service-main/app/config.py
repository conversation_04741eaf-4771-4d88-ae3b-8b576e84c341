from typing import List, Dict, Any
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    
    # Ethereum settings
    ETH_API_URL: str
    ETH_API_KEY: str
    ETH_MIN_CONFIRMATIONS: int
    ETHERSCAN_API_KEY: str  # Add this line


    # Основные настройки
    APP_NAME: str = "blockchain-monitor"
    DEBUG: bool = False
    PORT: int = 8004
    LOG_LEVEL: str = "INFO"
    
    # Настройки базы данных
    DATABASE_URL: str
    
    # Настройки Kafka
    KAFKA_BOOTSTRAP_SERVERS: str
    KAFKA_TRANSACTION_TOPIC: str = "transaction-events"
    KAFKA_BALANCE_UPDATES_TOPIC: str = "balance-updates" 
    
    # Настройки блокчейн API
    BTC_API_URL: str = "https://api.blockcypher.com/v1/btc/main"
    BTC_API_KEY: str = ""
    BTC_MIN_CONFIRMATIONS: int = 2
    
    ETH_API_URL: str = "https://mainnet.infura.io/v3/"
    ETH_API_KEY: str = ""
    ETH_MIN_CONFIRMATIONS: int = 12
    
    TRON_API_URL: str = "https://api.trongrid.io"
    TRON_API_KEY: str = ""
    TRON_MIN_CONFIRMATIONS: int = 19
    
    # Настройки мониторинга
    MONITORING_INTERVAL_SECONDS: int = 60
    TRANSACTION_BATCH_SIZE: int = 100
    
    # Wallet Service URL для получения адресов
    WALLET_SERVICE_URL: str = "http://localhost:8003"
    
    model_config = {
        "env_file": ".env",
        "case_sensitive": True
    }

settings = Settings()