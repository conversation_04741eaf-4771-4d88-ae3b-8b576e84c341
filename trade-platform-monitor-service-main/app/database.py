import logging
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from datetime import datetime

from app.config import settings

# Настройка логирования
logger = logging.getLogger(__name__)

# Создание движка SQLAlchemy
engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Функция для получения сессии БД
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Функция для проверки соединения с БД при запуске
def check_db_connection():
    try:
        with engine.connect() as conn:
            conn.execute(text("SELECT 1"))
            logger.info("Соединение с БД установлено")
            return True
    except Exception as e:
        logger.error(f"Ошибка соединения с БД: {str(e)}")
        return False

# Функция для создания всех таблиц в БД
def create_tables():
    Base.metadata.create_all(bind=engine)
    logger.info("Таблицы в БД созданы")