import logging
import requests
import time
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session

from app.config import settings
from app.models.transaction import Transaction
from app.kafka.producer import send_transaction_event, send_deposit_confirmed_event

# Настройка логирования
logger = logging.getLogger(__name__)

class TronMonitor:
    """Монитор для отслеживания транзакций в сети TRON (TRX и TRC20 токены)"""
    
    @staticmethod
    def get_wallet_addresses(db: Session) -> List[Dict[str, Any]]:
        """
        Получение всех TRON адресов из базы данных
        
        Returns:
            List[Dict[str, Any]]: Список словарей с информацией о кошельках
        """
        try:
            # В реальном приложении здесь должен быть запрос к сервису кошельков
            # или к базе данных для получения списка адресов и их пользователей
            response = requests.get(
                f"{settings.WALLET_SERVICE_URL}/api/wallets/tron/all",
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения TRON адресов: {response.text}")
                return []
            
            return response.json()
        except Exception as e:
            logger.error(f"Ошибка при получении TRON адресов: {str(e)}")
            return []
    
    @staticmethod
    def fetch_transactions(address: str) -> List[Dict[str, Any]]:
        """
        Получение всех транзакций для заданного TRON адреса
        
        Args:
            address: TRON адрес
            
        Returns:
            List[Dict[str, Any]]: Список транзакций
        """
        try:
            url = f"{settings.TRON_API_URL}/v1/accounts/{address}/transactions"
            headers = {}
            
            if settings.TRON_API_KEY:
                headers["TRON-PRO-API-KEY"] = settings.TRON_API_KEY
            
            params = {
                "limit": 50,
                "only_confirmed": True
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения транзакций для адреса {address}: {response.text}")
                return []
            
            data = response.json()
            return data.get("data", [])
        except Exception as e:
            logger.error(f"Ошибка при получении транзакций для адреса {address}: {str(e)}")
            return []
    
    @staticmethod
    def fetch_trc20_transactions(address: str) -> List[Dict[str, Any]]:
        """
        Получение TRC20 транзакций для заданного TRON адреса
        
        Args:
            address: TRON адрес
            
        Returns:
            List[Dict[str, Any]]: Список TRC20 транзакций
        """
        try:
            url = f"{settings.TRON_API_URL}/v1/accounts/{address}/transactions/trc20"
            headers = {}
            
            if settings.TRON_API_KEY:
                headers["TRON-PRO-API-KEY"] = settings.TRON_API_KEY
            
            params = {
                "limit": 50,
                "only_confirmed": True
            }
            
            response = requests.get(url, headers=headers, params=params)
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения TRC20 транзакций для адреса {address}: {response.text}")
                return []
            
            data = response.json()
            return data.get("data", [])
        except Exception as e:
            logger.error(f"Ошибка при получении TRC20 транзакций для адреса {address}: {str(e)}")
            return []
    
    @staticmethod
    def get_current_block() -> int:
        """
        Получение информации о текущем блоке в сети TRON
        
        Returns:
            int: Номер текущего блока
        """
        try:
            url = f"{settings.TRON_API_URL}/wallet/getnowblock"
            headers = {}
            
            if settings.TRON_API_KEY:
                headers["TRON-PRO-API-KEY"] = settings.TRON_API_KEY
            
            response = requests.get(url, headers=headers)
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения текущего блока TRON: {response.text}")
                return 0
            
            data = response.json()
            return data.get("block_header", {}).get("raw_data", {}).get("number", 0)
        except Exception as e:
            logger.error(f"Ошибка при получении текущего блока TRON: {str(e)}")
            return 0
    
    @staticmethod
    def process_trc20_transactions(db: Session, wallet_data: Dict[str, Any], transactions: List[Dict[str, Any]], current_block: int):
        """
        Обработка TRC20 транзакций для кошелька
        
        Args:
            db: Сессия базы данных
            wallet_data: Данные о кошельке
            transactions: Список транзакций
            current_block: Текущий номер блока
        """
        address = wallet_data["address"]
        user_id = wallet_data["user_id"]
        
        for tx in transactions:
            try:
                # Проверяем, это USDT транзакция
                token_info = tx.get("token_info", {})
                symbol = token_info.get("symbol", "")
                
                if symbol != "USDT":
                    continue
                
                # Проверяем, что это входящая транзакция
                is_incoming = tx.get("to", "") == address
                
                if not is_incoming:
                    continue
                
                tx_hash = tx.get("transaction_id", "")
                
                # Получаем количество токенов, с учетом десятичных знаков
                token_decimals = int(token_info.get("decimals", 6))
                value = int(tx.get("value", 0))
                amount = value / (10 ** token_decimals)
                
                if amount <= 0:
                    continue
                
                # ИСПРАВЛЕНИЕ: корректное вычисление подтверждений
                # В TRC20 транзакциях block_number часто отсутствует напрямую
                # Используем фиксированное значение подтверждений для подтвержденных транзакций
                # API обычно возвращает только подтвержденные транзакции
                confirmations = settings.TRON_MIN_CONFIRMATIONS  # Безопасное значение по умолчанию
                
                # Проверяем, существует ли уже транзакция в базе
                existing_tx = db.query(Transaction).filter(
                    Transaction.tx_hash == tx_hash
                ).first()
                
                if existing_tx:
                    # Обновляем существующую транзакцию
                    if existing_tx.confirmations != confirmations:
                        existing_tx.confirmations = confirmations
                        
                        # Определяем статус на основе количества подтверждений
                        if confirmations >= settings.TRON_MIN_CONFIRMATIONS:
                            if existing_tx.status != "confirmed":
                                existing_tx.status = "confirmed"
                                
                                # Отправляем события, если транзакция подтверждена и еще не обработана
                                if not existing_tx.is_processed:
                                    existing_tx.is_processed = True
                                    
                                    # Отправляем событие в основной топик транзакций
                                    send_transaction_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency=existing_tx.currency,
                                        tx_hash=existing_tx.tx_hash,
                                        status="confirmed"
                                    )
                                    
                                    # Отправляем событие в Treasury Service для консолидации средств
                                    send_deposit_confirmed_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency="USDT-TRC20",  # Указываем формат USDT-TRC20 для Treasury Service
                                        wallet_address=existing_tx.wallet_address,
                                        tx_hash=existing_tx.tx_hash
                                    )
                                    
                                    logger.info(f"TRC20 транзакция {tx_hash} подтверждена и отправлена в Kafka и Treasury Service")
                        
                        db.commit()
                        logger.info(f"Обновлена TRC20 транзакция {tx_hash}, подтверждений: {confirmations}")
                else:
                    # Создаем новую транзакцию
                    status = "confirmed" if confirmations >= settings.TRON_MIN_CONFIRMATIONS else "pending"
                    is_processed = status == "confirmed"
                    
                    new_tx = Transaction(
                        user_id=user_id,
                        wallet_address=address,
                        tx_hash=tx_hash,
                        currency="USDT",
                        network="TRC20",
                        amount=amount,
                        confirmations=confirmations,
                        status=status,
                        is_processed=is_processed,
                        created_at=datetime.utcnow()
                    )
                    
                    db.add(new_tx)
                    db.commit()
                    
                    logger.info(f"Создана новая TRC20 транзакция {tx_hash}, сумма: {amount} USDT, статус: {status}")
                    
                    # Отправляем события, если транзакция уже подтверждена
                    if status == "confirmed":
                        # Отправляем событие в основной топик транзакций
                        send_transaction_event(
                            user_id=user_id,
                            amount=amount,
                            currency="USDT",
                            tx_hash=tx_hash,
                            status="confirmed"
                        )
                        
                        # Отправляем событие в Treasury Service для консолидации средств
                        send_deposit_confirmed_event(
                            user_id=user_id,
                            amount=amount,
                            currency="USDT-TRC20",  # Указываем формат USDT-TRC20 для Treasury Service
                            wallet_address=address,
                            tx_hash=tx_hash
                        )
                        
                        logger.info(f"Новая подтвержденная TRC20 транзакция {tx_hash} отправлена в Kafka и Treasury Service")
                
            except Exception as e:
                db.rollback()
                logger.error(f"Ошибка при обработке TRC20 транзакции {tx.get('transaction_id', 'unknown')}: {str(e)}")
    
    @staticmethod
    def monitor(db: Session):
        """
        Основная функция мониторинга TRON транзакций
        
        Args:
            db: Сессия базы данных
        """
        try:
            start_time = time.time()
            logger.info("Начало мониторинга TRON/TRC20 транзакций")
            
            # Получаем текущий блок
            current_block = TronMonitor.get_current_block()
            if current_block == 0:
                logger.error("Не удалось получить текущий блок TRON")
                return
            
            logger.info(f"Текущий блок TRON: {current_block}")
            
            # Получаем все TRON адреса
            wallet_addresses = TronMonitor.get_wallet_addresses(db)
            logger.info(f"Получено {len(wallet_addresses)} TRON адресов для мониторинга")
            
            # Обрабатываем каждый адрес
            for wallet_data in wallet_addresses:
                address = wallet_data["address"]
                logger.info(f"Проверка TRON транзакций для адреса {address}")
                
                # Получаем TRC20 транзакции для адреса
                trc20_transactions = TronMonitor.fetch_trc20_transactions(address)
                logger.info(f"Получено {len(trc20_transactions)} TRC20 транзакций для адреса {address}")
                
                # Обрабатываем TRC20 транзакции
                TronMonitor.process_trc20_transactions(db, wallet_data, trc20_transactions, current_block)
            
            elapsed_time = time.time() - start_time
            logger.info(f"Мониторинг TRON/TRC20 транзакций завершен, время выполнения: {elapsed_time:.2f} секунд")
            
        except Exception as e:
            logger.error(f"Ошибка при мониторинге TRON/TRC20 транзакций: {str(e)}")