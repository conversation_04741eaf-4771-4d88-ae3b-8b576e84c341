import logging
import requests
import time
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session
from web3 import Web3

from app.config import settings
from app.models.transaction import Transaction
from app.kafka.producer import send_transaction_event, send_deposit_confirmed_event

# Настройка логирования
logger = logging.getLogger(__name__)

class ETHMonitor:
    """Монитор для отслеживания транзакций в сети Ethereum (ETH и ERC20 токены)"""
    
    @staticmethod
    def get_wallet_addresses(db: Session) -> List[Dict[str, Any]]:
        """
        Получение всех Ethereum адресов из базы данных
        
        Returns:
            List[Dict[str, Any]]: Список словарей с информацией о кошельках
        """
        try:
            # В реальном приложении здесь должен быть запрос к сервису кошельков
            # или к базе данных для получения списка адресов и их пользователей
            response = requests.get(
                f"{settings.WALLET_SERVICE_URL}/api/wallets/eth/all",
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения ETH адресов: {response.text}")
                return []
            
            return response.json()
        except Exception as e:
            logger.error(f"Ошибка при получении ETH адресов: {str(e)}")
            return []
    
    @staticmethod
    def fetch_eth_transactions(address: str) -> List[Dict[str, Any]]:
        """
        Получение ETH транзакций для заданного Ethereum адреса
        
        Args:
            address: Ethereum адрес
            
        Returns:
            List[Dict[str, Any]]: Список транзакций
        """
        try:
            url = f"https://api.etherscan.io/api"
            params = {
                "module": "account",
                "action": "txlist",
                "address": address,
                "startblock": 0,
                "endblock": ********,
                "sort": "desc",
                "apikey": settings.ETHERSCAN_API_KEY
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения ETH транзакций для адреса {address}: {response.text}")
                return []
            
            data = response.json()
            if data.get("status") != "1":
                logger.error(f"Ошибка API Etherscan: {data.get('message')}")
                return []
            
            return data.get("result", [])
        except Exception as e:
            logger.error(f"Ошибка при получении ETH транзакций для адреса {address}: {str(e)}")
            return []
    
    @staticmethod
    def fetch_erc20_transactions(address: str, contract_address: str = None) -> List[Dict[str, Any]]:
        """
        Получение транзакций ERC20 токенов для заданного Ethereum адреса
        
        Args:
            address: Ethereum адрес
            contract_address: Адрес контракта токена (если None, возвращает все ERC20 транзакции)
            
        Returns:
            List[Dict[str, Any]]: Список транзакций
        """
        try:
            url = f"https://api.etherscan.io/api"
            params = {
                "module": "account",
                "action": "tokentx",
                "address": address,
                "startblock": 0,
                "endblock": ********,
                "sort": "desc",
                "apikey": settings.ETHERSCAN_API_KEY
            }
            
            if contract_address:
                params["contractaddress"] = contract_address
            
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения ERC20 транзакций для адреса {address}: {response.text}")
                return []
            
            data = response.json()
            if data.get("status") != "1":
                # Если нет транзакций, API возвращает статус "0"
                if data.get("message") == "No transactions found":
                    return []
                logger.error(f"Ошибка API Etherscan: {data.get('message')}")
                return []
            
            return data.get("result", [])
        except Exception as e:
            logger.error(f"Ошибка при получении ERC20 транзакций для адреса {address}: {str(e)}")
            return []
    
    @staticmethod
    def get_current_block_number() -> int:
        """
        Получение текущего номера блока в сети Ethereum
        
        Returns:
            int: Номер блока
        """
        try:
            # Убедимся, что URL сформирован правильно
            infura_url = f"{settings.ETH_API_URL}{settings.ETH_API_KEY}"
            logger.info(f"Подключение к Ethereum API по URL: {infura_url}")
            
            # Используем provider_class и provider_kwargs для большей гибкости
            w3 = Web3(Web3.HTTPProvider(
                infura_url,
                request_kwargs={'timeout': 30}
            ))
            
            # Проверим подключение
            if not w3.is_connected():
                logger.error("Не удалось подключиться к Ethereum API")
                raise Exception("Web3 не подключен к Ethereum API")
                
            return w3.eth.block_number
        except Exception as e:
            logger.error(f"Ошибка при получении текущего номера блока Ethereum: {str(e)}")
            # Резервный механизм с использованием Etherscan API
            try:
                url = "https://api.etherscan.io/api"
                params = {
                    "module": "proxy",
                    "action": "eth_blockNumber",
                    "apikey": settings.ETHERSCAN_API_KEY
                }
                
                logger.info(f"Использование запасного метода через Etherscan API")
                response = requests.get(url, params=params)
                
                if response.status_code != 200:
                    logger.error(f"Ошибка получения текущего блока: {response.text}")
                    return 0
                
                data = response.json()
                if data.get("status") == "0":
                    logger.error(f"Ошибка API Etherscan: {data.get('message')}")
                    return 0
                    
                block_number = int(data.get("result", "0x0"), 16)
                logger.info(f"Получен номер блока через Etherscan API: {block_number}")
                return block_number
            except Exception as e2:
                logger.error(f"Вторичная ошибка при получении текущего блока: {str(e2)}")
                return 0
    
    @staticmethod
    def process_eth_transactions(db: Session, wallet_data: Dict[str, Any], transactions: List[Dict[str, Any]], current_block: int):
        """
        Обработка ETH транзакций для кошелька
        
        Args:
            db: Сессия базы данных
            wallet_data: Данные о кошельке
            transactions: Список транзакций
            current_block: Текущий номер блока
        """
        address = wallet_data["address"].lower()
        user_id = wallet_data["user_id"]
        
        for tx in transactions:
            try:
                # Проверяем, входящая ли это транзакция для нашего адреса
                is_incoming = tx.get("to", "").lower() == address
                
                if not is_incoming:
                    continue
                
                tx_hash = tx.get("hash")
                # Сумма в Wei, переводим в ETH
                amount = float(tx.get("value", 0)) / 1e18
                
                if amount <= 0:
                    continue
                
                # Вычисляем количество подтверждений
                block_number = int(tx.get("blockNumber", 0))
                confirmations = current_block - block_number if block_number > 0 else 0
                
                # Проверяем, существует ли уже транзакция в базе
                existing_tx = db.query(Transaction).filter(
                    Transaction.tx_hash == tx_hash
                ).first()
                
                if existing_tx:
                    # Обновляем существующую транзакцию
                    if existing_tx.confirmations != confirmations:
                        existing_tx.confirmations = confirmations
                        
                        # Определяем статус на основе количества подтверждений
                        if confirmations >= settings.ETH_MIN_CONFIRMATIONS:
                            if existing_tx.status != "confirmed":
                                existing_tx.status = "confirmed"
                                
                                # Отправляем событие в Kafka, если транзакция подтверждена и еще не обработана
                                if not existing_tx.is_processed:
                                    existing_tx.is_processed = True
                                    
                                    # Отправляем событие в основной топик транзакций
                                    send_transaction_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency=existing_tx.currency,
                                        tx_hash=existing_tx.tx_hash,
                                        status="confirmed"
                                    )
                                    
                                    # Отправляем событие в Treasury Service для консолидации средств
                                    send_deposit_confirmed_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency=existing_tx.currency,
                                        wallet_address=existing_tx.wallet_address,
                                        tx_hash=existing_tx.tx_hash
                                    )
                                    
                                    logger.info(f"Транзакция ETH {tx_hash} подтверждена и отправлена в Kafka и Treasury Service")
                        
                        db.commit()
                        logger.info(f"Обновлена транзакция ETH {tx_hash}, подтверждений: {confirmations}")
                else:
                    # Создаем новую транзакцию
                    status = "confirmed" if confirmations >= settings.ETH_MIN_CONFIRMATIONS else "pending"
                    is_processed = status == "confirmed"
                    
                    new_tx = Transaction(
                        user_id=user_id,
                        wallet_address=address,
                        tx_hash=tx_hash,
                        currency="ETH",
                        network="ERC20",
                        amount=amount,
                        confirmations=confirmations,
                        status=status,
                        is_processed=is_processed,
                        created_at=datetime.utcnow()
                    )
                    
                    db.add(new_tx)
                    db.commit()
                    
                    logger.info(f"Создана новая транзакция ETH {tx_hash}, сумма: {amount} ETH, статус: {status}")
                    
                    # Отправляем событие в Kafka, если транзакция уже подтверждена
                    if status == "confirmed":
                        # Отправляем событие в основной топик транзакций
                        send_transaction_event(
                            user_id=user_id,
                            amount=amount,
                            currency="ETH",
                            tx_hash=tx_hash,
                            status="confirmed"
                        )
                        
                        # Отправляем событие в Treasury Service для консолидации средств
                        send_deposit_confirmed_event(
                            user_id=user_id,
                            amount=amount,
                            currency="ETH",
                            wallet_address=address,
                            tx_hash=tx_hash
                        )
                        
                        logger.info(f"Новая подтвержденная транзакция ETH {tx_hash} отправлена в Kafka и Treasury Service")
                
            except Exception as e:
                db.rollback()
                logger.error(f"Ошибка при обработке ETH транзакции {tx.get('hash', 'unknown')}: {str(e)}")
    
    @staticmethod
    def process_erc20_transactions(db: Session, wallet_data: Dict[str, Any], transactions: List[Dict[str, Any]], current_block: int):
        """
        Обработка ERC20 транзакций для кошелька
        
        Args:
            db: Сессия базы данных
            wallet_data: Данные о кошельке
            transactions: Список транзакций
            current_block: Текущий номер блока
        """
        address = wallet_data["address"].lower()
        user_id = wallet_data["user_id"]
        
        for tx in transactions:
            try:
                # Проверяем, входящая ли это транзакция для нашего адреса
                is_incoming = tx.get("to", "").lower() == address
                
                if not is_incoming:
                    continue
                
                tx_hash = tx.get("hash")
                token_symbol = tx.get("tokenSymbol", "")
                
                # Поддерживаем только USDC
                if token_symbol != "USDC":
                    continue
                
                # Получаем количество токенов, с учетом десятичных знаков
                token_decimals = int(tx.get("tokenDecimal", 18))
                amount = float(tx.get("value", 0)) / (10 ** token_decimals)
                
                if amount <= 0:
                    continue
                
                # Вычисляем количество подтверждений
                block_number = int(tx.get("blockNumber", 0))
                confirmations = current_block - block_number if block_number > 0 else 0
                
                # Проверяем, существует ли уже транзакция в базе
                existing_tx = db.query(Transaction).filter(
                    Transaction.tx_hash == tx_hash
                ).first()
                
                if existing_tx:
                    # Обновляем существующую транзакцию
                    if existing_tx.confirmations != confirmations:
                        existing_tx.confirmations = confirmations
                        
                        # Определяем статус на основе количества подтверждений
                        if confirmations >= settings.ETH_MIN_CONFIRMATIONS:
                            if existing_tx.status != "confirmed":
                                existing_tx.status = "confirmed"
                                
                                # Отправляем событие в Kafka, если транзакция подтверждена и еще не обработана
                                if not existing_tx.is_processed:
                                    existing_tx.is_processed = True
                                    
                                    # Отправляем событие в основной топик транзакций
                                    send_transaction_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency=existing_tx.currency,
                                        tx_hash=existing_tx.tx_hash,
                                        status="confirmed"
                                    )
                                    
                                    # Отправляем событие в Treasury Service для консолидации средств
                                    send_deposit_confirmed_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency=existing_tx.currency,
                                        wallet_address=existing_tx.wallet_address,
                                        tx_hash=existing_tx.tx_hash
                                    )
                                    
                                    logger.info(f"Транзакция ERC20 {tx_hash} подтверждена и отправлена в Kafka и Treasury Service")
                        
                        db.commit()
                        logger.info(f"Обновлена транзакция ERC20 {tx_hash}, подтверждений: {confirmations}")
                else:
                    # Создаем новую транзакцию
                    status = "confirmed" if confirmations >= settings.ETH_MIN_CONFIRMATIONS else "pending"
                    is_processed = status == "confirmed"
                    
                    new_tx = Transaction(
                        user_id=user_id,
                        wallet_address=address,
                        tx_hash=tx_hash,
                        currency="USDC",
                        network="ERC20",
                        amount=amount,
                        confirmations=confirmations,
                        status=status,
                        is_processed=is_processed,
                        created_at=datetime.utcnow()
                    )
                    
                    db.add(new_tx)
                    db.commit()
                    
                    logger.info(f"Создана новая ERC20 транзакция {tx_hash}, сумма: {amount} USDC, статус: {status}")
                    
                    # Отправляем событие в Kafka, если транзакция уже подтверждена
                    if status == "confirmed":
                        # Отправляем событие в основной топик транзакций
                        send_transaction_event(
                            user_id=user_id,
                            amount=amount,
                            currency="USDC",
                            tx_hash=tx_hash,
                            status="confirmed"
                        )
                        
                        # Отправляем событие в Treasury Service для консолидации средств
                        send_deposit_confirmed_event(
                            user_id=user_id,
                            amount=amount,
                            currency="USDC-ERC20",  # Указываем формат USDC-ERC20 для Treasury Service
                            wallet_address=address,
                            tx_hash=tx_hash
                        )
                        
                        logger.info(f"Новая подтвержденная ERC20 транзакция {tx_hash} отправлена в Kafka и Treasury Service")
                
            except Exception as e:
                db.rollback()
                logger.error(f"Ошибка при обработке ERC20 транзакции {tx.get('hash', 'unknown')}: {str(e)}")
    
    @staticmethod
    def monitor(db: Session):
        """
        Основная функция мониторинга Ethereum транзакций
        
        Args:
            db: Сессия базы данных
        """
        try:
            start_time = time.time()
            logger.info("Начало мониторинга ETH/ERC20 транзакций")
            
            # Получаем текущий номер блока
            current_block = ETHMonitor.get_current_block_number()
            if current_block == 0:
                logger.error("Не удалось получить текущий номер блока Ethereum")
                return
            
            logger.info(f"Текущий блок Ethereum: {current_block}")
            
            # Получаем все Ethereum адреса
            wallet_addresses = ETHMonitor.get_wallet_addresses(db)
            logger.info(f"Получено {len(wallet_addresses)} ETH адресов для мониторинга")
            
            # Обрабатываем каждый адрес
            for wallet_data in wallet_addresses:
                address = wallet_data["address"]
                logger.info(f"Проверка ETH транзакций для адреса {address}")
                
                # Получаем ETH транзакции для адреса
                eth_transactions = ETHMonitor.fetch_eth_transactions(address)
                logger.info(f"Получено {len(eth_transactions)} ETH транзакций для адреса {address}")
                
                # Обрабатываем ETH транзакции
                ETHMonitor.process_eth_transactions(db, wallet_data, eth_transactions, current_block)
                
                # Получаем ERC20 транзакции для адреса
                erc20_transactions = ETHMonitor.fetch_erc20_transactions(address)
                logger.info(f"Получено {len(erc20_transactions)} ERC20 транзакций для адреса {address}")
                
                # Обрабатываем ERC20 транзакции
                ETHMonitor.process_erc20_transactions(db, wallet_data, erc20_transactions, current_block)
            
            elapsed_time = time.time() - start_time
            logger.info(f"Мониторинг ETH/ERC20 транзакций завершен, время выполнения: {elapsed_time:.2f} секунд")
            
        except Exception as e:
            logger.error(f"Ошибка при мониторинге ETH/ERC20 транзакций: {str(e)}")