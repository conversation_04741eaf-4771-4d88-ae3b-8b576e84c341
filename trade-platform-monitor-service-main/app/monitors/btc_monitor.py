import logging
import requests
import time
from datetime import datetime
from typing import List, Dict, Any
from sqlalchemy.orm import Session

from app.config import settings
from app.models.transaction import Transaction
from app.kafka.producer import send_transaction_event, send_deposit_confirmed_event

# Настройка логирования
logger = logging.getLogger(__name__)

class BTCMonitor:
    """Монитор для отслеживания транзакций в сети Bitcoin"""
    
    @staticmethod
    def get_wallet_addresses(db: Session) -> List[Dict[str, Any]]:
        """
        Получение всех Bitcoin адресов из базы данных
        
        Returns:
            List[Dict[str, Any]]: Список словарей с информацией о кошельках
        """
        try:
            # В реальном приложении здесь должен быть запрос к сервису кошельков
            # или к базе данных для получения списка адресов и их пользователей
            response = requests.get(
                f"{settings.WALLET_SERVICE_URL}/api/wallets/btc/all",
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения BTC адресов: {response.text}")
                return []
            
            return response.json()
        except Exception as e:
            logger.error(f"Ошибка при получении BTC адресов: {str(e)}")
            return []
    
    @staticmethod
    def get_current_block_height() -> int:
        """
        Получение текущей высоты блока Bitcoin.
        
        Returns:
            int: Текущая высота блока
        """
        try:
            url = f"{settings.BTC_API_URL}/blocks/tip/height"
            params = {}
            if settings.BTC_API_KEY:
                params["token"] = settings.BTC_API_KEY
            
            logger.info(f"Запрос текущей высоты блока из {url}")
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                height = int(response.text)
                logger.info(f"Текущая высота блока Bitcoin: {height}")
                return height
            
            logger.error(f"Ошибка получения высоты блока: HTTP {response.status_code}")
            return 0
        except Exception as e:
            logger.error(f"Ошибка при получении текущей высоты блока: {str(e)}")
            return 0
    
    @staticmethod
    def fetch_transactions(address: str) -> List[Dict[str, Any]]:
        """
        Получение транзакций для заданного Bitcoin адреса
        
        Args:
            address: Bitcoin адрес
            
        Returns:
            List[Dict[str, Any]]: Список транзакций
        """
        try:
            url = f"{settings.BTC_API_URL}/addrs/{address}/full"
            params = {"limit": 50}
            
            if settings.BTC_API_KEY:
                params["token"] = settings.BTC_API_KEY
            
            logger.info(f"Запрос транзакций для адреса {address} из BlockCypher API: {url}")
            response = requests.get(url, params=params)
            
            if response.status_code != 200:
                logger.error(f"Ошибка получения транзакций для адреса {address}: {response.text}")
                return []
            
            data = response.json()
            logger.debug(f"Получен ответ от API для адреса {address}")
            return data.get("txs", [])
        except Exception as e:
            logger.error(f"Ошибка при получении транзакций для адреса {address}: {str(e)}")
            return []
    
    @staticmethod
    def process_transactions(db: Session, wallet_data: Dict[str, Any], transactions: List[Dict[str, Any]]):
        """
        Обработка транзакций Bitcoin для кошелька
        
        Args:
            db: Сессия базы данных
            wallet_data: Данные о кошельке
            transactions: Список транзакций
        """
        address = wallet_data["address"]
        user_id = wallet_data["user_id"]
        
        # Получаем текущую высоту блока для правильного расчета подтверждений
        current_block_height = BTCMonitor.get_current_block_height()
        
        logger.info(f"Обработка {len(transactions)} транзакций для адреса {address}")
        logger.info(f"BTC_MIN_CONFIRMATIONS установлено в {settings.BTC_MIN_CONFIRMATIONS}")
        
        for tx in transactions:
            try:
                # Отладочное логирование - исходные данные транзакции
                tx_hash = tx.get("hash", "unknown")
                logger.debug(f"Начало анализа транзакции: {tx_hash}")
                
                # Проверяем, входящая ли это транзакция для нашего адреса
                is_incoming = False
                amount = 0.0
                
                # Получаем выходы транзакции
                outputs = tx.get("outputs", [])
                logger.debug(f"Транзакция {tx_hash} имеет {len(outputs)} выходов")
                
                for output in outputs:
                    # Получаем адреса выхода
                    addresses = output.get("addresses", [])
                    
                    for addr in addresses:
                        if addr == address:
                            is_incoming = True
                            value = output.get("value", 0)
                            amount += value / 1e8  # Конвертация из сатоши в BTC
                            logger.debug(f"Обнаружен входящий платеж для {address}: {value} сатоши ({value/1e8} BTC)")
                
                if not is_incoming:
                    logger.debug(f"Транзакция {tx_hash} не является входящей для адреса {address}")
                    continue
                    
                if amount <= 0:
                    logger.debug(f"Транзакция {tx_hash} имеет нулевую или отрицательную сумму: {amount}")
                    continue
                
                logger.info(f"Обнаружена входящая транзакция {tx_hash} для {address} на сумму {amount} BTC")
                
                # Новый способ расчета подтверждений на основе высоты блока
                block_height = tx.get("block_height", 0)
                
                # Если транзакция еще не в блоке, у нее 0 подтверждений
                confirmations = 0
                
                if block_height > 0 and current_block_height > 0:
                    confirmations = current_block_height - block_height + 1
                
                logger.info(f"[ВАЖНО] Транзакция {tx_hash} в блоке {block_height}, текущий блок {current_block_height}, вычисленные подтверждения: {confirmations}/{settings.BTC_MIN_CONFIRMATIONS}")
                
                # Проверяем, существует ли уже транзакция в базе
                existing_tx = db.query(Transaction).filter(
                    Transaction.tx_hash == tx_hash
                ).first()
                
                if existing_tx:
                    # Обновляем существующую транзакцию
                    logger.info(f"Транзакция {tx_hash} уже существует в БД, текущий статус: {existing_tx.status}, подтверждения: {existing_tx.confirmations}, обработана: {existing_tx.is_processed}")
                    
                    if existing_tx.confirmations != confirmations:
                        old_confirmations = existing_tx.confirmations
                        existing_tx.confirmations = confirmations
                        logger.info(f"Обновлены подтверждения с {old_confirmations} на {confirmations}")
                        
                        # Определяем статус на основе количества подтверждений
                        if confirmations >= settings.BTC_MIN_CONFIRMATIONS:
                            if existing_tx.status != "confirmed":
                                old_status = existing_tx.status
                                existing_tx.status = "confirmed"
                                logger.info(f"Обновлен статус с {old_status} на confirmed")
                                
                                # Отправляем событие в Kafka, если транзакция подтверждена и еще не обработана
                                if not existing_tx.is_processed:
                                    existing_tx.is_processed = True
                                    
                                    logger.info(f"[ВАЖНО] Отправка подтвержденной транзакции {tx_hash} в Kafka")
                                    
                                    # Отправляем событие в основной топик транзакций
                                    result1 = send_transaction_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency="BTC",
                                        tx_hash=existing_tx.tx_hash,
                                        status="confirmed"
                                    )
                                    
                                    logger.info(f"Результат отправки события транзакции: {result1}")
                                    
                                    # Отправляем событие в Treasury Service для консолидации средств
                                    # ИСПРАВЛЕНО: добавлен формат "BTC-BTC" по аналогии с другими валютами
                                    result2 = send_deposit_confirmed_event(
                                        user_id=existing_tx.user_id,
                                        amount=existing_tx.amount,
                                        currency="BTC-BTC",  # Специальный формат для Treasury
                                        wallet_address=existing_tx.wallet_address,
                                        tx_hash=existing_tx.tx_hash
                                    )
                                    
                                    logger.info(f"Результат отправки события в Treasury: {result2}")
                                    
                                    logger.info(f"[ВАЖНО] Транзакция {tx_hash} подтверждена и отправлена в Kafka и Treasury Service")
                                else:
                                    logger.info(f"Транзакция {tx_hash} уже была обработана ранее")
                            else:
                                logger.info(f"Транзакция {tx_hash} уже имеет статус confirmed")
                        else:
                            logger.info(f"Недостаточно подтверждений ({confirmations}/{settings.BTC_MIN_CONFIRMATIONS}) для отправки в Treasury")
                        
                        db.commit()
                        logger.info(f"Обновлена транзакция {tx_hash}, подтверждений: {confirmations}")
                    else:
                        logger.info(f"Количество подтверждений не изменилось: {confirmations}")
                else:
                    # Создаем новую транзакцию
                    logger.info(f"Создаем новую запись для транзакции {tx_hash}")
                    
                    status = "confirmed" if confirmations >= settings.BTC_MIN_CONFIRMATIONS else "pending"
                    is_processed = status == "confirmed"
                    
                    new_tx = Transaction(
                        user_id=user_id,
                        wallet_address=address,
                        tx_hash=tx_hash,
                        currency="BTC",
                        network="BTC",
                        amount=amount,
                        confirmations=confirmations,
                        status=status,
                        is_processed=is_processed,
                        created_at=datetime.utcnow()
                    )
                    
                    db.add(new_tx)
                    db.commit()
                    
                    logger.info(f"Создана новая транзакция {tx_hash}, сумма: {amount} BTC, статус: {status}, подтверждений: {confirmations}/{settings.BTC_MIN_CONFIRMATIONS}")
                    
                    # Отправляем событие в Kafka, если транзакция уже подтверждена
                    if status == "confirmed":
                        logger.info(f"[ВАЖНО] Отправка новой подтвержденной транзакции {tx_hash} в Kafka")
                        
                        # Отправляем событие в основной топик транзакций
                        result1 = send_transaction_event(
                            user_id=user_id,
                            amount=amount,
                            currency="BTC",
                            tx_hash=tx_hash,
                            status="confirmed"
                        )
                        
                        logger.info(f"Результат отправки события транзакции: {result1}")
                        
                        # Отправляем событие в Treasury Service для консолидации средств
                        # ИСПРАВЛЕНО: добавлен формат "BTC-BTC" по аналогии с другими валютами
                        result2 = send_deposit_confirmed_event(
                            user_id=user_id,
                            amount=amount,
                            currency="BTC-BTC",  # Специальный формат для Treasury
                            wallet_address=address,
                            tx_hash=tx_hash
                        )
                        
                        logger.info(f"Результат отправки события в Treasury: {result2}")
                        
                        logger.info(f"[ВАЖНО] Новая подтвержденная транзакция {tx_hash} отправлена в Kafka и Treasury Service")
                    else:
                        logger.info(f"Транзакция {tx_hash} не имеет достаточно подтверждений для отправки: {confirmations}/{settings.BTC_MIN_CONFIRMATIONS}")
                
            except Exception as e:
                db.rollback()
                logger.error(f"Ошибка при обработке транзакции {tx.get('hash', 'unknown')}: {str(e)}", exc_info=True)
    
    @staticmethod
    def monitor(db: Session):
        """
        Основная функция мониторинга Bitcoin транзакций
        
        Args:
            db: Сессия базы данных
        """
        try:
            start_time = time.time()
            logger.info("Начало мониторинга BTC транзакций")
            
            # Получаем все Bitcoin адреса
            wallet_addresses = BTCMonitor.get_wallet_addresses(db)
            logger.info(f"Получено {len(wallet_addresses)} BTC адресов для мониторинга")
            
            # Обрабатываем каждый адрес
            for wallet_data in wallet_addresses:
                address = wallet_data["address"]
                logger.info(f"Проверка транзакций для адреса {address}")
                
                # Получаем транзакции для адреса
                transactions = BTCMonitor.fetch_transactions(address)
                logger.info(f"Получено {len(transactions)} транзакций для адреса {address}")
                
                # Обрабатываем транзакции
                BTCMonitor.process_transactions(db, wallet_data, transactions)
            
            elapsed_time = time.time() - start_time
            logger.info(f"Мониторинг BTC транзакций завершен, время выполнения: {elapsed_time:.2f} секунд")
            
        except Exception as e:
            logger.error(f"Ошибка при мониторинге BTC транзакций: {str(e)}", exc_info=True)