import logging
import asyncio
import signal
import sys
import time
import schedule
from threading import Thread
from contextlib import contextmanager
from fastapi import FastAPI, Depends, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session

from app.config import settings
from app.database import get_db, check_db_connection, create_tables
from app.monitors.btc_monitor import BTCMonitor
from app.monitors.eth_monitor import ETHMonitor
from app.monitors.tron_monitor import TronMonitor

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
)
logger = logging.getLogger(__name__)

app = FastAPI(title="Blockchain Monitor Service")

# Настройка CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "https://app.bitmei.com",
        "https://dev.bitmei.com",
        "https://bitmei.com",
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3012",
        "https://auth-api.eu.ngrok.io",
        "https://profile-api.eu.ngrok.io",
        "https://wallet-api.eu.ngrok.io",
        "https://balance-api.eu.ngrok.io",
        "https://balance-ws.eu.ngrok.io",
        "https://analysis-api.eu.ngrok.io",
        "https://analysis-ws.eu.ngrok.io",
        "https://trading-api.eu.ngrok.io",
        "https://trading-ws.eu.ngrok.io",
        "https://withdraw-api.eu.ngrok.io",
        "https://referal-api.eu.ngrok.io",
        "https://support-api.eu.ngrok.io",
        "https://media-api.eu.ngrok.io",
        "https://trading-chart-ws.eu.ngrok.io"
    ],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Переменные для управления фоновыми задачами
should_exit = False
monitor_thread = None

@contextmanager
def get_db_context():
    """Контекстный менеджер для сессии БД"""
    db = next(get_db())
    try:
        yield db
    finally:
        db.close()

def run_btc_monitor():
    """Запуск мониторинга BTC транзакций"""
    with get_db_context() as db:
        logger.info("Запуск мониторинга BTC транзакций")
        BTCMonitor.monitor(db)

def run_eth_monitor():
    """Запуск мониторинга ETH/ERC20 транзакций"""
    with get_db_context() as db:
        logger.info("Запуск мониторинга ETH/ERC20 транзакций")
        ETHMonitor.monitor(db)

def run_tron_monitor():
    """Запуск мониторинга TRON/TRC20 транзакций"""
    with get_db_context() as db:
        logger.info("Запуск мониторинга TRON/TRC20 транзакций")
        TronMonitor.monitor(db)

def run_scheduled_tasks():
    """Запуск запланированных задач"""
    global should_exit
    
    schedule.every(settings.MONITORING_INTERVAL_SECONDS).seconds.do(run_btc_monitor)
    schedule.every(settings.MONITORING_INTERVAL_SECONDS).seconds.do(run_eth_monitor)
    schedule.every(settings.MONITORING_INTERVAL_SECONDS).seconds.do(run_tron_monitor)
    
    # Запускаем задачи сразу при старте
    run_btc_monitor()
    run_eth_monitor()
    run_tron_monitor()
    
    while not should_exit:
        schedule.run_pending()
        time.sleep(1)

def start_monitor_thread():
    """Запуск отдельного потока для мониторинга"""
    global monitor_thread
    monitor_thread = Thread(target=run_scheduled_tasks, daemon=True)
    monitor_thread.start()
    logger.info("Фоновый мониторинг запущен")

def stop_monitor_thread():
    """Остановка потока мониторинга"""
    global should_exit, monitor_thread
    if monitor_thread:
        logger.info("Остановка фонового мониторинга")
        should_exit = True
        monitor_thread.join(timeout=10)
        if monitor_thread.is_alive():
            logger.warning("Не удалось корректно остановить поток мониторинга")
        else:
            logger.info("Фоновый мониторинг остановлен")

@app.on_event("startup")
async def startup_event():
    """Действия при запуске приложения"""
    logger.info("Запуск Blockchain Monitor Service")
    
    # Проверка соединения с БД
    if not check_db_connection():
        logger.error("Не удалось подключиться к БД, завершение работы")
        sys.exit(1)
    
    # Создание таблиц в БД
    create_tables()
    
    # Запуск фонового мониторинга
    start_monitor_thread()

@app.on_event("shutdown")
async def shutdown_event():
    """Действия при остановке приложения"""
    logger.info("Остановка Blockchain Monitor Service")
    stop_monitor_thread()

@app.get("/health")
async def health_check():
    """Проверка здоровья сервиса"""
    return {"status": "healthy", "service": "blockchain-monitor"}

@app.get("/transactions/{tx_hash}")
async def get_transaction(tx_hash: str, db: Session = Depends(get_db)):
    """
    Получение информации о транзакции по хешу
    
    Args:
        tx_hash: Хеш транзакции
        db: Сессия базы данных
        
    Returns:
        dict: Информация о транзакции
    """
    from app.models.transaction import Transaction
    
    transaction = db.query(Transaction).filter(Transaction.tx_hash == tx_hash).first()
    
    if not transaction:
        return {"error": "Transaction not found"}
    
    return {
        "tx_hash": transaction.tx_hash,
        "currency": transaction.currency,
        "network": transaction.network,
        "amount": transaction.amount,
        "confirmations": transaction.confirmations,
        "status": transaction.status,
        "created_at": transaction.created_at.isoformat()
    }

def handle_sigterm(*args):
    """Обработка сигнала SIGTERM"""
    logger.info("Получен сигнал SIGTERM, завершение работы")
    stop_monitor_thread()
    sys.exit(0)

# Регистрация обработчика SIGTERM
signal.signal(signal.SIGTERM, handle_sigterm)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run("app.main:app", host="0.0.0.0", port=settings.PORT, reload=settings.DEBUG)