import os
from typing import Any, Dict, List, Optional
from decimal import Decimal
from pydantic_settings import BaseSettings
from pydantic import PostgresDsn, validator, HttpUrl


class Settings(BaseSettings):
    """Application settings."""
    # Application settings
    APP_NAME: str = os.getenv("APP_NAME", "treasury-service")
    PROJECT_NAME: str = "Treasury Service"
    API_V1_STR: str = "/api"
    SECRET_KEY: Optional[str] = None
    DEBUG: bool = os.getenv("DEBUG", "False").lower() in ("true", "1", "t")
    PORT: int = int(os.getenv("PORT", "8005"))
    
    # CORS settings
    BACKEND_CORS_ORIGINS: List[str] = [
        "https://app.bitmei.com",
        "https://dev.bitmei.com",
        "https://bitmei.com",
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:3012",
        "https://auth-api.eu.ngrok.io",
        "https://profile-api.eu.ngrok.io",
        "https://wallet-api.eu.ngrok.io",
        "https://balance-api.eu.ngrok.io",
        "https://balance-ws.eu.ngrok.io",
        "https://analysis-api.eu.ngrok.io",
        "https://analysis-ws.eu.ngrok.io",
        "https://trading-api.eu.ngrok.io",
        "https://trading-ws.eu.ngrok.io",
        "https://withdraw-api.eu.ngrok.io",
        "https://referal-api.eu.ngrok.io",
        "https://support-api.eu.ngrok.io",
        "https://media-api.eu.ngrok.io",
        "https://trading-chart-ws.eu.ngrok.io"
    ]
    
    # Postgres settings
    POSTGRES_SERVER: str = os.getenv("POSTGRES_SERVER", "localhost")
    POSTGRES_USER: str = os.getenv("POSTGRES_USER", "postgres")
    POSTGRES_PASSWORD: str = os.getenv("POSTGRES_PASSWORD", "postgres")
    POSTGRES_DB: str = os.getenv("POSTGRES_DB", "trade_platform_db")
    DATABASE_URL: Optional[str] = os.getenv("DATABASE_URL")
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None
    
    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        if values.get("DATABASE_URL"):
            return values.get("DATABASE_URL")
        return PostgresDsn.build(
            scheme="postgresql+asyncpg",
            user=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )
        
    # Corporate wallet addresses (format: address,type)
    BTC_CORPORATE_WALLET: Optional[str] = os.getenv("BTC_CORPORATE_WALLET", "")
    ETH_CORPORATE_WALLET: Optional[str] = os.getenv("ETH_CORPORATE_WALLET", "")
    USDC_ERC20_CORPORATE_WALLET: Optional[str] = os.getenv("USDC_ERC20_CORPORATE_WALLET", "")
    USDT_TRC20_CORPORATE_WALLET: Optional[str] = os.getenv("USDT_TRC20_CORPORATE_WALLET", "")
        
    # Fee wallet addresses (format: address,private_key)
    BTC_FEE_WALLET: Optional[str] = os.getenv("BTC_FEE_WALLET", "")
    ETH_FEE_WALLET: Optional[str] = os.getenv("ETH_FEE_WALLET", "")
    TRON_FEE_WALLET: Optional[str] = os.getenv("TRON_FEE_WALLET", "")
    
    # Minimum balances for fee wallets
    BTC_FEE_WALLET_MIN_BALANCE: Decimal = Decimal(os.getenv("BTC_FEE_WALLET_MIN_BALANCE", "0.001"))
    ETH_FEE_WALLET_MIN_BALANCE: Decimal = Decimal(os.getenv("ETH_FEE_WALLET_MIN_BALANCE", "0.01"))
    TRON_FEE_WALLET_MIN_BALANCE: Decimal = Decimal(os.getenv("TRON_FEE_WALLET_MIN_BALANCE", "10"))
    
    # Notifications
    NOTIFY_LOW_FEE_BALANCE: bool = os.getenv("NOTIFY_LOW_FEE_BALANCE", "True").lower() in ("true", "1", "t")
    ADMIN_EMAIL: Optional[str] = os.getenv("ADMIN_EMAIL", "")
    
    # Privacy settings
    BTC_INTERMEDIATE_WALLETS: str = os.getenv("BTC_INTERMEDIATE_WALLETS", "")
    ETH_INTERMEDIATE_WALLETS: str = os.getenv("ETH_INTERMEDIATE_WALLETS", "")
    TRON_INTERMEDIATE_WALLETS: str = os.getenv("TRON_INTERMEDIATE_WALLETS", "")
    BTC_INTERMEDIATE_KEYS: str = os.getenv("BTC_INTERMEDIATE_KEYS", "")
    ETH_INTERMEDIATE_KEYS: str = os.getenv("ETH_INTERMEDIATE_KEYS", "")
    TRON_INTERMEDIATE_KEYS: str = os.getenv("TRON_INTERMEDIATE_KEYS", "")

    # Вычисляемые списки (будут доступны как свойства)
    @property
    def BTC_INTERMEDIATE_WALLETS_LIST(self) -> List[str]:
        return [x.strip() for x in self.BTC_INTERMEDIATE_WALLETS.split(',') if x.strip()]

    @property
    def ETH_INTERMEDIATE_WALLETS_LIST(self) -> List[str]:
        return [x.strip() for x in self.ETH_INTERMEDIATE_WALLETS.split(',') if x.strip()]

    @property
    def TRON_INTERMEDIATE_WALLETS_LIST(self) -> List[str]:
        return [x.strip() for x in self.TRON_INTERMEDIATE_WALLETS.split(',') if x.strip()]

    @property
    def BTC_INTERMEDIATE_KEYS_LIST(self) -> List[str]:
        return [x.strip() for x in self.BTC_INTERMEDIATE_KEYS.split(',') if x.strip()]

    @property
    def ETH_INTERMEDIATE_KEYS_LIST(self) -> List[str]:
        return [x.strip() for x in self.ETH_INTERMEDIATE_KEYS.split(',') if x.strip()]

    @property
    def TRON_INTERMEDIATE_KEYS_LIST(self) -> List[str]:
        return [x.strip() for x in self.TRON_INTERMEDIATE_KEYS.split(',') if x.strip()]


    # Privacy configuration
    PRIVACY_ENABLED: bool = os.getenv("PRIVACY_ENABLED", "False").lower() in ("true", "1", "t")
    PRIVACY_HOPS: int = int(os.getenv("PRIVACY_HOPS", "2"))
    PRIVACY_MIN_DELAY: int = int(os.getenv("PRIVACY_MIN_DELAY", "300"))
    PRIVACY_MAX_DELAY: int = int(os.getenv("PRIVACY_MAX_DELAY", "1800"))
    
    # Testnet mode
    TESTNET_MODE: bool = os.getenv("TESTNET_MODE", "False").lower() in ("true", "1", "t")
    
    # Kafka settings
    KAFKA_BOOTSTRAP_SERVERS: str = os.getenv("KAFKA_BOOTSTRAP_SERVERS", "localhost:9093")
    KAFKA_BALANCE_UPDATES_TOPIC: str = os.getenv("KAFKA_BALANCE_UPDATES_TOPIC", "balance-updates")
    KAFKA_BLOCKCHAIN_EVENTS_TOPIC: str = os.getenv("KAFKA_BLOCKCHAIN_EVENTS_TOPIC", "blockchain-events")
    KAFKA_TREASURY_EVENTS_TOPIC: str = os.getenv("KAFKA_TREASURY_EVENTS_TOPIC", "treasury-events")
    KAFKA_TRANSACTION_TOPIC: str = os.getenv("KAFKA_TRANSACTION_TOPIC", "transaction-events")
    
    # Wallet Service Kafka topics
    WALLET_SERVICE_REQUEST_TOPIC: str = os.getenv("WALLET_SERVICE_REQUEST_TOPIC", "wallet-service-requests")
    WALLET_SERVICE_RESPONSE_TOPIC: str = os.getenv("WALLET_SERVICE_RESPONSE_TOPIC", "wallet-service-responses")
    WALLET_SERVICE_REQUEST_TIMEOUT: int = int(os.getenv("WALLET_SERVICE_REQUEST_TIMEOUT", "30"))

    # Wallet Service URL
    WALLET_SERVICE_URL: str = os.getenv("WALLET_SERVICE_URL", "http://localhost:8003")
    
    # Vault settings
    VAULT_URL: str = os.getenv("VAULT_URL", "http://localhost:8200")
    VAULT_TOKEN: str = os.getenv("VAULT_TOKEN", "dev-token")
    VAULT_KEYS_PATH: str = os.getenv("VAULT_KEYS_PATH", "kv/data/secret/treasury/blockchain")
    
    # Blockchain API settings
    # Bitcoin
    BTC_API_URL: str = os.getenv("BTC_API_URL", "https://api.blockcypher.com/v1/btc/main")
    BTC_API_KEY: str = os.getenv("BTC_API_KEY", "")
    BTC_MIN_CONFIRMATIONS: int = int(os.getenv("BTC_MIN_CONFIRMATIONS", "2"))
    
    # Ethereum
    ETH_API_URL: str = os.getenv("ETH_API_URL", "https://mainnet.infura.io/v3/")
    ETH_API_KEY: str = os.getenv("ETH_API_KEY", "")
    ETH_MIN_CONFIRMATIONS: int = int(os.getenv("ETH_MIN_CONFIRMATIONS", "12"))
    ETHERSCAN_API_KEY: str = os.getenv("ETHERSCAN_API_KEY", "")
    ETH_MAINNET_RPC_URL: str = os.getenv("ETH_MAINNET_RPC_URL", "")
    ETH_TESTNET_RPC_URL: str = os.getenv("ETH_TESTNET_RPC_URL", "")
    ETH_MAINNET_EXPLORER_API: str = os.getenv("ETH_MAINNET_EXPLORER_API", "https://api.etherscan.io")
    ETH_TESTNET_EXPLORER_API: str = os.getenv("ETH_TESTNET_EXPLORER_API", "https://api-goerli.etherscan.io")
    
    # Tron
    TRON_API_URL: str = os.getenv("TRON_API_URL", "https://api.trongrid.io")
    TRON_API_KEY: str = os.getenv("TRON_API_KEY", "")
    TRON_MIN_CONFIRMATIONS: int = int(os.getenv("TRON_MIN_CONFIRMATIONS", "19"))
    TRON_MAINNET_API_URL: str = os.getenv("TRON_MAINNET_API_URL", "https://api.trongrid.io")
    TRON_TESTNET_API_URL: str = os.getenv("TRON_TESTNET_API_URL", "https://api.shasta.trongrid.io")
    
    # Transaction confirmation thresholds
    BTC_CONFIRMATION_THRESHOLD: int = int(os.getenv("BTC_CONFIRMATION_THRESHOLD", BTC_MIN_CONFIRMATIONS))
    ETH_CONFIRMATION_THRESHOLD: int = int(os.getenv("ETH_CONFIRMATION_THRESHOLD", ETH_MIN_CONFIRMATIONS))
    TRON_CONFIRMATION_THRESHOLD: int = int(os.getenv("TRON_CONFIRMATION_THRESHOLD", TRON_MIN_CONFIRMATIONS))
    
    # Monitoring settings
    MONITORING_INTERVAL_SECONDS: int = int(os.getenv("MONITORING_INTERVAL_SECONDS", "60"))
    TRANSACTION_BATCH_SIZE: int = int(os.getenv("TRANSACTION_BATCH_SIZE", "100"))
    
    # Logging
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    
    class Config:
        case_sensitive = True
        env_file = ".env"


settings = Settings()