import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict, Optional

from app.core.config import settings


class J<PERSON><PERSON>ormatter(logging.Formatter):
    """
    JSON log formatter for structured logging.
    """
    def format(self, record: logging.LogRecord) -> str:
        log_record: Dict[str, Any] = {
            "timestamp": datetime.utcnow().isoformat(),
            "level": record.levelname,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add exception info if available
        if record.exc_info:
            log_record["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
            }
        
        # Add extra fields from record
        if hasattr(record, "extra"):
            log_record.update(record.extra)
        
        return json.dumps(log_record)


def setup_logging() -> None:
    """
    Configure logging for the application.
    """
    # Get log level from settings
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    
    # Create handlers
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(JSONFormatter())
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    root_logger.addHandler(console_handler)
    
    # Remove existing handlers if any
    for handler in root_logger.handlers[:]:
        if handler is not console_handler:
            root_logger.removeHandler(handler)
    
    # Configure library loggers
    for logger_name in ["uvicorn", "uvicorn.access", "sqlalchemy.engine"]:
        lib_logger = logging.getLogger(logger_name)
        lib_logger.handlers = []
        lib_logger.propagate = True


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger with the given name.
    
    Args:
        name: Logger name (typically __name__ of the module)
        
    Returns:
        Configured logger
    """
    return logging.getLogger(name)


def log_extra(
    logger: logging.Logger,
    level: str,
    message: str,
    extra: Optional[Dict[str, Any]] = None
) -> None:
    """
    Log a message with extra fields.
    
    Args:
        logger: Logger instance
        level: Log level (debug, info, warning, error, critical)
        message: Log message
        extra: Extra fields to include in the log
    """
    if extra is None:
        extra = {}
    
    log_method = getattr(logger, level.lower())
    
    # Create LogRecord with extra fields
    log_method(message, extra={"extra": extra})