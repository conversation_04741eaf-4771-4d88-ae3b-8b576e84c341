from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional
from uuid import UUID
from pydantic import BaseModel, Field

from app.db.models.task import TaskStatus

class TaskBase(BaseModel):
    """Base schema for consolidation task"""
    scheduled_time: Optional[datetime] = None

class TaskCreate(TaskBase):
    """Schema for creating a consolidation task"""
    pass

class TaskUpdate(BaseModel):
    """Schema for updating a consolidation task"""
    status: Optional[TaskStatus] = None
    scheduled_time: Optional[datetime] = None

# Добавляем специальную схему для обновления расписания
class TaskUpdateSchedule(BaseModel):
    """Schema for updating only the schedule of a task"""
    scheduled_time: datetime

class TaskInDBBase(TaskBase):
    """Base schema for a consolidation task in DB"""
    id: UUID
    status: TaskStatus
    total_transactions: int
    completed_transactions: int
    total_amount: Dict[str, float]
    fee_estimation: Dict[str, float]
    created_at: datetime
    updated_at: datetime

    class Config:
        # Заменено orm_mode на from_attributes для Pydantic v2
        from_attributes = True

class Task(TaskInDBBase):
    """Schema for task response"""
    completion_percentage: float = Field(..., description="Task completion percentage")

# Добавлено для совместимости с кодом, который ожидает этот класс
class TaskResponse(Task):
    """Alias for Task schema (for API response compatibility)"""
    pass

# Добавлено еще одно название для того же класса
class TaskDetailResponse(Task):
    """Alias for Task schema (for detailed API response compatibility)"""
    pass

class TaskList(BaseModel):
    """Schema for task list response"""
    tasks: List[Task]
    total: int