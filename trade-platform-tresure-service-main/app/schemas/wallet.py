from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional, List
from pydantic import BaseModel

# Перечисление для типов кошельков
class WalletType(str, Enum):
    """Enum for wallet types."""
    HOT = "hot"
    COLD = "cold"

class WalletBase(BaseModel):
    """Base schema for destination wallet"""
    address: str
    currency: str
    network: str
    type: WalletType
    balance: Decimal = Decimal(0)
    is_active: bool = True

class WalletCreate(WalletBase):
    """Schema for creating a destination wallet"""
    pass

class WalletUpdate(BaseModel):
    """Schema for updating a destination wallet"""
    balance: Optional[Decimal] = None
    is_active: Optional[bool] = None

class WalletInDBBase(WalletBase):
    """Base schema for a destination wallet in DB"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        # Используем from_attributes вместо orm_mode для Pydantic v2
        from_attributes = True

class Wallet(WalletInDBBase):
    """Schema for wallet response"""
    pass

# Добавлен для совместимости с API
class WalletResponse(Wallet):
    """Alias for Wallet schema (for API response compatibility)"""
    pass

class WalletList(BaseModel):
    """Schema for wallet list response"""
    wallets: List[Wallet]
    total: int