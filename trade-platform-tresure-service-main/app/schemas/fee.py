from datetime import datetime
from decimal import Decimal
from typing import Optional
from uuid import UUID
from pydantic import BaseModel

class FeeBase(BaseModel):
    """Base schema for fee settings"""
    currency: str
    network: str
    min_consolidation_amount: Decimal
    max_fee_percentage: Decimal
    priority: str = "medium"  # low, medium, high
    is_active: bool = True

class FeeCreate(FeeBase):
    """Schema for creating fee settings"""
    pass

class FeeUpdate(BaseModel):
    """Schema for updating fee settings"""
    min_consolidation_amount: Optional[Decimal] = None
    max_fee_percentage: Optional[Decimal] = None
    priority: Optional[str] = None
    is_active: Optional[bool] = None

# Добавляем псевдоним для FeeUpdate
class FeeSettingsUpdate(FeeUpdate):
    """Alias for FeeUpdate schema (for API consistency)"""
    pass

class FeeInDBBase(FeeBase):
    """Base schema for fee settings in DB"""
    id: UUID
    updated_at: datetime

    class Config:
        # Заменено orm_mode на from_attributes для Pydantic v2
        from_attributes = True

class Fee(FeeInDBBase):
    """Schema for fee settings response"""
    pass

# Добавлен псевдоним для класса Fee для совместимости с API
class FeeSettingsResponse(Fee):
    """Alias for Fee schema (for API response compatibility)"""
    pass

class FeeList(BaseModel):
    """Schema for fee settings list response"""
    fees: list[Fee]
    total: int