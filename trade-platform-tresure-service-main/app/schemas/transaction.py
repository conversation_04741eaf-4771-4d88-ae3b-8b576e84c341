from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import Optional
from uuid import UUID
from pydantic import BaseModel

from app.db.models.transaction import TransactionStatus

class TransactionBase(BaseModel):
    """Base schema for consolidation transaction"""
    source_wallet: str
    destination_wallet: str
    user_id: Optional[int] = None
    currency: str
    network: str
    amount: Decimal

class TransactionCreate(TransactionBase):
    """Schema for creating a consolidation transaction"""
    task_id: UUID

class TransactionUpdate(BaseModel):
    """Schema for updating a consolidation transaction"""
    status: Optional[TransactionStatus] = None
    tx_hash: Optional[str] = None
    fee: Optional[Decimal] = None
    error_message: Optional[str] = None

class TransactionInDBBase(TransactionBase):
    """Base schema for a consolidation transaction in DB"""
    id: UUID
    task_id: UUID
    status: TransactionStatus
    tx_hash: Optional[str] = None
    fee: Optional[Decimal] = None
    error_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        # Заменено orm_mode на from_attributes для Pydantic v2
        from_attributes = True

class Transaction(TransactionInDBBase):
    """Schema for transaction response"""
    pass

# Добавлен псевдоним для класса Transaction для совместимости с API
class TransactionResponse(Transaction):
    """Alias for Transaction schema (for API response compatibility)"""
    pass

class TransactionList(BaseModel):
    """Schema for transaction list response"""
    transactions: list[Transaction]
    total: int