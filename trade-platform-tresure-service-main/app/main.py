import asyncio
import random
from datetime import datetime
from decimal import Decima<PERSON>
from typing import List, <PERSON><PERSON>, Optional
from fastapi import <PERSON>AP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.openapi.utils import get_openapi
from prometheus_client import make_asgi_app
from sqlalchemy import create_engine, select

from app.api.routes import api_router
from app.core.config import settings
from app.core.logging import setup_logging, get_logger
from app.db.session import AsyncSessionLocal
from app.db.base import Base
from app.db.models.transaction import ConsolidationTransaction, TransactionStatus
from app.services.kafka_service import (
    close_producer, 
    consume_deposit_events, 
    consume_wallet_service_responses,
    request_private_key
)
from app.services.fee_service import FeeService
from app.services.wallet_service import WalletService
from app.services.fee_wallet_service import FeeWalletService
from app.services.blockchain.base import get_blockchain_connector
from sqlalchemy.ext.asyncio import AsyncSession

# Setup logging
setup_logging()
logger = get_logger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Treasury Service",
    description="Service for managing cryptocurrency assets and consolidating funds",
    version="1.0.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.BACKEND_CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Add Prometheus metrics
metrics_app = make_asgi_app()
app.mount("/metrics", metrics_app)

# Include API routes
app.include_router(api_router, prefix=settings.API_V1_STR)


# Custom OpenAPI schema
def custom_openapi():
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=app.title,
        version=app.version,
        description=app.description,
        routes=app.routes,
    )
    
    # Add additional info
    openapi_schema["info"]["x-logo"] = {
        "url": "https://fastapi.tiangolo.com/img/logo-margin/logo-teal.png"
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# Helper function to determine network from currency
def _determine_network(currency: str) -> str:
    """
    Determine blockchain network for a currency.
    """
    if "-" in currency:
        base, network = currency.split("-", 1)
        if network == "ERC20":
            return "ETH"
        elif network == "TRC20":
            return "TRON"
        elif network == "BTC":  # Добавляем проверку для формата BTC-BTC
            return "BTC"
        # Для случаев, когда базовая валюта совпадает с сетью
        elif base == network and base in ["BTC", "ETH", "TRON"]:
            return base
    
    if currency in ["BTC"]:
        return "BTC"
    elif currency in ["ETH"]:
        return "ETH"
    elif currency in ["TRX", "TRON"]:
        return "TRON"
    elif currency == "USDC":
        return "ETH"  # USDC по умолчанию в сети Ethereum
    
    logger.warning(f"Unknown currency: {currency}, defaulting to ETH network")
    return "ETH"


# Helper function to get intermediate wallets
async def _get_intermediate_wallets(network: str) -> List[Tuple[str, str]]:
    """
    Get list of intermediate wallets for a specific network
    """
    if network == "BTC":
        addresses = settings.BTC_INTERMEDIATE_WALLETS_LIST  # Используем новое имя свойства
        keys = settings.BTC_INTERMEDIATE_KEYS_LIST
    elif network == "ETH":
        addresses = settings.ETH_INTERMEDIATE_WALLETS_LIST
        keys = settings.ETH_INTERMEDIATE_KEYS_LIST
    elif network == "TRON":
        addresses = settings.TRON_INTERMEDIATE_WALLETS_LIST
        keys = settings.TRON_INTERMEDIATE_KEYS_LIST
    else:
        logger.warning(f"Unknown network for intermediate wallets: {network}")
        return []
        
    # Check if addresses and keys lists have the same length
    if len(addresses) != len(keys):
        logger.error(f"Mismatch in intermediate wallet configuration for {network}. "
                    f"Addresses: {len(addresses)}, Keys: {len(keys)}")
        return []
        
    # Combine addresses and keys into tuples
    return list(zip(addresses, keys))


# Execute privacy transfer through intermediate wallets
async def _execute_privacy_transfer(
    connector,
    tx: ConsolidationTransaction,
    intermediate_wallets: List[Tuple[str, str]],
    fee_wallet_address: str,
    fee_wallet_private_key: str
) -> str:
    """
    Execute private transfer through intermediate wallets
    """
    # Determine number of intermediate hops
    num_hops = min(settings.PRIVACY_HOPS, len(intermediate_wallets))
    
    # Select random intermediate wallets
    selected_wallets = random.sample(intermediate_wallets, num_hops)
    
    # Original transaction amount and currency
    amount = tx.amount
    currency = tx.currency  # Сохраняем информацию о валюте
    
    # Step 1: Transfer from source wallet to first intermediate wallet
    first_address, first_key = selected_wallets[0]
    
    # Slightly adjust amount to complicate tracking
    first_amount = amount * Decimal(1 - random.uniform(0.001, 0.003))
    
    # Record transaction hash
    first_tx_hash = await connector.send_with_fee_wallet(
        tx.source_wallet,
        first_address,
        first_amount,
        fee_wallet_address,
        fee_wallet_private_key,
        "medium",
        currency  # Передаем тип валюты
    )
    
    logger.info(f"Privacy transfer step 1/{num_hops+1}: Transaction {tx.id} sent to first intermediate wallet. "
               f"Hash: {first_tx_hash}")
    
    # Wait random time between transactions
    delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
    logger.info(f"Waiting {delay} seconds before next privacy transfer step")
    await asyncio.sleep(delay)
    
    # Go through the chain of intermediate wallets
    current_amount = first_amount
    current_address = first_address
    current_key = first_key
    last_tx_hash = first_tx_hash
    
    for i in range(1, num_hops):
        next_address, next_key = selected_wallets[i]
        
        # Slightly adjust amount for each step
        current_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.002))
        
        # Используем новую функцию, которая работает как send_with_fee_wallet, 
        # но принимает приватный ключ напрямую
        middle_tx_hash = await connector.create_and_broadcast_transaction_with_fee(
            current_address,
            next_address,
            current_amount,
            current_key,
            fee_wallet_address,
            fee_wallet_private_key,
            "medium",
            currency
        )
        
        logger.info(f"Privacy transfer step {i+1}/{num_hops+1}: Intermediate transfer completed. "
                   f"Hash: {middle_tx_hash}")
        
        # Update current data
        current_address = next_address
        current_key = next_key
        last_tx_hash = middle_tx_hash
        
        # Wait another random time
        delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
        logger.info(f"Waiting {delay} seconds before next privacy transfer step")
        await asyncio.sleep(delay)
    
    # Final step: from last intermediate wallet to corporate wallet
    final_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.002))
    
    # Используем ту же функцию для финальной транзакции
    final_tx_hash = await connector.create_and_broadcast_transaction_with_fee(
        current_address,
        tx.destination_wallet,
        final_amount,
        current_key,
        fee_wallet_address,
        fee_wallet_private_key,
        "medium",
        currency
    )
    
    logger.info(f"Privacy transfer step {num_hops+1}/{num_hops+1}: Final transfer completed. "
               f"Hash: {final_tx_hash}")
    
    return final_tx_hash


def _normalize_currency(currency: str, network: str) -> str:
    if "-" in currency:
        return currency
        
    if currency in ["USDC", "USDT", "DAI"] and network == "ETH":
        return f"{currency}-ERC20"
        
    if currency in ["USDT"] and network == "TRON":
        return f"{currency}-TRC20"
        
    return currency


# Kafka message handler
async def handle_kafka_deposit_message(data):
    """Handle deposit message from Kafka."""
    logger.info(f"Received deposit message: {data}")
    
    if data.get('event_type') != 'deposit_confirmed':
        return
        
    async with AsyncSessionLocal() as db:
        # Extract data
        user_id = data.get('user_id')
        amount = Decimal(data.get('amount', '0'))
        currency = data.get('currency', '')
        wallet_address = data.get('wallet_address', '')
        
        # Determine network based on currency
        network = _determine_network(currency)

        currency = _normalize_currency(currency, network)
        
        # Check if we should consolidate this deposit
        fee_service = FeeService()
        should_consolidate, reason = await fee_service.should_consolidate(db, currency, network, amount)
        
        if not should_consolidate:
            logger.info(f"Skipping consolidation: {reason}")
            return
        
        # Get optimal time for transaction
        optimal_time = await fee_service.get_optimal_transaction_time(currency, network)
        
        # Find destination wallet
        wallet_service = WalletService()
        destination_wallets = await wallet_service.get_wallets(
            db, currency=currency, network=network, is_active=True
        )
        
        if not destination_wallets:
            logger.error(f"No active destination wallet found for {currency}/{network}")
            return
            
        destination_wallet = destination_wallets[0]
        
        # Get fee wallet address
        fee_wallet_service = FeeWalletService()
        fee_wallet_address, _ = await fee_wallet_service.get_fee_wallet_data(network)
        
        # Create transaction with "scheduled" status
        transaction = ConsolidationTransaction(
            source_wallet=wallet_address,
            destination_wallet=destination_wallet.address,
            user_id=user_id,
            currency=currency,
            network=network,
            amount=amount,
            status=TransactionStatus.SCHEDULED,
            scheduled_time=optimal_time,
            fee_wallet_address=fee_wallet_address
        )
        
        db.add(transaction)
        await db.commit()
        
        logger.info(
            f"Created scheduled transaction for {amount} {currency} "
            f"from {wallet_address} to {destination_wallet.address}, "
            f"scheduled for {optimal_time}"
        )


# Background task for processing scheduled transactions
async def process_scheduled_transactions():
    """Process scheduled transactions."""
    while True:
        try:
            async with AsyncSessionLocal() as db:
                # Get transactions that are scheduled to execute now
                now = datetime.utcnow()
                transactions_query = select(ConsolidationTransaction).where(
                    ConsolidationTransaction.status == TransactionStatus.SCHEDULED,
                    ConsolidationTransaction.scheduled_time <= now
                ).limit(10)
                
                result = await db.execute(transactions_query)
                transactions = result.scalars().all()
                
                logger.info(f"Found {len(transactions)} scheduled transactions to process")
                
                for tx in transactions:
                    # Update status to processing
                    tx.status = TransactionStatus.PROCESSING
                    tx.attempts += 1
                    tx.last_attempt = now
                    await db.commit()
                    
                    # Process transaction
                    await process_transaction(db, tx)
                    
        except Exception as e:
            logger.error(f"Error in transaction processor: {str(e)}")
            
        await asyncio.sleep(60)  # Check every minute


# Process a single transaction
async def process_transaction(db: AsyncSession, tx: ConsolidationTransaction):
    """Process a single transaction."""
    try:
        # Get fee wallet service
        fee_wallet_service = FeeWalletService()
        
        # Get fee wallet data directly from the configuration
        fee_wallet_address, fee_wallet_private_key = await fee_wallet_service.get_fee_wallet_data(tx.network)
        
        if not fee_wallet_address or not fee_wallet_private_key:
            tx.status = TransactionStatus.ERROR
            tx.error_message = f"No fee wallet configured for network {tx.network}"
            await db.commit()
            logger.error(f"Transaction {tx.id} failed: {tx.error_message}")
            return
            
        # Check fee wallet balance
        has_sufficient_balance, balance = await fee_wallet_service.check_fee_wallet_balance(tx.network)
        
        if not has_sufficient_balance:
            tx.status = TransactionStatus.ERROR
            tx.error_message = f"Insufficient fee wallet balance for {tx.network}: {balance}"
            await db.commit()
            logger.error(f"Transaction {tx.id} failed: {tx.error_message}")
            return
            
        # Get blockchain connector
        connector = get_blockchain_connector(tx.network)
        if not connector:
            tx.status = TransactionStatus.ERROR
            tx.error_message = f"No connector available for network {tx.network}"
            await db.commit()
            logger.error(f"Transaction {tx.id} failed: {tx.error_message}")
            return
            
        # Логируем для отладки
        logger.debug(f"Processing transaction {tx.id}: network={tx.network}, currency={tx.currency}")
        
        # Проверяем режим приватности и сеть транзакции
        if settings.PRIVACY_ENABLED:
            try:
                # Если это TRON транзакция, используем специальный приватный сервис
                if tx.network == "TRON":
                    # Импортируем и используем PrivacyTronService
                    from app.services.privacy_tron_service import PrivacyTronService
                    
                    privacy_tron_service = PrivacyTronService()
                    
                    # Получаем промежуточные кошельки
                    intermediate_wallets = await _get_intermediate_wallets(tx.network)
                    
                    if not intermediate_wallets or len(intermediate_wallets) < 1:
                        logger.warning(f"Privacy mode enabled but no intermediate wallets found for TRON. "
                                      f"Falling back to direct transfer.")
                        tx_hash = await connector.send_with_fee_wallet(
                            tx.source_wallet,
                            tx.destination_wallet,
                            tx.amount,
                            fee_wallet_address,
                            fee_wallet_private_key,
                            "medium",
                            tx.currency
                        )
                    else:
                        # Выполняем приватную передачу через PrivacyTronService
                        tx_hash = await privacy_tron_service.execute_private_transaction(
                            tx.source_wallet,
                            tx.destination_wallet,
                            tx.amount,
                            fee_wallet_address,
                            fee_wallet_private_key,
                            tx.currency
                        )
                        
                        if not tx_hash:
                            raise ValueError("Failed to execute TRON privacy transaction")
                else:
                    # Для других сетей используем стандартный приватный трансфер
                    # Get intermediate wallets for this network
                    intermediate_wallets = await _get_intermediate_wallets(tx.network)
                    
                    if not intermediate_wallets or len(intermediate_wallets) < 1:
                        logger.warning(f"Privacy mode enabled but no intermediate wallets found for {tx.network}. "
                                      f"Falling back to direct transfer.")
                        tx_hash = await connector.send_with_fee_wallet(
                            tx.source_wallet,
                            tx.destination_wallet,
                            tx.amount,
                            fee_wallet_address,
                            fee_wallet_private_key,
                            "medium",
                            tx.currency  # Добавляем передачу валюты
                        )
                    else:
                        # Execute private transfer through intermediate wallets
                        tx_hash = await _execute_privacy_transfer(
                            connector,
                            tx,
                            intermediate_wallets,
                            fee_wallet_address,
                            fee_wallet_private_key
                        )
                
                # Update transaction status to SENT
                tx.status = TransactionStatus.SENT
                tx.tx_hash = tx_hash
                tx.updated_at = datetime.utcnow()
                await db.commit()
                logger.info(f"Transaction {tx.id} successfully processed with privacy mode, hash: {tx_hash}")
                
            except Exception as e:
                tx.status = TransactionStatus.ERROR
                tx.error_message = f"Privacy transfer error: {str(e)}"
                await db.commit()
                logger.error(f"Error in privacy transfer for transaction {tx.id}: {str(e)}")
        else:
            # Стандартная отправка без режима приватности
            try:
                logger.debug(f"Стандартная отправка: network={tx.network}, currency={tx.currency}, amount={tx.amount}")
                
                # Всегда передаем параметр currency для всех сетей
                tx_hash = await connector.send_with_fee_wallet(
                    tx.source_wallet,
                    tx.destination_wallet,
                    tx.amount,
                    fee_wallet_address,
                    fee_wallet_private_key,
                    "medium",
                    tx.currency  # Всегда передаем тип валюты
                )
                
                # Update transaction with success
                tx.status = TransactionStatus.SENT
                tx.tx_hash = tx_hash
                tx.updated_at = datetime.utcnow()
                await db.commit()
                
                logger.info(f"Transaction {tx.id} successfully sent, hash: {tx_hash}")
                
            except Exception as e:
                tx.status = TransactionStatus.ERROR
                tx.error_message = str(e)
                await db.commit()
                logger.error(f"Error executing transaction {tx.id}: {str(e)}")
            
    except Exception as e:
        logger.error(f"Error processing transaction {tx.id}: {str(e)}")
        tx.status = TransactionStatus.ERROR
        tx.error_message = str(e)
        await db.commit()


# Background task for checking sent transactions
async def check_sent_transactions():
    """Check status of sent transactions in the blockchain."""
    while True:
        try:
            async with AsyncSessionLocal() as db:
                # Get sent transactions
                sent_query = select(ConsolidationTransaction).where(
                    ConsolidationTransaction.status == TransactionStatus.SENT
                ).limit(50)
                
                result = await db.execute(sent_query)
                transactions = result.scalars().all()
                
                for tx in transactions:
                    # Get blockchain connector
                    connector = get_blockchain_connector(tx.network)
                    if not connector:
                        continue
                        
                    # Check transaction status
                    tx_status = await connector.get_transaction_status(tx.tx_hash)
                    
                    # Update transaction if confirmed
                    if tx_status.get("status") == "confirmed":
                        tx.status = TransactionStatus.CONFIRMED
                        tx.updated_at = datetime.utcnow()
                        await db.commit()
                        
                        logger.info(
                            f"Transaction {tx.id} confirmed with "
                            f"{tx_status.get('confirmations')} confirmations"
                        )
                        
        except Exception as e:
            logger.error(f"Error checking transaction status: {str(e)}")
            
        await asyncio.sleep(120)  # Check every 2 minutes


# Startup event
@app.on_event("startup")
async def startup_event():
    logger.info(f"Starting {settings.APP_NAME}")
    
    # Create database tables
    logger.info("Creating database tables if they don't exist...")
    try:
        # Use synchronous connection for creating tables
        sync_db_url = str(settings.SQLALCHEMY_DATABASE_URI).replace("postgresql+asyncpg", "postgresql")
        engine = create_engine(sync_db_url)
        
        # Create all tables
        Base.metadata.create_all(bind=engine)
        logger.info("Database tables created successfully")
        
        # Initialize corporate wallets
        async with AsyncSessionLocal() as db:
            from app.services.setup_service import initialize_corporate_wallets
            await initialize_corporate_wallets(db)
    except Exception as e:
        logger.error(f"Error creating database tables: {e}")
    
    # Start Kafka consumers in the background
    asyncio.create_task(consume_deposit_events(handle_kafka_deposit_message))
    
    # Start Kafka consumer for Wallet Service responses
    asyncio.create_task(consume_wallet_service_responses())
    
    # Start background transaction processors
    asyncio.create_task(process_scheduled_transactions())
    asyncio.create_task(check_sent_transactions())
    
    # Start vacuum service for intermediate wallets
    from app.services.vacuum_service import VacuumService
    logger.info("Starting vacuum service for intermediate wallets")
    vacuum_service = VacuumService()
    asyncio.create_task(vacuum_service.start(interval_seconds=86400))  # Check once a day
    
    logger.info(f"{settings.APP_NAME} started successfully")


# Shutdown event
@app.on_event("shutdown")
async def shutdown_event():
    logger.info(f"Shutting down {settings.APP_NAME}")
    
    # Close Kafka producer
    await close_producer()


# Health check endpoint
@app.get("/health")
async def health_check():
    return {"status": "healthy", "service": "treasury"}


# 404 handler
@app.exception_handler(404)
async def not_found_exception_handler(request, exc):
    return HTTPException(
        status_code=404,
        detail="The requested URL was not found on the server",
    )


if __name__ == "__main__":
    import uvicorn
    
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=settings.DEBUG,
    )