from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import String, DateTime, Numeric, Boolean
from sqlalchemy.orm import Mapped, mapped_column

from app.db.base import Base


class DestinationWallet(Base):
    """
    Database model for destination wallets.
    
    These are the company-owned wallets where funds are consolidated.
    """
    __tablename__ = "destinationwallet"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    
    # Wallet address
    address: Mapped[str] = mapped_column(String, unique=True, index=True)
    
    # Currency code (BTC, ETH, USDT, etc.)
    currency: Mapped[str] = mapped_column(String, index=True)
    
    # Network (BTC, ETH, ERC20, TRC20, etc.)
    network: Mapped[str] = mapped_column(String, index=True)
    
    # Wallet type (hot, cold)
    type: Mapped[str] = mapped_column(String, index=True)
    
    # Current balance
    balance: Mapped[Decimal] = mapped_column(Numeric(precision=36, scale=18), default=0)
    
    # Whether the wallet is active
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    
    # When the wallet was created
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # When the wallet was last updated
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, onupdate=datetime.utcnow)
    
    def __repr__(self) -> str:
        return f"<DestinationWallet id={self.id} address={self.address} currency={self.currency} type={self.type}>"