from datetime import datetime
import uuid
from decimal import Decimal
from enum import Enum
from typing import Optional, TYPE_CHECKING

from sqlalchemy import String, ForeignKey, Integer, DateTime, Numeric, Text
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base import Base

# Импортируем только при проверке типов
if TYPE_CHECKING:
    from app.db.models.task import ConsolidationTask


# Определение перечисления для статусов транзакций
class TransactionStatus(str, Enum):
    """Enum for consolidation transaction statuses."""
    NEW = "new"
    SCHEDULED = "scheduled"  # Новый статус для запланированных транзакций
    PROCESSING = "processing"  # Статус во время обработки
    PENDING = "pending"
    SENT = "sent"
    CONFIRMED = "confirmed"
    ERROR = "error"


class ConsolidationTransaction(Base):
    """
    Database model for consolidation transactions.
    
    Each transaction represents a transfer of funds from a temporary wallet
    to a company wallet.
    """
    __tablename__ = "consolidationtransaction"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    
    # Reference to consolidation task
    task_id: Mapped[Optional[int]] = mapped_column(Integer, ForeignKey("consolidationtask.id"), index=True, nullable=True)
    task: Mapped[Optional["ConsolidationTask"]] = relationship(back_populates="transactions")
    
    # Существующие поля
    source_wallet: Mapped[str] = mapped_column(String, index=True)
    destination_wallet: Mapped[str] = mapped_column(String)
    user_id: Mapped[Optional[int]] = mapped_column(Integer, index=True)
    currency: Mapped[str] = mapped_column(String, index=True)
    network: Mapped[str] = mapped_column(String, index=True)
    amount: Mapped[Decimal] = mapped_column(Numeric(precision=36, scale=18))
    fee: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=36, scale=18))
    tx_hash: Mapped[Optional[str]] = mapped_column(String, index=True)
    status: Mapped[str] = mapped_column(String, index=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text)
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, onupdate=datetime.utcnow)
    
    # Новые поля
    scheduled_time: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    fee_wallet_address: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    gas_price: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=36, scale=18), nullable=True)
    attempts: Mapped[int] = mapped_column(Integer, default=0)
    last_attempt: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    def __repr__(self) -> str:
        return f"<ConsolidationTransaction id={self.id} currency={self.currency} amount={self.amount} status={self.status}>"