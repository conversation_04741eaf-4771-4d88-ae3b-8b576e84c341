from datetime import datetime
from decimal import Decimal
from typing import Optional

from sqlalchemy import String, DateTime, Numeric, Boolean
from sqlalchemy.orm import Mapped, mapped_column

from app.db.base import Base


class FeeSettings(Base):
    """
    Database model for fee settings.
    
    Controls the fee-related parameters for consolidation operations.
    """
    __tablename__ = "feesettings"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    
    # Currency code (BTC, ETH, USDT, etc.)
    currency: Mapped[str] = mapped_column(String, index=True)
    
    # Network (BTC, ETH, ERC20, TRC20, etc.)
    network: Mapped[str] = mapped_column(String, index=True)
    
    # Minimum amount required for consolidation
    min_consolidation_amount: Mapped[Decimal] = mapped_column(Numeric(precision=36, scale=18))
    
    # Maximum acceptable fee percentage
    max_fee_percentage: Mapped[Decimal] = mapped_column(Numeric(precision=10, scale=2))
    
    # Transaction priority (low, medium, high)
    priority: Mapped[str] = mapped_column(String, default="medium")
    
    # Whether these settings are active
    is_active: Mapped[bool] = mapped_column(Boolean, default=True, index=True)
    
    # When the settings were created
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # When the settings were last updated
    updated_at: Mapped[datetime] = mapped_column(
        DateTime, 
        default=datetime.utcnow, 
        onupdate=datetime.utcnow
    )
    
    def __repr__(self) -> str:
        return f"<FeeSettings id={self.id} currency={self.currency} network={self.network}>"