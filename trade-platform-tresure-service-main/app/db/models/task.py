from datetime import datetime
from enum import Enum
from typing import Dict, Any, List, Optional, TYPE_CHECKING

from sqlalchemy import String, DateTime, Integer, JSON
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base import Base

# Импортируем только при проверке типов
if TYPE_CHECKING:
    from app.db.models.transaction import ConsolidationTransaction


# Определение перечисления для статусов задач
class TaskStatus(str, Enum):
    """Enum for consolidation task statuses."""
    NEW = "new"
    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    ERROR = "error"
    CANCELLED = "cancelled"


class ConsolidationTask(Base):
    """
    Database model for consolidation tasks.
    
    A consolidation task represents a batch operation to transfer funds
    from temporary user wallets to company wallets.
    """
    __tablename__ = "consolidationtask"
    
    id: Mapped[int] = mapped_column(primary_key=True)
    
    # Task status (new, pending, in_progress, completed, error, cancelled)
    status: Mapped[str] = mapped_column(String, index=True)
    
    # When the task was created
    created_at: Mapped[datetime] = mapped_column(DateTime, default=datetime.utcnow)
    
    # When the task was last updated
    updated_at: Mapped[Optional[datetime]] = mapped_column(DateTime, onupdate=datetime.utcnow)
    
    # When the task is scheduled to be executed
    scheduled_time: Mapped[datetime] = mapped_column(DateTime)
    
    # Total number of transactions in this task
    total_transactions: Mapped[int] = mapped_column(Integer, default=0)
    
    # Number of completed transactions
    completed_transactions: Mapped[int] = mapped_column(Integer, default=0)
    
    # Total amount by currency (JSON structure)
    # Example: {"BTC": "0.5", "ETH": "2.0"}
    total_amount: Mapped[Optional[Dict[str, str]]] = mapped_column(JSON)
    
    # Estimated fee by currency (JSON structure)
    # Example: {"BTC": "0.0001", "ETH": "0.002"}
    fee_estimation: Mapped[Optional[Dict[str, str]]] = mapped_column(JSON)
    
    # Relationship with transactions
    transactions: Mapped[List["ConsolidationTransaction"]] = relationship(back_populates="task")
    
    def __repr__(self) -> str:
        return f"<ConsolidationTask id={self.id} status={self.status} transactions={self.total_transactions}>"