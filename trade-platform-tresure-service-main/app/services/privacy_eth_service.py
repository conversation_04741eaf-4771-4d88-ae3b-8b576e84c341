# privacy_eth_service.py
import asyncio
import random
from decimal import Decimal
from typing import List, Tuple, Optional, Dict, Any
from web3 import Web3

from app.core.config import settings
from app.core.logging import get_logger
from app.db.models.transaction import ConsolidationTransaction
from app.services.blockchain.base import get_blockchain_connector

logger = get_logger(__name__)

# Определяем собственную функцию middleware чтобы избежать проблем с импортом
def geth_poa_middleware(make_request, web3):
    """
    Простая реализация middleware для PoA-цепочек.
    Возвращает функцию make_request без изменений.
    Это заглушка, которую можно будет заменить на настоящую реализацию при необходимости.
    """
    return make_request

class PrivacyEthService:
    """Сервис для выполнения приватных транзакций Ethereum через множество промежуточных кошельков"""
    
    def __init__(self):
        # Получаем коннектор ETH
        self.connector = get_blockchain_connector("ETH")
        
        # Загружаем промежуточные кошельки из настроек
        self.intermediate_wallets = []
        
        # Парсим промежуточные кошельки и ключи из настроек
        eth_addresses = settings.ETH_INTERMEDIATE_WALLETS_LIST
        eth_keys = settings.ETH_INTERMEDIATE_KEYS_LIST
        
        if len(eth_addresses) == len(eth_keys):
            self.intermediate_wallets = list(zip([addr.strip() for addr in eth_addresses], 
                                              [key.strip() for key in eth_keys]))
        else:
            logger.error("Несоответствие в конфигурации промежуточных кошельков ETH. "
                       f"Адреса: {len(eth_addresses)}, Ключи: {len(eth_keys)}")
                       
        # Минимальный требуемый баланс ETH для транзакций ERC20
        self.min_eth_balance = Decimal('0.01')
        
        # Инициализируем Web3 подключение для прямых вызовов контрактов
        if settings.TESTNET_MODE:
            self.web3 = Web3(Web3.HTTPProvider(settings.ETH_TESTNET_RPC_URL))
        else:
            self.web3 = Web3(Web3.HTTPProvider(settings.ETH_MAINNET_RPC_URL))
            
        # Применяем middleware для PoA-цепочек если нужно
        self.web3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        # Адреса контрактов токенов (только USDC)
        self.token_contracts = {
            "USDC-ERC20": "******************************************"
        }
        
        logger.info(f"[PRIVACY_ETH] Сервис приватных транзакций ETH инициализирован. "
                   f"Доступно промежуточных кошельков: {len(self.intermediate_wallets)}")
    
    async def check_wallet_eth_balance(self, address: str) -> Tuple[bool, Decimal]:
        """
        Проверяет баланс ETH на кошельке
        
        Args:
            address: Адрес кошелька
            
        Returns:
            Tuple (достаточно ETH, текущий баланс)
        """
        try:
            balance = await self.connector.get_balance(address)
            logger.info(f"[PRIVACY_ETH] Баланс кошелька {address}: {balance} ETH")
            return balance >= self.min_eth_balance, balance
        except Exception as e:
            logger.error(f"[PRIVACY_ETH] Ошибка при проверке баланса кошелька {address}: {str(e)}")
            return False, Decimal('0')
    
    async def fund_wallet_if_needed(self, address: str, fee_wallet: str, fee_wallet_key: str) -> bool:
        """
        Проверяет баланс кошелька и при необходимости пополняет его ETH
        
        Args:
            address: Адрес для проверки
            fee_wallet: Адрес fee-кошелька
            fee_wallet_key: Приватный ключ fee-кошелька
            
        Returns:
            bool: Успешность операции
        """
        sufficient, balance = await self.check_wallet_eth_balance(address)
        
        if sufficient:
            logger.info(f"[PRIVACY_ETH] Кошелек {address} имеет достаточный баланс: {balance} ETH")
            return True
            
        # Рассчитываем, сколько ETH нужно отправить
        needed_amount = self.min_eth_balance - balance
        
        logger.info(f"[PRIVACY_ETH] Пополнение кошелька {address} на {needed_amount} ETH с fee-кошелька {fee_wallet}")
        
        try:
            # Конвертируем адреса в формат с контрольной суммой
            fee_wallet_checksum = Web3.to_checksum_address(fee_wallet)
            address_checksum = Web3.to_checksum_address(address)
            
            # Конвертируем сумму в wei
            amount_wei = int(needed_amount * Decimal(10**18))
            
            # Получаем цену газа
            gas_price = await self.connector._get_gas_price("medium")
            
            # Получаем nonce для fee-кошелька
            nonce = self.web3.eth.get_transaction_count(fee_wallet_checksum)
            
            # Строим транзакцию
            tx = {
                'nonce': nonce,
                'to': address_checksum,
                'value': amount_wei,
                'gas': 21000,  # Стандартный лимит газа для ETH
                'gasPrice': gas_price,
                'chainId': self.web3.eth.chain_id
            }
            
            # Подписываем транзакцию
            signed_tx = self.web3.eth.account.sign_transaction(tx, fee_wallet_key)
            
            # Отправляем транзакцию
            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
            hex_hash = Web3.to_hex(tx_hash)
            
            logger.info(f"[PRIVACY_ETH] Кошелек {address} пополнен, хеш: {hex_hash}")
            
            # Ждем, пока транзакция будет подтверждена
            for _ in range(30):  # Максимум 30 секунд ожидания
                try:
                    receipt = self.web3.eth.get_transaction_receipt(tx_hash)
                    if receipt and receipt.blockNumber:
                        break
                except:
                    pass
                await asyncio.sleep(1)
                
            return True
            
        except Exception as e:
            logger.error(f"[PRIVACY_ETH] Ошибка при пополнении кошелька {address}: {str(e)}")
            return False
    
    async def get_token_contract_address(self, currency: str) -> Optional[str]:
        """Получение адреса контракта токена для указанной валюты."""
        if currency in self.token_contracts:
            return self.token_contracts[currency]
        else:
            logger.warning(f"[PRIVACY_ETH] Неизвестный токен: {currency}")
            return None
    
    async def execute_private_transaction(
        self,
        source_address: str,
        destination_address: str,
        amount: Decimal,
        fee_wallet: str,
        fee_wallet_key: str,
        currency: str = "ETH",
        **kwargs
    ) -> Optional[str]:
        """
        Выполняет приватную транзакцию через цепочку промежуточных кошельков
        
        Args:
            source_address: Исходный адрес
            destination_address: Конечный адрес (корпоративный)
            amount: Сумма перевода
            fee_wallet: Адрес комиссионного кошелька
            fee_wallet_key: Приватный ключ комиссионного кошелька
            currency: Валюта (по умолчанию ETH, может быть USDC-ERC20)
            
        Returns:
            Optional[str]: Хеш финальной транзакции или None в случае ошибки
        """
        if not self.connector:
            logger.error("[PRIVACY_ETH] Не удалось получить ETH коннектор")
            return None
            
        # Определяем количество промежуточных шагов
        num_hops = min(settings.PRIVACY_HOPS, len(self.intermediate_wallets))
        
        if num_hops < 1:
            logger.error("[PRIVACY_ETH] Недостаточно промежуточных кошельков для приватной транзакции")
            return None
            
        # Выбираем случайные промежуточные кошельки
        selected_wallets = random.sample(self.intermediate_wallets, num_hops)
        
        try:
            # Проверяем, является ли это токеном ERC20
            is_token = currency != "ETH"
            
            if is_token:
                # Получаем адрес контракта токена для ERC20
                token_contract = await self.get_token_contract_address(currency)
                if not token_contract:
                    logger.error(f"[PRIVACY_ETH] Неизвестный токен: {currency}")
                    return None
            
            # Убедимся, что исходный адрес имеет достаточно ETH для газа (если токен)
            # или для самого перевода (если ETH)
            if is_token:
                source_funded = await self.fund_wallet_if_needed(source_address, fee_wallet, fee_wallet_key)
                if not source_funded:
                    logger.error(f"[PRIVACY_ETH] Не удалось обеспечить достаточный баланс ETH на исходном кошельке {source_address}")
                    return None
            
            # Шаг 1: Отправляем с исходного кошелька на первый промежуточный
            first_address, first_key = selected_wallets[0]
            
            # Пополняем первый промежуточный кошелек при необходимости (для ETH и токенов)
            first_funded = await self.fund_wallet_if_needed(first_address, fee_wallet, fee_wallet_key)
            if not first_funded:
                logger.error(f"[PRIVACY_ETH] Не удалось обеспечить достаточный баланс ETH на первом промежуточном кошельке {first_address}")
                return None
                
            # Немного изменяем сумму для усложнения отслеживания
            first_amount = amount * Decimal(1 - random.uniform(0.001, 0.005))
            
            # Получаем приватный ключ исходного кошелька
            source_key = await self.connector.get_private_key(source_address)
            
            # Выполняем первую транзакцию исходя из типа валюты
            if is_token:
                # Для токенов ERC20
                first_tx_hash = await self._send_token(
                    source_address,
                    first_address,
                    first_amount,
                    source_key,
                    token_contract,
                    currency
                )
            else:
                # Для ETH
                first_tx_hash = await self.connector.create_and_broadcast_transaction(
                    source_address,
                    first_address,
                    first_amount,
                    source_key,
                    "medium"
                )
            
            if not first_tx_hash:
                logger.error("[PRIVACY_ETH] Первая транзакция не удалась")
                return None
                
            logger.info(f"[PRIVACY_ETH] Шаг 1/{num_hops+1}: Транзакция отправлена на первый промежуточный кошелек. "
                       f"Хеш: {first_tx_hash}")
            
            # Ждем случайное время перед следующей транзакцией
            delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
            logger.info(f"[PRIVACY_ETH] Ожидание {delay} секунд перед следующим шагом")
            await asyncio.sleep(delay)
            
            # Переменные для отслеживания текущего состояния цепочки
            current_amount = first_amount
            current_address = first_address
            current_key = first_key
            last_tx_hash = first_tx_hash
            
            # Проходим через цепочку промежуточных кошельков
            for i in range(1, num_hops):
                next_address, next_key = selected_wallets[i]
                
                # Пополняем следующий кошелек при необходимости
                next_funded = await self.fund_wallet_if_needed(next_address, fee_wallet, fee_wallet_key)
                if not next_funded:
                    logger.error(f"[PRIVACY_ETH] Не удалось обеспечить достаточный баланс ETH на промежуточном кошельке {next_address}")
                    continue  # Продолжаем с другим кошельком
                    
                # Немного изменяем сумму снова
                current_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.003))
                
                # Отправляем средства на следующий промежуточный кошелек
                if is_token:
                    # Для токенов ERC20
                    hop_tx_hash = await self._send_token(
                        current_address,
                        next_address,
                        current_amount,
                        current_key,
                        token_contract,
                        currency
                    )
                else:
                    # Для ETH
                    hop_tx_hash = await self.connector.create_and_broadcast_transaction(
                        current_address,
                        next_address,
                        current_amount,
                        current_key,
                        "medium"
                    )
                
                if not hop_tx_hash:
                    logger.error(f"[PRIVACY_ETH] Промежуточная транзакция {i+1} не удалась")
                    continue
                    
                logger.info(f"[PRIVACY_ETH] Шаг {i+1}/{num_hops+1}: Транзакция отправлена на следующий промежуточный кошелек. "
                           f"Хеш: {hop_tx_hash}")
                
                # Обновляем текущие переменные
                current_address = next_address
                current_key = next_key
                last_tx_hash = hop_tx_hash
                
                # Ждем случайное время между транзакциями
                delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
                logger.info(f"[PRIVACY_ETH] Ожидание {delay} секунд перед следующим шагом")
                await asyncio.sleep(delay)
            
            # Финальная транзакция: с последнего промежуточного на корпоративный кошелек
            final_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.002))
            
            # Отправляем финальную транзакцию
            if is_token:
                # Для токенов ERC20
                final_tx_hash = await self._send_token(
                    current_address,
                    destination_address,
                    final_amount,
                    current_key,
                    token_contract,
                    currency
                )
            else:
                # Для ETH
                final_tx_hash = await self.connector.create_and_broadcast_transaction(
                    current_address,
                    destination_address,
                    final_amount,
                    current_key,
                    "medium"
                )
            
            if not final_tx_hash:
                logger.error("[PRIVACY_ETH] Финальная транзакция не удалась")
                return None
                
            logger.info(f"[PRIVACY_ETH] Шаг {num_hops+1}/{num_hops+1}: Финальная транзакция выполнена. "
                       f"Хеш: {final_tx_hash}")
            
            return final_tx_hash
            
        except Exception as e:
            logger.error(f"[PRIVACY_ETH] Ошибка выполнения приватной транзакции: {str(e)}")
            return None
    
    async def _send_token(
        self,
        sender: str,
        recipient: str,
        amount: Decimal,
        private_key: str,
        token_contract: str,
        currency: str
    ) -> Optional[str]:
        """
        Отправляет токены ERC20 с одного кошелька на другой
        
        Args:
            sender: Адрес отправителя
            recipient: Адрес получателя
            amount: Количество токенов
            private_key: Приватный ключ отправителя
            token_contract: Адрес контракта токена
            currency: Код валюты токена
            
        Returns:
            Optional[str]: Хеш транзакции или None при ошибке
        """
        try:
            # ABI для функции transfer в ERC20
            erc20_abi = [
                {
                    "constant": False,
                    "inputs": [
                        {"name": "_to", "type": "address"},
                        {"name": "_value", "type": "uint256"}
                    ],
                    "name": "transfer",
                    "outputs": [{"name": "", "type": "bool"}],
                    "type": "function"
                },
                {
                    "constant": True,
                    "inputs": [],
                    "name": "decimals",
                    "outputs": [{"name": "", "type": "uint8"}],
                    "type": "function"
                }
            ]
            
            # Конвертируем адреса в формат с контрольной суммой
            sender_checksum = Web3.to_checksum_address(sender)
            recipient_checksum = Web3.to_checksum_address(recipient)
            token_contract_checksum = Web3.to_checksum_address(token_contract)
            
            # Создаем экземпляр контракта
            contract = self.web3.eth.contract(address=token_contract_checksum, abi=erc20_abi)
            
            # Получаем количество десятичных знаков
            decimals = contract.functions.decimals().call()
            
            # Конвертируем сумму в единицы токена
            token_amount = int(amount * Decimal(10 ** decimals))
            
            # Получаем цену газа
            gas_price = await self.connector._get_gas_price("medium")
            
            # Получаем nonce для отправителя
            nonce = self.web3.eth.get_transaction_count(sender_checksum)
            
            # Строим транзакцию
            tx = contract.functions.transfer(
                recipient_checksum,
                token_amount
            ).build_transaction({
                'from': sender_checksum,
                'nonce': nonce,
                'gasPrice': gas_price,
                'gas': 100000,  # Стандартный лимит газа для ERC20
                'chainId': self.web3.eth.chain_id
            })
            
            # Подписываем транзакцию
            signed_tx = self.web3.eth.account.sign_transaction(tx, private_key)
            
            # Отправляем транзакцию
            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
            hex_hash = Web3.to_hex(tx_hash)
            
            logger.info(f"[PRIVACY_ETH] Транзакция токена отправлена: {sender} -> {recipient}, "
                       f"количество: {amount} {currency}, хеш: {hex_hash}")
            
            return hex_hash
            
        except Exception as e:
            logger.error(f"[PRIVACY_ETH] Ошибка отправки токена: {str(e)}")
            return None