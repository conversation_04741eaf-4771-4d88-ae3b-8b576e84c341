from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.core.logging import get_logger
from app.db.models.task import ConsolidationTask
from app.db.models.transaction import ConsolidationTransaction
from app.schemas.task import TaskCreate, TaskUpdateSchedule
from app.schemas.transaction import TransactionResponse


logger = get_logger(__name__)


class TaskService:
    """
    Service for managing consolidation tasks.
    """
    
    async def get_tasks(
        self, 
        db: AsyncSession, 
        status: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[ConsolidationTask]:
        """
        Get a list of consolidation tasks with optional filtering.
        
        Args:
            db: Database session
            status: Optional status filter
            limit: Maximum number of tasks to return
            offset: Number of tasks to skip
            
        Returns:
            List of tasks
        """
        query = select(ConsolidationTask)
        
        if status:
            query = query.where(ConsolidationTask.status == status)
            
        query = query.limit(limit).offset(offset).order_by(ConsolidationTask.created_at.desc())
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        return list(tasks)
    
    async def get_task_detail(
        self, 
        db: AsyncSession, 
        task_id: uuid.UUID
    ) -> Optional[Dict[str, Any]]:
        """
        Get detailed information about a specific task.
        
        Args:
            db: Database session
            task_id: Task ID
            
        Returns:
            Task details including transactions, or None if not found
        """
        # Get task
        task_query = select(ConsolidationTask).where(ConsolidationTask.id == task_id)
        result = await db.execute(task_query)
        task = result.scalars().first()
        
        if not task:
            return None
        
        # Get transactions for this task
        transactions_query = select(ConsolidationTransaction).where(
            ConsolidationTransaction.task_id == task_id
        ).order_by(ConsolidationTransaction.created_at.desc())
        
        result = await db.execute(transactions_query)
        transactions = result.scalars().all()
        
        # Convert to response format
        return {
            "id": task.id,
            "status": task.status,
            "created_at": task.created_at,
            "updated_at": task.updated_at,
            "scheduled_time": task.scheduled_time,
            "total_transactions": task.total_transactions,
            "completed_transactions": task.completed_transactions,
            "total_amount": task.total_amount,
            "fee_estimation": task.fee_estimation,
            "transactions": [TransactionResponse.from_orm(tx) for tx in transactions]
        }
    
    async def create_task(
        self, 
        db: AsyncSession, 
        task_data: TaskCreate
    ) -> ConsolidationTask:
        """
        Create a new consolidation task.
        
        Args:
            db: Database session
            task_data: Task creation data
            
        Returns:
            Created task
        """
        task = ConsolidationTask(
            status=task_data.status or "new",
            scheduled_time=task_data.scheduled_time or datetime.utcnow(),
            total_transactions=0,
            completed_transactions=0,
            total_amount={},
            fee_estimation={}
        )
        
        db.add(task)
        await db.commit()
        await db.refresh(task)
        
        logger.info(f"Created new consolidation task with ID: {task.id}")
        
        return task
    
    async def cancel_task(
        self, 
        db: AsyncSession, 
        task_id: uuid.UUID
    ) -> Optional[ConsolidationTask]:
        """
        Cancel a task if possible (only if it's in 'new' status).
        
        Args:
            db: Database session
            task_id: Task ID
            
        Returns:
            Updated task or None if not found/not cancellable
        """
        # Get task
        query = select(ConsolidationTask).where(
            ConsolidationTask.id == task_id,
            ConsolidationTask.status == "new"
        )
        result = await db.execute(query)
        task = result.scalars().first()
        
        if not task:
            return None
        
        # Update status
        task.status = "cancelled"
        task.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(task)
        
        logger.info(f"Cancelled task with ID: {task_id}")
        
        return task
    
    async def update_schedule(
        self, 
        db: AsyncSession, 
        task_id: uuid.UUID, 
        schedule_data: TaskUpdateSchedule
    ) -> Optional[ConsolidationTask]:
        """
        Update the scheduled time for a task.
        
        Args:
            db: Database session
            task_id: Task ID
            schedule_data: New schedule data
            
        Returns:
            Updated task or None if not found/not updatable
        """
        # Get task
        query = select(ConsolidationTask).where(
            ConsolidationTask.id == task_id,
            ConsolidationTask.status.in_(["new", "pending"])
        )
        result = await db.execute(query)
        task = result.scalars().first()
        
        if not task:
            return None
        
        # Update scheduled time
        task.scheduled_time = schedule_data.scheduled_time
        task.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(task)
        
        logger.info(f"Updated schedule for task with ID: {task_id} to {schedule_data.scheduled_time}")
        
        return task
    
    async def set_task_status(
        self, 
        db: AsyncSession, 
        task_id: uuid.UUID, 
        status: str
    ) -> Optional[ConsolidationTask]:
        """
        Update the status of a task.
        
        Args:
            db: Database session
            task_id: Task ID
            status: New status
            
        Returns:
            Updated task or None if not found
        """
        # Get task
        query = select(ConsolidationTask).where(ConsolidationTask.id == task_id)
        result = await db.execute(query)
        task = result.scalars().first()
        
        if not task:
            return None
        
        # Update status
        task.status = status
        task.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(task)
        
        logger.info(f"Updated status for task with ID: {task_id} to {status}")
        
        return task
    
    async def get_pending_tasks(
        self, 
        db: AsyncSession, 
        limit: int = 10
    ) -> List[ConsolidationTask]:
        """
        Get pending tasks scheduled for execution.
        
        Args:
            db: Database session
            limit: Maximum number of tasks to return
            
        Returns:
            List of pending tasks
        """
        now = datetime.utcnow()
        
        query = select(ConsolidationTask).where(
            ConsolidationTask.status.in_(["new", "pending"]),
            ConsolidationTask.scheduled_time <= now
        ).order_by(
            ConsolidationTask.scheduled_time
        ).limit(limit)
        
        result = await db.execute(query)
        tasks = result.scalars().all()
        
        return list(tasks)