import asyncio
import aiohttp
from decimal import Decimal
from typing import Dict, Any, Tuple, Optional
import time

from bitcoinlib.services.services import Service
from bitcoinlib.wallets import Wallet, wallet_create_or_open

from app.services.blockchain.base import BlockchainConnector, register_connector
from app.core.config import settings
from app.core.logging import get_logger


logger = get_logger(__name__)


class BitcoinConnector(BlockchainConnector):
    """Bitcoin blockchain connector implementation using BlockCypher API with bitcoinlib fallback."""
    
    def __init__(self):
        self.network = 'testnet' if settings.TESTNET_MODE else 'bitcoin'
        # Для Bitcoin будем в основном использовать BlockCypher API
        self.api_url = settings.BTC_API_URL
        self.api_key = settings.BTC_API_KEY
        
        # Сохраняем bitcoinlib для fallback операций
        self.service = Service(network=self.network)
        
    async def refresh_connection(self):
        """Обновляет соединение с Bitcoin API перед критическими операциями."""
        try:
            logger.info("[BTC] Обновление соединения с Bitcoin API")
            
            # Проверяем подключение к BlockCypher API - ИСПРАВЛЕНО: используем правильный эндпоинт
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/blocks/latest"  # Исправленный эндпоинт
                params = {}
                if self.api_key:
                    params["token"] = self.api_key
                
                try:
                    async with session.get(url, params=params, timeout=10) as response:
                        if response.status == 200:
                            data = await response.json()
                            block_height = data.get("height", 0)
                            logger.info(f"[BTC] Соединение с BlockCypher API успешно обновлено. Высота блока: {block_height}")
                            
                            # Обновляем сетевые настройки
                            self.network = 'testnet' if settings.TESTNET_MODE else 'bitcoin'
                            return True
                        else:
                            logger.warning(f"[BTC] Ошибка соединения с BlockCypher API: HTTP {response.status}")
                            raise Exception(f"HTTP error {response.status}")
                except Exception as e:
                    logger.warning(f"[BTC] Не удалось подключиться к BlockCypher API: {str(e)}")
            
            # Попытка использовать альтернативный API
            try:
                backup_url = "https://api.blockcypher.com/v1/btc/main"
                if settings.TESTNET_MODE:
                    backup_url = "https://api.blockcypher.com/v1/btc/test3"
                    
                logger.info(f"[BTC] Попытка подключения к резервному API: {backup_url}")
                
                self.api_url = backup_url
                async with aiohttp.ClientSession() as session:
                    url = f"{self.api_url}/blocks/latest"  # Исправленный эндпоинт
                    async with session.get(url, timeout=10) as response:
                        if response.status == 200:
                            data = await response.json()
                            block_height = data.get("height", 0)
                            logger.info(f"[BTC] Резервное соединение установлено. Высота блока: {block_height}")
                            return True
            except Exception as backup_error:
                logger.error(f"[BTC] Не удалось установить резервное соединение: {str(backup_error)}")
            
            # Пробуем обновить bitcoinlib Service
            try:
                loop = asyncio.get_event_loop()
                # Создаем новый экземпляр Service с разными провайдерами - ИСПРАВЛЕНО: удален несуществующий провайдер
                providers = ['blockchaininfo', 'bitaps', 'blockcypher']  # Удален 'blocksmurfer'
                self.service = await loop.run_in_executor(
                    None, 
                    lambda: Service(network=self.network, providers=providers)
                )
                
                # Проверяем работу
                test_addr = "**********************************"  # Адрес genesis блока
                await loop.run_in_executor(None, lambda: self.service.getbalance(test_addr))
                
                logger.info("[BTC] Соединение восстановлено через bitcoinlib")
                return True
                
            except Exception as lib_error:
                logger.error(f"[BTC] Не удалось обновить bitcoinlib: {str(lib_error)}")
                return False
                
        except Exception as e:
            logger.error(f"[BTC] Критическая ошибка при обновлении соединения: {str(e)}")
            return False
            
    async def get_balance(self, address: str) -> Decimal:
        """
        Get BTC balance for the given address.
        First tries to use BlockCypher API, falls back to bitcoinlib if API fails.
        """
        try:
            logger.info(f"[BTC] Запрос баланса для адреса {address}")
            
            # Try using BlockCypher API first
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/addrs/{address}/balance"
                params = {}
                if self.api_key:
                    params["token"] = self.api_key
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        # BlockCypher returns balance in satoshis
                        balance_satoshi = data.get('final_balance', 0)
                        # Convert from satoshi to BTC
                        return Decimal(balance_satoshi) / Decimal(100000000)
        except Exception as e:
            logger.warning(f"Error getting Bitcoin balance from BlockCypher API: {str(e)}, falling back to bitcoinlib")
        
        # Fallback to bitcoinlib
        try:
            # Run in a thread pool since bitcoinlib is synchronous
            loop = asyncio.get_event_loop()
            balance_satoshi = await loop.run_in_executor(
                None, 
                lambda: self.service.getbalance(address)
            )
            # Convert from satoshi to BTC
            return Decimal(balance_satoshi) / Decimal(100000000)
        except Exception as e:
            logger.error(f"Failed to get Bitcoin balance: {str(e)}")
            return Decimal(0)
    
    async def estimate_fee(self, 
                          source_address: str, 
                          destination_address: str, 
                          amount: Decimal, 
                          priority: str = "medium") -> Decimal:
        """
        Estimate Bitcoin transaction fee.
        Uses BlockCypher API for fee estimation if available.
        """
        try:
            # Try using BlockCypher API for fee estimation
            if self.api_key:
                async with aiohttp.ClientSession() as session:
                    url = f"{self.api_url}/txs/fee"
                    params = {"token": self.api_key}
                    
                    async with session.get(url, params=params) as response:
                        if response.status == 200:
                            data = await response.json()
                            
                            # Map priority to fee rate
                            fee_rates = {
                                "high": data.get('high_fee_per_kb', 50000),
                                "medium": data.get('medium_fee_per_kb', 25000),
                                "low": data.get('low_fee_per_kb', 10000)
                            }
                            
                            fee_per_kb = fee_rates.get(priority, fee_rates["medium"])
                            
                            # Estimate transaction size (simplified)
                            # Typical transaction ~250 bytes (1 input, 2 outputs)
                            tx_size_bytes = 250
                            
                            # Calculate fee in satoshis
                            fee_satoshi = (tx_size_bytes * fee_per_kb) / 1000
                            
                            # Convert to BTC
                            return Decimal(fee_satoshi) / Decimal(100000000)
        except Exception as e:
            logger.warning(f"Error estimating fee from BlockCypher API: {str(e)}, falling back to bitcoinlib")
        
        # Fallback to bitcoinlib
        # Get fee estimates per byte
        fee_per_kb = await self._get_fee_rate(priority)
        
        # Estimate transaction size (simplified)
        # Typical transaction ~250 bytes (1 input, 2 outputs)
        tx_size_bytes = 250
        
        # Calculate fee in satoshis
        fee_satoshi = (tx_size_bytes * fee_per_kb) / 1000
        
        # Convert to BTC
        return Decimal(fee_satoshi) / Decimal(100000000)
    
    async def create_transaction(self,
                                source_address: str, 
                                destination_address: str, 
                                amount: Decimal, 
                                priority: str = "medium") -> Tuple[str, Dict[str, Any]]:
        """Create and sign a Bitcoin transaction."""
        # Обновляем соединение перед созданием транзакции
        await self.refresh_connection()
        
        private_key = await self.get_private_key(source_address)
        
        # Amount in satoshis
        amount_satoshi = int(amount * Decimal(100000000))
        
        # Get fee
        fee = await self.estimate_fee(source_address, destination_address, amount, priority)
        fee_satoshi = int(fee * Decimal(100000000))
        
        loop = asyncio.get_event_loop()
        
        # Create or open wallet for this address
        wallet = await loop.run_in_executor(
            None,
            lambda: wallet_create_or_open(
                f"temp_{source_address}", 
                keys=private_key,
                network=self.network
            )
        )
        
        # Create transaction
        tx = await loop.run_in_executor(
            None,
            lambda: wallet.send_to(
                destination_address,
                amount_satoshi,
                fee=fee_satoshi,
                offline=True  # Don't broadcast yet
            )
        )
        
        # Return transaction ID and raw transaction
        return tx.txid, {"raw_hex": tx.raw_hex()}
    
    async def broadcast_transaction(self, raw_transaction: Dict[str, Any]) -> str:
        """
        Broadcast signed Bitcoin transaction.
        Tries BlockCypher API first, falls back to bitcoinlib.
        """
        # Обновляем соединение перед отправкой транзакции
        await self.refresh_connection()
        
        raw_hex = raw_transaction['raw_hex']
        
        try:
            # Try using BlockCypher API for broadcasting
            if self.api_key:
                async with aiohttp.ClientSession() as session:
                    url = f"{self.api_url}/txs/push"
                    payload = {
                        "tx": raw_hex,
                        "token": self.api_key
                    }
                    
                    async with session.post(url, json=payload) as response:
                        if response.status == 200:
                            data = await response.json()
                            return data.get('tx', {}).get('hash', '')
        except Exception as e:
            logger.warning(f"Error broadcasting transaction via BlockCypher API: {str(e)}, falling back to bitcoinlib")
        
        # Fallback to bitcoinlib
        loop = asyncio.get_event_loop()
        txid = await loop.run_in_executor(
            None,
            lambda: self.service.sendrawtransaction(raw_hex)
        )
        
        return txid
    
    async def get_transaction_status(self, tx_hash: str) -> Dict[str, Any]:
        """
        Get status of a Bitcoin transaction.
        
        First tries to get data from BlockCypher API if API key is available,
        then falls back to bitcoinlib if API fails or API key is not available.
        """
        # Обновляем соединение перед проверкой статуса
        await self.refresh_connection()
        
        # Получаем текущую высоту блока для вычисления подтверждений
        current_block_height = await self._get_current_block_height()
        
        try:
            # Try using BlockCypher API first
            async with aiohttp.ClientSession() as session:
                api_url = f"{self.api_url}/txs/{tx_hash}"
                params = {}
                if self.api_key:
                    params["token"] = self.api_key
                
                async with session.get(api_url, params=params) as response:
                    if response.status == 200:
                        tx_data = await response.json()
                        
                        # Используем информацию о блоке транзакции и текущем блоке для вычисления подтверждений
                        block_height = tx_data.get('block_height', 0)
                        
                        # Если транзакция еще не попала в блок
                        if block_height == 0:
                            confirmations = 0
                        else:
                            # Вычисляем подтверждения на основе высоты блока
                            confirmations = current_block_height - block_height + 1 if current_block_height > 0 else 0
                        
                        logger.info(f"[BTC] Транзакция {tx_hash}: блок {block_height}, текущий блок {current_block_height}, подтверждений: {confirmations}")
                        
                        status = "pending"
                        if confirmations >= settings.BTC_CONFIRMATION_THRESHOLD:
                            status = "confirmed"
                        elif confirmations >= 1:
                            status = "processing"
                            
                        return {
                            "status": status,
                            "confirmations": confirmations,
                            "block_height": block_height,
                            "timestamp": tx_data.get('received'),
                            "fee": Decimal(tx_data.get('fees', 0)) / Decimal(100000000) if tx_data.get('fees') else None
                        }
        except Exception as e:
            logger.warning(f"Error getting Bitcoin transaction status from BlockCypher API: {str(e)}, falling back to bitcoinlib")
        
        # Fall back to bitcoinlib
        try:
            loop = asyncio.get_event_loop()
            
            tx_info = await loop.run_in_executor(
                None,
                lambda: self.service.gettransaction(tx_hash)
            )
            
            # Используем информацию из bitcoinlib
            block_height = tx_info.get('blockheight', 0)
            
            # Если транзакция еще не в блоке
            if block_height == 0:
                confirmations = 0
            else:
                # Вычисляем подтверждения на основе высоты блока
                confirmations = current_block_height - block_height + 1 if current_block_height > 0 else 0
            
            logger.info(f"[BTC] Транзакция {tx_hash} (bitcoinlib): блок {block_height}, текущий блок {current_block_height}, подтверждений: {confirmations}")
            
            status = "pending"
            if confirmations >= settings.BTC_CONFIRMATION_THRESHOLD:
                status = "confirmed"
            elif confirmations >= 1:
                status = "processing"
                
            return {
                "status": status,
                "confirmations": confirmations,
                "block_height": block_height,
                "timestamp": tx_info.get('time'),
                "fee": Decimal(tx_info.get('fee', 0)) / Decimal(100000000) if tx_info.get('fee') else None
            }
        except Exception as e:
            logger.error(f"Error getting Bitcoin transaction status: {str(e)}")
            return {"status": "unknown", "error": str(e)}
    
    async def _get_current_block_height(self) -> int:
        """
        Получение текущей высоты блока Bitcoin.
        
        Returns:
            int: Текущая высота блока
        """
        try:
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/blocks/latest"
                params = {}
                if self.api_key:
                    params["token"] = self.api_key
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        height = data.get('height', 0)
                        logger.debug(f"[BTC] Текущая высота блока Bitcoin: {height}")
                        return height
                
                return 0
        except Exception as e:
            logger.error(f"[BTC] Ошибка при получении текущей высоты блока: {str(e)}")
            return 0
    
    async def validate_address(self, address: str) -> bool:
        """
        Validate Bitcoin address format.
        Uses BlockCypher API if available, falls back to bitcoinlib.
        """
        try:
            # Try using BlockCypher API first
            if self.api_key:
                async with aiohttp.ClientSession() as session:
                    url = f"{self.api_url}/addrs/{address}"
                    params = {"token": self.api_key}
                    
                    async with session.get(url, params=params) as response:
                        return response.status == 200
        except Exception:
            logger.warning(f"Error validating address via BlockCypher API, falling back to bitcoinlib")
        
        # Fallback to bitcoinlib
        loop = asyncio.get_event_loop()
        
        try:
            is_valid = await loop.run_in_executor(
                None,
                lambda: self.service.validateaddress(address)
            )
            return is_valid
        except Exception:
            return False
        
    async def get_private_key(self, address: str) -> str:
        """
        Get private key for the address by requesting it from Wallet Service.
        
        Args:
            address: The wallet address
                
        Returns:
            The private key as a string
        
        Raises:
            ValueError: If unable to retrieve the private key
        """
        from app.services.kafka_service import request_private_key
        
        private_key = await request_private_key(address, "BTC")
        if not private_key:
            raise ValueError(f"Failed to get private key for BTC address {address} from Wallet Service")
        
        return private_key
    
    async def _get_fee_rate(self, priority: str) -> int:
        """
        Get fee rate in satoshis per KB based on priority.
        Uses BlockCypher API if available, falls back to bitcoinlib.
        """
        try:
            # Try using BlockCypher API first
            async with aiohttp.ClientSession() as session:
                url = f"{self.api_url}/txs/fee"
                params = {}
                if self.api_key:
                    params["token"] = self.api_key
                
                async with session.get(url, params=params) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Map priority to fee rate
                        fee_rates = {
                            "high": data.get('high_fee_per_kb', 50000),
                            "medium": data.get('medium_fee_per_kb', 25000),
                            "low": data.get('low_fee_per_kb', 10000)
                        }
                        
                        return fee_rates.get(priority, fee_rates["medium"])
        except Exception:
            logger.warning(f"Error getting fee rates from BlockCypher API, falling back to bitcoinlib")
        
        # Fallback to bitcoinlib
        loop = asyncio.get_event_loop()
        
        # Get recommended fees
        fees = await loop.run_in_executor(
            None,
            lambda: self.service.estimatefee()
        )
        
        # Map priority to blocks target
        blocks_target = {
            "high": 1,     # Target confirmation in 1 block
            "medium": 3,   # Target confirmation in 3 blocks
            "low": 6       # Target confirmation in 6 blocks
        }.get(priority, 3)
        
        # Get fee rate for target block
        fee_rate = fees.get(blocks_target, fees.get(3, 10000))  # Default to 10000 satoshis/KB
        
        return fee_rate
    
    async def get_token_balance(self, address: str, token_contract: str) -> Decimal:
        """
        Bitcoin doesn't have tokens, so this is a placeholder.
        Always returns 0.
        """
        return Decimal(0)

    async def send_with_fee_wallet(self, 
                            source_address: str, 
                            destination_address: str, 
                            amount: Decimal,
                            fee_wallet: str,
                            fee_wallet_private_key: str,
                            priority: str = "medium",
                            currency: str = "BTC") -> str:
        """
        Create and broadcast a transaction using a separate fee wallet.
        In Bitcoin, fees are paid by the sender, so we just use standard method.
        
        Args:
            currency: Added for compatibility with other connectors, ignored for BTC
        """
        # Обновляем соединение перед выполнением транзакции
        await self.refresh_connection()
        
        # Bitcoin doesn't support paying fees from a separate wallet,
        # so we just use the standard method
        return await self._create_and_broadcast_transaction(
            source_address, 
            destination_address, 
            amount, 
            priority
        )
        
    async def _create_and_broadcast_transaction(self, 
                                            source_address: str, 
                                            destination_address: str, 
                                            amount: Decimal, 
                                            priority: str = "medium") -> str:
        """
        Helper method to create and broadcast a transaction in one step.
        """
        # Обновляем соединение перед выполнением транзакции
        await self.refresh_connection()
        
        # Get private key
        private_key = await self.get_private_key(source_address)
        
        # Amount in satoshis
        amount_satoshi = int(amount * Decimal(100000000))
        
        # Get fee
        fee = await self.estimate_fee(source_address, destination_address, amount, priority)
        fee_satoshi = int(fee * Decimal(100000000))
        
        loop = asyncio.get_event_loop()
        
        # Create or open wallet for this address
        wallet = await loop.run_in_executor(
            None,
            lambda: wallet_create_or_open(
                f"temp_{source_address}", 
                keys=private_key,
                network=self.network
            )
        )
        
        # Create and broadcast transaction
        tx = await loop.run_in_executor(
            None,
            lambda: wallet.send_to(
                destination_address,
                amount_satoshi,
                fee=fee_satoshi
            )
        )
        
        return tx.txid

    async def get_token_contract_address(self, currency: str) -> Optional[str]:
        """
        Bitcoin doesn't have token contracts, so this always returns None.
        """
        return None
    
    async def create_and_broadcast_transaction(
        self,
        source_address: str,
        destination_address: str,
        amount: Decimal,
        private_key: str,
        priority: str = "medium",
        currency: str = "BTC"  # Added for compatibility with other connectors
    ) -> str:
        """
        Create and broadcast a transaction using the provided private key.
        
        Args:
            source_address: Source wallet address
            destination_address: Destination wallet address
            amount: Amount to transfer
            private_key: Private key of the source wallet
            priority: Transaction priority (low, medium, high)
            currency: Added for compatibility with other connectors
            
        Returns:
            Transaction hash
        """
        # Обновляем соединение перед выполнением транзакции
        await self.refresh_connection()
        
        # Amount in satoshis
        amount_satoshi = int(amount * Decimal(100000000))
        
        # Get fee
        fee = await self.estimate_fee(source_address, destination_address, amount, priority)
        fee_satoshi = int(fee * Decimal(100000000))
        
        loop = asyncio.get_event_loop()
        
        max_retries = 3
        retry_count = 0
        
        while retry_count < max_retries:
            try:
                # Create or open wallet for this address with provided key
                wallet = await loop.run_in_executor(
                    None,
                    lambda: wallet_create_or_open(
                        f"temp_{source_address}",
                        keys=private_key,
                        network=self.network
                    )
                )
                
                # Create and broadcast transaction
                tx = await loop.run_in_executor(
                    None,
                    lambda: wallet.send_to(
                        destination_address,
                        amount_satoshi,
                        fee=fee_satoshi
                    )
                )
                
                logger.info(f"[BTC] Транзакция успешно отправлена: {source_address} -> {destination_address}, сумма: {amount}, хеш: {tx.txid}")
                return tx.txid
                
            except Exception as e:
                retry_count += 1
                logger.warning(f"[BTC] Ошибка при отправке транзакции (попытка {retry_count}/{max_retries}): {str(e)}")
                
                if retry_count >= max_retries:
                    logger.error(f"[BTC] Все попытки отправки транзакции исчерпаны: {str(e)}")
                    raise
                
                # Если ошибка связана с комиссией, увеличиваем ее
                if "fee" in str(e).lower():
                    fee_satoshi = int(fee_satoshi * 1.3)  # Увеличиваем комиссию на 30%
                    logger.info(f"[BTC] Увеличиваем комиссию до {fee_satoshi / 100000000} BTC")
                
                # Ждем перед повторной попыткой
                await asyncio.sleep(2)

    async def create_and_broadcast_transaction_with_fee(
        self,
        source_address: str, 
        destination_address: str, 
        amount: Decimal,
        source_private_key: str,
        fee_wallet: str,
        fee_wallet_private_key: str,
        priority: str = "medium",
        currency: str = "BTC"
    ) -> str:
        """
        Create and broadcast a transaction with private key directly provided.
        For Bitcoin, we don't need the fee wallet, so we ignore it.
        
        Args:
            source_address: Source wallet address
            destination_address: Destination wallet address
            amount: Amount to transfer
            source_private_key: Private key of source wallet
            fee_wallet: Fee wallet address (ignored for BTC)
            fee_wallet_private_key: Fee wallet private key (ignored for BTC)
            priority: Transaction priority
            currency: Currency type (ignored for BTC)
            
        Returns:
            Transaction hash
        """
        # В Bitcoin мы не используем fee wallet для оплаты комиссий
        # просто вызываем обычный метод с приватным ключом
        return await self.create_and_broadcast_transaction(
            source_address,
            destination_address,
            amount,
            source_private_key,
            priority
        )

# Register connector
register_connector("BTC", BitcoinConnector)