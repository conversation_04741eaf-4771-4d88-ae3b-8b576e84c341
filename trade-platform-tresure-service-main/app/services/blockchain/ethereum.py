from decimal import Decimal
from typing import Dict, Any, Tu<PERSON>, Optional
import time
import json
import base64

import aiohttp
from eth_account import Account
from web3 import Web3
from web3.providers import HTTPProvider
import asyncio

from app.services.blockchain.base import BlockchainConnector, register_connector
from app.core.config import settings
from app.core.logging import get_logger

# Определяем собственную функцию middleware
logger = get_logger(__name__)


# Создаем кастомный HTTPProvider с логированием
class LoggingHTTPProvider(HTTPProvider):
    def make_request(self, method, params):
        # Логируем запрос
        request_data = {"method": method, "params": params}
        logger.debug(f"[ETH] Отправляем RPC запрос: {json.dumps(request_data, default=str)}")
        
        try:
            # Засекаем время начала запроса
            start_time = time.time()
            
            # Выполняем запрос
            response = super().make_request(method, params)
            
            # Считаем время выполнения
            elapsed_time = time.time() - start_time
            
            # Логируем ответ
            if 'error' in response:
                logger.warning(f"[ETH] Получен ответ с ошибкой ({elapsed_time:.2f}с): {response['error']}")
            else:
                result_str = str(response.get('result', '(no result)'))
                if len(result_str) > 200:
                    result_str = result_str[:200] + '...'
                logger.debug(f"[ETH] Получен успешный ответ ({elapsed_time:.2f}с): {result_str}")
                
            return response
        except Exception as e:
            logger.error(f"[ETH] Исключение при выполнении RPC запроса: {str(e)}")
            raise


def geth_poa_middleware(make_request, web3):
    """
    Заглушка для geth_poa_middleware, которую мы будем использовать
    вместо импорта, который вызывает проблемы с Pylance.
    """
    # Функция-обертка для обработки запросов
    def middleware(method, params):
        return make_request(method, params)
    
    return middleware


class EthereumConnector(BlockchainConnector):
    """Ethereum blockchain connector implementation using web3.py."""
    
    def __init__(self):
        # Initialize web3 connection with logging provider
        if settings.TESTNET_MODE:
            provider_url = settings.ETH_TESTNET_RPC_URL
            self.explorer_api = settings.ETH_TESTNET_EXPLORER_API
        else:
            provider_url = settings.ETH_MAINNET_RPC_URL
            self.explorer_api = settings.ETH_MAINNET_EXPLORER_API
        
        logger.info(f"[ETH] Инициализация коннектора с RPC URL: {provider_url}")
        
        # Добавляем заголовки авторизации, если они настроены
        headers = {}
        if hasattr(settings, 'INFURA_PROJECT_ID') and hasattr(settings, 'INFURA_PROJECT_SECRET'):
            logger.info(f"[ETH] Используем Infura Project ID для авторизации")
            auth_string = f"{settings.INFURA_PROJECT_ID}:{settings.INFURA_PROJECT_SECRET}"
            encoded_auth = base64.b64encode(auth_string.encode()).decode()
            headers["Authorization"] = f"Basic {encoded_auth}"
        
        # Создаем провайдер с логированием
        provider = LoggingHTTPProvider(
            endpoint_uri=provider_url,
            request_kwargs={"headers": headers} if headers else None
        )
        
        self.web3 = Web3(provider)
            
        # Полностью отключаем использование middleware
        # self.web3.middleware_onion.inject(geth_poa_middleware, layer=0)
        
        # Адреса контрактов токенов (только USDC)
        self.token_contracts = {
            "USDC-ERC20": "******************************************"
        }
        
        # Проверяем соединение
        try:
            chain_id = self.web3.eth.chain_id
            logger.info(f"[ETH] Успешное подключение к сети Ethereum, chain_id: {chain_id}")
        except Exception as e:
            logger.error(f"[ETH] Ошибка при проверке соединения с RPC: {str(e)}")
        
        logger.info(f"[ETH] Ethereum коннектор инициализирован: testnet={settings.TESTNET_MODE}")
        
    async def get_balance(self, address: str) -> Decimal:
        """Получение баланса ETH для указанного адреса."""
        try:
            logger.info(f"[ETH] Запрос баланса для адреса {address}")
            # Web3.py синхронный, используем простую обертку
            checksum_address = Web3.to_checksum_address(address)
            balance_wei = self.web3.eth.get_balance(checksum_address)
            # Конвертируем из wei в ETH
            balance_eth = Decimal(balance_wei) / Decimal(10**18)
            logger.info(f"[ETH] Получен баланс {balance_eth} ETH для адреса {address}")
            return balance_eth
        except Exception as e:
            logger.error(f"[ETH] Ошибка при получении баланса для адреса {address}: {str(e)}")
            return Decimal(0)
    
    async def estimate_fee(self, 
                          source_address: str, 
                          destination_address: str, 
                          amount: Decimal, 
                          priority: str = "medium") -> Decimal:
        """Оценка комиссии Ethereum транзакции."""
        try:
            # Получаем цену газа в зависимости от приоритета
            gas_price_wei = await self._get_gas_price(priority)
            
            # Стандартный ETH перевод использует 21000 газа
            gas_limit = 21000
            
            # Рассчитываем комиссию в wei
            fee_wei = gas_price_wei * gas_limit
            
            # Конвертируем в ETH
            return Decimal(fee_wei) / Decimal(10**18)
        except Exception as e:
            logger.error(f"[ETH] Ошибка при оценке комиссии: {str(e)}")
            return Decimal('0.0005')  # Значение по умолчанию
    
    async def create_transaction(self,
                                source_address: str, 
                                destination_address: str, 
                                amount: Decimal, 
                                priority: str = "medium") -> Tuple[str, Dict[str, Any]]:
        """Создание и подписание Ethereum транзакции."""
        try:
            # Получаем приватный ключ из безопасного хранилища
            private_key = await self.get_private_key(source_address)
            
            # Конвертируем адреса в формат с контрольной суммой
            source_checksum = Web3.to_checksum_address(source_address)
            destination_checksum = Web3.to_checksum_address(destination_address)
            
            # Конвертируем сумму в wei
            amount_wei = int(amount * Decimal(10**18))
            
            # Получаем цену газа в зависимости от приоритета
            gas_price_wei = await self._get_gas_price(priority)
            
            # Получаем nonce для аккаунта
            nonce = self.web3.eth.get_transaction_count(source_checksum)
            
            # Формируем транзакцию
            tx = {
                'nonce': nonce,
                'to': destination_checksum,
                'value': amount_wei,
                'gas': 21000,  # Стандартный лимит газа для ETH перевода
                'gasPrice': gas_price_wei,
                'chainId': self.web3.eth.chain_id
            }
            
            # Подписываем транзакцию
            signed_tx = self.web3.eth.account.sign_transaction(tx, private_key)
            
            # Получаем хеш транзакции
            tx_hash = Web3.to_hex(signed_tx.hash)
            
            # Возвращаем хеш и сырые данные транзакции
            return tx_hash, {"raw_tx": signed_tx.rawTransaction.hex()}
        except Exception as e:
            logger.error(f"[ETH] Ошибка при создании транзакции: {str(e)}")
            raise
    
    async def broadcast_transaction(self, raw_transaction: Dict[str, Any]) -> str:
        """Отправка подписанной Ethereum транзакции в сеть."""
        max_retries = 3
        retry_count = 0
        
        try:
            raw_tx_hex = raw_transaction['raw_tx']
            raw_tx_bytes = bytes.fromhex(raw_tx_hex.replace('0x', ''))
            
            logger.debug(f"[ETH] Подготовлена транзакция для отправки: {raw_tx_hex[:50]}...")
            
            while retry_count < max_retries:
                try:
                    # Отправляем сырую транзакцию
                    logger.debug(f"[ETH] Отправка raw_transaction, попытка {retry_count+1}/{max_retries}")
                    tx_hash = self.web3.eth.send_raw_transaction(raw_tx_bytes)
                    logger.info(f"[ETH] Транзакция успешно отправлена, хеш: {Web3.to_hex(tx_hash)}")
                    
                    return Web3.to_hex(tx_hash)
                except Exception as e:
                    retry_count += 1
                    logger.warning(f"[ETH] Ошибка при отправке транзакции (попытка {retry_count}/{max_retries}): {str(e)}")
                    
                    if retry_count >= max_retries:
                        logger.error(f"[ETH] Все попытки отправки транзакции исчерпаны: {str(e)}")
                        raise
                    
                    # Ждем перед повторной попыткой
                    logger.debug(f"[ETH] Ожидание {2} секунд перед повторной попыткой")
                    await asyncio.sleep(2)
        except Exception as e:
            logger.error(f"[ETH] Ошибка при отправке транзакции: {str(e)}")
            raise
    
    async def get_transaction_status(self, tx_hash: str) -> Dict[str, Any]:
        """Получение статуса Ethereum транзакции."""
        try:
            # Пытаемся получить квитанцию транзакции
            receipt = self.web3.eth.get_transaction_receipt(tx_hash)
            
            # Если квитанция существует, проверяем подтверждения
            if receipt:
                current_block = self.web3.eth.block_number
                confirmations = current_block - receipt.blockNumber + 1
                
                status = "pending"
                if confirmations >= settings.ETH_CONFIRMATION_THRESHOLD:
                    status = "confirmed"
                elif confirmations >= 1:
                    status = "processing"
                    
                return {
                    "status": status,
                    "confirmations": confirmations,
                    "block_number": receipt.blockNumber,
                    "success": receipt.status == 1,  # 1 = успех, 0 = ошибка
                    "gas_used": receipt.gasUsed,
                    "fee": Decimal(receipt.gasUsed * receipt.effectiveGasPrice) / Decimal(10**18),
                }
            else:
                # Транзакция в ожидании
                return {"status": "pending", "confirmations": 0}
        except Exception as e:
            logger.error(f"[ETH] Ошибка при проверке статуса транзакции {tx_hash}: {str(e)}")
            # Транзакция не найдена
            return {"status": "not_found"}
        
    async def validate_address(self, address: str) -> bool:
        """Проверка формата Ethereum адреса."""
        try:
            return self.web3.is_address(address)
        except Exception as e:
            logger.error(f"[ETH] Ошибка при проверке адреса {address}: {str(e)}")
            return False
        
    async def get_private_key(self, address: str) -> str:
        """
        Получение приватного ключа для адреса путем запроса в Wallet Service.
        
        Args:
            address: Адрес кошелька
                
        Returns:
            Приватный ключ в виде строки
        
        Raises:
            ValueError: Если не удалось получить приватный ключ
        """
        try:
            from app.services.kafka_service import request_private_key
            
            private_key = await request_private_key(address, "ETH")
            if not private_key:
                raise ValueError(f"Не удалось получить приватный ключ для ETH адреса {address} из Wallet Service")
            
            return private_key
        except Exception as e:
            logger.error(f"[ETH] Ошибка при получении приватного ключа для {address}: {str(e)}")
            raise
    
    async def _get_gas_price(self, priority: str) -> int:
        """Получение цены газа в wei в зависимости от приоритета."""
        # Получаем текущие цены газа из сети
        try:
            logger.debug(f"[ETH] Запрос цены газа из API {self.explorer_api}, приоритет: {priority}")
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.explorer_api}/api?module=gastracker&action=gasoracle") as response:
                    if response.status == 200:
                        response_text = await response.text()
                        logger.debug(f"[ETH] Ответ от API цены газа: {response_text[:200]}...")
                        
                        data = await response.json()
                        if data.get('status') == '1':
                            result = data.get('result', {})
                            
                            # Сопоставляем приоритет с категорией цены газа
                            gas_prices = {  
                                "low": int(float(result.get('SafeGasPrice', 20)) * 10**9),  # Конвертируем Gwei в Wei
                                "medium": int(float(result.get('ProposeGasPrice', 30)) * 10**9),
                                "high": int(float(result.get('FastGasPrice', 50)) * 10**9)
                            }
                            
                            selected_price = gas_prices.get(priority, gas_prices["medium"])
                            logger.info(f"[ETH] Получена цена газа для приоритета {priority}: {selected_price / 10**9} Gwei")
                            return selected_price
                        else:
                            logger.warning(f"[ETH] API вернуло ошибку: {data}")
                    else:
                        logger.warning(f"[ETH] Ошибка при запросе цены газа: HTTP {response.status}")
        except Exception as e:
            logger.error(f"[ETH] Ошибка при получении цены газа из API: {str(e)}")
            
        logger.info("[ETH] Используем запасной вариант - цена газа из Web3 провайдера")
        # Запасной вариант - цена газа из Web3 провайдера
        try:
            gas_price = self.web3.eth.gas_price
            logger.debug(f"[ETH] Получена базовая цена газа из Web3: {gas_price / 10**9} Gwei")
            
            # Корректируем в зависимости от приоритета
            if priority == "high":
                gas_price = int(gas_price * 1.3)  # на 30% выше
                logger.debug(f"[ETH] Цена газа скорректирована для high: {gas_price / 10**9} Gwei")
            elif priority == "low":
                gas_price = int(gas_price * 0.7)  # на 30% ниже
                logger.debug(f"[ETH] Цена газа скорректирована для low: {gas_price / 10**9} Gwei")
                
            return gas_price
        except Exception as e:
            logger.error(f"[ETH] Ошибка при получении базовой цены газа из Web3: {str(e)}")
            # Если всё совсем плохо, используем хардкодед значения
            fallback_prices = {"low": 20 * 10**9, "medium": 30 * 10**9, "high": 50 * 10**9}
            fallback_price = fallback_prices.get(priority, fallback_prices["medium"])
            logger.warning(f"[ETH] Используем фиксированную цену газа: {fallback_price / 10**9} Gwei")
            return fallback_price
    
    async def get_token_balance(self, address: str, token_contract: str) -> Decimal:
        """Получение баланса токена для указанного адреса."""
        # ABI для метода balanceOf в ERC20
        abi = [
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }
        ]
        
        try:
            # Создаем контракт
            contract = self.web3.eth.contract(address=Web3.to_checksum_address(token_contract), abi=abi)
            
            # Получаем баланс и количество десятичных знаков
            balance = contract.functions.balanceOf(Web3.to_checksum_address(address)).call()
            decimals = contract.functions.decimals().call()
            
            # Конвертируем с учетом десятичных знаков
            return Decimal(balance) / Decimal(10 ** decimals)
        except Exception as e:
            logger.error(f"[ETH] Ошибка при получении баланса токена: {str(e)}")
            return Decimal(0)

    async def get_token_contract_address(self, currency: str) -> Optional[str]:
        """Получение адреса контракта токена для указанной валюты."""
        if currency in self.token_contracts:
            return self.token_contracts[currency]
        else:
            logger.warning(f"[ETH] Неизвестный токен: {currency}")
            return None

    async def send_with_fee_wallet(self, 
                        source_address: str, 
                        destination_address: str, 
                        amount: Decimal,
                        fee_wallet: str,
                        fee_wallet_private_key: str,
                        priority: str = "medium",
                        currency: str = "ETH") -> str:
        """
        Создание и отправка транзакции с использованием отдельного fee-кошелька.
        Для токенов ERC20 fee-кошелек платит за газ и пополняет ETH при необходимости.
        
        Args:
            source_address: Адрес кошелька-отправителя
            destination_address: Адрес кошелька-получателя
            amount: Сумма перевода
            fee_wallet: Адрес fee-кошелька для газа
            fee_wallet_private_key: Приватный ключ fee-кошелька
            priority: Приоритет транзакции (low, medium, high)
            currency: Тип валюты (ETH или USDC-ERC20)
            
        Returns:
            Хеш транзакции
        """
        try:
            logger.info(f"[ETH] Выполнение send_with_fee_wallet: {source_address} -> {destination_address}, {amount} {currency}")

            # Обновляем соединение перед критической операцией
            await self.refresh_connection()
            
            # Проверяем, является ли это переводом токена
            is_token = currency != "ETH"
            
            # Конвертируем адреса в формат с контрольной суммой
            source_checksum = Web3.to_checksum_address(source_address)
            dest_checksum = Web3.to_checksum_address(destination_address)
            fee_wallet_checksum = Web3.to_checksum_address(fee_wallet)
            
            # Получаем цену газа
            gas_price = await self._get_gas_price(priority)
            
            # Проверяем баланс ETH на кошельке отправителя
            eth_balance = self.web3.eth.get_balance(source_checksum)
            
            # Рассчитываем необходимое количество ETH для комиссии
            required_gas = 100000 if is_token else 21000
            required_eth_wei = gas_price * required_gas
            
            # Если баланса недостаточно, пополняем с fee-кошелька
            if eth_balance < required_eth_wei:
                # Рассчитываем сколько ETH отправить (с запасом)
                eth_to_send = required_eth_wei * 3  # С запасом в 3 раза больше
                
                # Получаем nonce для fee-кошелька
                fee_nonce = self.web3.eth.get_transaction_count(fee_wallet_checksum)
                
                # Создаем транзакцию пополнения ETH
                gas_tx = {
                    'nonce': fee_nonce,
                    'to': source_checksum,
                    'value': eth_to_send,
                    'gas': 21000,
                    'gasPrice': gas_price,
                    'chainId': self.web3.eth.chain_id
                }
                
                # Подписываем транзакцию пополнения
                signed_gas_tx = self.web3.eth.account.sign_transaction(gas_tx, fee_wallet_private_key)
                
                # Отправляем транзакцию пополнения
                gas_tx_hash = self.web3.eth.send_raw_transaction(signed_gas_tx.rawTransaction)
                gas_tx_hex_hash = Web3.to_hex(gas_tx_hash)
                
                logger.info(f"[ETH] Отправлено {eth_to_send / 10**18} ETH для газа на {source_address}, хеш: {gas_tx_hex_hash}")
                
                # Ждем подтверждения транзакции пополнения
                for _ in range(30):  # Максимум 30 секунд ожидания
                    try:
                        receipt = self.web3.eth.get_transaction_receipt(gas_tx_hash)
                        if receipt and receipt.blockNumber:
                            logger.info(f"[ETH] Транзакция пополнения ETH подтверждена")
                            break
                    except Exception as e:
                        pass
                    await asyncio.sleep(1)
            
            # Получаем приватный ключ исходного кошелька
            source_private_key = await self.get_private_key(source_address)
            
            # Для токенов получаем адрес контракта
            if is_token:
                token_contract = await self.get_token_contract_address(currency)
                if not token_contract:
                    raise ValueError(f"[ETH] Адрес контракта не найден для токена {currency}")
                token_contract_checksum = Web3.to_checksum_address(token_contract)
                
                # ABI для токена ERC20
                erc20_abi = [
                    {
                        "constant": False,
                        "inputs": [
                            {"name": "_to", "type": "address"},
                            {"name": "_value", "type": "uint256"}
                        ],
                        "name": "transfer",
                        "outputs": [{"name": "", "type": "bool"}],
                        "type": "function"
                    },
                    {
                        "constant": True,
                        "inputs": [],
                        "name": "decimals",
                        "outputs": [{"name": "", "type": "uint8"}],
                        "type": "function"
                    }
                ]
                
                # Создаем контракт
                contract = self.web3.eth.contract(address=token_contract_checksum, abi=erc20_abi)
                
                # Получаем количество десятичных знаков токена
                decimals = contract.functions.decimals().call()
                
                # Конвертируем сумму в единицы токена
                token_amount = int(amount * Decimal(10 ** decimals))
                
                # Получаем nonce для исходного кошелька (возможно обновился после пополнения ETH)
                nonce = self.web3.eth.get_transaction_count(source_checksum)
                
                # Приблизительный расход газа для ERC20 транзакций
                estimated_gas = 100000
                
                # Создаем транзакцию перевода токенов
                token_tx = contract.functions.transfer(
                    dest_checksum,
                    token_amount
                ).build_transaction({
                    'from': source_checksum,  # Отправитель токенов - исходный кошелек
                    'nonce': nonce,
                    'gasPrice': gas_price,
                    'gas': estimated_gas,
                    'chainId': self.web3.eth.chain_id
                })
                
                logger.debug(f"[ETH] Подготовлена token-транзакция: {token_tx}")
                
                # Логика повторных попыток для токен-транзакции
                max_retries = 3
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        if retry_count > 0:
                            # Увеличиваем gas_price с каждой попыткой
                            gas_price = int(gas_price * (Decimal('1') + Decimal('0.2') * retry_count))
                            logger.info(f"[ETH] Повторная попытка {retry_count} с увеличенной ценой газа: {gas_price / 10**9} Gwei")
                            token_tx['gasPrice'] = gas_price
                            # Обновляем nonce, так как мог измениться
                            token_tx['nonce'] = self.web3.eth.get_transaction_count(source_checksum)
                        
                        # Подписываем транзакцию приватным ключом исходного кошелька
                        logger.debug(f"[ETH] Подписываем token-транзакцию приватным ключом исходного кошелька")
                        signed_tx = self.web3.eth.account.sign_transaction(token_tx, source_private_key)
                        
                        # Отправляем транзакцию
                        try:
                            logger.debug(f"[ETH] Отправляем token-транзакцию")
                            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        except AttributeError:
                            if hasattr(signed_tx, 'raw_transaction'):
                                logger.debug(f"[ETH] Используем raw_transaction атрибут для token-транзакции")
                                tx_hash = self.web3.eth.send_raw_transaction(signed_tx.raw_transaction)
                            else:
                                attrs = dir(signed_tx)
                                logger.error(f"[ETH] SignedTransaction не имеет ожидаемых атрибутов. Доступны: {attrs}")
                                raise ValueError("Не удалось извлечь данные транзакции из подписанной транзакции")
                        
                        hex_hash = Web3.to_hex(tx_hash)
                        logger.info(f"[ETH] Транзакция {currency} отправлена: {source_address} -> {destination_address}, "
                                f"сумма: {amount}, хеш: {hex_hash}")
                        
                        return hex_hash
                    except Exception as e:
                        retry_count += 1
                        logger.warning(f"[ETH] Ошибка при отправке токен-транзакции (попытка {retry_count}/{max_retries}): {str(e)}")
                        
                        if retry_count >= max_retries:
                            logger.error(f"[ETH] Все попытки отправки токен-транзакции исчерпаны: {str(e)}")
                            raise
                        
                        logger.debug(f"[ETH] Ожидание {2} секунд перед повторной попыткой отправки token-транзакции")
                        await asyncio.sleep(2)
                
            else:
                # Для обычной ETH транзакции
                # Получаем nonce для исходного кошелька (возможно обновился после пополнения ETH)
                nonce = self.web3.eth.get_transaction_count(source_checksum)
                
                # Конвертируем сумму в wei
                amount_wei = int(amount * Decimal(10**18))
                
                # Создаем транзакцию 
                tx = {
                    'nonce': nonce,
                    'to': dest_checksum,
                    'value': amount_wei,
                    'gas': 21000,  # Стандартный лимит газа для ETH
                    'gasPrice': gas_price,
                    'chainId': self.web3.eth.chain_id,
                    'from': source_checksum  # Отправитель - исходный кошелек
                }
                
                logger.debug(f"[ETH] Подготовлена ETH-транзакция: {tx}")
                
                # Логика повторных попыток для ETH-транзакции
                max_retries = 3
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        if retry_count > 0:
                            # Увеличиваем gas_price с каждой попыткой
                            gas_price = int(gas_price * (Decimal('1') + Decimal('0.2') * retry_count))
                            logger.info(f"[ETH] Повторная попытка {retry_count} с увеличенной ценой газа: {gas_price / 10**9} Gwei")
                            tx['gasPrice'] = gas_price
                            # Обновляем nonce, так как мог измениться
                            tx['nonce'] = self.web3.eth.get_transaction_count(source_checksum)
                        
                        # Подписываем транзакцию приватным ключом исходного кошелька
                        logger.debug(f"[ETH] Подписываем ETH-транзакцию приватным ключом исходного кошелька")
                        signed_tx = self.web3.eth.account.sign_transaction(tx, source_private_key)
                        
                        # Отправляем транзакцию
                        try:
                            logger.debug(f"[ETH] Отправляем ETH-транзакцию")
                            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        except AttributeError:
                            if hasattr(signed_tx, 'raw_transaction'):
                                logger.debug(f"[ETH] Используем raw_transaction атрибут для ETH-транзакции")
                                tx_hash = self.web3.eth.send_raw_transaction(signed_tx.raw_transaction)
                            else:
                                attrs = dir(signed_tx)
                                logger.error(f"[ETH] SignedTransaction не имеет ожидаемых атрибутов. Доступны: {attrs}")
                                raise ValueError("Не удалось извлечь данные транзакции из подписанной транзакции")
                        
                        hex_hash = Web3.to_hex(tx_hash)
                        logger.info(f"[ETH] ETH транзакция отправлена: {source_address} -> {destination_address}, "
                                f"сумма: {amount}, хеш: {hex_hash}")
                        
                        return hex_hash
                    except Exception as e:
                        retry_count += 1
                        logger.warning(f"[ETH] Ошибка при отправке ETH-транзакции (попытка {retry_count}/{max_retries}): {str(e)}")
                        
                        if retry_count >= max_retries:
                            logger.error(f"[ETH] Все попытки отправки ETH-транзакции исчерпаны: {str(e)}")
                            raise
                        
                        logger.debug(f"[ETH] Ожидание {2} секунд перед повторной попыткой отправки ETH-транзакции")
                        await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"[ETH] Ошибка в send_with_fee_wallet: {str(e)}")
            raise ValueError(f"Ошибка отправки с fee wallet: {str(e)}")

    async def create_and_broadcast_transaction_with_fee(
        self,
        source_address: str, 
        destination_address: str, 
        amount: Decimal,
        source_private_key: str,  # Единственное отличие - приватный ключ передается напрямую
        fee_wallet: str,
        fee_wallet_private_key: str,
        priority: str = "medium",
        currency: str = "ETH"
    ) -> str:
        """
        Создание и отправка транзакции с использованием переданного приватного ключа.
        Для токенов ERC20 fee-кошелек платит за газ и пополняет ETH при необходимости.
        
        Идентична функции send_with_fee_wallet, но использует переданный ключ вместо запроса через Kafka.
        """
        try:
            logger.info(f"[ETH] Выполнение create_and_broadcast_transaction_with_fee: {source_address} -> {destination_address}, {amount} {currency}")
            
            # Обновляем соединение перед критической операцией
            await self.refresh_connection()
            
            # Проверяем, является ли это переводом токена
            is_token = currency != "ETH"
            
            # Конвертируем адреса в формат с контрольной суммой
            source_checksum = Web3.to_checksum_address(source_address)
            dest_checksum = Web3.to_checksum_address(destination_address)
            fee_wallet_checksum = Web3.to_checksum_address(fee_wallet)
            
            # Получаем цену газа
            gas_price = await self._get_gas_price(priority)
            
            # Проверяем баланс ETH на кошельке отправителя
            eth_balance = self.web3.eth.get_balance(source_checksum)
            
            # Рассчитываем необходимое количество ETH для комиссии
            required_gas = 100000 if is_token else 21000
            required_eth_wei = gas_price * required_gas
            
            # Если баланса недостаточно, пополняем с fee-кошелька
            if eth_balance < required_eth_wei:
                # Рассчитываем сколько ETH отправить (с запасом)
                eth_to_send = required_eth_wei * 3  # С запасом в 3 раза больше
                
                # Получаем nonce для fee-кошелька
                fee_nonce = self.web3.eth.get_transaction_count(fee_wallet_checksum)
                
                # Создаем транзакцию пополнения ETH
                gas_tx = {
                    'nonce': fee_nonce,
                    'to': source_checksum,
                    'value': eth_to_send,
                    'gas': 21000,
                    'gasPrice': gas_price,
                    'chainId': self.web3.eth.chain_id
                }
                
                # Подписываем транзакцию пополнения
                signed_gas_tx = self.web3.eth.account.sign_transaction(gas_tx, fee_wallet_private_key)
                
                # Отправляем транзакцию пополнения
                gas_tx_hash = self.web3.eth.send_raw_transaction(signed_gas_tx.rawTransaction)
                gas_tx_hex_hash = Web3.to_hex(gas_tx_hash)
                
                logger.info(f"[ETH] Отправлено {eth_to_send / 10**18} ETH для газа на {source_address}, хеш: {gas_tx_hex_hash}")
                
                # Ждем подтверждения транзакции пополнения
                for _ in range(30):  # Максимум 30 секунд ожидания
                    try:
                        receipt = self.web3.eth.get_transaction_receipt(gas_tx_hash)
                        if receipt and receipt.blockNumber:
                            logger.info(f"[ETH] Транзакция пополнения ETH подтверждена")
                            break
                    except Exception as e:
                        pass
                    await asyncio.sleep(1)
            
            # ОТЛИЧИЕ: Не запрашиваем ключ через Kafka, а используем переданный параметр
            # source_private_key = await self.get_private_key(source_address)
            
            # Для токенов получаем адрес контракта
            if is_token:
                token_contract = await self.get_token_contract_address(currency)
                if not token_contract:
                    raise ValueError(f"[ETH] Адрес контракта не найден для токена {currency}")
                token_contract_checksum = Web3.to_checksum_address(token_contract)
                
                # ABI для токена ERC20
                erc20_abi = [
                    {
                        "constant": False,
                        "inputs": [
                            {"name": "_to", "type": "address"},
                            {"name": "_value", "type": "uint256"}
                        ],
                        "name": "transfer",
                        "outputs": [{"name": "", "type": "bool"}],
                        "type": "function"
                    },
                    {
                        "constant": True,
                        "inputs": [],
                        "name": "decimals",
                        "outputs": [{"name": "", "type": "uint8"}],
                        "type": "function"
                    }
                ]
                
                # Создаем контракт
                contract = self.web3.eth.contract(address=token_contract_checksum, abi=erc20_abi)
                
                # Получаем количество десятичных знаков токена
                decimals = contract.functions.decimals().call()
                
                # Конвертируем сумму в единицы токена
                token_amount = int(amount * Decimal(10 ** decimals))
                
                # Получаем nonce для исходного кошелька (возможно обновился после пополнения ETH)
                nonce = self.web3.eth.get_transaction_count(source_checksum)
                
                # Приблизительный расход газа для ERC20 транзакций
                estimated_gas = 100000
                
                # Создаем транзакцию перевода токенов
                token_tx = contract.functions.transfer(
                    dest_checksum,
                    token_amount
                ).build_transaction({
                    'from': source_checksum,  # Отправитель токенов - исходный кошелек
                    'nonce': nonce,
                    'gasPrice': gas_price,
                    'gas': estimated_gas,
                    'chainId': self.web3.eth.chain_id
                })
                
                logger.debug(f"[ETH] Подготовлена token-транзакция: {token_tx}")
                
                # Логика повторных попыток для токен-транзакции
                max_retries = 3
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        if retry_count > 0:
                            # Увеличиваем gas_price с каждой попыткой
                            gas_price = int(gas_price * (Decimal('1') + Decimal('0.2') * retry_count))
                            logger.info(f"[ETH] Повторная попытка {retry_count} с увеличенной ценой газа: {gas_price / 10**9} Gwei")
                            token_tx['gasPrice'] = gas_price
                            # Обновляем nonce, так как мог измениться
                            token_tx['nonce'] = self.web3.eth.get_transaction_count(source_checksum)
                        
                        # Подписываем транзакцию приватным ключом исходного кошелька
                        logger.debug(f"[ETH] Подписываем token-транзакцию приватным ключом исходного кошелька")
                        signed_tx = self.web3.eth.account.sign_transaction(token_tx, source_private_key)
                        
                        # Отправляем транзакцию
                        try:
                            logger.debug(f"[ETH] Отправляем token-транзакцию")
                            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        except AttributeError:
                            if hasattr(signed_tx, 'raw_transaction'):
                                logger.debug(f"[ETH] Используем raw_transaction атрибут для token-транзакции")
                                tx_hash = self.web3.eth.send_raw_transaction(signed_tx.raw_transaction)
                            else:
                                attrs = dir(signed_tx)
                                logger.error(f"[ETH] SignedTransaction не имеет ожидаемых атрибутов. Доступны: {attrs}")
                                raise ValueError("Не удалось извлечь данные транзакции из подписанной транзакции")
                        
                        hex_hash = Web3.to_hex(tx_hash)
                        logger.info(f"[ETH] Транзакция {currency} отправлена: {source_address} -> {destination_address}, "
                                f"сумма: {amount}, хеш: {hex_hash}")
                        
                        return hex_hash
                    except Exception as e:
                        retry_count += 1
                        logger.warning(f"[ETH] Ошибка при отправке токен-транзакции (попытка {retry_count}/{max_retries}): {str(e)}")
                        
                        if retry_count >= max_retries:
                            logger.error(f"[ETH] Все попытки отправки токен-транзакции исчерпаны: {str(e)}")
                            raise
                        
                        logger.debug(f"[ETH] Ожидание {2} секунд перед повторной попыткой отправки token-транзакции")
                        await asyncio.sleep(2)
                
            else:
                # Для обычной ETH транзакции
                # Получаем nonce для исходного кошелька (возможно обновился после пополнения ETH)
                nonce = self.web3.eth.get_transaction_count(source_checksum)
                
                # Конвертируем сумму в wei
                amount_wei = int(amount * Decimal(10**18))
                
                # Создаем транзакцию 
                tx = {
                    'nonce': nonce,
                    'to': dest_checksum,
                    'value': amount_wei,
                    'gas': 21000,  # Стандартный лимит газа для ETH
                    'gasPrice': gas_price,
                    'chainId': self.web3.eth.chain_id,
                    'from': source_checksum  # Отправитель - исходный кошелек
                }
                
                logger.debug(f"[ETH] Подготовлена ETH-транзакция: {tx}")
                
                # Логика повторных попыток для ETH-транзакции
                max_retries = 3
                retry_count = 0
                
                while retry_count < max_retries:
                    try:
                        if retry_count > 0:
                            # Увеличиваем gas_price с каждой попыткой
                            gas_price = int(gas_price * (Decimal('1') + Decimal('0.2') * retry_count))
                            logger.info(f"[ETH] Повторная попытка {retry_count} с увеличенной ценой газа: {gas_price / 10**9} Gwei")
                            tx['gasPrice'] = gas_price
                            # Обновляем nonce, так как мог измениться
                            tx['nonce'] = self.web3.eth.get_transaction_count(source_checksum)
                        
                        # Подписываем транзакцию приватным ключом исходного кошелька
                        logger.debug(f"[ETH] Подписываем ETH-транзакцию приватным ключом исходного кошелька")
                        signed_tx = self.web3.eth.account.sign_transaction(tx, source_private_key)
                        
                        # Отправляем транзакцию
                        try:
                            logger.debug(f"[ETH] Отправляем ETH-транзакцию")
                            tx_hash = self.web3.eth.send_raw_transaction(signed_tx.rawTransaction)
                        except AttributeError:
                            if hasattr(signed_tx, 'raw_transaction'):
                                logger.debug(f"[ETH] Используем raw_transaction атрибут для ETH-транзакции")
                                tx_hash = self.web3.eth.send_raw_transaction(signed_tx.raw_transaction)
                            else:
                                attrs = dir(signed_tx)
                                logger.error(f"[ETH] SignedTransaction не имеет ожидаемых атрибутов. Доступны: {attrs}")
                                raise ValueError("Не удалось извлечь данные транзакции из подписанной транзакции")
                        
                        hex_hash = Web3.to_hex(tx_hash)
                        logger.info(f"[ETH] ETH транзакция отправлена: {source_address} -> {destination_address}, "
                                f"сумма: {amount}, хеш: {hex_hash}")
                        
                        return hex_hash
                    except Exception as e:
                        retry_count += 1
                        logger.warning(f"[ETH] Ошибка при отправке ETH-транзакции (попытка {retry_count}/{max_retries}): {str(e)}")
                        
                        if retry_count >= max_retries:
                            logger.error(f"[ETH] Все попытки отправки ETH-транзакции исчерпаны: {str(e)}")
                            raise
                        
                        logger.debug(f"[ETH] Ожидание {2} секунд перед повторной попыткой отправки ETH-транзакции")
                        await asyncio.sleep(2)
                
        except Exception as e:
            logger.error(f"[ETH] Ошибка в create_and_broadcast_transaction_with_fee: {str(e)}")
            raise ValueError(f"Ошибка при создании и отправке транзакции с fee wallet: {str(e)}")

    async def refresh_connection(self):
        """Обновляет соединение с Ethereum RPC перед критическими операциями."""
        try:
            logger.info("[ETH] Обновление соединения с Ethereum RPC")
            
            # Создаем URL из настроек API
            if settings.TESTNET_MODE:
                # Для тестовой сети (если настроено)
                provider_url = f"{settings.ETH_API_URL}{settings.ETH_API_KEY}"
            else:
                # Для основной сети Ethereum
                provider_url = f"{settings.ETH_API_URL}{settings.ETH_API_KEY}"
            
            logger.info(f"[ETH] Использую RPC URL: {provider_url}")
            
            # Настройки таймаута для HTTP-запросов
            request_kwargs = {"timeout": 30}
            
            # Создаем провайдер с логированием
            provider = LoggingHTTPProvider(
                endpoint_uri=provider_url,
                request_kwargs=request_kwargs
            )
            
            # Заменяем текущий веб3 на новый
            self.web3 = Web3(provider)
            
            # Проверяем соединение
            chain_id = self.web3.eth.chain_id
            logger.info(f"[ETH] Соединение успешно обновлено. Chain ID: {chain_id}")
            return True
        except Exception as e:
            logger.error(f"[ETH] Ошибка при обновлении соединения: {str(e)}")
            # Попытка восстановления с использованием резервного RPC (если нужно)
            try:
                # Можно добавить резервный RPC, например Infura или публичный узел
                backup_url = "https://rpc.ankr.com/eth"
                logger.info(f"[ETH] Попытка подключения к резервному RPC: {backup_url}")
                
                backup_provider = LoggingHTTPProvider(
                    endpoint_uri=backup_url,
                    request_kwargs={"timeout": 30}
                )
                
                self.web3 = Web3(backup_provider)
                chain_id = self.web3.eth.chain_id
                logger.info(f"[ETH] Резервное соединение установлено. Chain ID: {chain_id}")
                return True
            except Exception as backup_error:
                logger.error(f"[ETH] Не удалось установить резервное соединение: {str(backup_error)}")
                return False

# Регистрируем коннектор
register_connector("ETH", EthereumConnector)