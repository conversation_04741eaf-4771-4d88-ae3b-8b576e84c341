from abc import ABC, abstractmethod
from decimal import Decimal
from typing import Dict, Op<PERSON>, Tu<PERSON>, Any

from app.core.logging import get_logger

logger = get_logger(__name__)


class BlockchainConnector(ABC):
    """Base abstract class for all blockchain connectors."""

    @abstractmethod
    async def get_balance(self, address: str) -> Decimal:
        """Get balance for the given address."""
        pass
    
    @abstractmethod
    async def get_token_balance(self, address: str, token_contract: str) -> Decimal:
        """
        Get token balance for the given address.
        
        Args:
            address: The wallet address
            token_contract: The token contract address
            
        Returns:
            Token balance as Decimal
        """
        pass

    @abstractmethod
    async def estimate_fee(self, 
                           source_address: str, 
                           destination_address: str, 
                           amount: Decimal, 
                           priority: str = "medium") -> Decimal:
        """
        Estimate transaction fee.
        
        Args:
            source_address: The source wallet address
            destination_address: The destination wallet address
            amount: Amount to transfer
            priority: Transaction priority (low, medium, high)
            
        Returns:
            Estimated fee in the blockchain's native currency
        """
        pass

    @abstractmethod
    async def create_transaction(self, 
                                source_address: str, 
                                destination_address: str, 
                                amount: Decimal, 
                                priority: str = "medium") -> Tuple[str, Dict[str, Any]]:
        """
        Create and sign a transaction but don't broadcast it yet.
        
        Args:
            source_address: The source wallet address
            destination_address: The destination wallet address
            amount: Amount to transfer
            priority: Transaction priority (low, medium, high)
            
        Returns:
            Tuple with transaction hash and raw transaction data
        """
        pass

    @abstractmethod
    async def broadcast_transaction(self, raw_transaction: Dict[str, Any]) -> str:
        """
        Broadcast the signed transaction to the network.
        
        Args:
            raw_transaction: The signed transaction data
            
        Returns:
            Transaction hash
        """
        pass

    @abstractmethod
    async def get_transaction_status(self, tx_hash: str) -> Dict[str, Any]:
        """
        Get the status of a transaction.
        
        Args:
            tx_hash: Transaction hash
            
        Returns:
            Dictionary with transaction status information
        """
        pass

    @abstractmethod
    async def validate_address(self, address: str) -> bool:
        """
        Validate if an address is correctly formatted.
        
        Args:
            address: The address to validate
            
        Returns:
            True if address is valid, False otherwise
        """
        pass

    @abstractmethod
    async def get_private_key(self, address: str) -> str:
        """
        Get private key for the address from secure storage.
        
        Args:
            address: The wallet address
            
        Returns:
            The private key as a string
        """
        pass
    
    @abstractmethod
    async def send_with_fee_wallet(self, 
                              source_address: str, 
                              destination_address: str, 
                              amount: Decimal,
                              fee_wallet: str,
                              fee_wallet_private_key: str,
                              priority: str = "medium") -> str:
        """
        Create and broadcast a transaction using a separate fee wallet.
        
        Args:
            source_address: The source wallet address
            destination_address: The destination wallet address
            amount: Amount to transfer
            fee_wallet: The wallet address to pay fees from
            fee_wallet_private_key: Private key of the fee wallet
            priority: Transaction priority (low, medium, high)
            
        Returns:
            Transaction hash
        """
        pass
    
    @abstractmethod
    async def get_token_contract_address(self, currency: str) -> Optional[str]:
        """
        Get token contract address for a given currency.
        
        Args:
            currency: The currency code (e.g., USDT-TRC20, USDC-ERC20)
            
        Returns:
            Contract address or None if not a token
        """
        pass


# Mapping of blockchain network codes to connector classes
# This will be populated with implementations
_connector_classes = {}

# Cache of connector instances
_connector_instances = {}


def register_connector(network_code: str, connector_class: type):
    """
    Register a blockchain connector class for a network code.
    
    Args:
        network_code: Network code (BTC, ETH, TRON, etc.)
        connector_class: BlockchainConnector class implementation
    """
    _connector_classes[network_code] = connector_class
    logger.info(f"Registered blockchain connector for network: {network_code}")


def get_blockchain_connector(network: str) -> Optional[BlockchainConnector]:
    """
    Get a blockchain connector instance for a network.
    
    Args:
        network: Network code (BTC, ETH, TRON, etc.)
        
    Returns:
        BlockchainConnector instance or None if not available
    """
    # If network is currency-specific, extract the base network
    base_network = network
    if network.endswith("-ERC20"):
        base_network = "ETH"
    elif network.endswith("-TRC20"):
        base_network = "TRON"
    
    # Check cache first
    if base_network in _connector_instances:
        return _connector_instances[base_network]
    
    # Create new instance if class is registered
    if base_network in _connector_classes:
        try:
            connector = _connector_classes[base_network]()
            _connector_instances[base_network] = connector
            return connector
        except Exception as e:
            logger.error(f"Error creating connector for network {base_network}: {str(e)}")
    
    logger.warning(f"No connector available for network: {network}")
    return None