import asyncio
import base58
from decimal import Decimal
from typing import Dict, Any, <PERSON><PERSON>, Optional
import requests
import time

import aiohttp
from tronpy import Tron
from tronpy.keys import PrivateKey
from tronpy.providers import HTTPProvider

from app.services.blockchain.base import BlockchainConnector, register_connector
from app.core.config import settings
from app.core.logging import get_logger


logger = get_logger(__name__)


class TronHTTPProvider(HTTPProvider):
    """Кастомный HTTP провайдер с поддержкой API-ключа"""
    def __init__(self, endpoint_uri=None, timeout=60, api_key=None):
        super().__init__(endpoint_uri, timeout)
        self._session = requests.Session()
        if api_key:
            self._session.headers.update({"TRON-PRO-API-KEY": api_key})
    
    def make_request(self, method, params=None):
        if params is None:
            params = {}
        url = self.endpoint_uri + "/" + method
        resp = self._session.post(url, json=params, timeout=self.timeout)
        resp.raise_for_status()
        return resp.json()


class TronConnector(BlockchainConnector):
    """Tron blockchain connector implementation using tronpy."""
    
    def __init__(self):
        """Initialize Tron client with API key"""
        try:
            api_key = settings.TRON_API_KEY

            if settings.TESTNET_MODE:
                api_url = settings.TRON_TESTNET_API_URL
                provider = TronHTTPProvider(endpoint_uri=api_url, api_key=api_key)
                self.client = Tron(provider=provider, network="shasta")
            else:
                api_url = settings.TRON_MAINNET_API_URL
                provider = TronHTTPProvider(endpoint_uri=api_url, api_key=api_key)
                self.client = Tron(provider=provider)

            self.api_url = api_url
            logger.info(f"TRON коннектор инициализирован: testnet={settings.TESTNET_MODE}, API-ключ={'используется' if api_key else 'не настроен'}")  
                
        except Exception as e:
            logger.error(f"Ошибка при инициализации TRON коннектора: {str(e)}")
            raise
            
    async def get_balance(self, address: str) -> Decimal:
        """Get TRX balance for the given address."""
        try:
            # Use async HTTP request instead of tronpy direct call for better concurrency
            async with aiohttp.ClientSession() as session:
                headers = {"TRON-PRO-API-KEY": settings.TRON_API_KEY} if settings.TRON_API_KEY else {}
                url = f"{self.api_url}/v1/accounts/{address}"
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Get TRX balance
                        balance_sun = int(data.get('data', [{}])[0].get('balance', 0))
                        
                        # Convert from SUN to TRX (1 TRX = 1,000,000 SUN)
                        return Decimal(balance_sun) / Decimal(1000000)
                    else:
                        return Decimal(0)
        except Exception:
            # Fallback to tronpy
            account = self.client.get_account(address)
            balance_sun = account.balance if account else 0
            return Decimal(balance_sun) / Decimal(1000000)
    
    async def estimate_fee(self, 
                          source_address: str, 
                          destination_address: str, 
                          amount: Decimal, 
                          priority: str = "medium") -> Decimal:
        """Estimate Tron transaction fee."""
        # TRON has fixed fees for different operations
        # Standard TRX transfer costs 0.1 TRX
        return Decimal('0.1')
    
    async def create_transaction(self,
                                source_address: str, 
                                destination_address: str, 
                                amount: Decimal, 
                                priority: str = "medium") -> Tuple[str, Dict[str, Any]]:
        """Create and sign a Tron transaction."""
        # Get private key from secure storage
        private_key_hex = await self.get_private_key(source_address)
        private_key = PrivateKey(bytes.fromhex(private_key_hex))
        
        # Convert amount to SUN (1 TRX = 1,000,000 SUN)
        amount_sun = int(amount * Decimal(1000000))
        
        # Create transaction
        txn = self.client.trx.transfer(source_address, destination_address, amount_sun)
        
        # Build and sign transaction
        signed_txn = txn.build().sign(private_key)
        
        # Get transaction hash
        tx_hash = signed_txn.txid
        
        # Return transaction hash and raw transaction
        return tx_hash, {"signed_txn": signed_txn}
    
    async def broadcast_transaction(self, raw_transaction: Dict[str, Any]) -> str:
        """Broadcast signed Tron transaction."""
        signed_txn = raw_transaction["signed_txn"]
        result = self.client.broadcast(signed_txn)
        
        if not result.get('result'):
            raise Exception(f"Broadcast failed: {result.get('message')}")
        
        return signed_txn.txid
    
    async def get_transaction_status(self, tx_hash: str) -> Dict[str, Any]:
        """Get status of a Tron transaction."""
        try:
            # Use async HTTP request
            async with aiohttp.ClientSession() as session:
                headers = {"TRON-PRO-API-KEY": settings.TRON_API_KEY} if settings.TRON_API_KEY else {}
                url = f"{self.api_url}/v1/transactions/{tx_hash}"
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        tx_info = data.get('data', [{}])[0]
                        
                        # Check if transaction exists and has block number
                        if not tx_info:
                            return {"status": "not_found"}
                        
                        block_number = tx_info.get('blockNumber')
                        if not block_number:
                            return {"status": "pending", "confirmations": 0}
                        
                        # Get current block number
                        current_block_url = f"{self.api_url}/v1/blocks/latest"
                        async with session.get(current_block_url, headers=headers) as block_response:
                            if block_response.status == 200:
                                block_data = await block_response.json()
                                current_block = block_data.get('blockNumber', block_number)
                                confirmations = current_block - block_number + 1
                                
                                status = "pending"
                                if confirmations >= settings.TRON_CONFIRMATION_THRESHOLD:
                                    status = "confirmed"
                                elif confirmations >= 1:
                                    status = "processing"
                                    
                                return {
                                    "status": status,
                                    "confirmations": confirmations,
                                    "block_number": block_number,
                                    "timestamp": tx_info.get('timestamp'),
                                    "fee": Decimal(tx_info.get('fee', 0)) / Decimal(1000000)
                                }
                    
                    # Transaction not found or API error
                    return {"status": "not_found"}
        except Exception:
            # Fallback to a simpler approach
            try:
                tx_info = self.client.get_transaction(tx_hash)
                if tx_info and hasattr(tx_info, 'block_number') and tx_info.block_number:
                    return {
                        "status": "confirmed",
                        "block_number": tx_info.block_number
                    }
                return {"status": "pending"}
            except:
                return {"status": "not_found"}
        
    async def validate_address(self, address: str) -> bool:
        """Validate Tron address format."""
        try:
            # Check if address starts with T and is 34 characters long
            if not address.startswith('T') or len(address) != 34:
                return False
                
            # Try to decode the base58 address
            try:
                decoded = base58.b58decode_check(address)
                return len(decoded) == 21 and decoded[0] == 0x41  # 0x41 is the TRON address prefix
            except:
                return False
        except:
            return False
        
    async def get_private_key(self, address: str) -> str:
        """
        Get private key for the address by requesting it from Wallet Service.
        
        Args:
            address: The wallet address
                
        Returns:
            The private key as a string
        
        Raises:
            ValueError: If unable to retrieve the private key
        """
        from app.services.kafka_service import request_private_key
        
        private_key = await request_private_key(address, "TRON")
        if not private_key:
            raise ValueError(f"Failed to get private key for TRON address {address} from Wallet Service")
        
        return private_key
    
    async def get_token_balance(self, address: str, token_contract: str) -> Decimal:
        """Get token balance for TRC20 tokens."""
        try:
            # Use async HTTP request
            async with aiohttp.ClientSession() as session:
                headers = {"TRON-PRO-API-KEY": settings.TRON_API_KEY} if settings.TRON_API_KEY else {}
                url = f"{self.api_url}/v1/accounts/{address}/assets"
                async with session.get(url, headers=headers) as response:
                    if response.status == 200:
                        data = await response.json()
                        assets = data.get('data', [])
                        
                        # Find the asset with matching contract address
                        for asset in assets:
                            if asset.get('key') == token_contract:
                                balance = int(asset.get('value', 0))
                                # Most TRC20 have 6 decimals
                                decimals = 6  
                                return Decimal(balance) / Decimal(10 ** decimals)
                        
                        # Asset not found
                        return Decimal(0)
                    else:
                        return Decimal(0)
        except Exception as e:
            logger.error(f"Error getting token balance: {str(e)}")
            return Decimal(0)

    async def get_token_contract_address(self, currency: str) -> Optional[str]:
        """Get token contract address for a currency."""
        # Маппинг токенов на контракты
        token_contracts = {
            "USDT-TRC20": "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t",
            "USDC-TRC20": "TEkxiTehnzSmSe2XqrBj4w32RUN966rdz8"
        }
        
        if currency in token_contracts:
            return token_contracts[currency]
        else:
            logger.warning(f"Unknown token: {currency}")
            return None

    async def send_with_fee_wallet(self, 
            source_address: str, 
            destination_address: str, 
            amount: Decimal,
            fee_wallet: str,
            fee_wallet_private_key: str,
            priority: str = "medium",
            currency: str = "USDT-TRC20") -> str:
        """
        Create and broadcast a transaction using a separate fee wallet.
        For TRON, first check TRX balance and send more if needed, then execute the main transaction.
        """
        # Обновляем соединение перед выполнением критической операции
        if not await self.refresh_connection():
            raise ConnectionError("[TRON] Не удалось установить соединение с Tron API")
            
        try:
            # Проверяем баланс TRX на кошельке отправителя
            source_trx_balance = await self.get_balance(source_address)
            logger.info(f"[TRON] Баланс TRX на исходном кошельке {source_address}: {source_trx_balance} TRX")
            
            # Минимальное количество TRX, необходимое для транзакции USDT-TRC20
            required_trx = Decimal('15.0')  # 15 TRX должно хватить для покрытия энергии
            
            # Если баланс недостаточен, отправляем нужное количество TRX с fee-кошелька
            if source_trx_balance < required_trx:
                # Рассчитываем, сколько TRX нужно отправить
                transfer_amount = required_trx - source_trx_balance
                
                logger.info(f"[TRON] Недостаточный баланс TRX ({source_trx_balance}). Отправка {transfer_amount} TRX с fee_wallet {fee_wallet} на исходный кошелек {source_address}")
                
                # Создаем транзакцию отправки TRX с fee-кошелька
                try:
                    # Преобразуем приватный ключ
                    private_key_obj = PrivateKey(bytes.fromhex(fee_wallet_private_key))
                    amount_sun = int(transfer_amount * Decimal(1000000))
                    
                    # Создаем, подписываем и отправляем транзакцию напрямую
                    txn = self.client.trx.transfer(fee_wallet, source_address, amount_sun)
                    txn_obj = txn.build()
                    signed_txn = txn_obj.sign(private_key_obj)
                    broadcast_result = signed_txn.broadcast()
                    
                    logger.info(f"[TRON] Результат отправки TRX: {broadcast_result}")
                    
                    # Правильно получаем хеш транзакции из результата broadcast
                    fee_transfer_hash = broadcast_result.get('txid')
                    logger.info(f"[TRON] Хеш транзакции отправки TRX: {fee_transfer_hash}")
                    
                    # Ждем фиксированное время для подтверждения транзакции
                    await asyncio.sleep(10)  # Ждем 10 секунд
                    
                except Exception as e:
                    logger.error(f"[TRON] Ошибка при отправке TRX с fee-кошелька: {str(e)}")
                    # Если не удалось отправить TRX, возможно, стоит прервать операцию
                    raise ValueError(f"Не удалось отправить TRX с fee-кошелька: {str(e)}")
            else:
                logger.info(f"[TRON] Баланс TRX достаточен ({source_trx_balance} TRX), пропускаем отправку с fee-кошелька")
            
            # Обновляем соединение перед выполнением основной транзакции
            # Это может быть полезно в случае, если предыдущие операции заняли много времени
            if not await self.refresh_connection():
                raise ConnectionError("[TRON] Не удалось обновить соединение перед выполнением основной транзакции")
            
            # Получаем приватный ключ исходного кошелька
            source_private_key = await self.get_private_key(source_address)
            
            # Попытка выполнить основную транзакцию
            try:
                # Если это TRX, используем встроенный метод отправки
                if currency == "TRX":
                    # Код для отправки TRX
                    private_key_obj = PrivateKey(bytes.fromhex(source_private_key))
                    amount_sun = int(amount * Decimal(1000000))
                    
                    logger.info(f"[TRON] Отправка {amount} TRX с адреса {source_address} на адрес {destination_address}")
                    
                    txn = self.client.trx.transfer(source_address, destination_address, amount_sun)
                    built_txn = txn.build()
                    signed_txn = built_txn.sign(private_key_obj)
                    broadcast_result = signed_txn.broadcast()
                    
                    if broadcast_result and isinstance(broadcast_result, dict) and broadcast_result.get('result'):
                        tx_hash = broadcast_result.get('txid')
                        logger.info(f"[TRON] TRX транзакция успешно отправлена, хеш: {tx_hash}")
                        return tx_hash
                    
                    raise ValueError(f"Failed to broadcast TRX transaction: {broadcast_result}")
                # Если это TRC20 токен (по умолчанию), используем отправку токенов
                else:
                    token_contract = await self.get_token_contract_address(currency)
                    if not token_contract:
                        raise ValueError(f"Неизвестный токен: {currency}")
                    
                    logger.info(f"[TRON] Отправка {amount} {currency} с адреса {source_address} на адрес {destination_address}")
                    
                    # Получаем контракт токена
                    contract = self.client.get_contract(token_contract)
                    logger.info(f"[TRON] Получен контракт токена {token_contract}")
                    
                    # Большинство TRC20 токенов, включая USDT, имеют 6 десятичных знаков
                    decimals = 6
                    token_amount = int(amount * Decimal(10 ** decimals))
                    
                    # Создаем транзакцию вызова смарт-контракта с достаточным fee_limit
                    private_key_obj = PrivateKey(bytes.fromhex(source_private_key))
                    func = contract.functions.transfer
                    txn = func(destination_address, token_amount).with_owner(source_address).fee_limit(50_000_000)  # 50 TRX
                    
                    # Build, sign and broadcast
                    built_txn = txn.build()
                    signed_txn = built_txn.sign(private_key_obj)
                    broadcast_result = signed_txn.broadcast()
                    
                    if broadcast_result and isinstance(broadcast_result, dict) and broadcast_result.get('result'):
                        tx_hash = broadcast_result.get('txid')
                        logger.info(f"[TRON] {currency} транзакция успешно отправлена, хеш: {tx_hash}")
                        return tx_hash
                    
                    raise ValueError(f"Failed to broadcast {currency} transaction: {broadcast_result}")
            except Exception as e:
                logger.error(f"[TRON] Ошибка при отправке основной транзакции: {str(e)}")
                raise ValueError(f"Не удалось выполнить основную транзакцию: {str(e)}")
                    
        except Exception as e:
            logger.error(f"[TRON] Критическая ошибка в send_with_fee_wallet: {str(e)}")
            raise
            
    async def _send_trx_from_fee_wallet(self, 
                                       fee_wallet: str, 
                                       fee_wallet_private_key: str, 
                                       destination: str, 
                                       amount: Decimal) -> str:
        """
        Отправляет TRX с fee-кошелька на указанный адрес.
        
        Args:
            fee_wallet: Адрес fee-кошелька
            fee_wallet_private_key: Приватный ключ fee-кошелька
            destination: Адрес получателя
            amount: Количество TRX для отправки
            
        Returns:
            Хеш транзакции
        """
        # Создаем объект приватного ключа
        private_key_obj = PrivateKey(bytes.fromhex(fee_wallet_private_key))
        
        # Конвертируем сумму в SUN
        amount_sun = int(amount * Decimal(1000000))
        
        # Создаем, подписываем и отправляем транзакцию
        txn = self.client.trx.transfer(fee_wallet, destination, amount_sun)
        result = txn.build().sign(private_key_obj).broadcast()
        
        # Проверяем результат
        if not result.get('result'):
            raise ValueError(f"Не удалось отправить TRX с fee-кошелька: {result.get('message')}")
            
        return txn.txid
        
    async def _wait_for_confirmation(self, tx_hash: str, max_wait: int = 15) -> None:
        """
        Ожидает подтверждения транзакции или истечения времени ожидания.
        
        Args:
            tx_hash: Хеш транзакции
            max_wait: Максимальное время ожидания в секундах
        """
        logger.info(f"Ожидание подтверждения транзакции {tx_hash}...")
        
        start_time = time.time()
        while time.time() - start_time < max_wait:
            status = await self.get_transaction_status(tx_hash)
            
            if status.get('status') == 'confirmed' or status.get('status') == 'processing':
                logger.info(f"Транзакция {tx_hash} подтверждена с {status.get('confirmations', 0)} подтверждениями")
                return
                
            if status.get('status') == 'not_found':
                logger.warning(f"Транзакция {tx_hash} не найдена в блокчейне")
            
            await asyncio.sleep(3)  # Ждем 3 секунды между проверками
            
        logger.warning(f"Превышено время ожидания подтверждения для транзакции {tx_hash}")

    async def create_and_broadcast_trc20_transaction(
        self,
        source_address: str,
        destination_address: str,
        token_contract: str,
        amount: Decimal,
        private_key: str
    ) -> str:
        """
        Create and broadcast a TRC20 token transaction.
        
        Args:
            source_address: Source wallet address
            destination_address: Destination wallet address
            token_contract: TRC20 token contract address
            amount: Amount of tokens to transfer
            private_key: Private key of the source wallet
            
        Returns:
            Transaction hash
        """
        # Обновляем соединение перед выполнением критической операции
        if not await self.refresh_connection():
            raise ConnectionError("[TRON] Не удалось установить соединение с Tron API")
        
        try:
            # Create private key instance
            private_key_obj = PrivateKey(bytes.fromhex(private_key))
            
            # Получаем контракт токена
            contract = self.client.get_contract(token_contract)
            logger.info(f"[TRON] Получен контракт токена {token_contract}")
            
            # Convert amount to token units (most TRC20 tokens have 6 decimals)
            decimals = 6
            token_amount = int(amount * Decimal(10 ** decimals))
            
            # Создаем транзакцию вызова смарт-контракта
            func = contract.functions.transfer
            txn = func(destination_address, token_amount).with_owner(source_address)
            logger.info(f"[TRON] Создана TRC20 транзакция: отправитель={source_address}, получатель={destination_address}, сумма={amount}, токен={token_contract}")
            
            # Build, sign and broadcast the transaction
            built_txn = txn.build()
            signed_txn = built_txn.sign(private_key_obj)
            broadcast_result = signed_txn.broadcast()
            
            # Check if successful
            if broadcast_result and isinstance(broadcast_result, dict) and broadcast_result.get('result'):
                tx_hash = broadcast_result.get('txid')
                logger.info(f"[TRON] Транзакция токена успешно отправлена, хеш: {tx_hash}")
                return tx_hash
            
            # Failed transaction
            raise ValueError(f"Failed to broadcast TRC20 transaction: {broadcast_result}")
            
        except Exception as e:
            logger.error(f"[TRON] Ошибка при создании и отправке TRC20 транзакции: {str(e)}")
            import traceback
            logger.error(f"[TRON] Стек вызова: {traceback.format_exc()}")
            raise
    
    async def refresh_connection(self):
        """Обновляет соединение с Tron API перед критическими операциями."""
        try:
            logger.info("[TRON] Обновление соединения с Tron API")
            
            api_key = settings.TRON_API_KEY
            
            # Создаем URL из настроек API
            if settings.TESTNET_MODE:
                # Для тестовой сети (Shasta)
                api_url = settings.TRON_TESTNET_API_URL
                provider = TronHTTPProvider(endpoint_uri=api_url, api_key=api_key)
                self.client = Tron(provider=provider, network="shasta")
            else:
                # Для основной сети Tron
                api_url = settings.TRON_MAINNET_API_URL
                provider = TronHTTPProvider(endpoint_uri=api_url, api_key=api_key)
                self.client = Tron(provider=provider)
                
            self.api_url = api_url
            logger.info(f"[TRON] Соединение успешно обновлено.")
            return True
        except Exception as e:
            logger.error(f"[TRON] Ошибка при обновлении соединения: {str(e)}")
            # Попытка восстановления с использованием резервного API (если нужно)
            try:
                # Резервный узел Tron API (публичный или альтернативный)
                backup_url = "https://api.trongrid.io"  # Пример резервного URL
                logger.info(f"[TRON] Попытка подключения к резервному API: {backup_url}")
                
                backup_provider = TronHTTPProvider(endpoint_uri=backup_url)
                
                if settings.TESTNET_MODE:
                    self.client = Tron(provider=backup_provider, network="shasta")
                else:
                    self.client = Tron(provider=backup_provider)
                    
                self.api_url = backup_url
                logger.info(f"[TRON] Резервное соединение установлено.")
                return True
            except Exception as backup_error:
                logger.error(f"[TRON] Не удалось установить резервное соединение: {str(backup_error)}")
                return False

# Register connector
register_connector("TRON", TronConnector)