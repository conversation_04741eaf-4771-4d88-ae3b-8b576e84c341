# Import base blockchain connector functionality
from app.services.blockchain.base import BlockchainConnector, register_connector, get_blockchain_connector

# Import all connector implementations to ensure they are registered
# This is crucial - without these imports, the register_connector() calls won't execute
from app.services.blockchain.bitcoin import BitcoinConnector

# Import ethereum connector with try-except to handle different versions of web3.py
try:
    from app.services.blockchain.ethereum import EthereumConnector
except ImportError as e:
    # Log error but don't crash - allows the server to start even if Ethereum is unavailable
    from app.core.logging import get_logger
    logger = get_logger(__name__)
    logger.error(f"Error importing EthereumConnector: {str(e)}")
    logger.warning("Ethereum blockchain connector will not be available")

from app.services.blockchain.tron import TronConnector

# Re-export the get_blockchain_connector function for easier access
__all__ = ['get_blockchain_connector', 'BlockchainConnector']