from decimal import Decima<PERSON>
from typing import Op<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.core.logging import get_logger
from app.services.blockchain.base import get_blockchain_connector

logger = get_logger(__name__)

class FeeWalletService:
    """Service for managing fee wallets"""
    
    async def get_fee_wallet_data(self, network: str) -> Tuple[Optional[str], Optional[str]]:
        """
        Get fee wallet address and private key for a network
        
        Args:
            network: Network code (BTC, ETH, TRON)
            
        Returns:
            Tuple of (address, private_key) or (None, None) if not configured
        """
        if network == "BTC":
            wallet_data = settings.BTC_FEE_WALLET
        elif network == "ETH":
            wallet_data = settings.ETH_FEE_WALLET
        elif network == "TRON":
            wallet_data = settings.TRON_FEE_WALLET
        else:
            logger.error(f"Unknown network for fee wallet: {network}")
            return None, None
            
        if not wallet_data:
            logger.error(f"No fee wallet configured for network {network}")
            return None, None
            
        try:
            # Split address and private key
            wallet_address, private_key = wallet_data.split(',', 1)
            return wallet_address.strip(), private_key.strip()
        except ValueError:
            logger.error(f"Invalid fee wallet data format for {network}")
            return None, None
    
    async def check_fee_wallet_balance(self, network: str) -> Tuple[bool, Decimal]:
        """
        Check fee wallet balance
        
        Args:
            network: Network code (BTC, ETH, TRON)
            
        Returns:
            Tuple of (is_sufficient, current_balance)
        """
        # Get wallet data
        wallet_address, _ = await self.get_fee_wallet_data(network)
        if not wallet_address:
            return False, Decimal(0)
            
        # Get blockchain connector
        connector = get_blockchain_connector(network)
        if not connector:
            logger.error(f"No connector found for network {network}")
            return False, Decimal(0)
            
        # Get balance
        try:
            balance = await connector.get_balance(wallet_address)
            
            # Determine minimum balance
            if network == "BTC":
                min_balance = settings.BTC_FEE_WALLET_MIN_BALANCE
            elif network == "ETH":
                min_balance = settings.ETH_FEE_WALLET_MIN_BALANCE
            elif network == "TRON":
                min_balance = settings.TRON_FEE_WALLET_MIN_BALANCE
            else:
                min_balance = Decimal(0)
                
            # Check if balance is sufficient
            is_sufficient = balance >= min_balance
            
            # Log if balance is low
            if not is_sufficient and settings.NOTIFY_LOW_FEE_BALANCE:
                logger.warning(
                    f"Low fee wallet balance for {network}: {balance} (minimum: {min_balance})"
                )
                # Here you can add notification logic
                
            return is_sufficient, balance
            
        except Exception as e:
            logger.error(f"Error checking fee wallet balance for {network}: {str(e)}")
            return False, Decimal(0)
    
    async def get_network_for_currency(self, currency: str) -> str:
        """
        Determine the network for a currency
        
        Args:
            currency: Currency code (BTC, ETH, USDT-TRC20, etc.)
            
        Returns:
            Network code (BTC, ETH, TRON)
        """
        if currency == "BTC":
            return "BTC"
        elif currency == "ETH":
            return "ETH"
        elif currency in ["TRX", "TRON"]:
            return "TRON"
        elif "-ERC20" in currency:
            return "ETH"
        elif "-TRC20" in currency:
            return "TRON"
        else:
            logger.warning(f"Unknown currency: {currency}, defaulting to ETH")
            return "ETH"