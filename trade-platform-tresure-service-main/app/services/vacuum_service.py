import asyncio
import random
from decimal import Decimal
from typing import List, Tuple, Optional, Dict, Any
import time
from datetime import datetime

from app.core.config import settings
from app.core.logging import get_logger
from app.services.blockchain.base import get_blockchain_connector
from app.services.fee_wallet_service import FeeWalletService
from app.services.wallet_service import WalletService
from app.db.session import AsyncSessionLocal
from sqlalchemy import select
from app.db.models.wallet import DestinationWallet

logger = get_logger(__name__)

class VacuumService:
    """
    Сервис для "пылесоса" промежуточных кошельков.
    
    Проверяет балансы промежуточных кошельков и переводит
    найденные средства на корпоративные кошельки.
    """
    
    def __init__(self):
        """Инициализация сервиса"""
        self.min_balance_to_transfer = {
            "BTC": Decimal("0.0001"),         # Минимальный баланс BTC для перевода 
            "USDC-ERC20": Decimal("1.0"),     # Минимальный баланс USDC-ERC20 для перевода
            "USDT-TRC20": Decimal("1.0")      # Минимальный баланс USDT-TRC20 для перевода
        }
        
        # Резерв для оплаты комиссий, который нужно оставить на кошельке
        self.fee_reserve = {
            "BTC": Decimal("0.0005"),
            "ETH": Decimal("0.01"),
            "TRX": Decimal("10.0")
        }
        
        # Кэш для корпоративных кошельков
        self.corporate_wallets = {}
        
        # Кэш для коннекторов блокчейнов
        self._btc_connector = None
        self._eth_connector = None
        self._tron_connector = None
        
        logger.info("Сервис пылесоса инициализирован")

    async def start(self, interval_seconds: int = 3600) -> None:
        """
        Запускает процесс пылесоса с заданным интервалом.
        
        Args:
            interval_seconds: Интервал между запусками в секундах (по умолчанию 1 час)
        """
        logger.info(f"Запуск сервиса пылесоса. Интервал: {interval_seconds} секунд")
        
        while True:
            try:
                logger.info("Начинаем проверку промежуточных кошельков")
                
                # Обновляем информацию о корпоративных кошельках
                await self._load_corporate_wallets()
                
                # Проверяем и переводим средства с BTC кошельков
                await self._vacuum_btc_wallets()
                
                # Проверяем и переводим средства с ETH кошельков (ETH и USDC-ERC20)
                await self._vacuum_eth_wallets()
                
                # Проверяем и переводим средства с TRON кошельков (TRX и USDT-TRC20)
                await self._vacuum_tron_wallets()
                
                logger.info(f"Проверка промежуточных кошельков завершена. Следующая проверка через {interval_seconds} секунд")
            except Exception as e:
                logger.error(f"Ошибка в процессе работы пылесоса: {str(e)}")
            
            # Ждем до следующего запуска
            await asyncio.sleep(interval_seconds)
    
    async def _load_corporate_wallets(self) -> None:
        """Загружает информацию о корпоративных кошельках из БД"""
        async with AsyncSessionLocal() as db:
            wallets_query = select(DestinationWallet).where(DestinationWallet.is_active == True)
            result = await db.execute(wallets_query)
            wallets = result.scalars().all()
            
            # Обновляем кэш корпоративных кошельков
            for wallet in wallets:
                wallet_key = f"{wallet.currency}"
                self.corporate_wallets[wallet_key] = wallet.address
                logger.debug(f"Загружен корпоративный кошелек для {wallet.currency}: {wallet.address}")
    
    async def _get_btc_connector(self):
        """Получает коннектор для BTC"""
        if not self._btc_connector:
            self._btc_connector = get_blockchain_connector("BTC")
            if not self._btc_connector:
                raise ValueError("Не удалось получить коннектор для BTC")
        return self._btc_connector
    
    async def _get_eth_connector(self):
        """Получает коннектор для ETH"""
        if not self._eth_connector:
            self._eth_connector = get_blockchain_connector("ETH")
            if not self._eth_connector:
                raise ValueError("Не удалось получить коннектор для ETH")
        return self._eth_connector
    
    async def _get_tron_connector(self):
        """Получает коннектор для TRON"""
        if not self._tron_connector:
            self._tron_connector = get_blockchain_connector("TRON")
            if not self._tron_connector:
                raise ValueError("Не удалось получить коннектор для TRON")
        return self._tron_connector
    
    async def _get_fee_wallet_data(self, network: str) -> Tuple[str, str]:
        """
        Получает данные fee-кошелька для указанной сети.
        
        Args:
            network: Сеть (BTC, ETH, TRON)
            
        Returns:
            Tuple[str, str]: Адрес и приватный ключ fee-кошелька
        
        Raises:
            ValueError: Если не удалось получить данные fee-кошелька
        """
        fee_wallet_service = FeeWalletService()
        fee_wallet_address, fee_wallet_key = await fee_wallet_service.get_fee_wallet_data(network)
        
        if not fee_wallet_address or not fee_wallet_key:
            raise ValueError(f"Не удалось получить данные fee-кошелька для сети {network}")
        
        return fee_wallet_address, fee_wallet_key
    
    async def _vacuum_btc_wallets(self) -> None:
        """Проверяет и переводит средства с промежуточных BTC кошельков"""
        try:
            # Получаем список промежуточных BTC кошельков
            btc_wallets = settings.BTC_INTERMEDIATE_WALLETS_LIST
            btc_keys = settings.BTC_INTERMEDIATE_KEYS_LIST
            
            if len(btc_wallets) != len(btc_keys):
                logger.error(f"Несоответствие в конфигурации промежуточных BTC кошельков. "
                           f"Адресов: {len(btc_wallets)}, Ключей: {len(btc_keys)}")
                return
            
            # Получаем корпоративный кошелек для BTC
            btc_corporate_wallet = self.corporate_wallets.get("BTC")
            if not btc_corporate_wallet:
                logger.error("Не найден корпоративный кошелек для BTC")
                return
            
            # Получаем данные fee-кошелька
            fee_wallet_address, fee_wallet_key = await self._get_fee_wallet_data("BTC")
            
            # Получаем коннектор
            connector = await self._get_btc_connector()
            
            # Проверяем каждый промежуточный кошелек
            for i, (wallet_address, wallet_key) in enumerate(zip(btc_wallets, btc_keys)):
                try:
                    # Проверяем баланс
                    balance = await connector.get_balance(wallet_address)
                    logger.info(f"Промежуточный BTC кошелек {wallet_address}: баланс = {balance} BTC")
                    
                    # Если баланс выше минимального, переводим средства
                    min_balance = self.min_balance_to_transfer["BTC"]
                    if balance > min_balance:
                        # Оставляем небольшой резерв для комиссий
                        transfer_amount = balance - self.fee_reserve["BTC"]
                        
                        if transfer_amount > Decimal("0"):
                            logger.info(f"Перевод {transfer_amount} BTC с {wallet_address} на {btc_corporate_wallet}")
                            
                            # Создаем и отправляем транзакцию
                            try:
                                tx_hash = await connector.create_and_broadcast_transaction(
                                    wallet_address,
                                    btc_corporate_wallet,
                                    transfer_amount,
                                    wallet_key,
                                    "medium"
                                )
                                
                                logger.info(f"Успешный перевод BTC: {tx_hash}")
                                
                                # Делаем небольшую паузу между транзакциями
                                await asyncio.sleep(5)
                            except Exception as tx_error:
                                logger.error(f"Ошибка при переводе BTC с {wallet_address}: {str(tx_error)}")
                except Exception as wallet_error:
                    logger.error(f"Ошибка при обработке BTC кошелька {wallet_address}: {str(wallet_error)}")
        except Exception as e:
            logger.error(f"Ошибка в _vacuum_btc_wallets: {str(e)}")
    
    async def _vacuum_eth_wallets(self) -> None:
        """Проверяет и переводит средства с промежуточных ETH кошельков (ETH и USDC-ERC20)"""
        try:
            # Получаем список промежуточных ETH кошельков
            eth_wallets = settings.ETH_INTERMEDIATE_WALLETS_LIST
            eth_keys = settings.ETH_INTERMEDIATE_KEYS_LIST
            
            if len(eth_wallets) != len(eth_keys):
                logger.error(f"Несоответствие в конфигурации промежуточных ETH кошельков. "
                           f"Адресов: {len(eth_wallets)}, Ключей: {len(eth_keys)}")
                return
            
            # Получаем корпоративный кошелек для USDC-ERC20
            usdc_corporate_wallet = self.corporate_wallets.get("USDC-ERC20")
            
            if not usdc_corporate_wallet:
                logger.error("Не найден корпоративный кошелек для USDC-ERC20")
                return
            
            # Получаем данные fee-кошелька
            fee_wallet_address, fee_wallet_key = await self._get_fee_wallet_data("ETH")
            
            # Получаем коннектор
            connector = await self._get_eth_connector()
            
            # Получаем контракт токена USDC-ERC20
            usdc_contract = await connector.get_token_contract_address("USDC-ERC20")
            
            # Проверяем каждый промежуточный кошелек
            for i, (wallet_address, wallet_key) in enumerate(zip(eth_wallets, eth_keys)):
                try:
                    # Проверяем баланс ETH только для оценки достаточности средств для комиссий
                    eth_balance = await connector.get_balance(wallet_address)
                    logger.info(f"Промежуточный ETH кошелек {wallet_address}: баланс = {eth_balance} ETH")
                    
                    # Проверяем балансы токенов ERC20
                    if usdc_corporate_wallet and usdc_contract:
                        try:
                            usdc_balance = await connector.get_token_balance(wallet_address, usdc_contract)
                            logger.info(f"Промежуточный кошелек {wallet_address}: баланс USDC-ERC20 = {usdc_balance}")
                            
                            # Если баланс USDC выше минимального, переводим средства
                            usdc_min_balance = self.min_balance_to_transfer["USDC-ERC20"]
                            if usdc_balance > usdc_min_balance:
                                logger.info(f"Перевод {usdc_balance} USDC-ERC20 с {wallet_address} на {usdc_corporate_wallet}")
                                
                                # Отправляем токены
                                try:
                                    # Убедимся, что на кошельке достаточно ETH для комиссии
                                    if eth_balance < self.fee_reserve["ETH"]:
                                        logger.warning(f"Недостаточно ETH для комиссии на {wallet_address}. Пополняем с fee-кошелька.")
                                        
                                        # Отправляем ETH с fee-кошелька для комиссии
                                        eth_needed = self.fee_reserve["ETH"] - eth_balance
                                        await connector.create_and_broadcast_transaction(
                                            fee_wallet_address,
                                            wallet_address,
                                            eth_needed,
                                            fee_wallet_key,
                                            "medium"
                                        )
                                        
                                        # Ждем подтверждения
                                        await asyncio.sleep(15)
                                    
                                    # Отправляем токены через контракт
                                    tx_hash = await connector._send_token(
                                        wallet_address,
                                        usdc_corporate_wallet,
                                        usdc_balance,
                                        wallet_key,
                                        usdc_contract,
                                        "USDC-ERC20"
                                    )
                                    
                                    logger.info(f"Успешный перевод USDC-ERC20: {tx_hash}")
                                    
                                    # Делаем небольшую паузу между транзакциями
                                    await asyncio.sleep(5)
                                except Exception as token_tx_error:
                                    logger.error(f"Ошибка при переводе USDC-ERC20 с {wallet_address}: {str(token_tx_error)}")
                        except Exception as usdc_error:
                            logger.error(f"Ошибка при проверке баланса USDC-ERC20 для {wallet_address}: {str(usdc_error)}")
                except Exception as wallet_error:
                    logger.error(f"Ошибка при обработке ETH кошелька {wallet_address}: {str(wallet_error)}")
        except Exception as e:
            logger.error(f"Ошибка в _vacuum_eth_wallets: {str(e)}")
    
    async def _vacuum_tron_wallets(self) -> None:
        """Проверяет и переводит средства с промежуточных TRON кошельков (только USDT-TRC20)"""
        try:
            # Получаем список промежуточных TRON кошельков
            tron_wallets = settings.TRON_INTERMEDIATE_WALLETS_LIST
            tron_keys = settings.TRON_INTERMEDIATE_KEYS_LIST
            
            if len(tron_wallets) != len(tron_keys):
                logger.error(f"Несоответствие в конфигурации промежуточных TRON кошельков. "
                           f"Адресов: {len(tron_wallets)}, Ключей: {len(tron_keys)}")
                return
            
            # Получаем корпоративный кошелек для USDT-TRC20
            usdt_corporate_wallet = self.corporate_wallets.get("USDT-TRC20")
            
            if not usdt_corporate_wallet:
                logger.error("Не найден корпоративный кошелек для USDT-TRC20")
                return
            
            # Получаем данные fee-кошелька
            fee_wallet_address, fee_wallet_key = await self._get_fee_wallet_data("TRON")
            
            # Получаем коннектор
            connector = await self._get_tron_connector()
            
            # Получаем контракт токена USDT-TRC20
            usdt_contract = await connector.get_token_contract_address("USDT-TRC20")
            
            # Проверяем каждый промежуточный кошелек
            for i, (wallet_address, wallet_key) in enumerate(zip(tron_wallets, tron_keys)):
                try:
                    # Проверяем баланс TRX для оценки достаточности комиссии
                    trx_balance = await connector.get_balance(wallet_address)
                    logger.info(f"Промежуточный TRON кошелек {wallet_address}: баланс = {trx_balance} TRX")
                    
                    # Проверяем баланс USDT-TRC20, только если настроен корпоративный кошелек
                    if usdt_corporate_wallet and usdt_contract:
                        try:
                            usdt_balance = await connector.get_token_balance(wallet_address, usdt_contract)
                            logger.info(f"Промежуточный кошелек {wallet_address}: баланс USDT-TRC20 = {usdt_balance}")
                            
                            # Если баланс USDT выше минимального, переводим средства
                            usdt_min_balance = self.min_balance_to_transfer["USDT-TRC20"]
                            if usdt_balance > usdt_min_balance:
                                logger.info(f"Перевод {usdt_balance} USDT-TRC20 с {wallet_address} на {usdt_corporate_wallet}")
                                
                                # Проверяем, достаточно ли TRX для комиссии
                                if trx_balance < self.fee_reserve["TRX"]:
                                    logger.warning(f"Недостаточно TRX для комиссии на {wallet_address}. Пополняем с fee-кошелька.")
                                    
                                    # Отправляем TRX с fee-кошелька для комиссии
                                    trx_needed = self.fee_reserve["TRX"] - trx_balance
                                    await connector.create_and_broadcast_transaction(
                                        fee_wallet_address,
                                        wallet_address,
                                        trx_needed,
                                        fee_wallet_key,
                                        "medium"
                                    )
                                    
                                    # Ждем подтверждения
                                    await asyncio.sleep(15)
                                
                                # Отправляем токены
                                try:
                                    tx_hash = await connector.create_and_broadcast_trc20_transaction(
                                        wallet_address,
                                        usdt_corporate_wallet,
                                        usdt_contract,
                                        usdt_balance,
                                        wallet_key
                                    )
                                    
                                    logger.info(f"Успешный перевод USDT-TRC20: {tx_hash}")
                                    
                                    # Делаем небольшую паузу между транзакциями
                                    await asyncio.sleep(5)
                                except Exception as token_tx_error:
                                    logger.error(f"Ошибка при переводе USDT-TRC20 с {wallet_address}: {str(token_tx_error)}")
                        except Exception as usdt_error:
                            logger.error(f"Ошибка при проверке баланса USDT-TRC20 для {wallet_address}: {str(usdt_error)}")
                except Exception as wallet_error:
                    logger.error(f"Ошибка при обработке TRON кошелька {wallet_address}: {str(wallet_error)}")
        except Exception as e:
            logger.error(f"Ошибка в _vacuum_tron_wallets: {str(e)}")
    
    async def vacuum_now(self) -> Dict[str, Any]:
        """
        Запускает процесс пылесоса немедленно и однократно.
        
        Returns:
            Dict[str, Any]: Результаты проверки и перевода
        """
        logger.info("Запуск пылесоса (однократно)")
        
        results = {
            "timestamp": datetime.utcnow().isoformat(),
            "status": "success",
            "details": {}
        }
        
        try:
            # Обновляем информацию о корпоративных кошельках
            await self._load_corporate_wallets()
            
            # Проверяем и переводим средства с BTC кошельков
            logger.info("Проверка BTC кошельков...")
            await self._vacuum_btc_wallets()
            results["details"]["btc"] = "completed"
            
            # Проверяем и переводим средства с ETH кошельков
            logger.info("Проверка ETH кошельков...")
            await self._vacuum_eth_wallets()
            results["details"]["eth"] = "completed"
            
            # Проверяем и переводим средства с TRON кошельков
            logger.info("Проверка TRON кошельков...")
            await self._vacuum_tron_wallets()
            results["details"]["tron"] = "completed"
            
            logger.info("Пылесос успешно завершил работу")
        except Exception as e:
            logger.error(f"Ошибка при выполнении пылесоса: {str(e)}")
            results["status"] = "error"
            results["error"] = str(e)
        
        return results