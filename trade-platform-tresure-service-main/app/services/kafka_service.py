import json
import asyncio
import uuid
import time
from typing import Dict, Any, Optional, List, Callable, Awaitable
from asyncio import Future  # Исправлено - импортируем Future из asyncio, а не из typing

from aiokafka import AIOKafkaProducer, AIOKafkaConsumer
from aiokafka.errors import KafkaError

from app.core.config import settings
from app.core.logging import get_logger

logger = get_logger(__name__)


# Global producer instance
_producer = None

# Хранилище pending запросов (request_id -> Future)
_pending_requests: Dict[str, Future] = {}


async def get_producer() -> AIOKafkaProducer:
    """
    Get or create Kafka producer instance.
    
    Returns:
        AIOKafkaProducer instance
    """
    global _producer
    
    if _producer is None:
        _producer = AIOKafkaProducer(
            bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
            value_serializer=lambda v: json.dumps(v).encode('utf-8'),
            retry_backoff_ms=500,
            request_timeout_ms=10000
        )
        await _producer.start()
        
    return _producer


async def publish_event(topic: str, data: Dict[str, Any]) -> bool:
    """
    Publish an event to a Kafka topic.
    
    Args:
        topic: Kafka topic
        data: Event data
        
    Returns:
        True if successful, False otherwise
    """
    try:
        producer = await get_producer()
        await producer.send_and_wait(topic, data)
        logger.debug(f"Published event to topic {topic}: {data}")
        return True
    except KafkaError as e:
        logger.error(f"Error publishing event to topic {topic}: {str(e)}")
        return False
    except Exception as e:
        logger.error(f"Unexpected error publishing event to topic {topic}: {str(e)}")
        return False


async def consume_events(
    topic: str,
    group_id: str,
    handler: Callable[[Dict[str, Any]], Awaitable[None]],
    auto_offset_reset: str = "latest"
) -> None:
    """
    Start consuming events from a Kafka topic.
    
    Args:
        topic: Kafka topic
        group_id: Consumer group ID
        handler: Async function to handle received messages
        auto_offset_reset: Offset reset strategy ("earliest" or "latest")
    """
    consumer = AIOKafkaConsumer(
        topic,
        bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
        group_id=group_id,
        auto_offset_reset=auto_offset_reset,
        value_deserializer=lambda m: json.loads(m.decode('utf-8')),
        enable_auto_commit=False,
        heartbeat_interval_ms=10000,  # Увеличить интервал хартбитов
        session_timeout_ms=30000  
    )
    
    await consumer.start()
    
    try:
        logger.info(f"Started consuming from topic {topic} with group ID {group_id}")
        
        async for message in consumer:
            try:
                data = message.value
                logger.debug(f"Received message from topic {topic}: {data}")
                
                # Process message
                await handler(data)
                
                # Commit offset
                await consumer.commit()
            except Exception as e:
                logger.error(f"Error processing message from topic {topic}: {str(e)}")
    finally:
        await consumer.stop()


async def consume_deposit_events(
    handler: Callable[[Dict[str, Any]], Awaitable[None]]
) -> None:
    """
    Start consuming deposit confirmation events.
    
    Args:
        handler: Async function to handle received deposit events
    """
    await consume_events(
        settings.KAFKA_BALANCE_UPDATES_TOPIC,
        "treasury-service",
        handler
    )


async def consume_blockchain_events(
    handler: Callable[[Dict[str, Any]], Awaitable[None]]
) -> None:
    """
    Start consuming blockchain events.
    
    Args:
        handler: Async function to handle received blockchain events
    """
    await consume_events(
        settings.KAFKA_BLOCKCHAIN_EVENTS_TOPIC,
        "treasury-service",
        handler
    )


async def close_producer() -> None:
    """Close Kafka producer."""
    global _producer
    
    if _producer is not None:
        await _producer.stop()
        _producer = None


# НОВЫЕ ФУНКЦИИ ДЛЯ РАБОТЫ С WALLET SERVICE

async def request_private_key(wallet_address: str, currency: str) -> Optional[str]:
    """
    Request private key from Wallet Service via Kafka.
    
    Args:
        wallet_address: Wallet address
        currency: Currency code (BTC, ETH, TRON)
        
    Returns:
        Private key as string or None if request failed
    """
    request_id = str(uuid.uuid4())
    logger.info(f"Requesting private key for address {wallet_address}, currency {currency}, request_id {request_id}")
    
    # Создаем Future для ожидания ответа
    future = asyncio.get_event_loop().create_future()
    _pending_requests[request_id] = future
    
    # Формируем запрос
    request_data = {
        "request_id": request_id,
        "action": "get_private_key",
        "wallet_address": wallet_address,
        "currency": currency,
        "timestamp": int(time.time())
    }
    
    # Отправляем запрос
    await publish_event(settings.WALLET_SERVICE_REQUEST_TOPIC, request_data)
    
    try:
        # Ждем ответа с таймаутом
        response = await asyncio.wait_for(future, timeout=settings.WALLET_SERVICE_REQUEST_TIMEOUT)
        
        if response and "private_key" in response:
            return response["private_key"]
        else:
            logger.error(f"No private key in response for request {request_id}")
            return None
    except asyncio.TimeoutError:
        logger.error(f"Timeout waiting for private key response for request {request_id}")
        return None
    finally:
        # Убираем запрос из pending
        _pending_requests.pop(request_id, None)


async def handle_wallet_service_response(data: Dict[str, Any]) -> None:
    """Handle response from Wallet Service."""
    request_id = data.get("request_id")
    
    if not request_id or request_id not in _pending_requests:
        logger.warning(f"Received response for unknown request ID: {request_id}")
        return
    
    # Получаем Future для этого запроса
    future = _pending_requests[request_id]
    
    if not future.done():
        future.set_result(data)
    else:
        logger.warning(f"Future for request {request_id} already done")


async def consume_wallet_service_responses() -> None:
    """Start consuming responses from Wallet Service."""
    await consume_events(
        settings.WALLET_SERVICE_RESPONSE_TOPIC,
        "treasury-service",
        handle_wallet_service_response
    )