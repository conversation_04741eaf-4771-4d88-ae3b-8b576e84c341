import logging
from sqlalchemy import select, func
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.config import settings
from app.db.models.wallet import DestinationWallet

# Настройка логирования
logger = logging.getLogger(__name__)

async def initialize_corporate_wallets(db: AsyncSession):
    """Инициализация корпоративных кошельков из .env при запуске."""
    
    # Проверяем, есть ли уже кошельки в БД
    query = select(func.count()).select_from(DestinationWallet)
    result = await db.execute(query)
    count = result.scalar()
    
    # Если кошельки уже есть, пропускаем
    if count > 0:
        logger.info(f"В базе уже есть {count} корпоративных кошельков, пропускаем инициализацию")
        return
    
    # Список кошельков для инициализации
    wallets_to_add = []
    
    # BTC кошелек
    if settings.BTC_CORPORATE_WALLET:
        try:
            address, wallet_type = settings.BTC_CORPORATE_WALLET.split(',')
            wallets_to_add.append({
                'address': address.strip(),
                'currency': 'BTC',
                'network': 'BTC',
                'type': wallet_type.strip(),
                'is_active': True
            })
        except ValueError:
            logger.error(f"Неверный формат BTC_CORPORATE_WALLET: {settings.BTC_CORPORATE_WALLET}, должен быть 'адрес,тип'")
    
    # ETH кошелек
    if settings.ETH_CORPORATE_WALLET:
        try:
            address, wallet_type = settings.ETH_CORPORATE_WALLET.split(',')
            wallets_to_add.append({
                'address': address.strip(),
                'currency': 'ETH',
                'network': 'ETH',
                'type': wallet_type.strip(),
                'is_active': True
            })
        except ValueError:
            logger.error(f"Неверный формат ETH_CORPORATE_WALLET: {settings.ETH_CORPORATE_WALLET}, должен быть 'адрес,тип'")
    
    # USDC кошелек
    if settings.USDC_ERC20_CORPORATE_WALLET:
        try:
            address, wallet_type = settings.USDC_ERC20_CORPORATE_WALLET.split(',')
            wallets_to_add.append({
                'address': address.strip(),
                'currency': 'USDC-ERC20',
                'network': 'ETH',
                'type': wallet_type.strip(),
                'is_active': True
            })
        except ValueError:
            logger.error(f"Неверный формат USDC_ERC20_CORPORATE_WALLET: {settings.USDC_ERC20_CORPORATE_WALLET}, должен быть 'адрес,тип'")
    
    # USDT TRC20 кошелек
    if settings.USDT_TRC20_CORPORATE_WALLET:
        try:
            address, wallet_type = settings.USDT_TRC20_CORPORATE_WALLET.split(',')
            wallets_to_add.append({
                'address': address.strip(),
                'currency': 'USDT-TRC20',
                'network': 'TRC20',
                'type': wallet_type.strip(),
                'is_active': True
            })
        except ValueError:
            logger.error(f"Неверный формат USDT_TRC20_CORPORATE_WALLET: {settings.USDT_TRC20_CORPORATE_WALLET}, должен быть 'адрес,тип'")
    
    # Добавляем кошельки в БД
    for wallet_data in wallets_to_add:
        wallet = DestinationWallet(**wallet_data)
        db.add(wallet)
    
    await db.commit()
    logger.info(f"Инициализировано {len(wallets_to_add)} корпоративных кошельков")