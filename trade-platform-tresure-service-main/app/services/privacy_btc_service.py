# privacy_btc_service.py
import asyncio
import random
from decimal import Decimal
from typing import List, Tuple, Optional, Dict, Any

from app.core.config import settings
from app.core.logging import get_logger
from app.services.blockchain.base import get_blockchain_connector

logger = get_logger(__name__)

class PrivacyBtcService:
    """Сервис для выполнения приватных транзакций Bitcoin через множество промежуточных кошельков"""
    
    def __init__(self):
        # Получаем коннектор BTC
        self.connector = get_blockchain_connector("BTC")
        
        # Загружаем промежуточные кошельки из настроек
        self.intermediate_wallets = []
        
        # Парсим промежуточные кошельки и ключи из настроек
        btc_addresses = settings.BTC_INTERMEDIATE_WALLETS_LIST
        btc_keys = settings.BTC_INTERMEDIATE_KEYS_LIST
        
        if len(btc_addresses) == len(btc_keys):
            self.intermediate_wallets = list(zip([addr.strip() for addr in btc_addresses], 
                                              [key.strip() for key in btc_keys]))
        else:
            logger.error("Несоответствие в конфигурации промежуточных кошельков BTC. "
                       f"Адреса: {len(btc_addresses)}, Ключи: {len(btc_keys)}")
                       
        # Минимальный требуемый баланс BTC для транзакций
        self.min_btc_balance = Decimal('0.0005')
        
        logger.info(f"[PRIVACY_BTC] Сервис приватных транзакций BTC инициализирован. "
                   f"Доступно промежуточных кошельков: {len(self.intermediate_wallets)}")
    
    async def check_wallet_btc_balance(self, address: str) -> Tuple[bool, Decimal]:
        """
        Проверяет баланс BTC на кошельке
        
        Args:
            address: Адрес кошелька
            
        Returns:
            Tuple (достаточно BTC, текущий баланс)
        """
        try:
            # Обновляем соединение перед запросом баланса
            await self.connector.refresh_connection()
            
            balance = await self.connector.get_balance(address)
            logger.info(f"[PRIVACY_BTC] Баланс кошелька {address}: {balance} BTC")
            return balance >= self.min_btc_balance, balance
        except Exception as e:
            logger.error(f"[PRIVACY_BTC] Ошибка при проверке баланса кошелька {address}: {str(e)}")
            return False, Decimal('0')
    
    async def fund_wallet_if_needed(self, address: str, fee_wallet: str, fee_wallet_key: str) -> bool:
        """
        Проверяет баланс кошелька и при необходимости пополняет его BTC
        
        Args:
            address: Адрес для проверки
            fee_wallet: Адрес fee-кошелька
            fee_wallet_key: Приватный ключ fee-кошелька
            
        Returns:
            bool: Успешность операции
        """
        sufficient, balance = await self.check_wallet_btc_balance(address)
        
        if sufficient:
            logger.info(f"[PRIVACY_BTC] Кошелек {address} имеет достаточный баланс: {balance} BTC")
            return True
            
        # Рассчитываем, сколько BTC нужно отправить
        needed_amount = self.min_btc_balance - balance
        
        logger.info(f"[PRIVACY_BTC] Пополнение кошелька {address} на {needed_amount} BTC с fee-кошелька {fee_wallet}")
        
        try:
            # Отправляем BTC с fee-кошелька на целевой адрес
            # Используем метод с прямым указанием приватного ключа
            tx_hash = await self.connector.create_and_broadcast_transaction(
                fee_wallet,
                address,
                needed_amount,
                fee_wallet_key,
                "medium"
            )
            
            logger.info(f"[PRIVACY_BTC] Кошелек {address} пополнен, хеш: {tx_hash}")
            
            # Ждем, пока транзакция получит хотя бы одно подтверждение
            max_wait_time = 30  # максимальное время ожидания в секундах
            start_time = asyncio.get_event_loop().time()
            
            while asyncio.get_event_loop().time() - start_time < max_wait_time:
                try:
                    status = await self.connector.get_transaction_status(tx_hash)
                    if status.get('confirmations', 0) >= 1:
                        logger.info(f"[PRIVACY_BTC] Транзакция пополнения подтверждена")
                        break
                except Exception as e:
                    logger.warning(f"[PRIVACY_BTC] Ошибка проверки статуса: {str(e)}")
                
                await asyncio.sleep(3)  # проверяем статус каждые 3 секунды
                
            return True
            
        except Exception as e:
            logger.error(f"[PRIVACY_BTC] Ошибка при пополнении кошелька {address}: {str(e)}")
            return False
    
    async def execute_private_transaction(
        self,
        source_address: str,
        destination_address: str,
        amount: Decimal,
        fee_wallet: str,
        fee_wallet_key: str,
        **kwargs
    ) -> Optional[str]:
        """
        Выполняет приватную транзакцию через цепочку промежуточных кошельков
        
        Args:
            source_address: Исходный адрес
            destination_address: Конечный адрес (корпоративный)
            amount: Сумма перевода
            fee_wallet: Адрес комиссионного кошелька
            fee_wallet_key: Приватный ключ комиссионного кошелька
            
        Returns:
            Optional[str]: Хеш финальной транзакции или None в случае ошибки
        """
        if not self.connector:
            logger.error("[PRIVACY_BTC] Не удалось получить BTC коннектор")
            return None
            
        # Определяем количество промежуточных шагов
        num_hops = min(settings.PRIVACY_HOPS, len(self.intermediate_wallets))
        
        if num_hops < 1:
            logger.error("[PRIVACY_BTC] Недостаточно промежуточных кошельков для приватной транзакции")
            return None
            
        # Выбираем случайные промежуточные кошельки
        selected_wallets = random.sample(self.intermediate_wallets, num_hops)
        
        try:
            # Шаг 1: Отправляем с исходного кошелька на первый промежуточный
            first_address, first_key = selected_wallets[0]
            
            # Обновляем соединение перед выполнением транзакции
            await self.connector.refresh_connection()
            
            # Пополняем первый промежуточный кошелек при необходимости
            first_funded = await self.fund_wallet_if_needed(first_address, fee_wallet, fee_wallet_key)
            if not first_funded:
                logger.error(f"[PRIVACY_BTC] Не удалось обеспечить достаточный баланс BTC на первом промежуточном кошельке {first_address}")
                return None
                
            # Немного изменяем сумму для усложнения отслеживания
            first_amount = amount * Decimal(1 - random.uniform(0.001, 0.005))
            
            # Получаем приватный ключ исходного кошелька
            source_key = await self.connector.get_private_key(source_address)
            
            # Выполняем первую транзакцию
            first_tx_hash = await self.connector.create_and_broadcast_transaction(
                source_address,
                first_address,
                first_amount,
                source_key,
                "medium"
            )
            
            if not first_tx_hash:
                logger.error("[PRIVACY_BTC] Первая транзакция не удалась")
                return None
                
            logger.info(f"[PRIVACY_BTC] Шаг 1/{num_hops+1}: Транзакция отправлена на первый промежуточный кошелек. "
                       f"Хеш: {first_tx_hash}")
            
            # Ждем случайное время перед следующей транзакцией
            delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
            logger.info(f"[PRIVACY_BTC] Ожидание {delay} секунд перед следующим шагом")
            await asyncio.sleep(delay)
            
            # Переменные для отслеживания текущего состояния цепочки
            current_amount = first_amount
            current_address = first_address
            current_key = first_key
            last_tx_hash = first_tx_hash
            
            # Проходим через цепочку промежуточных кошельков
            for i in range(1, num_hops):
                # Обновляем соединение перед каждой транзакцией
                await self.connector.refresh_connection()
                
                next_address, next_key = selected_wallets[i]
                
                # Пополняем следующий кошелек при необходимости
                next_funded = await self.fund_wallet_if_needed(next_address, fee_wallet, fee_wallet_key)
                if not next_funded:
                    logger.error(f"[PRIVACY_BTC] Не удалось обеспечить достаточный баланс BTC на промежуточном кошельке {next_address}")
                    continue  # Продолжаем с другим кошельком
                    
                # Немного изменяем сумму снова
                current_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.003))
                
                # Отправляем средства на следующий промежуточный кошелек
                hop_tx_hash = await self.connector.create_and_broadcast_transaction(
                    current_address,
                    next_address,
                    current_amount,
                    current_key,
                    "medium"
                )
                
                if not hop_tx_hash:
                    logger.error(f"[PRIVACY_BTC] Промежуточная транзакция {i+1} не удалась")
                    continue
                    
                logger.info(f"[PRIVACY_BTC] Шаг {i+1}/{num_hops+1}: Транзакция отправлена на следующий промежуточный кошелек. "
                           f"Хеш: {hop_tx_hash}")
                
                # Ждем небольшое время для получения одного подтверждения
                # Это важно для Bitcoin, чтобы избежать проблем с двойной тратой
                await asyncio.sleep(5)
                
                # Проверяем статус транзакции
                try:
                    status = await self.connector.get_transaction_status(hop_tx_hash)
                    logger.info(f"[PRIVACY_BTC] Транзакция {hop_tx_hash} имеет {status.get('confirmations', 0)} подтверждений")
                except Exception as e:
                    logger.warning(f"[PRIVACY_BTC] Ошибка проверки статуса: {str(e)}")
                
                # Обновляем текущие переменные
                current_address = next_address
                current_key = next_key
                last_tx_hash = hop_tx_hash
                
                # Ждем случайное время между транзакциями
                delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
                logger.info(f"[PRIVACY_BTC] Ожидание {delay} секунд перед следующим шагом")
                await asyncio.sleep(delay)
            
            # Обновляем соединение перед финальной транзакцией
            await self.connector.refresh_connection()
            
            # Финальная транзакция: с последнего промежуточного на корпоративный кошелек
            final_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.002))
            
            # Отправляем финальную транзакцию
            final_tx_hash = await self.connector.create_and_broadcast_transaction(
                current_address,
                destination_address,
                final_amount,
                current_key,
                "medium"
            )
            
            if not final_tx_hash:
                logger.error("[PRIVACY_BTC] Финальная транзакция не удалась")
                return None
                
            logger.info(f"[PRIVACY_BTC] Шаг {num_hops+1}/{num_hops+1}: Финальная транзакция выполнена. "
                       f"Хеш: {final_tx_hash}")
            
            return final_tx_hash
            
        except Exception as e:
            logger.error(f"[PRIVACY_BTC] Ошибка выполнения приватной транзакции: {str(e)}")
            return None

    async def wait_for_confirmation(self, tx_hash: str, min_confirmations: int = 1, timeout: int = 30) -> bool:
        """
        Ожидает заданное количество подтверждений для транзакции
        
        Args:
            tx_hash: Хеш транзакции
            min_confirmations: Минимальное количество подтверждений
            timeout: Максимальное время ожидания в секундах
            
        Returns:
            bool: True если получено нужное число подтверждений, False в противном случае
        """
        logger.info(f"[PRIVACY_BTC] Ожидание {min_confirmations} подтверждений для транзакции {tx_hash}")
        
        start_time = asyncio.get_event_loop().time()
        
        while asyncio.get_event_loop().time() - start_time < timeout:
            try:
                status = await self.connector.get_transaction_status(tx_hash)
                confirmations = status.get('confirmations', 0)
                
                logger.info(f"[PRIVACY_BTC] Транзакция {tx_hash} имеет {confirmations} подтверждений")
                
                if confirmations >= min_confirmations:
                    logger.info(f"[PRIVACY_BTC] Получено необходимое количество подтверждений: {confirmations}")
                    return True
            except Exception as e:
                logger.warning(f"[PRIVACY_BTC] Ошибка при проверке подтверждений: {str(e)}")
            
            await asyncio.sleep(3)
        
        logger.warning(f"[PRIVACY_BTC] Не удалось получить {min_confirmations} подтверждений за {timeout} секунд")
        return False