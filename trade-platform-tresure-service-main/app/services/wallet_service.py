from typing import List, Optional
from datetime import datetime
import uuid

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import get_logger
from app.db.models.wallet import DestinationWallet
from app.schemas.wallet import WalletCreate, WalletUpdate
from app.services.blockchain.base import get_blockchain_connector


logger = get_logger(__name__)


class WalletService:
    """
    Service for managing destination wallets.
    """
    
    async def get_wallets(
        self, 
        db: AsyncSession,
        currency: Optional[str] = None,
        network: Optional[str] = None,
        type: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[DestinationWallet]:
        """
        Get a list of destination wallets with optional filtering.
        
        Args:
            db: Database session
            currency: Optional currency filter
            network: Optional network filter
            type: Optional wallet type filter
            is_active: Optional active status filter
            
        Returns:
            List of wallets
        """
        query = select(DestinationWallet)
        
        if currency:
            query = query.where(DestinationWallet.currency == currency)
            
        if network:
            query = query.where(DestinationWallet.network == network)
            
        if type:
            query = query.where(DestinationWallet.type == type)
            
        if is_active is not None:
            query = query.where(DestinationWallet.is_active == is_active)
            
        result = await db.execute(query)
        wallets = result.scalars().all()
        
        return list(wallets)
    
    async def get_wallet(
        self, 
        db: AsyncSession, 
        wallet_id: uuid.UUID
    ) -> Optional[DestinationWallet]:
        """
        Get a specific wallet by ID.
        
        Args:
            db: Database session
            wallet_id: Wallet ID
            
        Returns:
            Wallet if found, None otherwise
        """
        query = select(DestinationWallet).where(DestinationWallet.id == wallet_id)
        result = await db.execute(query)
        wallet = result.scalars().first()
        
        return wallet
    
    async def create_wallet(
        self, 
        db: AsyncSession, 
        wallet_data: WalletCreate
    ) -> DestinationWallet:
        """
        Create a new destination wallet.
        
        Args:
            db: Database session
            wallet_data: Wallet creation data
            
        Returns:
            Created wallet
            
        Raises:
            ValueError: If wallet with the same address already exists or address is invalid
        """
        # Check if wallet with the same address already exists
        query = select(DestinationWallet).where(DestinationWallet.address == wallet_data.address)
        result = await db.execute(query)
        existing_wallet = result.scalars().first()
        
        if existing_wallet:
            raise ValueError(f"Wallet with address {wallet_data.address} already exists")
        
        # Validate wallet address with blockchain connector
        connector = get_blockchain_connector(wallet_data.network)
        if connector:
            is_valid = await connector.validate_address(wallet_data.address)
            if not is_valid:
                raise ValueError(f"Invalid wallet address for {wallet_data.network} network: {wallet_data.address}")
        
        # Create new wallet
        wallet = DestinationWallet(
            address=wallet_data.address,
            currency=wallet_data.currency,
            network=wallet_data.network,
            type=wallet_data.type,
            balance=wallet_data.balance or 0,
            is_active=wallet_data.is_active
        )
        
        db.add(wallet)
        await db.commit()
        await db.refresh(wallet)
        
        logger.info(f"Created new destination wallet with address: {wallet.address} for {wallet.currency}/{wallet.network}")
        
        return wallet
    
    async def update_wallet(
        self, 
        db: AsyncSession, 
        wallet_id: uuid.UUID, 
        wallet_data: WalletUpdate
    ) -> Optional[DestinationWallet]:
        """
        Update a wallet.
        
        Args:
            db: Database session
            wallet_id: Wallet ID
            wallet_data: Wallet update data
            
        Returns:
            Updated wallet if found, None otherwise
        """
        # Get wallet
        query = select(DestinationWallet).where(DestinationWallet.id == wallet_id)
        result = await db.execute(query)
        wallet = result.scalars().first()
        
        if not wallet:
            return None
        
        # Update fields
        update_data = wallet_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(wallet, key, value)
        
        wallet.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(wallet)
        
        logger.info(f"Updated wallet with ID: {wallet_id}")
        
        return wallet
    
    async def set_wallet_active(
        self, 
        db: AsyncSession, 
        wallet_id: uuid.UUID, 
        is_active: bool
    ) -> Optional[DestinationWallet]:
        """
        Set wallet active status.
        
        Args:
            db: Database session
            wallet_id: Wallet ID
            is_active: Whether the wallet should be active
            
        Returns:
            Updated wallet if found, None otherwise
        """
        # Get wallet
        query = select(DestinationWallet).where(DestinationWallet.id == wallet_id)
        result = await db.execute(query)
        wallet = result.scalars().first()
        
        if not wallet:
            return None
        
        # Update status
        wallet.is_active = is_active
        wallet.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(wallet)
        
        logger.info(f"Set wallet with ID: {wallet_id} active status to: {is_active}")
        
        return wallet
    
    async def update_wallet_balance(
        self, 
        db: AsyncSession, 
        address: str
    ) -> Optional[DestinationWallet]:
        """
        Update wallet balance from blockchain.
        
        Args:
            db: Database session
            address: Wallet address
            
        Returns:
            Updated wallet if found, None otherwise
        """
        # Get wallet
        query = select(DestinationWallet).where(DestinationWallet.address == address)
        result = await db.execute(query)
        wallet = result.scalars().first()
        
        if not wallet:
            return None
        
        # Get balance from blockchain
        connector = get_blockchain_connector(wallet.network)
        if connector:
            try:
                balance = await connector.get_balance(address)
                
                # Update wallet balance
                wallet.balance = balance
                wallet.updated_at = datetime.utcnow()
                
                await db.commit()
                await db.refresh(wallet)
                
                logger.info(f"Updated balance for wallet {address}: {balance} {wallet.currency}")
                
                return wallet
            except Exception as e:
                logger.error(f"Failed to update balance for wallet {address}: {str(e)}")
        
        return wallet