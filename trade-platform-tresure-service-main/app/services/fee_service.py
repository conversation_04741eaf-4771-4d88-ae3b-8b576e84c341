from typing import List, Optional, Dict, Tuple
from datetime import datetime, timedelta
import uuid
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update

from app.core.logging import get_logger
from app.db.models.fee import FeeSettings
from app.schemas.fee import FeeSettingsUpdate
from app.services.blockchain.base import get_blockchain_connector


logger = get_logger(__name__)


class FeeService:
    """
    Service for managing fee settings and optimization.
    """
    
    async def get_fee_settings(
        self, 
        db: AsyncSession,
        currency: Optional[str] = None,
        network: Optional[str] = None,
        is_active: Optional[bool] = None
    ) -> List[FeeSettings]:
        """
        Get fee settings with optional filtering.
        
        Args:
            db: Database session
            currency: Optional currency filter
            network: Optional network filter
            is_active: Optional active status filter
            
        Returns:
            List of fee settings
        """
        query = select(FeeSettings)
        
        if currency:
            query = query.where(FeeSettings.currency == currency)
            
        if network:
            query = query.where(FeeSettings.network == network)
            
        if is_active is not None:
            query = query.where(FeeSettings.is_active == is_active)
            
        result = await db.execute(query)
        settings = result.scalars().all()
        
        return list(settings)
    
    async def get_fee_setting(
        self, 
        db: AsyncSession, 
        fee_id: uuid.UUID
    ) -> Optional[FeeSettings]:
        """
        Get fee setting by ID.
        
        Args:
            db: Database session
            fee_id: Fee setting ID
            
        Returns:
            Fee setting if found, None otherwise
        """
        query = select(FeeSettings).where(FeeSettings.id == fee_id)
        result = await db.execute(query)
        setting = result.scalars().first()
        
        return setting
    
    async def update_fee_setting(
        self, 
        db: AsyncSession, 
        fee_id: uuid.UUID, 
        fee_data: FeeSettingsUpdate
    ) -> Optional[FeeSettings]:
        """
        Update fee setting.
        
        Args:
            db: Database session
            fee_id: Fee setting ID
            fee_data: Fee setting update data
            
        Returns:
            Updated fee setting if found, None otherwise
        """
        # Get fee setting
        query = select(FeeSettings).where(FeeSettings.id == fee_id)
        result = await db.execute(query)
        setting = result.scalars().first()
        
        if not setting:
            return None
        
        # Update fields
        update_data = fee_data.dict(exclude_unset=True)
        for key, value in update_data.items():
            setattr(setting, key, value)
        
        setting.updated_at = datetime.utcnow()
        
        await db.commit()
        await db.refresh(setting)
        
        logger.info(f"Updated fee setting with ID: {fee_id}")
        
        return setting
    
    async def create_fee_setting(
        self, 
        db: AsyncSession, 
        fee_data: FeeSettingsUpdate
    ) -> FeeSettings:
        """
        Create a new fee setting.
        
        Args:
            db: Database session
            fee_data: Fee setting creation data
            
        Returns:
            Created fee setting
            
        Raises:
            ValueError: If setting already exists for this currency/network
        """
        # Ensure all required fields are present
        if not fee_data.currency or not fee_data.network:
            raise ValueError("Currency and network are required")
        
        if fee_data.min_consolidation_amount is None:
            raise ValueError("Minimum consolidation amount is required")
            
        if fee_data.max_fee_percentage is None:
            raise ValueError("Maximum fee percentage is required")
        
        # Check if setting already exists for this currency/network
        query = select(FeeSettings).where(
            FeeSettings.currency == fee_data.currency,
            FeeSettings.network == fee_data.network
        )
        result = await db.execute(query)
        existing = result.scalars().first()
        
        if existing:
            raise ValueError(f"Fee setting for {fee_data.currency}/{fee_data.network} already exists")
        
        # Create new setting
        setting = FeeSettings(
            currency=fee_data.currency,
            network=fee_data.network,
            min_consolidation_amount=fee_data.min_consolidation_amount,
            max_fee_percentage=fee_data.max_fee_percentage,
            priority=fee_data.priority or "medium",
            is_active=fee_data.is_active if fee_data.is_active is not None else True
        )
        
        db.add(setting)
        await db.commit()
        await db.refresh(setting)
        
        logger.info(f"Created new fee setting for {setting.currency}/{setting.network}")
        
        return setting
    
    async def should_consolidate(
        self,
        db: AsyncSession,
        currency: str,
        network: str,
        amount: Decimal
    ) -> Tuple[bool, str]:
        """
        Determine if a transaction should be consolidated based on amount and fee settings.
        
        Args:
            db: Database session
            currency: Currency code
            network: Network code
            amount: Amount to consolidate
            
        Returns:
            Tuple of (should_consolidate, reason)
        """
        # Get fee settings
        query = select(FeeSettings).where(
            FeeSettings.currency == currency,
            FeeSettings.network == network,
            FeeSettings.is_active == True
        )
        result = await db.execute(query)
        settings = result.scalars().first()
        
        if not settings:
            # No settings found, use default logic (always consolidate)
            return True, "No specific fee settings found, using defaults"
        
        # Check if amount meets minimum consolidation threshold
        if amount < settings.min_consolidation_amount:
            return False, f"Amount {amount} is below minimum consolidation threshold {settings.min_consolidation_amount}"
        
        # Get current fee estimate
        connector = get_blockchain_connector(network)
        if not connector:
            return True, f"No connector for network {network}, unable to estimate fees"
        
        try:
            # Use dummy addresses for fee estimation
            # In a production environment, you'd use actual wallet addresses
            dummy_source = self._get_dummy_address(network)
            dummy_dest = self._get_dummy_address(network, is_dest=True)
            
            fee = await connector.estimate_fee(
                dummy_source,
                dummy_dest,
                amount,
                settings.priority
            )
            
            # Check if fee percentage is acceptable
            fee_percentage = (fee / amount) * 100
            
            if fee_percentage > settings.max_fee_percentage:
                return False, f"Fee percentage {fee_percentage}% exceeds maximum {settings.max_fee_percentage}%"
            
            return True, f"Fee percentage {fee_percentage}% is below maximum {settings.max_fee_percentage}%"
            
        except Exception as e:
            logger.error(f"Error estimating fee: {str(e)}")
            # Default to consolidate on error
            return True, f"Error estimating fee: {str(e)}"
    
    async def get_optimal_transaction_time(
        self,
        currency: str,
        network: str
    ) -> datetime:
        """
        Determine the optimal time to execute a transaction based on historical fee data.
        
        Args:
            currency: Currency code
            network: Network code
            
        Returns:
            Optimal datetime for transaction
        """
        # Default to now + 1 hour if no optimization possible
        default_time = datetime.utcnow() + timedelta(hours=1)
        
        if network not in ["BTC", "ETH"]:
            # Most other networks have fixed fees, so no optimization needed
            return default_time
        
        try:
            # For Bitcoin and Ethereum, we can try to find optimal times
            if network == "BTC":
                # Bitcoin fees typically lower on weekends
                now = datetime.utcnow()
                day_of_week = now.weekday()
                
                if day_of_week < 5:  # Weekday
                    # If it's a weekday, schedule for weekend if it's within 48 hours
                    days_until_saturday = 5 - day_of_week
                    if days_until_saturday <= 2:
                        return now.replace(hour=10) + timedelta(days=days_until_saturday)
                
                # Otherwise schedule for early morning (UTC) when activity is lower
                next_morning = now.replace(hour=5, minute=0, second=0)
                if next_morning < now:
                    next_morning += timedelta(days=1)
                return next_morning
                
            elif network == "ETH":
                # For Ethereum, check historical gas prices
                # In a real implementation, you'd use a gas price API or your own data
                # Here we're simplifying and using a fixed optimal time
                now = datetime.utcnow()
                
                # Typically, gas prices are lower on weekends and during night hours in US time
                # Schedule for around 2-4 AM EST (7-9 AM UTC)
                next_optimal = now.replace(hour=8, minute=0, second=0)
                if next_optimal < now:
                    next_optimal += timedelta(days=1)
                return next_optimal
                
        except Exception as e:
            logger.error(f"Error determining optimal transaction time: {str(e)}")
        
        # Default fallback
        return default_time
    
    def _get_dummy_address(self, network: str, is_dest: bool = False) -> str:
        """
        Get a dummy address for fee estimation.
        
        Args:
            network: Network code
            is_dest: Whether this is a destination address
            
        Returns:
            Dummy address for the network
        """
        suffix = "2" if is_dest else "1"
        
        if network == "BTC":
            return f"bc1qtest{suffix}"
        elif network == "ETH":
            return f"0xtest{suffix}"
        elif network == "TRON":
            return f"T{suffix}test"
        else:
            return f"address{suffix}"