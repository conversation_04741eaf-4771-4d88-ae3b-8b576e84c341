# privacy_tron_service.py
import asyncio
import random
from decimal import Decimal
from typing import List, Tuple, Optional, Dict, Any
from tronpy.keys import PrivateKey

from app.core.config import settings
from app.core.logging import get_logger
from app.db.models.transaction import ConsolidationTransaction
from app.services.blockchain.base import get_blockchain_connector

logger = get_logger(__name__)

class PrivacyTronService:
    """Сервис для обеспечения приватности транзакций TRON"""
    
    def __init__(self):
        # Получаем коннектор TRON
        self.connector = get_blockchain_connector("TRON")
        
        # Загружаем промежуточные кошельки из настроек
        self.intermediate_wallets = []
        
        # Парсим промежуточные кошельки и ключи из настроек
        tron_addresses = settings.TRON_INTERMEDIATE_WALLETS.split(",") if isinstance(settings.TRON_INTERMEDIATE_WALLETS, str) else settings.TRON_INTERMEDIATE_WALLETS
        tron_keys = settings.TRON_INTERMEDIATE_KEYS.split(",") if isinstance(settings.TRON_INTERMEDIATE_KEYS, str) else settings.TRON_INTERMEDIATE_KEYS
        
        if len(tron_addresses) == len(tron_keys):
            self.intermediate_wallets = list(zip([addr.strip() for addr in tron_addresses], 
                                                [key.strip() for key in tron_keys]))
        else:
            logger.error("Mismatch in TRON intermediate wallet configuration. "
                       f"Addresses: {len(tron_addresses)}, Keys: {len(tron_keys)}")
                       
        # Минимальное требуемое количество TRX для транзакций USDT-TRC20
        self.min_trx_balance = Decimal('15.0')
        
    async def check_wallet_trx_balance(self, address: str) -> Tuple[bool, Decimal]:
        """
        Проверяет баланс TRX на кошельке
        
        Args:
            address: Адрес кошелька
            
        Returns:
            Tuple (достаточно TRX, текущий баланс)
        """
        try:
            balance = await self.connector.get_balance(address)
            logger.info(f"[PRIVACY_TRON] Баланс кошелька {address}: {balance} TRX")
            return balance >= self.min_trx_balance, balance
        except Exception as e:
            logger.error(f"[PRIVACY_TRON] Ошибка при проверке баланса кошелька {address}: {str(e)}")
            return False, Decimal('0')
    
    async def fund_wallet_if_needed(self, address: str, fee_wallet: str, fee_wallet_key: str) -> bool:
        """
        Проверяет баланс кошелька и при необходимости пополняет его TRX
        
        Args:
            address: Адрес кошелька для проверки
            fee_wallet: Адрес fee-кошелька
            fee_wallet_key: Приватный ключ fee-кошелька
            
        Returns:
            bool: Успешность операции
        """
        sufficient, balance = await self.check_wallet_trx_balance(address)
        
        if sufficient:
            logger.info(f"[PRIVACY_TRON] Кошелек {address} имеет достаточный баланс: {balance} TRX")
            return True
            
        # Рассчитываем, сколько TRX нужно отправить
        needed_amount = self.min_trx_balance - balance
        
        logger.info(f"[PRIVACY_TRON] Пополнение кошелька {address} на {needed_amount} TRX с fee-кошелька {fee_wallet}")
        
        try:
            # Создаем приватный ключ
            private_key_obj = PrivateKey(bytes.fromhex(fee_wallet_key))
            amount_sun = int(needed_amount * Decimal(1000000))
            
            # Создаем и отправляем транзакцию
            txn = self.connector.client.trx.transfer(fee_wallet, address, amount_sun)
            txn_obj = txn.build()
            signed_txn = txn_obj.sign(private_key_obj)
            broadcast_result = signed_txn.broadcast()
            
            if not broadcast_result or not broadcast_result.get('result'):
                logger.error(f"[PRIVACY_TRON] Ошибка пополнения кошелька {address}: {broadcast_result}")
                return False
                
            tx_hash = broadcast_result.get('txid')
            logger.info(f"[PRIVACY_TRON] Кошелек {address} пополнен, хеш: {tx_hash}")
            
            # Ждем 10 секунд для подтверждения транзакции
            await asyncio.sleep(10)
            return True
            
        except Exception as e:
            logger.error(f"[PRIVACY_TRON] Ошибка при пополнении кошелька {address}: {str(e)}")
            return False
            
    async def execute_private_transaction(
        self,
        source_address: str,
        destination_address: str,
        amount: Decimal,
        fee_wallet: str,
        fee_wallet_key: str,
        currency: str = "USDT-TRC20",
        **kwargs
    ) -> Optional[str]:
        """
        Выполняет приватную транзакцию через цепочку промежуточных кошельков
        
        Args:
            source_address: Исходный адрес
            destination_address: Конечный адрес (корпоративный)
            amount: Сумма перевода
            fee_wallet: Адрес комиссионного кошелька
            fee_wallet_key: Приватный ключ комиссионного кошелька
            currency: Валюта (по умолчанию USDT-TRC20)
            
        Returns:
            Optional[str]: Хеш финальной транзакции или None в случае ошибки
        """
        if not self.connector:
            logger.error("[PRIVACY_TRON] Не удалось получить TRON коннектор")
            return None
            
        # Определяем количество промежуточных шагов
        num_hops = min(settings.PRIVACY_HOPS, len(self.intermediate_wallets))
        
        if num_hops < 1:
            logger.error("[PRIVACY_TRON] Недостаточно промежуточных кошельков для приватной транзакции")
            return None
            
        # Выбираем случайные промежуточные кошельки
        selected_wallets = random.sample(self.intermediate_wallets, num_hops)
        
        try:
            # Проверяем и пополняем исходный кошелек при необходимости
            source_funded = await self.fund_wallet_if_needed(source_address, fee_wallet, fee_wallet_key)
            if not source_funded:
                logger.error(f"[PRIVACY_TRON] Не удалось обеспечить достаточный баланс TRX на исходном кошельке {source_address}")
                return None
                
            # Шаг 1: Отправляем с исходного кошелька на первый промежуточный
            first_address, first_key = selected_wallets[0]
            
            # Проверяем и пополняем первый промежуточный кошелек при необходимости
            first_funded = await self.fund_wallet_if_needed(first_address, fee_wallet, fee_wallet_key)
            if not first_funded:
                logger.error(f"[PRIVACY_TRON] Не удалось обеспечить достаточный баланс TRX на первом промежуточном кошельке {first_address}")
                return None
                
            # Немного изменяем сумму для усложнения отслеживания
            first_amount = amount * Decimal(1 - random.uniform(0.001, 0.005))
            
            # Получаем приватный ключ исходного кошелька
            source_key = await self.connector.get_private_key(source_address)
            
            # Выполняем первую транзакцию исходя из типа валюты
            if currency == "TRX":
                # Для TRX используем стандартный перевод
                first_tx_hash = await self.connector.create_and_broadcast_transaction(
                    source_address,
                    first_address,
                    first_amount,
                    source_key,
                    "medium"
                )
            else:
                # Для TRC20 токенов (например USDT-TRC20)
                token_contract = await self.connector.get_token_contract_address(currency)
                if not token_contract:
                    logger.error(f"[PRIVACY_TRON] Не найден контракт токена для {currency}")
                    return None
                
                # Отправляем токен с высоким энергетическим лимитом
                contract = self.connector.client.get_contract(token_contract)
                
                # Преобразуем сумму токена (обычно 6 десятичных знаков для TRC20)
                token_amount = int(first_amount * Decimal(10 ** 6))
                
                # Создаем, подписываем и отправляем транзакцию токена
                private_key_obj = PrivateKey(bytes.fromhex(source_key))  # Исправлено здесь
                txn = contract.functions.transfer(first_address, token_amount) \
                    .with_owner(source_address) \
                    .fee_limit(50_000_000)  # 50 TRX для энергии
                
                built_txn = txn.build()
                signed_txn = built_txn.sign(private_key_obj)
                broadcast_result = signed_txn.broadcast()
                
                if not broadcast_result or not broadcast_result.get('result'):
                    logger.error(f"[PRIVACY_TRON] Ошибка отправки {currency}: {broadcast_result}")
                    return None
                
                first_tx_hash = broadcast_result.get('txid')
            
            logger.info(f"[PRIVACY_TRON] Шаг 1/{num_hops+1}: Транзакция отправлена на первый промежуточный кошелек. "
                       f"Хеш: {first_tx_hash}")
            
            # Ждем случайное время перед следующей транзакцией
            delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
            logger.info(f"[PRIVACY_TRON] Ожидание {delay} секунд перед следующим шагом")
            await asyncio.sleep(delay)
            
            # Переменные для отслеживания текущего состояния цепочки
            current_amount = first_amount
            current_address = first_address
            current_key = first_key
            last_tx_hash = first_tx_hash
            
            # Проходим через цепочку промежуточных кошельков
            for i in range(1, num_hops):
                next_address, next_key = selected_wallets[i]
                
                # Проверяем и пополняем следующий кошелек
                next_funded = await self.fund_wallet_if_needed(next_address, fee_wallet, fee_wallet_key)
                if not next_funded:
                    logger.error(f"[PRIVACY_TRON] Не удалось обеспечить достаточный баланс TRX на промежуточном кошельке {next_address}")
                    continue  # Продолжаем с другим кошельком
                    
                # Немного изменяем сумму снова
                current_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.003))
                
                # Отправляем средства на следующий промежуточный кошелек
                if currency == "TRX":
                    hop_tx_hash = await self.connector.create_and_broadcast_transaction(
                        current_address,
                        next_address,
                        current_amount,
                        current_key,
                        "medium"
                    )
                else:
                    # Для TRC20 токенов
                    token_contract = await self.connector.get_token_contract_address(currency)
                    contract = self.connector.client.get_contract(token_contract)
                    
                    # Преобразуем сумму токена
                    token_amount = int(current_amount * Decimal(10 ** 6))
                    
                    # Создаем, подписываем и отправляем транзакцию токена
                    private_key_obj = PrivateKey(bytes.fromhex(current_key))  # Исправлено здесь
                    txn = contract.functions.transfer(next_address, token_amount) \
                        .with_owner(current_address) \
                        .fee_limit(50_000_000)  # 50 TRX для энергии
                    
                    built_txn = txn.build()
                    signed_txn = built_txn.sign(private_key_obj)
                    broadcast_result = signed_txn.broadcast()
                    
                    if not broadcast_result or not broadcast_result.get('result'):
                        logger.error(f"[PRIVACY_TRON] Ошибка промежуточной транзакции {currency}: {broadcast_result}")
                        continue
                    
                    hop_tx_hash = broadcast_result.get('txid')
                
                logger.info(f"[PRIVACY_TRON] Шаг {i+1}/{num_hops+1}: Транзакция отправлена на следующий промежуточный кошелек. "
                           f"Хеш: {hop_tx_hash}")
                
                # Обновляем текущие переменные
                current_address = next_address
                current_key = next_key
                last_tx_hash = hop_tx_hash
                
                # Ждем случайное время между транзакциями
                delay = random.randint(settings.PRIVACY_MIN_DELAY, settings.PRIVACY_MAX_DELAY)
                logger.info(f"[PRIVACY_TRON] Ожидание {delay} секунд перед следующим шагом")
                await asyncio.sleep(delay)
            
            # Финальная транзакция: с последнего промежуточного на корпоративный кошелек
            final_amount = current_amount * Decimal(1 - random.uniform(0.001, 0.002))
            
            # Отправляем финальную транзакцию
            if currency == "TRX":
                final_tx_hash = await self.connector.create_and_broadcast_transaction(
                    current_address,
                    destination_address,
                    final_amount,
                    current_key,
                    "medium"
                )
            else:
                # Для TRC20 токенов
                token_contract = await self.connector.get_token_contract_address(currency)
                contract = self.connector.client.get_contract(token_contract)
                
                # Преобразуем сумму токена
                token_amount = int(final_amount * Decimal(10 ** 6))
                
                # Создаем, подписываем и отправляем транзакцию токена
                private_key_obj = PrivateKey(bytes.fromhex(current_key))  # Исправлено здесь
                txn = contract.functions.transfer(destination_address, token_amount) \
                    .with_owner(current_address) \
                    .fee_limit(50_000_000)  # 50 TRX для энергии
                
                built_txn = txn.build()
                signed_txn = built_txn.sign(private_key_obj)
                broadcast_result = signed_txn.broadcast()
                
                if not broadcast_result or not broadcast_result.get('result'):
                    logger.error(f"[PRIVACY_TRON] Ошибка финальной транзакции {currency}: {broadcast_result}")
                    return None
                
                final_tx_hash = broadcast_result.get('txid')
            
            logger.info(f"[PRIVACY_TRON] Шаг {num_hops+1}/{num_hops+1}: Финальная транзакция выполнена. "
                       f"Хеш: {final_tx_hash}")
            
            return final_tx_hash
            
        except Exception as e:
            logger.error(f"[PRIVACY_TRON] Ошибка выполнения приватной транзакции: {str(e)}")
            return None