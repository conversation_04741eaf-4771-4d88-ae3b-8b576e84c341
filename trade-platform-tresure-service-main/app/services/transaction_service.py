from typing import List, Dict, Any, Optional, Tu<PERSON>
from datetime import datetime, timed<PERSON><PERSON>
import uuid
from decimal import Decimal

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, func, text
from sqlalchemy.sql import text

from app.core.logging import get_logger
from app.db.models.transaction import ConsolidationTransaction
from app.db.models.task import ConsolidationTask
from app.db.models.wallet import DestinationWallet
from app.schemas.transaction import TransactionCreate, TransactionStatus
from app.services.blockchain.base import get_blockchain_connector
from app.services.kafka_service import publish_event
from app.core.config import settings


logger = get_logger(__name__)


class TransactionService:
    """
    Service for managing consolidation transactions.
    """
    
    async def create_transaction(
        self,
        db: AsyncSession,
        task_id: uuid.UUID,
        transaction_data: TransactionCreate
    ) -> ConsolidationTransaction:
        """
        Create a new consolidation transaction.
        
        Args:
            db: Database session
            task_id: ID of the consolidation task
            transaction_data: Transaction data
            
        Returns:
            Created transaction
            
        Raises:
            ValueError: If no active destination wallet found
        """
        # Find the appropriate destination wallet
        query = select(DestinationWallet).where(
            DestinationWallet.currency == transaction_data.currency,
            DestinationWallet.network == transaction_data.network,
            DestinationWallet.is_active == True
        )
        
        result = await db.execute(query)
        destination_wallet = result.scalars().first()
        
        if not destination_wallet:
            logger.error(f"No active destination wallet found for {transaction_data.currency}/{transaction_data.network}")
            raise ValueError(f"No active destination wallet found for {transaction_data.currency}/{transaction_data.network}")
        
        # Create transaction
        transaction = ConsolidationTransaction(
            task_id=task_id,
            source_wallet=transaction_data.source_wallet,
            destination_wallet=destination_wallet.address,
            user_id=transaction_data.user_id,
            currency=transaction_data.currency,
            network=transaction_data.network,
            amount=transaction_data.amount,
            status="new"
        )
        
        db.add(transaction)
        
        # Update task info
        query = select(ConsolidationTask).where(ConsolidationTask.id == task_id)
        result = await db.execute(query)
        task = result.scalars().first()
        
        if not task:
            await db.rollback()
            raise ValueError(f"Task with ID {task_id} not found")
        
        task.total_transactions += 1
        
        # Update total amount
        total_amount = task.total_amount or {}
        if transaction_data.currency in total_amount:
            total_amount[transaction_data.currency] = str(
                Decimal(total_amount[transaction_data.currency]) + transaction_data.amount
            )
        else:
            total_amount[transaction_data.currency] = str(transaction_data.amount)
            
        task.total_amount = total_amount
        
        await db.commit()
        await db.refresh(transaction)
        
        logger.info(f"Created transaction {transaction.id} in task {task_id}")
        
        return transaction
    
    async def execute_transaction(
        self,
        db: AsyncSession,
        transaction_id: uuid.UUID
    ) -> bool:
        """
        Execute a consolidation transaction.
        
        Args:
            db: Database session
            transaction_id: ID of the transaction to execute
            
        Returns:
            True if successful, False otherwise
        """
        # Get transaction
        query = select(ConsolidationTransaction).where(
            ConsolidationTransaction.id == transaction_id
        )
        result = await db.execute(query)
        transaction = result.scalars().first()
        
        if not transaction:
            logger.error(f"Transaction with ID {transaction_id} not found")
            return False
        
        # Skip if not in 'new' status
        if transaction.status != "new":
            logger.warning(f"Cannot execute transaction {transaction_id} with status {transaction.status}")
            return False
        
        try:
            # Get appropriate connector based on network
            connector = get_blockchain_connector(transaction.network)
            
            if not connector:
                raise ValueError(f"No connector available for network: {transaction.network}")
            
            # Estimate fee
            fee = await connector.estimate_fee(
                transaction.source_wallet,
                transaction.destination_wallet,
                transaction.amount,
                "medium"  # Medium priority by default
            )
            
            # Update transaction with estimated fee
            transaction.fee = fee
            
            # Create and sign transaction
            tx_hash, raw_tx = await connector.create_transaction(
                transaction.source_wallet,
                transaction.destination_wallet,
                transaction.amount,
                "medium"
            )
            
            # Store transaction hash
            transaction.tx_hash = tx_hash
            
            # Update status to 'pending'
            transaction.status = "pending"
            await db.commit()
            
            # Broadcast transaction
            tx_hash = await connector.broadcast_transaction(raw_tx)
            
            # Update transaction status
            transaction.status = "sent"
            transaction.tx_hash = tx_hash
            transaction.updated_at = datetime.utcnow()
            await db.commit()
            
            # Publish event to Kafka
            await self._publish_transaction_event(transaction, "consolidation_initiated")
            
            logger.info(f"Successfully executed transaction {transaction_id}, tx_hash: {tx_hash}")
            return True
            
        except Exception as e:
            # Update transaction with error
            transaction.status = "error"
            transaction.error_message = str(e)
            transaction.updated_at = datetime.utcnow()
            await db.commit()
            
            logger.error(f"Failed to execute transaction {transaction_id}: {str(e)}")
            return False
    
    async def check_transaction_status(
        self,
        db: AsyncSession,
        transaction_id: uuid.UUID
    ) -> Dict[str, Any]:
        """
        Check the status of a transaction in the blockchain.
        
        Args:
            db: Database session
            transaction_id: ID of the transaction to check
            
        Returns:
            Dictionary with transaction status information
        """
        # Get transaction
        query = select(ConsolidationTransaction).where(
            ConsolidationTransaction.id == transaction_id
        )
        result = await db.execute(query)
        transaction = result.scalars().first()
        
        if not transaction:
            logger.error(f"Transaction with ID {transaction_id} not found")
            return {"error": "Transaction not found"}
        
        # Skip if not in 'sent' status
        if transaction.status != "sent":
            return {"status": transaction.status}
        
        try:
            # Get appropriate connector
            connector = get_blockchain_connector(transaction.network)
            
            if not connector:
                raise ValueError(f"No connector available for network: {transaction.network}")
            
            # Check transaction status
            tx_status = await connector.get_transaction_status(transaction.tx_hash)
            
            # Update transaction status if confirmed
            if tx_status.get("status") == "confirmed":
                transaction.status = "confirmed"
                transaction.updated_at = datetime.utcnow()
                
                # Update task statistics
                query = select(ConsolidationTask).where(
                    ConsolidationTask.id == transaction.task_id
                )
                result = await db.execute(query)
                task = result.scalars().first()
                
                if task:
                    task.completed_transactions += 1
                    await db.commit()
                    
                    # Publish event to Kafka
                    await self._publish_transaction_event(transaction, "consolidation_completed")
                    
                logger.info(f"Transaction {transaction_id} confirmed with {tx_status.get('confirmations')} confirmations")
            
            return {
                "internal_status": transaction.status,
                "blockchain_status": tx_status
            }
            
        except Exception as e:
            logger.error(f"Failed to check transaction {transaction_id} status: {str(e)}")
            return {"error": str(e)}
    
    async def get_pending_transactions(
        self,
        db: AsyncSession,
        limit: int = 100
    ) -> List[ConsolidationTransaction]:
        """
        Get pending transactions that need status checks.
        
        Args:
            db: Database session
            limit: Maximum number of transactions to return
            
        Returns:
            List of pending transactions
        """
        query = select(ConsolidationTransaction).where(
            ConsolidationTransaction.status == "sent"
        ).limit(limit)
        
        result = await db.execute(query)
        return list(result.scalars().all())
    
    async def get_transactions_for_task(
        self,
        db: AsyncSession,
        task_id: uuid.UUID
    ) -> List[ConsolidationTransaction]:
        """
        Get all transactions for a specific task.
        
        Args:
            db: Database session
            task_id: Task ID
            
        Returns:
            List of transactions
        """
        query = select(ConsolidationTransaction).where(
            ConsolidationTransaction.task_id == task_id
        )
        
        result = await db.execute(query)
        return list(result.scalars().all())
    
    async def get_daily_stats(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        Get daily consolidation statistics.
        
        Args:
            db: Database session
            start_date: Start date for statistics
            end_date: End date for statistics
            
        Returns:
            Dictionary with daily statistics
        """
        # Use raw SQL for date truncation and aggregation
        # Note: This is PostgreSQL-specific SQL
        query = """
        SELECT 
            DATE_TRUNC('day', created_at) as day,
            currency,
            COUNT(*) as transaction_count,
            SUM(amount) as total_amount,
            SUM(fee) as total_fee,
            AVG(fee) as avg_fee
        FROM 
            consolidationtransaction
        WHERE 
            created_at BETWEEN :start_date AND :end_date
            AND status = 'confirmed'
        GROUP BY 
            DATE_TRUNC('day', created_at), 
            currency
        ORDER BY 
            day DESC, 
            currency
        """
        
        result = await db.execute(
            text(query), 
            {"start_date": start_date, "end_date": end_date}
        )
        
        rows = result.fetchall()
        
        # Transform into a more usable format
        daily_stats = {}
        for row in rows:
            day_str = row[0].strftime("%Y-%m-%d")
            currency = row[1]
            
            if day_str not in daily_stats:
                daily_stats[day_str] = {}
            
            daily_stats[day_str][currency] = {
                "transaction_count": row[2],
                "total_amount": str(row[3]),
                "total_fee": str(row[4]) if row[4] else "0",
                "avg_fee": str(row[5]) if row[5] else "0"
            }
        
        return daily_stats
    
    async def get_currency_stats(
        self,
        db: AsyncSession,
        currency: str,
        start_date: datetime,
        end_date: datetime
    ) -> Dict[str, Any]:
        """
        Get statistics for a specific currency.
        
        Args:
            db: Database session
            currency: Currency code
            start_date: Start date for statistics
            end_date: End date for statistics
            
        Returns:
            Dictionary with currency statistics
        """
        # Get transactions for this currency
        query = select(
            ConsolidationTransaction
        ).where(
            ConsolidationTransaction.currency == currency,
            ConsolidationTransaction.status == "confirmed",
            ConsolidationTransaction.created_at.between(start_date, end_date)
        )
        
        result = await db.execute(query)
        transactions = result.scalars().all()
        
        # Calculate statistics
        total_amount = sum(tx.amount for tx in transactions)
        total_fee = sum(tx.fee for tx in transactions if tx.fee)
        avg_fee = total_fee / len(transactions) if transactions else 0
        fee_percentage = (total_fee / total_amount * 100) if total_amount else 0
        
        # Group by day
        daily_data = {}
        for tx in transactions:
            day_str = tx.created_at.strftime("%Y-%m-%d")
            
            if day_str not in daily_data:
                daily_data[day_str] = {
                    "count": 0,
                    "amount": Decimal(0),
                    "fee": Decimal(0)
                }
            
            daily_data[day_str]["count"] += 1
            daily_data[day_str]["amount"] += tx.amount
            if tx.fee:
                daily_data[day_str]["fee"] += tx.fee
        
        # Format daily data for response
        daily_stats = [
            {
                "date": day,
                "transaction_count": data["count"],
                "total_amount": str(data["amount"]),
                "total_fee": str(data["fee"]),
                "fee_percentage": str(data["fee"] / data["amount"] * 100) if data["amount"] else "0"
            }
            for day, data in daily_data.items()
        ]
        
        # Sort by date
        daily_stats.sort(key=lambda x: x["date"], reverse=True)
        
        return {
            "currency": currency,
            "total_transactions": len(transactions),
            "total_amount": str(total_amount),
            "total_fee": str(total_fee),
            "avg_fee": str(avg_fee),
            "fee_percentage": str(fee_percentage),
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat(),
            "daily_stats": daily_stats
        }
    
    async def generate_report(
        self,
        db: AsyncSession,
        start_date: datetime,
        end_date: datetime,
        currency: Optional[str] = None,
        status: Optional[str] = None,
        limit: int = 1000
    ) -> Dict[str, Any]:
        """
        Generate a detailed report of consolidation transactions.
        
        Args:
            db: Database session
            start_date: Start date for report
            end_date: End date for report
            currency: Optional currency filter
            status: Optional status filter
            limit: Maximum number of transactions to include
            
        Returns:
            Dictionary with report data
        """
        # Build query
        query = select(ConsolidationTransaction).where(
            ConsolidationTransaction.created_at.between(start_date, end_date)
        )
        
        if currency:
            query = query.where(ConsolidationTransaction.currency == currency)
        
        if status:
            query = query.where(ConsolidationTransaction.status == status)
        
        # Add order and limit
        query = query.order_by(ConsolidationTransaction.created_at.desc()).limit(limit)
        
        # Execute query
        result = await db.execute(query)
        transactions = result.scalars().all()
        
        # Format for report
        report_data = [
            {
                "id": str(tx.id),
                "task_id": str(tx.task_id),
                "source_wallet": tx.source_wallet,
                "destination_wallet": tx.destination_wallet,
                "currency": tx.currency,
                "network": tx.network,
                "amount": str(tx.amount),
                "fee": str(tx.fee) if tx.fee else None,
                "status": tx.status,
                "tx_hash": tx.tx_hash,
                "created_at": tx.created_at.isoformat(),
                "updated_at": tx.updated_at.isoformat() if tx.updated_at else None
            }
            for tx in transactions
        ]
        
        # Calculate summary stats
        total_amount_by_currency = {}
        total_fee_by_currency = {}
        count_by_status = {}
        
        for tx in transactions:
            # Sum amounts by currency
            if tx.currency not in total_amount_by_currency:
                total_amount_by_currency[tx.currency] = Decimal(0)
            total_amount_by_currency[tx.currency] += tx.amount
            
            # Sum fees
            if tx.fee:
                fee_currency = tx.currency
                if fee_currency not in total_fee_by_currency:
                    total_fee_by_currency[fee_currency] = Decimal(0)
                total_fee_by_currency[fee_currency] += tx.fee
            
            # Count by status
            if tx.status not in count_by_status:
                count_by_status[tx.status] = 0
            count_by_status[tx.status] += 1
        
        # Format summary for response
        summary = {
            "total_transactions": len(transactions),
            "total_amount_by_currency": {k: str(v) for k, v in total_amount_by_currency.items()},
            "total_fee_by_currency": {k: str(v) for k, v in total_fee_by_currency.items()},
            "status_counts": count_by_status,
            "start_date": start_date.isoformat(),
            "end_date": end_date.isoformat()
        }
        
        return {
            "summary": summary,
            "transactions": report_data
        }
    
    async def _publish_transaction_event(
        self,
        transaction: ConsolidationTransaction,
        event_type: str
    ) -> None:
        """
        Publish transaction event to Kafka.
        
        Args:
            transaction: Transaction object
            event_type: Event type
        """
        event_data = {
            "event_type": event_type,
            "task_id": str(transaction.task_id),
            "transaction_id": str(transaction.id),
            "source_wallet": transaction.source_wallet,
            "destination_wallet": transaction.destination_wallet,
            "amount": str(transaction.amount),
            "currency": transaction.currency,
            "network": transaction.network,
            "tx_hash": transaction.tx_hash,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await publish_event(settings.KAFKA_TREASURY_EVENTS_TOPIC, event_data)