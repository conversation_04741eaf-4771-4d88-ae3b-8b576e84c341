from typing import Optional
from datetime import datetime, timedelta

from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.services.transaction_service import TransactionService


router = APIRouter()
transaction_service = TransactionService()


@router.get("/daily")
async def get_daily_stats(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Get daily consolidation statistics.
    """
    # Default to last 30 days if not specified
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    return await transaction_service.get_daily_stats(db, start_date, end_date)


@router.get("/currency/{currency}")
async def get_currency_stats(
    currency: str,
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Get statistics for a specific currency.
    """
    # Default to last 30 days if not specified
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    return await transaction_service.get_currency_stats(db, currency, start_date, end_date)


@router.get("/reports/transactions")
async def generate_transactions_report(
    start_date: Optional[datetime] = None,
    end_date: Optional[datetime] = None,
    currency: Optional[str] = None,
    status: Optional[str] = None,
    limit: int = Query(1000, ge=1, le=10000),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate a detailed report of consolidation transactions.
    """
    # Default to last 30 days if not specified
    if not end_date:
        end_date = datetime.utcnow()
    if not start_date:
        start_date = end_date - timedelta(days=30)
    
    return await transaction_service.generate_report(
        db, 
        start_date, 
        end_date, 
        currency=currency, 
        status=status, 
        limit=limit
    )