from typing import List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.schemas.wallet import (
    WalletCreate, 
    WalletResponse, 
    WalletUpdate
)
from app.services.wallet_service import WalletService


router = APIRouter()
wallet_service = WalletService()


@router.get("/", response_model=List[WalletResponse])
async def get_wallets(
    currency: Optional[str] = None,
    network: Optional[str] = None,
    type: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a list of destination wallets with optional filtering.
    """
    return await wallet_service.get_wallets(
        db,
        currency=currency,
        network=network,
        type=type,
        is_active=is_active
    )


@router.get("/{wallet_id}", response_model=WalletResponse)
async def get_wallet(
    wallet_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get detailed information about a specific wallet.
    """
    wallet = await wallet_service.get_wallet(db, wallet_id)
    
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Wallet with ID {wallet_id} not found"
        )
    
    return wallet


@router.post("/", response_model=WalletResponse, status_code=status.HTTP_201_CREATED)
async def create_wallet(
    wallet_data: WalletCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Add a new destination wallet.
    """
    try:
        return await wallet_service.create_wallet(db, wallet_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )


@router.put("/{wallet_id}", response_model=WalletResponse)
async def update_wallet(
    wallet_id: uuid.UUID,
    wallet_data: WalletUpdate,
    db: AsyncSession = Depends(get_db)
):
    """
    Update information about a wallet.
    """
    wallet = await wallet_service.update_wallet(db, wallet_id, wallet_data)
    
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Wallet with ID {wallet_id} not found"
        )
    
    return wallet


@router.put("/{wallet_id}/activate", response_model=WalletResponse)
async def activate_wallet(
    wallet_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Activate a wallet.
    """
    wallet = await wallet_service.set_wallet_active(db, wallet_id, True)
    
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Wallet with ID {wallet_id} not found"
        )
    
    return wallet


@router.put("/{wallet_id}/deactivate", response_model=WalletResponse)
async def deactivate_wallet(
    wallet_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Deactivate a wallet.
    """
    wallet = await wallet_service.set_wallet_active(db, wallet_id, False)
    
    if not wallet:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Wallet with ID {wallet_id} not found"
        )
    
    return wallet