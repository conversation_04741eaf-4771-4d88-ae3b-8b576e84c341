from fastapi import APIRouter

from app.api.routes import tasks, wallets, fees, stats

# Create API router
api_router = APIRouter()

# Include all route modules
api_router.include_router(tasks.router, prefix="/tasks", tags=["tasks"])
api_router.include_router(wallets.router, prefix="/wallets", tags=["wallets"])
api_router.include_router(fees.router, prefix="/fees", tags=["fees"])
api_router.include_router(stats.router, prefix="/stats", tags=["stats"])