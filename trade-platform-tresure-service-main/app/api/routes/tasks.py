from typing import List, Optional
from datetime import datetime
import uuid

from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.db.session import get_db
from app.db.models.task import ConsolidationTask
from app.db.models.transaction import ConsolidationTransaction
from app.schemas.task import (
    TaskCreate, 
    TaskResponse, 
    TaskDetailResponse,
    TaskUpdateSchedule
)
from app.schemas.transaction import TransactionResponse
from app.services.task_service import TaskService


router = APIRouter()
task_service = TaskService()


@router.get("/", response_model=List[TaskResponse])
async def get_tasks(
    status: Optional[str] = None,
    limit: int = Query(100, ge=1, le=500),
    offset: int = Query(0, ge=0),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a list of consolidation tasks with optional filtering by status.
    """
    return await task_service.get_tasks(db, status=status, limit=limit, offset=offset)


@router.get("/{task_id}", response_model=TaskDetailResponse)
async def get_task(
    task_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get detailed information about a specific consolidation task.
    """
    task = await task_service.get_task_detail(db, task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task with ID {task_id} not found"
        )
    
    return task


@router.post("/", response_model=TaskResponse, status_code=status.HTTP_201_CREATED)
async def create_task(
    task_data: TaskCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new consolidation task.
    """
    return await task_service.create_task(db, task_data)


@router.put("/{task_id}/cancel", response_model=TaskResponse)
async def cancel_task(
    task_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Cancel a task if possible (only if it's in 'new' status).
    """
    task = await task_service.cancel_task(db, task_id)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task with ID {task_id} not found or not in 'new' status"
        )
    
    return task


@router.put("/{task_id}/schedule", response_model=TaskResponse)
async def update_task_schedule(
    task_id: uuid.UUID,
    schedule_data: TaskUpdateSchedule,
    db: AsyncSession = Depends(get_db)
):
    """
    Update the scheduled time for a task.
    """
    task = await task_service.update_schedule(db, task_id, schedule_data)
    
    if not task:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Task with ID {task_id} not found or not in a status that allows rescheduling"
        )
    
    return task