from typing import List, Optional
import uuid

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from app.db.session import get_db
from app.schemas.fee import (
    FeeSettingsResponse, 
    FeeSettingsUpdate
)
from app.services.fee_service import FeeService


router = APIRouter()
fee_service = FeeService()


@router.get("/", response_model=List[FeeSettingsResponse])
async def get_fee_settings(
    currency: Optional[str] = None,
    network: Optional[str] = None,
    is_active: Optional[bool] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Get fee settings with optional filtering.
    """
    return await fee_service.get_fee_settings(
        db,
        currency=currency,
        network=network,
        is_active=is_active
    )


@router.get("/{fee_id}", response_model=FeeSettingsResponse)
async def get_fee_setting(
    fee_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a specific fee setting by ID.
    """
    setting = await fee_service.get_fee_setting(db, fee_id)
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Fee setting with ID {fee_id} not found"
        )
    
    return setting


@router.put("/{fee_id}", response_model=FeeSettingsResponse)
async def update_fee_setting(
    fee_id: uuid.UUID,
    fee_data: FeeSettingsUpdate,
    db: AsyncSession = Depends(get_db)
):
    """
    Update fee settings.
    """
    setting = await fee_service.update_fee_setting(db, fee_id, fee_data)
    
    if not setting:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Fee setting with ID {fee_id} not found"
        )
    
    return setting


@router.post("/", response_model=FeeSettingsResponse, status_code=status.HTTP_201_CREATED)
async def create_fee_setting(
    fee_data: FeeSettingsUpdate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new fee setting.
    """
    try:
        return await fee_service.create_fee_setting(db, fee_data)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )