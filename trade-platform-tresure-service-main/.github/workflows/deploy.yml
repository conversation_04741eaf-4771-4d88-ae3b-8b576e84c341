name: Deploy Treasury Service to AWS

on:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    env:
      AWS_HOST: "${{ secrets.TREASURE_SERVICE_IP }}"

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Setup SSH key for deploy
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.AWS_SSH_KEY }}" > ~/.ssh/aws-deploy-key.pem
          chmod 600 ~/.ssh/aws-deploy-key.pem
          ssh-keyscan -H $AWS_HOST >> ~/.ssh/known_hosts

      - name: Pull, install, restart on AWS
        run: |
          ssh -i ~/.ssh/aws-deploy-key.pem -o StrictHostKeyChecking=no ubuntu@$AWS_HOST '
            cd ~/trade-platform-tresure-service &&
            git pull origin main &&
            source venv/bin/activate &&
            pip install -r requirements.txt &&
            supervisorctl restart tresure-service
          '