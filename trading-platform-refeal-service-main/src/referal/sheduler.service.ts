import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import * as cron from 'node-cron';
import { ReferalService } from './referal.service';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Referal } from './referal.entity';

@Injectable()
export class SchedulerService implements OnModuleInit {
  private readonly logger = new Logger(SchedulerService.name);

  constructor(
    private readonly referalService: ReferalService,
    @InjectRepository(Referal)
    private readonly referalRepository: Repository<Referal>,
  ) {}

  // Инициализация cron задач при запуске модуля
  onModuleInit() {
    // Каждый час в 0 минут (обновление статистики торговли)
    cron.schedule('0 * * * *', () => {
      this.handleHourlyTradeStatsUpdate();
    });

    // Каждый день в полночь (00:00) - расчёт выплат
    cron.schedule('0 0 * * *', () => {
      this.handleDailyRewardsCalculation();
    });

    // 1-го числа каждого месяца в 01:00 - финальный расчёт за прошлый месяц
    cron.schedule('0 1 1 * *', () => {
      this.handleMonthlyFinalRewardsCalculation();
    });

    this.logger.log('Планировщик инициализирован: каждый час - обновление статистики, каждый день - расчёт выплат');
  }

  /**
   * Задача, запускаемая каждый час
   * Запрашивает актуальные данные о торговле у торгового сервиса
   */
  async handleHourlyTradeStatsUpdate() {
    this.logger.log('Запущено почасовое обновление статистики торговли');
    
    try {
      // Получаем текущую дату
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // JavaScript месяцы с 0
      
      // Проверяем, что Kafka-клиент подключен
      if (!this.referalService['kafkaClient'] || !this.referalService['kafkaClient'].connect) {
        this.logger.error('Kafka-клиент не инициализирован');
        return;
      }
      
      // Получаем всех пользователей с актуальным годом и месяцем
      const allReferals = await this.referalRepository.find({
        where: { year, month }
      });
      
      this.logger.log(`Найдено ${allReferals.length} пользователей для обновления статистики торговли`);
      
      // Обновляем данные для каждого пользователя
      let updatedCount = 0;
      for (const referal of allReferals) {
        try {
          // Запрашиваем актуальные данные о торговле
          await this.referalService.requestTradeStats(referal.userId, year, month);
          updatedCount++;
          
          this.logger.log(`Обновлена торговая статистика для пользователя ${referal.userId}`);
          
          // Небольшая пауза, чтобы не перегружать сервисы
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          this.logger.error(`Ошибка при обновлении торговой статистики для пользователя ${referal.userId}: ${error.message}`);
        }
      }
      
      this.logger.log(`Обновлена торговая статистика для ${updatedCount} из ${allReferals.length} пользователей`);
      
    } catch (error) {
      this.logger.error(`Ошибка при почасовом обновлении торговой статистики: ${error.message}`);
    }
  }

  /**
   * Задача, запускаемая каждый день в 00:00
   * Рассчитывает вознаграждения и прогнозы на основе обновленной статистики
   */
  async handleDailyRewardsCalculation() {
    this.logger.log('Запущен ежедневный расчёт вознаграждений и прогнозов');
    
    try {
      // Получаем текущую дату
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1; // JavaScript месяцы с 0
      
      // Рассчитываем вознаграждения с прогнозами для всех пользователей
      await this.calculateAllRewardsWithProjections(year, month);
      
    } catch (error) {
      this.logger.error(`Ошибка при ежедневном расчёте вознаграждений: ${error.message}`);
    }
  }

  /**
   * Задача, запускаемая 1-го числа каждого месяца в 01:00
   * Запрашивает данные о торговле за предыдущий месяц и рассчитывает финальные вознаграждения
   */
  async handleMonthlyFinalRewardsCalculation() {
    this.logger.log('Запущен ежемесячный финальный расчет вознаграждений');
    
    try {
      // Получаем дату предыдущего месяца
      const now = new Date();
      let year = now.getFullYear();
      let month = now.getMonth(); // Предыдущий месяц (JavaScript месяцы с 0)
      
      // Корректируем год, если текущий месяц - январь
      if (month === 0) {
        month = 12;
        year -= 1;
      }
      
      // Сначала обновляем статистику торговли за прошлый месяц
      await this.updateTradeStatsForMonth(year, month);
      
      // Затем делаем финальный расчёт вознаграждений
      await this.calculateAllRewards(year, month);
      
    } catch (error) {
      this.logger.error(`Ошибка при ежемесячном расчете вознаграждений: ${error.message}`);
    }
  }

  /**
   * Обновление торговой статистики за указанный месяц
   */
  private async updateTradeStatsForMonth(year: number, month: number) {
    this.logger.log(`Обновление торговой статистики за ${month}/${year}`);
    
    try {
      // Получаем всех пользователей за указанный период
      const allReferals = await this.referalRepository.find({
        where: { year, month }
      });
      
      this.logger.log(`Найдено ${allReferals.length} пользователей для финального обновления статистики`);
      
      let updatedCount = 0;
      for (const referal of allReferals) {
        try {
          // Запрашиваем финальные данные о торговле за месяц
          await this.referalService.requestTradeStats(referal.userId, year, month);
          updatedCount++;
          
          // Небольшая пауза между запросами
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          this.logger.error(`Ошибка при финальном обновлении статистики для пользователя ${referal.userId}: ${error.message}`);
        }
      }
      
      this.logger.log(`Финально обновлена статистика для ${updatedCount} из ${allReferals.length} пользователей`);
      
    } catch (error) {
      this.logger.error(`Ошибка при обновлении торговой статистики за месяц: ${error.message}`);
    }
  }
  
  /**
   * Рассчитывает вознаграждения для всех пользователей за указанный месяц
   * с добавлением прогнозных данных
   */
  private async calculateAllRewardsWithProjections(year: number, month: number) {
    this.logger.log(`Расчет текущих и прогнозируемых вознаграждений за ${month}/${year}`);
    
    try {
      // Получаем всех пользователей с рефералами
      const referrersData = await this.referalRepository
        .createQueryBuilder('referal')
        .select('referal.userId')
        .where('referal.totalReferrals > 0')
        .distinctOn(['referal.userId'])
        .getRawMany();
      
      this.logger.log(`Найдено ${referrersData.length} пользователей с рефералами для расчёта вознаграждений`);
      
      // Рассчитываем вознаграждения для каждого
      let calculatedCount = 0;
      for (const referrerData of referrersData) {
        try {
          // Сначала получаем актуальные данные о рефералах
          const userReferrals = await this.referalRepository.find({
            where: { 
              referrerId: referrerData.userId,
              year,
              month
            }
          });
          
          // Если есть рефералы, обновляем статистику и делаем прогноз
          if (userReferrals.length > 0) {
            await this.updateReferrerStatsWithProjections(referrerData.userId, year, month);
          }
          
          calculatedCount++;
          
          // Небольшая пауза между расчётами
          await new Promise(resolve => setTimeout(resolve, 50));
        } catch (error) {
          this.logger.error(`Ошибка при расчете вознаграждений с прогнозом для пользователя ${referrerData.userId}: ${error.message}`);
        }
      }
      
      this.logger.log(`Рассчитаны вознаграждения с прогнозом для ${calculatedCount} из ${referrersData.length} пользователей`);
    } catch (error) {
      this.logger.error(`Ошибка при расчете вознаграждений с прогнозом: ${error.message}`);
    }
  }
  
  /**
   * Стандартный расчет вознаграждений для всех пользователей за указанный месяц
   * (используется для финальных расчетов в начале месяца)
   */
  private async calculateAllRewards(year: number, month: number) {
    this.logger.log(`Финальный расчет вознаграждений за ${month}/${year}`);
    
    try {
      // Получаем всех пользователей с рефералами
      const referrersData = await this.referalRepository
        .createQueryBuilder('referal')
        .select('referal.userId')
        .where('referal.totalReferrals > 0')
        .distinctOn(['referal.userId'])
        .getRawMany();
      
      this.logger.log(`Найдено ${referrersData.length} пользователей с рефералами для финального расчёта`);
      
      // Рассчитываем вознаграждения для каждого
      let calculatedCount = 0;
      for (const referrerData of referrersData) {
        try {
          await this.referalService.calculateReward({
            userId: referrerData.userId,
            year,
            month
          });
          calculatedCount++;
          
          // Небольшая пауза между запросами
          await new Promise(resolve => setTimeout(resolve, 100));
        } catch (error) {
          this.logger.error(`Ошибка при финальном расчете вознаграждений для пользователя ${referrerData.userId}: ${error.message}`);
        }
      }
      
      this.logger.log(`Рассчитаны финальные вознаграждения для ${calculatedCount} из ${referrersData.length} пользователей`);
    } catch (error) {
      this.logger.error(`Ошибка при расчете финальных вознаграждений: ${error.message}`);
    }
  }
  
  /**
   * Обновление статистики с учетом прогнозов
   */
  private async updateReferrerStatsWithProjections(referrerId: number, year: number, month: number) {
    this.logger.log(`Обновление статистики с прогнозом для пользователя ${referrerId} за ${month}/${year}`);
    
    try {
      // Находим приглашающего
      const referrer = await this.referalRepository.findOne({
        where: { userId: referrerId, year, month }
      });
      
      if (!referrer) {
        this.logger.warn(`Приглашающий ${referrerId} не найден для периода ${month}/${year}`);
        return;
      }
      
      // Находим всех его рефералов
      const referrals = await this.referalRepository.find({
        where: { referrerId, year, month }
      });
      
      this.logger.log(`Найдено ${referrals.length} рефералов для пользователя ${referrerId}`);
      
      // Считаем общую статистику
      let totalTradingVolume = 0;
      let totalOrdersCount = 0;
      let totalCommission = 0;
      let activeReferrals = 0;
      
      referrals.forEach(referal => {
        totalTradingVolume += Number(referal.totalTradingVolume);
        totalOrdersCount += referal.totalOrdersCount;
        totalCommission += Number(referal.totalCommission);
        
        // Реферал считается активным, если совершил хотя бы одну сделку
        if (referal.totalOrdersCount > 0) {
          activeReferrals++;
        }
      });
      
      // Определяем уровень (tier) и процент вознаграждения
      let tierLevel = 0;
      let rewardPercentage = 0;
      
      // Tier 1: 20% при объеме до 15,000 USDT и минимум 3 активных рефералах
      if (totalTradingVolume <= 15000 && activeReferrals >= 3) {
        tierLevel = 1;
        rewardPercentage = 20;
      }
      // Tier 2: 40% при объеме 15,000-40,000 USDT и минимум 8 активных рефералах
      else if (totalTradingVolume > 15000 && totalTradingVolume <= 40000 && activeReferrals >= 8) {
        tierLevel = 2;
        rewardPercentage = 40;
      }
      // Tier 3: 60% при объеме более 40,000 USDT и минимум 15 активных рефералах
      else if (totalTradingVolume > 40000 && activeReferrals >= 15) {
        tierLevel = 3;
        rewardPercentage = 60;
      }
      
      // Рассчитываем сумму вознаграждения
      const rewardAmount = totalCommission * (rewardPercentage / 100);
      
      // Добавляем прогнозные показатели
      // Расчет прогноза до конца месяца
      const now = new Date();
      const daysInMonth = new Date(year, month, 0).getDate();
      const currentDay = now.getDate();
      const remainingDays = daysInMonth - currentDay;
      
      // Прогноз на основе среднедневных показателей
      const dailyAvgVolume = currentDay > 0 ? totalTradingVolume / currentDay : 0;
      const dailyAvgCommission = currentDay > 0 ? totalCommission / currentDay : 0;
      
      const projectedTotalVolume = totalTradingVolume + (dailyAvgVolume * remainingDays);
      const projectedTotalCommission = totalCommission + (dailyAvgCommission * remainingDays);
      
      // Прогноз уровня вознаграждения по прогнозируемым данным
      let projectedTierLevel = 0;
      let projectedRewardPercentage = 0;
      
      if (projectedTotalVolume <= 15000 && activeReferrals >= 3) {
        projectedTierLevel = 1;
        projectedRewardPercentage = 20;
      } else if (projectedTotalVolume > 15000 && projectedTotalVolume <= 40000 && activeReferrals >= 8) {
        projectedTierLevel = 2;
        projectedRewardPercentage = 40;
      } else if (projectedTotalVolume > 40000 && activeReferrals >= 15) {
        projectedTierLevel = 3;
        projectedRewardPercentage = 60;
      }
      
      const projectedRewardAmount = projectedTotalCommission * (projectedRewardPercentage / 100);
      
      // Обновляем данные приглашающего
      referrer.activeReferrals = activeReferrals;
      referrer.totalTradingVolume = totalTradingVolume;
      referrer.totalOrdersCount = totalOrdersCount;
      referrer.totalCommission = totalCommission;
      referrer.rewardPercentage = rewardPercentage;
      referrer.rewardAmount = rewardAmount;
      referrer.tierLevel = tierLevel;
      
      // Добавляем поля прогноза
      referrer.projectedTotalVolume = projectedTotalVolume;
      referrer.projectedTotalCommission = projectedTotalCommission;
      referrer.projectedTierLevel = projectedTierLevel;
      referrer.projectedRewardPercentage = projectedRewardPercentage;
      referrer.projectedRewardAmount = projectedRewardAmount;
      
      await this.referalRepository.save(referrer);
      this.logger.log(`Статистика с прогнозом обновлена для пользователя ${referrerId}:
        Текущие данные: Уровень ${tierLevel}, Процент ${rewardPercentage}%, Сумма ${rewardAmount}
        Прогноз: Уровень ${projectedTierLevel}, Процент ${projectedRewardPercentage}%, Сумма ${projectedRewardAmount}`);
      
    } catch (error) {
      this.logger.error(`Ошибка при обновлении статистики с прогнозом для пользователя ${referrerId}: ${error.message}`);
    }
  }
}