import { Controller, Get, Post, Body, Param, Query, Logger } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ReferalService } from './referal.service';
import { 
  ProfileReferalDto, 
  TradeStatsDto, 
  CreateReferalDto, 
  CalculateRewardDto 
} from './dto/referal.dto';

@Controller('referals')
export class ReferalController {
  private readonly logger = new Logger(ReferalController.name);

  constructor(private readonly referalService: ReferalService) {}

  // Обработка сообщения от профиль-сервиса при создании нового профиля
  @MessagePattern('profile.created')
  async handleProfileCreated(@Payload() data: ProfileReferalDto) {
    this.logger.log(`Получено сообщение о создании профиля: ${JSON.stringify(data)}`);
    
    try {
      // Создание записи реферала
      const referal = await this.referalService.createReferal({
        userId: data.userId,
        referralCode: data.referralCode,
        referrerCode: data.referrerCode
      });

      this.logger.log(`Реферал создан: ${JSON.stringify(referal)}`);
      return { success: true, referalId: referal.id };
    } catch (error) {
      this.logger.error(`Ошибка при создании реферала: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  // Обработка сообщения от торгового сервиса с данными о торговле
  @MessagePattern('trade.stats')
  async handleTradeStats(@Payload() data: TradeStatsDto) {
    this.logger.log(`Получены данные о торговле: ${JSON.stringify(data)}`);
    
    try {
      // Обновление статистики торговли для реферала
      const stats = await this.referalService.updateTradeStats(data);
      
      this.logger.log(`Статистика торговли обновлена: ${JSON.stringify(stats)}`);
      return { success: true, stats };
    } catch (error) {
      this.logger.error(`Ошибка при обновлении статистики: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  // Запрос статистики для расчета вознаграждений по месяцам
  @MessagePattern('reward.calculate.monthly')
  async calculateMonthlyRewards(@Payload() data: CalculateRewardDto) {
    this.logger.log(`Запрос на расчет вознаграждений за ${data.month}/${data.year} для пользователя ${data.userId}`);
    
    try {
      const calculationResult = await this.referalService.calculateReward(data);
      
      this.logger.log(`Расчет вознаграждений завершен: ${JSON.stringify(calculationResult)}`);
      return { success: true, reward: calculationResult };
    } catch (error) {
      this.logger.error(`Ошибка при расчете вознаграждений: ${error.message}`);
      return { success: false, error: error.message };
    }
  }

  // REST API для запроса данных торговли из трейд-сервиса
  @Post('request-trade-stats')
  async requestTradeStats(@Body() data: { 
    userId: number; 
    year: number; 
    month: number; 
  }) {
    this.logger.log(`REST запрос статистики торговли для userId: ${data.userId}`);
    return this.referalService.requestTradeStats(data.userId, data.year, data.month);
  }

  // REST API для получения информации о реферале по userId
  @Get('user/:userId')
  async getReferalByUserId(
    @Param('userId') userId: number,
    @Query('year') year?: number,
    @Query('month') month?: number
  ) {
    return this.referalService.getReferalByUserId(userId, year, month);
  }

  // REST API для получения всех рефералов определенного пользователя
  @Get('user/:userId/referrals')
  async getUserReferrals(
    @Param('userId') userId: number,
    @Query('year') year?: number,
    @Query('month') month?: number
  ) {
    return this.referalService.getUserReferrals(userId, year, month);
  }

  // REST API для ручного расчета вознаграждения
  @Post('calculate-reward')
  async calculateReward(@Body() data: CalculateRewardDto) {
    return this.referalService.calculateReward(data);
  }


  @Post('add-to-balance')
async addRewardToBalance(@Body() data: { userId: number }) {
  this.logger.log(`Запрос на добавление реферальной суммы к балансу пользователя ${data.userId}`);
  return this.referalService.addRewardToBalance(data.userId);
}

/**
 * Получение всех рефералов для админ-панели с пагинацией
 */
@Get('admin/all')
async getAllReferalsForAdmin(
  @Query('page') page: number = 1,
  @Query('limit') limit: number = 20,
  @Query('year') year?: number,
  @Query('month') month?: number
) {
  this.logger.log(`Запрос всех рефералов для админки: страница ${page}, лимит ${limit}`);
  
  // Если год и месяц не указаны, берем текущие
  if (!year || !month) {
    const currentDate = new Date();
    year = year || currentDate.getFullYear();
    month = month || currentDate.getMonth() + 1;
  }
  
  return this.referalService.getAllReferalsForAdmin(page, limit, year, month);
}

/**
 * Получение статистики по реферальной программе для админ-панели
 */
@Get('admin/statistics')
async getReferalStatistics(
  @Query('year') year?: number,
  @Query('month') month?: number
) {
  this.logger.log('Запрос статистики по реферальной программе');
  
  // Если год и месяц не указаны, берем текущие
  if (!year || !month) {
    const currentDate = new Date();
    year = year || currentDate.getFullYear();
    month = month || currentDate.getMonth() + 1;
  }
  
  return this.referalService.getReferalStatistics(year, month);
}

/**
 * Получение детальной информации о реферале для админ-панели
 */
@Get('admin/detail/:userId')
async getReferalDetail(
  @Param('userId') userId: number,
  @Query('year') year?: number,
  @Query('month') month?: number
) {
  this.logger.log(`Запрос детальной информации о реферале ${userId}`);
  
  // Если год и месяц не указаны, берем текущие
  if (!year || !month) {
    const currentDate = new Date();
    year = year || currentDate.getFullYear();
    month = month || currentDate.getMonth() + 1;
  }
  
  // Получаем информацию о реферале
  const referal = await this.referalService.getReferalByUserId(userId, year, month);
  
  // Получаем информацию о его рефералах
  const referrals = await this.referalService.getUserReferrals(userId, year, month);
  
  return {
    referal,
    referrals: referrals.referrals
  };
}

/**
 * Получение списка всех активных рефереров с их статистикой
 */
@Get('admin/referrers')
async getActiveReferrers(
  @Query('page') page: number = 1,
  @Query('limit') limit: number = 20,
  @Query('year') year?: number,
  @Query('month') month?: number
) {
  this.logger.log(`Запрос активных рефереров: страница ${page}, лимит ${limit}`);
  
  // Если год и месяц не указаны, берем текущие
  if (!year || !month) {
    const currentDate = new Date();
    year = year || currentDate.getFullYear();
    month = month || currentDate.getMonth() + 1;
  }
  
  return this.referalService.getActiveReferrers(page, limit, year, month);
}





/**
 * Получение статистики для дашборда пользователя
 * Возвращает: Total Referrals, Active Referrals, Total Earnings, Pending Earnings
 */
@Get('user/:userId/dashboard')
async getUserDashboard(
  @Param('userId') userId: number,
  @Query('year') year?: number,
  @Query('month') month?: number
) {
  this.logger.log(`Запрос дашборда для пользователя ${userId}`);
  
  // Если год и месяц не указаны, берем текущие
  if (!year || !month) {
    const currentDate = new Date();
    year = year || currentDate.getFullYear();
    month = month || currentDate.getMonth() + 1;
  }
  
  return this.referalService.getUserDashboard(userId, year, month);
}




}



