import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('referals')
export class Referal {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ name: 'user_id', unique: false })
  userId: number;

  @Column({ name: 'referral_code', unique: true })
  referralCode: string;

  @Column({ name: 'referrer_id', nullable: true, type: 'int' })
  referrerId?: number | null;

  @Column({ name: 'total_referrals', type: 'int', default: 0 })
  totalReferrals: number;
  
  @Column({ name: 'active_referrals', type: 'int', default: 0 })
  activeReferrals: number;

  @Column({ name: 'total_trading_volume', type: 'decimal', precision: 20, scale: 8, default: 0 })
  totalTradingVolume: number;

  @Column({ name: 'total_orders_count', type: 'int', default: 0 })
  totalOrdersCount: number;

  @Column({ name: 'total_commission', type: 'decimal', precision: 20, scale: 8, default: 0 })
  totalCommission: number;

  @Column({ name: 'reward_percentage', type: 'decimal', precision: 5, scale: 2, default: 0 })
  rewardPercentage: number;

  @Column({ name: 'reward_amount', type: 'decimal', precision: 20, scale: 8, default: 0 })
  rewardAmount: number;

  @Column({ name: 'tier_level', type: 'int', default: 0 })
  tierLevel: number;

  @Column({ name: 'year', type: 'int', default: 0 })
  year: number;

  @Column({ name: 'month', type: 'int', default: 0 })
  month: number;

  // Поля для прогнозных значений
  @Column({ name: 'projected_total_volume', type: 'decimal', precision: 20, scale: 8, default: 0 })
  projectedTotalVolume: number;

  @Column({ name: 'projected_total_commission', type: 'decimal', precision: 20, scale: 8, default: 0 })
  projectedTotalCommission: number;

  @Column({ name: 'projected_tier_level', type: 'int', default: 0 })
  projectedTierLevel: number;

  @Column({ name: 'projected_reward_percentage', type: 'decimal', precision: 5, scale: 2, default: 0 })
  projectedRewardPercentage: number;

  @Column({ name: 'projected_reward_amount', type: 'decimal', precision: 20, scale: 8, default: 0 })
  projectedRewardAmount: number;

  @Column({ name: 'created_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP' })
  createdAt: Date;

  @Column({ name: 'updated_at', type: 'timestamp', default: () => 'CURRENT_TIMESTAMP', onUpdate: 'CURRENT_TIMESTAMP' })
  updatedAt: Date;
}