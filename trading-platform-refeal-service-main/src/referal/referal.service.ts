import { Injectable, Logger, NotFoundException, Inject } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MoreThan, Repository } from 'typeorm';
import { Referal } from './referal.entity';
import { ClientKafka } from '@nestjs/microservices';
import { CreateReferalDto, ProfileReferalDto, TradeStatsDto, CalculateRewardDto } from './dto/referal.dto';

@Injectable()
export class ReferalService {
  private readonly logger = new Logger(ReferalService.name);

  constructor(
    @InjectRepository(Referal)
    private referalRepository: Repository<Referal>,
    
    @Inject('KAFKA_SERVICE')
    private readonly kafkaClient: ClientKafka,
  ) {}

  async onModuleInit() {
    // Подписываемся на необходимые топики
    this.kafkaClient.subscribeToResponseOf('trade.request.stats');
    await this.kafkaClient.connect();
    this.logger.log('Kafka клиент подключен');
  }

  // Создание нового реферала при получении данных из профиль-сервиса
  async createReferal(data: CreateReferalDto): Promise<Referal> {
    this.logger.log(`Создание нового реферала: ${JSON.stringify(data)}`);
    
    // Проверяем, не существует ли уже реферал с таким userId
    const existingReferal = await this.referalRepository.findOne({
      where: { userId: data.userId }
    });
    
    if (existingReferal) {
      this.logger.log(`Реферал с userId ${data.userId} уже существует`);
      return existingReferal;
    }
    
    // Если указан referrerCode, ищем реферала-приглашающего
    let referrer: Referal | null = null;
    if (data.referrerCode) {
      referrer = await this.referalRepository.findOne({
        where: { referralCode: data.referrerCode }
      });
      
      if (referrer) {
        this.logger.log(`Найден пригласивший пользователь с userId: ${referrer.userId}`);
        
        // Увеличиваем счетчик рефералов у приглашающего
        referrer.totalReferrals += 1;
        await this.referalRepository.save(referrer);
      } else {
        this.logger.warn(`Пригласивший с кодом ${data.referrerCode} не найден`);
      }
    }
    
    // Создаем новый реферал с текущими годом и месяцем
    const currentDate = new Date();
    const newReferal = new Referal();
    newReferal.userId = data.userId;
    newReferal.referralCode = data.referralCode;
    newReferal.referrerId = referrer ? referrer.userId : null;
    newReferal.year = currentDate.getFullYear();
    newReferal.month = currentDate.getMonth() + 1; // +1 т.к. месяцы в JS с 0
    newReferal.totalReferrals = 0;
    newReferal.activeReferrals = 0;
    newReferal.totalTradingVolume = 0;
    newReferal.totalOrdersCount = 0;
    newReferal.totalCommission = 0;
    newReferal.rewardPercentage = 0;
    newReferal.rewardAmount = 0;
    newReferal.tierLevel = 0;
    
    // Сохраняем в базе
    const savedReferal = await this.referalRepository.save(newReferal);
    this.logger.log(`Реферал успешно создан: ${JSON.stringify(savedReferal)}`);
    
    return savedReferal;
  }

  // Обновление статистики торговли для реферала
  async updateTradeStats(statsData: TradeStatsDto): Promise<Referal> {
    this.logger.log(`Обновление статистики торговли: ${JSON.stringify(statsData)}`);
    
    // Находим реферала по userId
    let referal = await this.referalRepository.findOne({
      where: { userId: statsData.userId, year: statsData.year, month: statsData.month }
    });
    
    if (!referal) {
      // Попробуем найти существующую запись реферала для данного пользователя
      const existingReferal = await this.referalRepository.findOne({
        where: { userId: statsData.userId }
      });
      
      // Если не найден, создаем новую запись за указанный месяц/год
      const newReferal = new Referal();
      newReferal.userId = statsData.userId;
      newReferal.referralCode = existingReferal ? existingReferal.referralCode : `ref-${statsData.userId}`;
      newReferal.referrerId = existingReferal ? existingReferal.referrerId : null;
      newReferal.year = statsData.year;
      newReferal.month = statsData.month;
      newReferal.totalTradingVolume = statsData.tradingVolume;
      newReferal.totalOrdersCount = statsData.ordersCount;
      newReferal.totalCommission = statsData.commissionAmount;
      newReferal.totalReferrals = existingReferal ? existingReferal.totalReferrals : 0;
      newReferal.activeReferrals = 0;
      newReferal.rewardPercentage = 0;
      newReferal.rewardAmount = 0;
      newReferal.tierLevel = 0;
      
      const savedReferal = await this.referalRepository.save(newReferal);
      this.logger.log(`Создана новая запись статистики: ${JSON.stringify(savedReferal)}`);
      
      // После создания новой записи необходимо обновить статистику приглашающего
      if (savedReferal.referrerId) {
        await this.updateReferrerStats(savedReferal.referrerId, statsData.year, statsData.month);
      }
      
      return savedReferal;
    }
    
    // Обновляем статистику
    referal.totalTradingVolume = statsData.tradingVolume;
    referal.totalOrdersCount = statsData.ordersCount;
    referal.totalCommission = statsData.commissionAmount;
    
    // Сохраняем обновленную статистику
    const updatedReferal = await this.referalRepository.save(referal);
    this.logger.log(`Статистика торговли обновлена: ${JSON.stringify(updatedReferal)}`);
    
    // Обновляем статистику приглашающего
    if (updatedReferal.referrerId) {
      await this.updateReferrerStats(updatedReferal.referrerId, statsData.year, statsData.month);
    }
    
    return updatedReferal;
  }

  // Обновление статистики приглашающего пользователя
  async updateReferrerStats(referrerId: number, year: number, month: number): Promise<void> {
    this.logger.log(`Обновление статистики приглашающего userId: ${referrerId} за ${month}/${year}`);
    
    // Находим приглашающего
    const referrer = await this.referalRepository.findOne({
      where: { userId: referrerId, year, month }
    });
    
    if (!referrer) {
      this.logger.warn(`Приглашающий ${referrerId} не найден для периода ${month}/${year}`);
      return;
    }
    
    // Находим всех его рефералов
    const referrals = await this.referalRepository.find({
      where: { referrerId, year, month }
    });
    
    this.logger.log(`Найдено ${referrals.length} рефералов для пользователя ${referrerId}`);
    
    // Считаем общую статистику
    let totalTradingVolume = 0;
    let totalOrdersCount = 0;
    let totalCommission = 0;
    let activeReferrals = 0;
    
    referrals.forEach(referal => {
      totalTradingVolume += Number(referal.totalTradingVolume);
      totalOrdersCount += referal.totalOrdersCount;
      totalCommission += Number(referal.totalCommission);
      
      // Реферал считается активным, если совершил хотя бы одну сделку
      if (referal.totalOrdersCount > 0) {
        activeReferrals++;
      }
    });
    
    // Определяем уровень (tier) и процент вознаграждения
    let tierLevel = 0;
    let rewardPercentage = 0;
    
    // Tier 1: 20% при объеме до 15,000 USDT и минимум 3 активных рефералах
    if (totalTradingVolume <= 15000 && activeReferrals >= 3) {
      tierLevel = 1;
      rewardPercentage = 20;
    }
    // Tier 2: 40% при объеме 15,000-40,000 USDT и минимум 8 активных рефералах
    else if (totalTradingVolume > 15000 && totalTradingVolume <= 40000 && activeReferrals >= 8) {
      tierLevel = 2;
      rewardPercentage = 40;
    }
    // Tier 3: 60% при объеме более 40,000 USDT и минимум 15 активных рефералах
    else if (totalTradingVolume > 40000 && activeReferrals >= 15) {
      tierLevel = 3;
      rewardPercentage = 60;
    }
    
    // Рассчитываем сумму вознаграждения
    const rewardAmount = totalCommission * (rewardPercentage / 100);
    
    // Обновляем данные приглашающего
    referrer.activeReferrals = activeReferrals;
    referrer.totalTradingVolume = totalTradingVolume;
    referrer.totalOrdersCount = totalOrdersCount;
    referrer.totalCommission = totalCommission;
    referrer.rewardPercentage = rewardPercentage;
    referrer.rewardAmount = rewardAmount;
    referrer.tierLevel = tierLevel;
    
    await this.referalRepository.save(referrer);
    this.logger.log(`Статистика приглашающего обновлена: ${JSON.stringify(referrer)}`);
  }

  // Получение информации о реферале по userId
  async getReferalByUserId(userId: number, year?: number, month?: number): Promise<Referal> {
    // Если год и месяц не указаны, берем текущие
    if (!year || !month) {
      const currentDate = new Date();
      year = year || currentDate.getFullYear();
      month = month || currentDate.getMonth() + 1;
    }
    
    const referal = await this.referalRepository.findOne({
      where: { userId, year, month }
    });
    
    if (!referal) {
      throw new NotFoundException(`Реферал с userId ${userId} за период ${month}/${year} не найден`);
    }
    
    return referal;
  }

  // Получение всех рефералов пользователя
  async getUserReferrals(userId: number, year?: number, month?: number) {
    // Если год и месяц не указаны, берем текущие
    if (!year || !month) {
      const currentDate = new Date();
      year = year || currentDate.getFullYear();
      month = month || currentDate.getMonth() + 1;
    }
    
    const referrals = await this.referalRepository.find({
      where: { referrerId: userId, year, month }
    });
    
    return {
      userId,
      period: `${month}/${year}`,
      totalReferrals: referrals.length,
      referrals
    };
  }

  // Расчет вознаграждения для заданного пользователя
  async calculateReward(data: CalculateRewardDto) {
    this.logger.log(`Расчет вознаграждения для userId: ${data.userId} за период ${data.month}/${data.year}`);
    
    try {
      // Находим реферала
      let referal;
      try {
        referal = await this.getReferalByUserId(data.userId, data.year, data.month);
      } catch (error) {
        // Если реферал не найден, создаем пустую запись со значениями по умолчанию
        return {
          userId: data.userId,
          year: data.year,
          month: data.month,
          totalReferrals: 0,
          activeReferrals: 0,
          totalTradingVolume: 0,
          totalOrdersCount: 0,
          totalCommission: 0,
          rewardPercentage: 0,
          rewardAmount: 0,
          tierLevel: 0
        };
      }
      
      // Если ни одного реферала у пользователя нет, возвращаем нулевое вознаграждение
      if (referal.totalReferrals === 0) {
        return {
          userId: data.userId,
          year: data.year,
          month: data.month,
          totalReferrals: 0,
          activeReferrals: 0,
          totalTradingVolume: 0,
          totalOrdersCount: 0,
          totalCommission: 0,
          rewardPercentage: 0,
          rewardAmount: 0,
          tierLevel: 0
        };
      }
      
      // Запускаем обновление статистики пригласившего
      await this.updateReferrerStats(data.userId, data.year, data.month);
      
      // Получаем обновленные данные
      const updatedReferal = await this.getReferalByUserId(data.userId, data.year, data.month);
      
      return {
        userId: updatedReferal.userId,
        year: updatedReferal.year,
        month: updatedReferal.month,
        totalReferrals: updatedReferal.totalReferrals,
        activeReferrals: updatedReferal.activeReferrals,
        totalTradingVolume: updatedReferal.totalTradingVolume,
        totalOrdersCount: updatedReferal.totalOrdersCount,
        totalCommission: updatedReferal.totalCommission,
        rewardPercentage: updatedReferal.rewardPercentage,
        rewardAmount: updatedReferal.rewardAmount,
        tierLevel: updatedReferal.tierLevel
      };
    } catch (error) {
      this.logger.error(`Ошибка при расчете вознаграждения: ${error.message}`);
      throw error;
    }
  }

  // Получение статистики торговли из сервиса торговли через Kafka
  async requestTradeStats(userId: number, year: number, month: number) {
    this.logger.log(`Запрос статистики торговли для userId: ${userId} за период ${month}/${year}`);
    
    try {
      // Проверяем подключение к Kafka
      if (!this.kafkaClient.connect) {
        await this.kafkaClient.connect();
        this.logger.log('Kafka клиент переподключен');
      }
      
      // Добавим больше логов
      this.logger.log(`Отправляем запрос в Kafka: trade.request.stats, данные: ${JSON.stringify({ userId, year, month })}`);
      
      // Отправляем запрос в сервис торговли
      const tradeStats = await this.kafkaClient.send(
        'trade.request.stats',
        { userId, year, month }
      ).toPromise();
      
      this.logger.log(`Получена статистика торговли: ${JSON.stringify(tradeStats)}`);
      
      // Если ответ пустой или null, логируем предупреждение
      if (!tradeStats) {
        this.logger.warn(`Получен пустой ответ от торгового сервиса для пользователя ${userId}`);
        return null;
      }
      
      // Обновляем статистику в нашей базе
      await this.updateTradeStats({
        userId,
        year,
        month,
        tradingVolume: tradeStats.tradingVolume || 0,
        ordersCount: tradeStats.ordersCount || 0,
        commissionAmount: tradeStats.commissionAmount || 0
      });
      
      return tradeStats;
    } catch (error) {
      this.logger.error(`Ошибка при запросе статистики торговли: ${error.message}`);
      throw error;
    }
  }



  // referal.service.ts - добавить новый метод
/**
 * Добавление реферальной суммы к балансу пользователя
 */
async addRewardToBalance(userId: number) {
  this.logger.log(`Добавление реферальной суммы к балансу пользователя ${userId}`);
  
  try {
    // Получаем текущую дату
    const now = new Date();
    const year = now.getFullYear();
    const month = now.getMonth() + 1; // JavaScript месяцы с 0
    
    // Находим реферальную запись пользователя
    const referal = await this.getReferalByUserId(userId, year, month);
    
    if (!referal) {
      throw new NotFoundException(`Реферал с userId ${userId} за период ${month}/${year} не найден`);
    }
    
    // Проверяем, есть ли доступная для вывода сумма
    if (referal.rewardAmount <= 0) {
      return {
        success: false,
        message: 'Нет доступной суммы для добавления к балансу',
        amount: 0
      };
    }
    
    // Сумма для добавления к балансу
    const amount = Number(referal.rewardAmount);
    
    // Отправляем запрос в сервис баланса через Kafka
    this.kafkaClient.emit(
      'balance.add.referal.reward',
      { 
        userId, 
        amount,
        referalPeriod: `${month}/${year}`,
        timestamp: new Date().toISOString()
      }
    );
    
    // Обнуляем сумму вознаграждения в реферальной системе
    // поскольку она уже добавлена к балансу
    referal.rewardAmount = 0;
    await this.referalRepository.save(referal);
    
    this.logger.log(`Реферальная сумма ${amount} успешно отправлена для добавления к балансу пользователя ${userId}`);
    
    return {
      success: true,
      message: 'Сумма успешно добавлена к балансу',
      amount
    };
  } catch (error) {
    this.logger.error(`Ошибка при добавлении реферальной суммы к балансу: ${error.message}`);
    throw error;
  }
}
  
/**
 * Получение всех рефералов для админ-панели с пагинацией
 */
async getAllReferalsForAdmin(page: number, limit: number, year: number, month: number) {
  this.logger.log(`Получение всех рефералов для админки: страница ${page}, лимит ${limit}, период ${month}/${year}`);
  
  const skip = (page - 1) * limit;
  
  const [referals, total] = await this.referalRepository.findAndCount({
    where: { year, month },
    skip,
    take: limit,
    order: {
      totalReferrals: 'DESC',
      totalTradingVolume: 'DESC'
    }
  });
  
  const pages = Math.ceil(total / limit);
  
  return {
    items: referals,
    total,
    page,
    limit,
    pages
  };
}

/**
 * Получение статистики по реферальной программе для админ-панели
 */
async getReferalStatistics(year: number, month: number) {
  this.logger.log(`Получение статистики по реферальной программе за ${month}/${year}`);
  
  // Получаем общее количество рефералов
  const totalReferals = await this.referalRepository.count({
    where: { year, month }
  });
  
  // Получаем количество активных рефереров (тех, у кого есть рефералы)
  const activeReferrers = await this.referalRepository
    .createQueryBuilder('referal')
    .where('referal.year = :year', { year })
    .andWhere('referal.month = :month', { month })
    .andWhere('referal.totalReferrals > 0')
    .getCount();
  
  // Получаем общий объем торговли
  const totalVolumeResult = await this.referalRepository
    .createQueryBuilder('referal')
    .select('SUM(referal.totalTradingVolume)', 'sum')
    .where('referal.year = :year', { year })
    .andWhere('referal.month = :month', { month })
    .getRawOne();
  
  const totalVolume = totalVolumeResult?.sum || 0;
  
  // Получаем общую сумму комиссий
  const totalCommissionResult = await this.referalRepository
    .createQueryBuilder('referal')
    .select('SUM(referal.totalCommission)', 'sum')
    .where('referal.year = :year', { year })
    .andWhere('referal.month = :month', { month })
    .getRawOne();
  
  const totalCommission = totalCommissionResult?.sum || 0;
  
  // Получаем общую сумму вознаграждений
  const totalRewardResult = await this.referalRepository
    .createQueryBuilder('referal')
    .select('SUM(referal.rewardAmount)', 'sum')
    .where('referal.year = :year', { year })
    .andWhere('referal.month = :month', { month })
    .getRawOne();
  
  const totalReward = totalRewardResult?.sum || 0;
  
  // Статистика по уровням (tier)
  const tier1Count = await this.referalRepository.count({
    where: { year, month, tierLevel: 1 }
  });
  
  const tier2Count = await this.referalRepository.count({
    where: { year, month, tierLevel: 2 }
  });
  
  const tier3Count = await this.referalRepository.count({
    where: { year, month, tierLevel: 3 }
  });
  
  return {
    totalReferals,
    activeReferrers,
    totalVolume,
    totalCommission,
    totalReward,
    tierStatistics: {
      tier1: tier1Count,
      tier2: tier2Count,
      tier3: tier3Count
    }
  };
}

/**
 * Получение списка активных рефереров
 */
async getActiveReferrers(page: number, limit: number, year: number, month: number) {
  this.logger.log(`Получение активных рефереров: страница ${page}, лимит ${limit}, период ${month}/${year}`);
  
  const skip = (page - 1) * limit;
  
  const [referrers, total] = await this.referalRepository.findAndCount({
    where: { year, month, totalReferrals: MoreThan(0) },
    skip,
    take: limit,
    order: {
      totalReferrals: 'DESC',
      rewardAmount: 'DESC'
    }
  });
  
  const pages = Math.ceil(total / limit);
  
  return {
    items: referrers,
    total,
    page,
    limit,
    pages
  };
}





/**
 * Получение данных для дашборда пользователя
 * Total Referrals, Active Referrals, Total Earnings, Pending Earnings
 */
async getUserDashboard(userId: number, year: number, month: number) {
  this.logger.log(`Получение данных дашборда для пользователя ${userId} за ${month}/${year}`);
  
  try {
    // Получаем данные пользователя за текущий месяц
    let currentMonthReferal;
    try {
      currentMonthReferal = await this.getReferalByUserId(userId, year, month);
    } catch (error) {
      // Если данных за текущий месяц нет, возвращаем нулевые значения
      return {
        totalReferrals: 0,
        activeReferrals: 0,
        totalEarnings: 0,
        pendingEarnings: 0
      };
    }
    
    // 1. Total Referrals - общее количество рефералов за все время
    const totalReferralsCount = await this.referalRepository.count({
      where: { referrerId: userId }
    });
    
    // 2. Active Referrals - активные рефералы в текущем месяце
    const activeReferrals = currentMonthReferal.activeReferrals || 0;
    
    // 3. Total Earnings - общие заработанные средства за все время
    const totalEarningsResult = await this.referalRepository
      .createQueryBuilder('referal')
      .select('SUM(referal.rewardAmount)', 'totalRewards')
      .where('referal.userId = :userId', { userId })
      .andWhere('referal.rewardAmount > 0')
      .getRawOne();
    
    const totalEarnings = Number(totalEarningsResult?.totalRewards || 0);
    
    // 4. Pending Earnings - ПРОГНОЗИРУЕМЫЙ доход за текущий месяц
    // Используем projectedRewardAmount, который рассчитывается планировщиком
    const pendingEarnings = Number(currentMonthReferal.projectedRewardAmount || 0);
    
    const dashboardData = {
      totalReferrals: totalReferralsCount,
      activeReferrals: activeReferrals,
      totalEarnings: totalEarnings,
      pendingEarnings: pendingEarnings
    };
    
    this.logger.log(`Дашборд для пользователя ${userId}: ${JSON.stringify(dashboardData)}`);
    
    return dashboardData;
    
  } catch (error) {
    this.logger.error(`Ошибка при получении дашборда для пользователя ${userId}: ${error.message}`);
    throw error;
  }
}



}