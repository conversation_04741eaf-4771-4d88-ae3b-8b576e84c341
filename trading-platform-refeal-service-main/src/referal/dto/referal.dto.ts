// DTOs для реферальной системы

// DTO для данных с профиль-сервиса
export class ProfileReferalDto {
    userId: number;
    email: string;
    firstName: string;
    lastName: string;
    referralCode: string;
    referrerCode?: string;
  }
  
  // DTO для данных с трейд-сервиса
  export class TradeStatsDto {
    userId: number;
    year: number;
    month: number;
    tradingVolume: number;
    ordersCount: number;
    commissionAmount: number;
  }
  
  // DTO для создания реферала
  export class CreateReferalDto {
    userId: number;
    referralCode: string;
    referrerCode?: string;
  }
  
  // DTO для расчета вознаграждения
  export class CalculateRewardDto {
    userId: number;
    year: number;
    month: number;
  }
  
  // DTO для результата расчета вознаграждения
  export class RewardResultDto {
    userId: number;
    year: number;
    month: number;
    totalReferrals: number;
    activeReferrals: number;
    totalTradingVolume: number;
    totalOrdersCount: number;
    totalCommission: number;
    rewardPercentage: number;
    rewardAmount: number;
    tierLevel: number;
  }