import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Referal } from './referal.entity';
import { ReferalService } from './referal.service';
import { ReferalController } from './referal.controller';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { SchedulerService } from './sheduler.service';


@Module({
  imports: [
    TypeOrmModule.forFeature([Referal]),

    // ✅ Добавляем Kafka-клиент для отправки сообщений
    ClientsModule.register([
      {
        name: 'KAFKA_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            brokers: ['172.31.10.207:9093'],
          },
          consumer: {
            groupId: 'referal-service-consumer',
          },
        },
      },
    ]),
  ],
  controllers: [ReferalController],
  providers: [ReferalService, SchedulerService],
  exports: [ReferalService], // ✅ Экспортируем, если надо использовать в других модулях
})
export class ReferalModule {}
