0
71
__consumer_offsets 22 0
transaction-events 0 19
auth.deleteUserByEmail.reply 0 1
profile.getUserIdByEmail.reply 0 11
__consumer_offsets 30 0
__consumer_offsets 8 39
__consumer_offsets 21 51
__consumer_offsets 4 0
__consumer_offsets 27 159
__consumer_offsets 7 0
__consumer_offsets 9 0
__consumer_offsets 46 0
__consumer_offsets 25 0
__consumer_offsets 35 0
__consumer_offsets 41 0
wallet-service-requests 0 34
__consumer_offsets 33 0
__consumer_offsets 23 0
__consumer_offsets 49 0
profile.request 0 0
__consumer_offsets 47 0
__consumer_offsets 16 0
__consumer_offsets 28 0
auth.updateTokenAfterProfileCreation.reply 0 0
__consumer_offsets 31 13030
__consumer_offsets 36 0
__consumer_offsets 42 0
__consumer_offsets 3 0
profile.comment.request 0 0
user.email.updated.reply 0 0
__consumer_offsets 18 0
auth.deleteUserByEmail 0 1
__consumer_offsets 37 0
auth.updateToken 0 3
wallet-events 0 26
__consumer_offsets 15 45
__consumer_offsets 24 0
auth.getUserByEmail.reply 0 3
auth.getUserByEmail 0 3
__consumer_offsets 38 0
__consumer_offsets 17 0
search.user 0 0
__consumer_offsets 48 69
__confluent.support.metrics 0 0
__consumer_offsets 19 0
__consumer_offsets 11 0
__consumer_offsets 13 0
__consumer_offsets 2 53
__consumer_offsets 43 0
__consumer_offsets 6 0
__consumer_offsets 14 0
auth.getUserByEmail.reply.reply 0 0
auth.updateToken.reply 0 2
user.email.updated 0 2
__consumer_offsets 20 0
__consumer_offsets 0 0
__consumer_offsets 44 0
__consumer_offsets 39 0
__consumer_offsets 12 0
profile.getUserIdByEmail 0 11
__consumer_offsets 45 0
__consumer_offsets 1 0
__consumer_offsets 5 0
__consumer_offsets 26 0
__consumer_offsets 29 0
__consumer_offsets 34 0
__consumer_offsets 10 0
__consumer_offsets 32 0
__consumer_offsets 40 0
balance-updates 0 19
wallet-service-responses 0 34
