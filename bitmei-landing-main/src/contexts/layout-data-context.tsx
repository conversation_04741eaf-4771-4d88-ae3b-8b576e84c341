"use client";

import React, { createContext, useContext } from "react";

type LayoutData = {
  heroTitle: string;
  heroSubtitle?: string;
};

const LayoutDataContext = createContext<LayoutData | null>(null);

export const useLayoutData = () => {
  const ctx = useContext(LayoutDataContext);
  if (!ctx)
    throw new Error("useLayoutData must be used inside LayoutDataProvider");
  return ctx;
};

export const LayoutDataProvider = ({
  value,
  children,
}: {
  value: LayoutData;
  children: React.ReactNode;
}) => {
  return (
    <LayoutDataContext.Provider value={value}>
      {children}
    </LayoutDataContext.Provider>
  );
};
