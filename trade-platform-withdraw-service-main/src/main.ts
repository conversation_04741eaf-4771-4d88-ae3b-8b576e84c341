import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';
import * as cors from 'cors';
import * as dotenv from 'dotenv';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Разрешаем CORS для всех методов
  app.use(cors({ origin: '*', methods: '*' }));

  // Подключаем Kafka Consumer
  const kafkaApp = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: Transport.KAFKA,
    options: {
      client: {
        brokers: ['172.31.10.207:9093'],
        connectionTimeout: 5000, // Добавляем явный таймаут
      },
      consumer: {
        groupId: 'withdraw-service-consumer',
        allowAutoTopicCreation: true, // Добавляем как в auth-сервисе
        sessionTimeout: 30000, // Добавляем как в auth-сервисе
      },
    },
  });

  kafkaApp.listen();
  await app.listen(8009);
  console.log('✅ Withdraw Service запущен на порту 8009');
}

bootstrap();
