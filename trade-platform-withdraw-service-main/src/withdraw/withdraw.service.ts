import { Injectable, Inject, InternalServerErrorException, ConflictException, NotFoundException, UnauthorizedException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { WithdrawalRequest, WithdrawalStatus } from './withdraw.entity';
import { Logger } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import * as nodemailer from 'nodemailer';

// Интерфейс для ответа от сервиса баланса
interface BalanceCheckResponse {
  userId: string;
  hasLockedBalance: boolean;
  lockedUsdBalance?: number;
  correlationId: string;
}

  // Интерфейс для пагинации
  interface PaginationParams {
    page: number;
    limit: number;
    status?: WithdrawalStatus;
    userId?: number;
  }
  
  interface PaginatedResult<T> {
    data: T[];
    total: number;
  }

// Интерфейс для создания запроса на вывод
interface CreateWithdrawalParams {
  userId: string;
  originalAmount?: number; // Исходная сумма
  amount: number; // Сумма после вычета комиссии
  destinationWallet: string;
  fee: number; // Рассчитанная комиссия
  currency: string;
  network: string;
  email: string;
  status: WithdrawalStatus;
}

// Интерфейс для запроса на уменьшение баланса
interface DeductBalanceRequest {
  userId: string;
  amount: number;
  currency: string;
  transactionReference: string;
  correlationId: string;
  event_type: string;
}

// Интерфейс для ответа на уменьшение баланса
interface DeductBalanceResponse {
  success: boolean;
  message?: string;
  correlationId: string;
}

@Injectable()
export class WithdrawService {
  private readonly logger = new Logger(WithdrawService.name);
  private verificationCodes = new Map<string, string>();
  
  private pendingBalanceChecks = new Map<string, (hasLockedBalance: boolean) => void>();
  private balanceCheckResults = new Map<string, boolean>();

  private deductBalanceTimeouts = new Map<string, NodeJS.Timeout>();
  private static deductBalanceCallbacks = new Map<string, (success: boolean) => void>();
  private deductResults = new Map<string, boolean>();

  private transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST ?? 'smtp.example.com',
    port: parseInt(process.env.SMTP_PORT ?? '587', 10),
    secure: false,
    auth: {
      user: process.env.SMTP_USER ?? '<EMAIL>',
      pass: process.env.SMTP_PASSWORD ?? 'password123',
    },
  });

  constructor(
    @InjectRepository(WithdrawalRequest)
    private readonly withdrawRepository: Repository<WithdrawalRequest>,

    @Inject('KAFKA_SERVICE')
    private readonly kafkaClient: ClientKafka,
  ) {}

  async onModuleInit() {
    this.kafkaClient.subscribeToResponseOf('check.locked.balance.response');
    this.kafkaClient.subscribeToResponseOf('check.locked.balance.reply');
    this.kafkaClient.subscribeToResponseOf('check.locked.balance');
    this.kafkaClient.subscribeToResponseOf('deduct.fixed.balance');
    this.kafkaClient.subscribeToResponseOf('deduct.fixed.balance.response');
    
    await this.kafkaClient.connect();
    this.logger.log('Kafka client connected, subscriptions set up');
  }

  private generateVerificationCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  async handleCheckLockedBalanceResponse(response: BalanceCheckResponse) {
    const userId = response.userId;
    const correlationId = response.correlationId;
    const hasLockedBalance = response.hasLockedBalance;
    
    this.logger.log(`Received balance response via MessagePattern: userId=${userId}, hasLockedBalance=${hasLockedBalance}`);
    
    this.balanceCheckResults.set(userId, hasLockedBalance);
    
    const callback = this.pendingBalanceChecks.get(userId);
    if (callback) {
      this.logger.log(`Calling callback for userId=${userId}`);
      callback(hasLockedBalance);
      this.pendingBalanceChecks.delete(userId);
    } else {
      this.logger.warn(`Callback for userId=${userId} not found`);
    }
  }

  async handleDeductBalanceResponse(response: DeductBalanceResponse) {
    const correlationId = response.correlationId;
    const success = response.success;
    
    this.logger.log(`Received response from balance service: correlationId=${correlationId}, success=${success}`);
    
    const callback = WithdrawService.deductBalanceCallbacks.get(correlationId);
    if (callback) {
      this.logger.log(`Found callback for correlationId=${correlationId}, calling directly with result ${success}`);
      callback(success);
      WithdrawService.deductBalanceCallbacks.delete(correlationId);
    } else {
      this.logger.warn(`Callback for correlationId=${correlationId} NOT found!`);
    }
  }

  /**
   * 🔥 NEW METHOD: Return funds to balance via Kafka
   */
  async returnFundsToBalance(userId: string, amount: number, currency: string, transactionReference: string, reason: string = 'withdrawal_rejected'): Promise<boolean> {
    try {
      this.logger.log(`💰 Sending funds return request: userId=${userId}, amount=${amount} ${currency}, reason=${reason}`);
      
      const correlationId = uuidv4();
      
      // Формируем сообщение для возврата средств
      const refundMessage = {
        userId,
        amount,
        currency,
        transactionReference,
        correlationId,
        reason,
        operation_type: "withdrawal_refund",
        timestamp: new Date().toISOString(),
        event_type: "add_funds"
      };
      
      // 🔥 SEND TO KAFKA VIA balance.add.funds TOPIC
      this.kafkaClient.emit('balance.add.funds', refundMessage);
      
      this.logger.log(`📤 Funds return request sent to Kafka: correlationId=${correlationId}`);
      this.logger.log(`   📍 Topic: balance.add.funds`);
      this.logger.log(`   📍 Amount: ${amount} ${currency}`);
      this.logger.log(`   📍 Reason: ${reason}`);
      
      return true;
    } catch (error) {
      this.logger.error(`❌ Error sending funds return request: ${error.message}`);
      return false;
    }
  }

  /**
   * 🔥 METHOD: Send withdrawal status update notification WITH CORRECT AMOUNTS
   */
  private async sendStatusUpdateEmail(
    email: string, 
    status: WithdrawalStatus, 
    amount: number, // This is already amount WITHOUT fee
    currency: string, 
    txReference: string, 
    rejectionReason?: string
  ): Promise<boolean> {
    try {
      let subject = '';
      let content = '';
      let statusColor = '#333';
      let statusText = '';
      
      switch (status) {
        case WithdrawalStatus.PENDING:
          subject = 'Withdrawal Request Created';
          statusText = 'Pending';
          statusColor = '#FFA500';
          content = `
            <p>Your withdrawal request for <strong>${amount} ${currency}</strong> has been successfully created and is awaiting processing.</p>
            <p>Request ID: <strong>${txReference}</strong></p>
            <p>Processing time may take from several minutes to several hours.</p>
          `;
          break;

        case WithdrawalStatus.IN_PROGRESS:
          subject = 'Withdrawal Request In Progress';
          statusText = 'In Progress';
          statusColor = '#2196F3';
          content = `
            <p>Your withdrawal request for <strong>${amount} ${currency}</strong> is now being processed.</p>
            <p>Request ID: <strong>${txReference}</strong></p>
            <p>Funds will be transferred to the specified wallet shortly.</p>
          `;
          break;

        case WithdrawalStatus.BLOCKCHAIN_CONFIRMING:
          subject = 'Withdrawal Sent, Awaiting Confirmations';
          statusText = 'Blockchain Confirmation';
          statusColor = '#FF9800';
          content = `
            <p>Your withdrawal of <strong>${amount} ${currency}</strong> has been sent to the blockchain.</p>
            <p>Request ID: <strong>${txReference}</strong></p>
            <p>The transaction is currently awaiting network confirmations. This may take from several minutes to an hour.</p>
            <p>You will receive a notification once the funds arrive in your wallet.</p>
          `;
          break;

        case WithdrawalStatus.APPROVED_DONE:
          subject = 'Withdrawal Successfully Completed';
          statusText = 'Completed';
          statusColor = '#4CAF50';
          content = `
            <p>Your withdrawal of <strong>${amount} ${currency}</strong> has been successfully completed!</p>
            <p>Request ID: <strong>${txReference}</strong></p>
            <p>Funds have been transferred to your specified wallet. The transaction has received the required number of network confirmations.</p>
            <p>Thank you for using our service!</p>
          `;
          break;

        case WithdrawalStatus.REJECTED:
          subject = 'Withdrawal Request Rejected';
          statusText = 'Rejected';
          statusColor = '#F44336';
          content = `
            <p>Unfortunately, your withdrawal request for <strong>${amount} ${currency}</strong> has been rejected.</p>
            <p>Request ID: <strong>${txReference}</strong></p>
            ${rejectionReason ? `<p><strong>Rejection Reason:</strong> ${rejectionReason}</p>` : ''}
            <p><strong>✅ Funds have been automatically returned to your balance.</strong> If you have any questions, please contact our support team.</p>
          `;
          break;

        default:
          return false;
      }

      await this.transporter.sendMail({
        from: `"No-Reply" <${process.env.SMTP_USER ?? '<EMAIL>'}>`,
        to: email,
        subject: subject,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background-color: #ffffff;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #333; margin: 0; font-size: 24px;">Withdrawal Status Update</h1>
            </div>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
              <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <span style="font-weight: bold; color: #333;">Status:</span>
                <span style="background-color: ${statusColor}; color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: bold; text-transform: uppercase;">
                  ${statusText}
                </span>
              </div>
              <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                <span style="color: #666;">Withdrawal Amount:</span>
                <span style="font-weight: bold;">${amount} ${currency}</span>
              </div>
              <div style="display: flex; justify-content: space-between;">
                <span style="color: #666;">Request ID:</span>
                <span style="font-weight: bold; font-family: monospace;">${txReference}</span>
              </div>
            </div>
            
            <div style="margin-bottom: 20px;">
              ${content}
            </div>
            
            <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
            
            <div style="text-align: center;">
              <p style="color: #888; font-size: 12px; margin: 0;">
                You can track the status of all your requests in the "Withdrawal History" section of your account.
              </p>
              <p style="color: #888; font-size: 12px; margin: 5px 0 0 0;">
                This is an automated message, please do not reply to this email.
              </p>
            </div>
          </div>
        `
      });
      
      this.logger.log(`📧 Status notification ${status} sent to ${email} for request ${txReference}`);
      return true;
    } catch (error) {
      this.logger.error(`❌ Error sending status notification: ${error.message}`);
      return false;
    }
  }

  private async sendVerificationEmail(email: string, code: string, amount: number, currency: string): Promise<boolean> {
    try {
      await this.transporter.sendMail({
        from: `"No-Reply" <${process.env.SMTP_USER ?? '<EMAIL>'}>`,
        to: email,
        subject: 'Withdrawal Verification Code',
        text: `Your verification code for withdrawal of ${amount} ${currency}: ${code}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background-color: #ffffff;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #333; margin: 0; font-size: 24px;">Withdrawal Verification</h1>
            </div>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
              <p style="margin: 0 0 15px 0; color: #333; font-size: 16px;">
                You have requested a withdrawal of <strong>${amount} ${currency}</strong> (amount already shown after fee deduction).
              </p>
              <p style="margin: 0; color: #666; font-size: 14px;">
                To confirm this operation, please enter the verification code:
              </p>
            </div>
            
            <div style="background-color: #f5f5f5; padding: 20px; text-align: center; font-size: 32px; letter-spacing: 8px; font-weight: bold; margin: 30px 0; border-radius: 8px; border: 2px dashed #007bff; color: #007bff;">
              ${code}
            </div>
            
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
              <p style="margin: 0; color: #856404; font-size: 14px;">
                <strong>⚠️ Security Notice:</strong> If you did not request this withdrawal, please ignore this message and contact our support team.
              </p>
            </div>
            
            <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
            
            <div style="text-align: center;">
              <p style="color: #888; font-size: 12px; margin: 0;">
                This verification code will expire in 10 minutes for security reasons.
              </p>
              <p style="color: #888; font-size: 12px; margin: 5px 0 0 0;">
                This is an automated message, please do not reply to this email.
              </p>
            </div>
          </div>
        `
      });
      
      this.logger.log(`Verification code ${code} sent to ${email}`);
      return true;
    } catch (error) {
      this.logger.error(`Error sending email: ${error.message}`);
      return false;
    }
  }

  private async sendWithdrawalConfirmationEmail(email: string, amount: number, currency: string, txReference: string): Promise<boolean> {
    try {
      await this.transporter.sendMail({
        from: `"No-Reply" <${process.env.SMTP_USER ?? '<EMAIL>'}>`,
        to: email,
        subject: 'Withdrawal Request Created',
        text: `Your withdrawal request for ${amount} ${currency} has been successfully created. Request ID: ${txReference}`,
        html: `
          <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 8px; background-color: #ffffff;">
            <div style="text-align: center; margin-bottom: 30px;">
              <h1 style="color: #333; margin: 0; font-size: 24px;">Withdrawal Request Created</h1>
            </div>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
              <p style="margin: 0 0 15px 0; color: #333; font-size: 16px;">
                Your withdrawal request for <strong>${amount} ${currency}</strong> (amount after fee deduction) has been successfully created and submitted for processing.
              </p>
              <p style="margin: 0; color: #666; font-size: 14px;">
                Request ID: <strong>${txReference}</strong>
              </p>
            </div>
            
            <div style="margin-bottom: 20px;">
              <p style="color: #333; margin: 0 0 10px 0;">You can track the status of your request in the "Withdrawal History" section of your account.</p>
            </div>
            
            <hr style="border: none; border-top: 1px solid #e0e0e0; margin: 20px 0;">
            
            <div style="text-align: center;">
              <p style="color: #888; font-size: 12px; margin: 0;">
                This is an automated message, please do not reply to this email.
              </p>
            </div>
          </div>
        `
      });
      
      this.logger.log(`Request notification sent to ${email}`);
      return true;
    } catch (error) {
      this.logger.error(`Error sending email: ${error.message}`);
      return false;
    }
  }

  async checkOpenPositions(userId: string): Promise<boolean> {
    try {
      this.logger.log(`Checking open positions for user ${userId}`);

      const correlationId = uuidv4();
      const message = {
        userId,
        correlationId,
        event_type: "check_locked_balance"
      };

      if (this.balanceCheckResults.has(userId)) {
        const cachedResult = this.balanceCheckResults.get(userId);
        if (cachedResult !== undefined) {
          this.logger.log(`Using cached result for userId=${userId}: ${cachedResult}`);
          return cachedResult;
        }
      }

      this.kafkaClient.emit('check.locked.balance', message);
      
      return new Promise((resolve) => {
        const timeout = setTimeout(() => {
          this.logger.warn(`Timeout waiting for balance response for userId=${userId}`);
          this.pendingBalanceChecks.delete(userId);
          resolve(false);
        }, 5000);
        
        this.pendingBalanceChecks.set(userId, (hasLockedBalance) => {
          clearTimeout(timeout);
          this.logger.log(`Received balance check result: ${hasLockedBalance}`);
          resolve(hasLockedBalance);
        });
      });
    } catch (error) {
      this.logger.error(`Error checking open positions: ${error.message}`);
      return false;
    }
  }

  async sendBalanceCheckRequest(userId: string): Promise<void> {
    this.logger.log(`Sending balance check request: ${userId}`);
    
    const message = {
      userId,
      correlationId: uuidv4(),
      event_type: "check_locked_balance"
    };
    
    this.kafkaClient.emit('check.locked.balance', message);
  }

  async deductFixedBalance(userId: string, amount: number, currency: string, transactionReference: string): Promise<boolean> {
    try {
      this.logger.log(`Sending balance deduction request for user ${userId} amount ${amount} ${currency}`);
  
      const correlationId = uuidv4();
      
      const responsePromise = new Promise<boolean>((resolve) => {
        const timeout = setTimeout(() => {
          this.logger.warn(`Timeout waiting for response for correlationId=${correlationId}`);
          WithdrawService.deductBalanceCallbacks.delete(correlationId);
          resolve(false);
        }, 10000);
        
        WithdrawService.deductBalanceCallbacks.set(correlationId, (success) => {
          this.logger.log(`Callback called for ${correlationId}, result: ${success}`);
          clearTimeout(timeout);
          resolve(success);
        });
      });
      
      const message = {
        userId,
        amount,
        currency,
        transactionReference,
        correlationId,
        event_type: "deduct_fixed_balance"
      };
      
      this.kafkaClient.emit('deduct.fixed.balance', message);
      this.logger.log(`Balance deduction request sent with correlationId=${correlationId}`);
      
      const result = await responsePromise;
      
      if (result) {
        this.logger.log(`Successful balance deduction for correlationId=${correlationId}`);
      } else {
        this.logger.warn(`No positive response received for correlationId=${correlationId}`);
      }
      
      return result;
    } catch (error) {
      this.logger.error(`Error deducting fixed balance: ${error.message}`);
      return false;
    }
  }
  
  setDeductResult(correlationId: string, success: boolean): void {
    this.logger.log(`Setting result for correlationId=${correlationId}: ${success}`);
    
    const callback = WithdrawService.deductBalanceCallbacks.get(correlationId);
    if (callback) {
      this.logger.log(`Found callback for correlationId=${correlationId}, calling callback with result ${success}`);
      callback(success);
      WithdrawService.deductBalanceCallbacks.delete(correlationId);
    } else {
      this.logger.warn(`Callback for correlationId=${correlationId} not found in setDeductResult`);
    }
  }

  /**
   * 🔥 CORRECTED METHOD: Create withdrawal request WITH AUTOMATIC FEE DEDUCTION
   */
  async createWithdrawalRequestOnly(params: CreateWithdrawalParams): Promise<WithdrawalRequest> {
    try {
      const internalTxReference = this.generateTransactionReference();
      
      this.logger.log(`💰 Creating withdrawal request:`);
      this.logger.log(`   📍 Original amount: ${params.originalAmount || 'not specified'}`);
      this.logger.log(`   📍 To withdraw (after fee): ${params.amount} ${params.currency}`);
      this.logger.log(`   📍 Fee: ${params.fee} ${params.currency}`);
      
      const newWithdrawal = this.withdrawRepository.create({
        userId: parseInt(params.userId),
        internalTxReference,
        amount: params.amount, // Amount WITHOUT fee
        currency: params.currency || 'USDT',
        network: params.network || 'TRC20',
        destinationWallets: [params.destinationWallet],
        fee: params.fee, // Calculated fee
        status: params.status || WithdrawalStatus.PENDING,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      
      (newWithdrawal as any).email = params.email;
      
      await this.withdrawRepository.save(newWithdrawal);
      
      this.logger.log(`✅ Created withdrawal request: ${JSON.stringify(newWithdrawal)}`);
      
      return newWithdrawal;
    } catch (error) {
      this.logger.error(`Error creating withdrawal request: ${error.message}`);
      throw error;
    }
  }

  async createWithdrawalRequest(params: CreateWithdrawalParams): Promise<WithdrawalRequest> {
    try {
      const withdrawal = await this.createWithdrawalRequestOnly(params);
      
      // 🔥 DEDUCT AMOUNT WITHOUT FEE (already calculated)
      const deductResult = await this.deductFixedBalance(
        params.userId,
        params.amount, // This is already amount WITHOUT fee
        params.currency || 'USDT',
        withdrawal.internalTxReference
      );
      
      if (!deductResult) {
        this.logger.error(`Failed to deduct balance for user ${params.userId}`);
        await this.updateWithdrawalStatus(
          withdrawal.id,
          WithdrawalStatus.REJECTED,
          'Failed to deduct balance'
        );
        throw new ConflictException('Failed to deduct balance for withdrawal');
      }
      
      return withdrawal;
    } catch (error) {
      this.logger.error(`Error creating withdrawal request: ${error.message}`);
      throw new InternalServerErrorException('Failed to create withdrawal request');
    }
  }

  async getWithdrawalById(id: string): Promise<WithdrawalRequest> {
    try {
      const withdrawal = await this.withdrawRepository.findOne({ 
        where: { id }
      });
      
      if (!withdrawal) {
        throw new NotFoundException(`Withdrawal request with ID ${id} not found`);
      }
      
      return withdrawal;
    } catch (error) {
      this.logger.error(`Error getting withdrawal request: ${error.message}`);
      throw error;
    }
  }

  async sendVerificationCode(withdrawalId: string): Promise<boolean> {
    try {
      const withdrawal = await this.getWithdrawalById(withdrawalId);
      
      const email = (withdrawal as any).email;
      
      if (!email) {
        this.logger.error(`Email not found for request ${withdrawalId}`);
        return false;
      }
      
      const verificationCode = this.generateVerificationCode();
      
      this.verificationCodes.set(withdrawalId, verificationCode);
      
      // 🔥 Send code with AMOUNT WITHOUT FEE
      const emailSent = await this.sendVerificationEmail(
        email, 
        verificationCode,
        withdrawal.amount, // This is already amount WITHOUT fee
        withdrawal.currency
      );
      
      return emailSent;
    } catch (error) {
      this.logger.error(`Error sending verification code: ${error.message}`);
      return false;
    }
  }

  async verifyWithdrawalCode(withdrawalId: string, code: string): Promise<boolean> {
    try {
      const withdrawal = await this.getWithdrawalById(withdrawalId);
      
      const savedCode = this.verificationCodes.get(withdrawalId);
      
      if (!savedCode || savedCode !== code) {
        this.logger.warn(`Invalid verification code for request ${withdrawalId}`);
        return false;
      }
      
      this.verificationCodes.delete(withdrawalId);
      
      return true;
    } catch (error) {
      this.logger.error(`Error verifying code: ${error.message}`);
      return false;
    }
  }

  async continueWithdrawalWithOpenPositions(withdrawalId: string): Promise<WithdrawalRequest> {
    try {
      const withdrawal = await this.getWithdrawalById(withdrawalId);
      
      if (withdrawal.status !== WithdrawalStatus.PENDING) {
        throw new ConflictException('Withdrawal request is already being processed or completed');
      }
      
      const userId = withdrawal.userId.toString();
      
      // 🔥 DEDUCT AMOUNT WITHOUT FEE (already calculated during creation)
      const deductResult = await this.deductFixedBalance(
        userId,
        withdrawal.amount, // This is already amount WITHOUT fee
        withdrawal.currency,
        withdrawal.internalTxReference
      );
      
      if (!deductResult) {
        this.logger.error(`Failed to deduct balance for user ${userId}`);
        await this.updateWithdrawalStatus(
          withdrawal.id,
          WithdrawalStatus.REJECTED,
          'Failed to deduct balance'
        );
        throw new ConflictException('Failed to deduct balance for withdrawal');
      }
      
      return withdrawal;
    } catch (error) {
      this.logger.error(`Error continuing withdrawal with open positions: ${error.message}`);
      throw error;
    }
  }

  /**
   * 🔥 CORRECTED METHOD: Update withdrawal status with AUTOMATIC FUNDS RETURN
   */
  async updateWithdrawalStatus(
    id: string,
    status: WithdrawalStatus,
    rejectionReason?: string
  ): Promise<WithdrawalRequest> {
    try {
      const withdrawal = await this.getWithdrawalById(id);
      
      const oldStatus = withdrawal.status;
      
      withdrawal.status = status;
      withdrawal.updatedAt = new Date();
      
      if (status === WithdrawalStatus.REJECTED && rejectionReason) {
        withdrawal.rejectionReason = rejectionReason;
      }
      
      if (status === WithdrawalStatus.APPROVED_DONE) {
        withdrawal.completedAt = new Date();
      }
      
      await this.withdrawRepository.save(withdrawal);
      
      this.logger.log(`🔄 Updated withdrawal request status ${id}: ${oldStatus} → ${status}`);
      
      // 🔥 AUTOMATIC FUNDS RETURN ON REJECTION
      if (status === WithdrawalStatus.REJECTED) {
        try {
          this.logger.log(`💰 Starting funds return for rejected request ${id}`);
          
          // Return amount back to balance
          const returnResult = await this.returnFundsToBalance(
            withdrawal.userId.toString(),
            withdrawal.amount, // Return amount WITHOUT fee
            withdrawal.currency,
            withdrawal.internalTxReference,
            rejectionReason || 'withdrawal_rejected'
          );
          
          if (returnResult) {
            this.logger.log(`✅ Funds ${withdrawal.amount} ${withdrawal.currency} successfully sent for return to user ${withdrawal.userId}`);
          } else {
            this.logger.error(`❌ Failed to send funds return request for user ${withdrawal.userId}`);
          }
        } catch (error) {
          this.logger.error(`❌ Error returning funds for request ${id}: ${error.message}`);
        }
      }
      
      // 🔥 SEND EMAIL NOTIFICATION FOR ALL STATUSES WITH AMOUNT WITHOUT FEE
      const email = (withdrawal as any).email;
      if (email) {
        try {
          this.logger.log(`📧 Sending email notification about status change to ${status} for request ${id}`);
          
          const emailSent = await this.sendStatusUpdateEmail(
            email,
            status,
            withdrawal.amount, // This is already amount WITHOUT fee
            withdrawal.currency,
            withdrawal.internalTxReference,
            rejectionReason
          );
          
          if (emailSent) {
            this.logger.log(`✅ Email notification about status ${status} successfully sent to ${email}`);
          } else {
            this.logger.error(`❌ Failed to send email notification about status ${status}`);
          }
        } catch (emailError) {
          this.logger.error(`❌ Error sending email notification: ${emailError.message}`);
        }
      } else {
        this.logger.warn(`⚠️ Email not found for request ${id}, notification not sent`);
      }
      
      return withdrawal;
    } catch (error) {
      this.logger.error(`❌ Error updating withdrawal request status: ${error.message}`);
      throw error;
    }
  }

  /**
   * Get user withdrawal history
   */
  async getUserWithdrawalHistory(userId: string): Promise<WithdrawalRequest[]> {
    try {
      return await this.withdrawRepository.find({
        where: { userId: parseInt(userId) },
        order: { createdAt: 'DESC' }
      });
    } catch (error) {
      this.logger.error(`Error getting withdrawal history: ${error.message}`);
      throw error;
    }
  }

  /**
   * Generate unique transaction reference
   */
  private generateTransactionReference(): string {
    const timestamp = new Date().getTime().toString().substring(6);
    const random = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    return `WD-${timestamp}-${random}`;
  }


/**
 * Get all withdrawal requests for admin
 */
async getAllWithdrawalsForAdmin(): Promise<WithdrawalRequest[]> {
  try {
    this.logger.log('📋 Getting all withdrawal requests for admin');
    
    const withdrawals = await this.withdrawRepository.find({
      order: { 
        createdAt: 'DESC' 
      }
    });
    
    // Add wallet address to each request
    const withdrawalsWithWallet = withdrawals.map(withdrawal => {
      const walletAddress = withdrawal.destinationWallets && withdrawal.destinationWallets.length > 0 
        ? withdrawal.destinationWallets[0] 
        : null;
      
      return {
        ...withdrawal,
        destinationWallet: walletAddress, // Add separate field for convenience
        // Add additional fields for admin
        originalAmount: withdrawal.amount + (withdrawal.fee || 0), // Original amount before fee deduction
        netAmount: withdrawal.amount, // Amount to withdraw (already without fee)
        feeAmount: withdrawal.fee || 0, // Fee amount
        userEmail: (withdrawal as any).email || null // User email if available
      };
    });
    
    this.logger.log(`✅ Retrieved ${withdrawalsWithWallet.length} withdrawal requests`);
    
    return withdrawalsWithWallet;
  } catch (error) {
    this.logger.error(`❌ Error getting all withdrawal requests: ${error.message}`);
    throw new InternalServerErrorException('Failed to get withdrawal requests list');
  }
}

/**
 * Get withdrawal requests with pagination and filtering
 */
async getAllWithdrawalsPaginated(params: PaginationParams): Promise<PaginatedResult<WithdrawalRequest>> {
  try {
    this.logger.log(`📋 Getting requests with pagination: page ${params.page}, limit ${params.limit}`);
    
    const queryBuilder = this.withdrawRepository.createQueryBuilder('withdrawal');
    
    // Filter by status
    if (params.status) {
      queryBuilder.andWhere('withdrawal.status = :status', { status: params.status });
    }
    
    // Filter by user
    if (params.userId) {
      queryBuilder.andWhere('withdrawal.userId = :userId', { userId: params.userId });
    }
    
    // Count total records
    const total = await queryBuilder.getCount();
    
    // Apply pagination and sorting
    const withdrawals = await queryBuilder
      .orderBy('withdrawal.createdAt', 'DESC')
      .skip((params.page - 1) * params.limit)
      .take(params.limit)
      .getMany();
    
    // Add wallet address and additional fields
    const withdrawalsWithDetails = withdrawals.map(withdrawal => {
      const walletAddress = withdrawal.destinationWallets && withdrawal.destinationWallets.length > 0 
        ? withdrawal.destinationWallets[0] 
        : null;
      
      return {
        ...withdrawal,
        destinationWallet: walletAddress,
        originalAmount: withdrawal.amount + (withdrawal.fee || 0),
        netAmount: withdrawal.amount,
        feeAmount: withdrawal.fee || 0,
        userEmail: (withdrawal as any).email || null
      };
    });
    
    this.logger.log(`✅ Retrieved ${withdrawalsWithDetails.length} requests out of ${total} total`);
    
    return {
      data: withdrawalsWithDetails,
      total
    };
  } catch (error) {
    this.logger.error(`❌ Error getting requests with pagination: ${error.message}`);
    throw new InternalServerErrorException('Failed to get withdrawal requests list');
  }
}

/**
 * Get withdrawal statistics
 */
async getWithdrawalStatistics(): Promise<any> {
  try {
    this.logger.log('📊 Getting withdrawal statistics');
    
    const queryBuilder = this.withdrawRepository.createQueryBuilder('withdrawal');
    
    // Statistics by status
    const statusStats = await queryBuilder
      .select('withdrawal.status', 'status')
      .addSelect('COUNT(*)', 'count')
      .addSelect('SUM(withdrawal.amount)', 'totalAmount')
      .groupBy('withdrawal.status')
      .getRawMany();
    
    // Overall statistics
    const totalStats = await queryBuilder
      .select('COUNT(*)', 'totalCount')
      .addSelect('SUM(withdrawal.amount)', 'totalAmount')
      .addSelect('SUM(withdrawal.fee)', 'totalFees')
      .getRawOne();
    
    // Statistics for last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentStats = await this.withdrawRepository
      .createQueryBuilder('withdrawal')
      .select('COUNT(*)', 'recentCount')
      .addSelect('SUM(withdrawal.amount)', 'recentAmount')
      .where('withdrawal.createdAt >= :date', { date: thirtyDaysAgo })
      .getRawOne();
    
    const statistics = {
      byStatus: statusStats.map(stat => ({
        status: stat.status,
        count: parseInt(stat.count),
        totalAmount: parseFloat(stat.totalAmount) || 0
      })),
      overall: {
        totalWithdrawals: parseInt(totalStats.totalCount) || 0,
        totalAmount: parseFloat(totalStats.totalAmount) || 0,
        totalFees: parseFloat(totalStats.totalFees) || 0
      },
      recent30Days: {
        count: parseInt(recentStats.recentCount) || 0,
        amount: parseFloat(recentStats.recentAmount) || 0
      }
    };
    
    this.logger.log(`✅ Statistics retrieved: total requests ${statistics.overall.totalWithdrawals}`);
    
    return statistics;
  } catch (error) {
    this.logger.error(`❌ Error getting statistics: ${error.message}`);
    throw new InternalServerErrorException('Failed to get statistics');
  }
}
}