import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

export enum WithdrawalStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  APPROVED_DONE = 'APPROVED_DONE',
  BLOCKCHAIN_CONFIRMING = 'BLOCKCHAIN_CONFIRMING',
  REJECTED = 'REJECTED'
}

@Entity('withdrawal_requests')
export class WithdrawalRequest {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ name: 'user_id' })
  userId: number;

  @Column({ nullable: true })
  email: string;

  @Column({ name: 'internal_tx_reference', unique: true })
  internalTxReference: string;

  @Column('decimal', { precision: 18, scale: 8 })
  amount: number;

  @Column({ default: 'USDT' })
  currency: string;

  @Column({ default: 'TRC20' })
  network: string;

  @Column('simple-array', { name: 'destination_wallets' })
  destinationWallets: string[];

  @Column('decimal', { precision: 18, scale: 8 })
  fee: number;

  @Column({
    type: 'enum',
    enum: WithdrawalStatus,
    default: WithdrawalStatus.PENDING
  })
  status: WithdrawalStatus;

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @Column({ name: 'completed_at', nullable: true })
  completedAt: Date;

  @Column({ name: 'blockchain_tx_hash', nullable: true })
  blockchainTxHash: string;

  @Column({ name: 'rejection_reason', nullable: true })
  rejectionReason: string;

  @Column({ name: 'requires_additional_kyc', default: false })
  requiresAdditionalKyc: boolean;

  @Column({ name: 'additional_kyc_submitted', default: false })
  additionalKycSubmitted: boolean;
}