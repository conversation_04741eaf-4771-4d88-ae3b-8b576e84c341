import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WithdrawalRequest } from './withdraw.entity';
import { WithdrawService } from './withdraw.service';
import { WithdrawController } from './withdraw.controller';
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    TypeOrmModule.forFeature([WithdrawalRequest]),

    // ✅ Добавляем Kafka-клиент для отправки сообщений
    ClientsModule.register([
      {
        name: 'KAFKA_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            brokers: ['172.31.10.207:9093'],
          },
          consumer: {
            groupId: 'withdraw-service-consumer',
          },
        },
      },
    ]),
  ],
  controllers: [WithdrawController],
  providers: [WithdrawService],
  exports: [WithdrawService], // ✅ Экспортируем, если надо использовать в других модулях
})
export class WithdrawModule {}
