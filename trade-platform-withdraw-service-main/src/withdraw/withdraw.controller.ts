import { Controller, Post, Body, Logger, Get, Param, NotFoundException, Put, Inject, Delete, BadRequestException, Query } from '@nestjs/common';
import { WithdrawService } from './withdraw.service';
import { WithdrawalRequest, WithdrawalStatus } from './withdraw.entity';
import { ClientKafka, Ctx, MessagePattern, Payload, RmqContext } from '@nestjs/microservices';

// DTO для создания запроса на вывод
class CreateWithdrawalDto {
  userId: string;
  amount: number; // Исходная сумма ДО вычета комиссии
  destinationWallet: string;
  fee?: number; // Будет рассчитываться автоматически
  currency: string;
  network: string;
  email: string;
  status: WithdrawalStatus;
}

// DTO для отправки кода верификации
class SendCodeDto {
  withdrawId: string;
}

// DTO для проверки кода верификации
class VerifyCodeDto {
  withdrawId: string;
  verificationCode: string;
}

// DTO для продолжения вывода с открытыми позициями
class ContinueWithdrawalDto {
  withdrawId: string;
}

// Интерфейс для ответа о заблокированном балансе
interface BalanceCheckResponse {
  userId: string;
  hasLockedBalance: boolean;
  lockedUsdBalance?: number;
  correlationId: string;
}

// Интерфейс для ответа на уменьшение баланса
interface DeductBalanceResponse {
  success: boolean;
  message?: string;
  correlationId: string;
}

// DTO для изменения статуса вывода (для админки)
class UpdateWithdrawalStatusDto {
  status: WithdrawalStatus;
  rejectionReason?: string;
}


interface CreateWithdrawalParams {
  userId: string;
  originalAmount?: number; // Исходная сумма
  amount: number; // Сумма после вычета комиссии
  destinationWallet: string;
  fee: number; // Рассчитанная комиссия
  currency: string;
  network: string;
  email: string;
  status: WithdrawalStatus;
}

@Controller('withdraw')
export class WithdrawController {
  private readonly logger = new Logger(WithdrawController.name);
  private balanceCheckResults = new Map<string, boolean>(); 
  private balanceDeductResults = new Map<string, boolean>();
  
  constructor(
    private readonly withdrawService: WithdrawService,
    @Inject('KAFKA_SERVICE')
    private readonly kafkaClient: ClientKafka,
  ) {}

  /**
   * Обработчик сообщений из топика check.locked.balance.response
   */
  @MessagePattern('check.locked.balance.response')
  async handleLockedBalanceResponse(@Payload() response: BalanceCheckResponse) {
    const userId = response.userId;
    const hasLockedBalance = response.hasLockedBalance;
    
    this.logger.log(`Получен ответ о заблокированном балансе: ${JSON.stringify(response)}`);
    
    // Сохраняем результат проверки
    this.balanceCheckResults.set(userId, hasLockedBalance);
  }


  /**
 * Обработчик сообщений из топика deduct.fixed.balance.response
 */
@MessagePattern('deduct.fixed.balance.response')
async handleDeductBalanceResponse(@Payload() response: DeductBalanceResponse) {
  const correlationId = response.correlationId;
  const success = response.success;
  
  this.logger.log(`Получен ответ об уменьшении баланса: ${JSON.stringify(response)}`);
  
  // Устанавливаем результат в сервисе
  this.withdrawService.setDeductResult(correlationId, success);
}

@Post('create')
async createWithdrawalRequest(@Body() createWithdrawalDto: CreateWithdrawalDto) {
  this.logger.log(`Создание запроса на вывод: ${JSON.stringify(createWithdrawalDto)}`);
  
  try {
    // 🔥 РАССЧИТЫВАЕМ КОМИССИЮ 0.1% ЗАРАНЕЕ
    const feePercentage = 0.1; // 0.1%
    const originalAmount = createWithdrawalDto.amount;
    const calculatedFee = originalAmount * (feePercentage / 100);
    const finalAmount = originalAmount - calculatedFee;
    
    this.logger.log(`💰 Расчет комиссии: исходная сумма ${originalAmount}, комиссия ${calculatedFee} (${feePercentage}%), к выводу ${finalAmount} ${createWithdrawalDto.currency}`);
    
    // Проверяем что после вычета комиссии остается положительная сумма
    if (finalAmount <= 0) {
      return {
        status: 'ERROR',
        message: 'Сумма вывода слишком мала для покрытия комиссии'
      };
    }
    
    // Шаг 1: Создаем только запись о выводе с учетом комиссии
    const withdrawal = await this.withdrawService.createWithdrawalRequestOnly({
      ...createWithdrawalDto,
      originalAmount: originalAmount, // Передаем исходную сумму
      amount: finalAmount, // Передаем сумму после вычета комиссии
      fee: calculatedFee, // Передаем рассчитанную комиссию
      status: WithdrawalStatus.PENDING
    });
    
    // Шаг 2: Отправляем запрос на проверку баланса
    await this.withdrawService.sendBalanceCheckRequest(createWithdrawalDto.userId);
    
    // Шаг 3: Ждем небольшую паузу для получения ответа
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Шаг 4: Проверяем сохраненный результат
    const hasOpenPositions = this.balanceCheckResults.get(createWithdrawalDto.userId) || false;
    this.balanceCheckResults.delete(createWithdrawalDto.userId); // Очищаем результат
    
    if (hasOpenPositions) {
      // Если есть открытые позиции, возвращаем соответствующий ответ
      return {
        status: 'OPEN_POSITIONS',
        message: 'У вас есть открытые позиции. Рекомендуется закрыть их перед выводом',
        withdrawId: withdrawal.id,
        originalAmount: originalAmount,
        fee: calculatedFee,
        finalAmount: finalAmount
      };
    }
    
    // Если нет открытых позиций, просто возвращаем успешный результат
    return {
      status: 'SUCCESS',
      message: `Запрос на вывод создан. К выводу ${finalAmount} ${createWithdrawalDto.currency} (комиссия ${calculatedFee} ${createWithdrawalDto.currency}). Требуется подтверждение`,
      withdrawId: withdrawal.id,
      originalAmount: originalAmount,
      fee: calculatedFee,
      finalAmount: finalAmount
    };
  } catch (error) {
    this.logger.error(`Ошибка при создании запроса на вывод: ${error.message}`);
    
    if (error.message.includes('insufficient funds')) {
      return {
        status: 'INSUFFICIENT_FUNDS',
        message: 'Недостаточно средств для вывода'
      };
    } else if (error.message.includes('invalid address')) {
      return {
        status: 'INVALID_ADDRESS',
        message: 'Неверный адрес кошелька'
      };
    }
    
    return {
      status: 'ERROR',
      message: 'Произошла ошибка при создании запроса на вывод'
    };
  }
}

  /**
   * Отправка кода подтверждения на email
   */
  @Post('send-code')
  async sendVerificationCode(@Body() sendCodeDto: SendCodeDto) {
    try {
      this.logger.log(`Запрос на отправку кода для вывода ID: ${sendCodeDto.withdrawId}`);
      
      // Отправляем код верификации
      const result = await this.withdrawService.sendVerificationCode(sendCodeDto.withdrawId);
      
      if (result) {
        return {
          success: true,
          message: 'Код подтверждения отправлен на email'
        };
      } else {
        return {
          success: false,
          message: 'Не удалось отправить код подтверждения'
        };
      }
    } catch (error) {
      this.logger.error(`Ошибка при отправке кода подтверждения: ${error.message}`);
      
      return {
        success: false,
        message: error.message || 'Произошла ошибка при отправке кода'
      };
    }
  }

  /**
   * Проверка кода и подтверждение вывода
   */
  
  @Post('verify')
  async verifyWithdrawal(@Body() verifyCodeDto: VerifyCodeDto) {
    try {
      this.logger.log(`Проверка кода для вывода ID: ${verifyCodeDto.withdrawId}`);
      
      // Проверяем код верификации
      const codeIsValid = await this.withdrawService.verifyWithdrawalCode(
        verifyCodeDto.withdrawId,
        verifyCodeDto.verificationCode
      );
      
      if (!codeIsValid) {
        return {
          status: 'ERROR',
          message: 'Неверный код подтверждения'
        };
      }
      
      // Получаем информацию о выводе
      const withdrawal = await this.withdrawService.getWithdrawalById(verifyCodeDto.withdrawId);
      
      // Отнимаем баланс только после проверки кода верификации
      const deductResult = await this.withdrawService.deductFixedBalance(
        withdrawal.userId.toString(),
        withdrawal.amount,
        withdrawal.currency,
        withdrawal.internalTxReference
      );
      
      if (!deductResult) {
        // Если не удалось отнять баланс, отклоняем заявку
        await this.withdrawService.updateWithdrawalStatus(
          withdrawal.id, 
          WithdrawalStatus.REJECTED,
          'Не удалось уменьшить баланс'
        );
        
        return {
          status: 'INSUFFICIENT_FUNDS',
          message: 'Недостаточно средств для вывода'
        };
      }
      
      // Обновляем статус заявки на "в процессе"
      await this.withdrawService.updateWithdrawalStatus(
        withdrawal.id,
        WithdrawalStatus.IN_PROGRESS
      );
      
      return {
        status: 'SUCCESS',
        message: 'Вывод успешно подтвержден',
        withdrawId: verifyCodeDto.withdrawId
      };
    } catch (error) {
      this.logger.error(`Ошибка при проверке кода: ${error.message}`);
      
      return {
        status: 'ERROR',
        message: error.message || 'Произошла ошибка при проверке кода'
      };
    }
  }

  /**
   * Продолжение вывода с открытыми позициями
   */
  @Post('continue-with-positions')
  async continueWithdrawalWithPositions(@Body() continueDto: ContinueWithdrawalDto) {
    try {
      this.logger.log(`Продолжение вывода с открытыми позициями ID: ${continueDto.withdrawId}`);
      
      // Получаем заявку и проверяем её существование
      const withdrawal = await this.withdrawService.getWithdrawalById(continueDto.withdrawId);
      
      if (!withdrawal) {
        return {
          status: 'ERROR',
          message: 'Заявка на вывод не найдена'
        };
      }
      
      // Просто возвращаем успешный ответ без отнятия баланса
      // Баланс будет отнят только при подтверждении через 2FA
      return {
        status: 'SUCCESS',
        message: 'Заявка на вывод продолжена, требуется подтверждение',
        withdrawId: withdrawal.id
      };
    } catch (error) {
      this.logger.error(`Ошибка при продолжении вывода: ${error.message}`);
      
      return {
        status: 'ERROR',
        message: error.message || 'Произошла ошибка при обработке запроса'
      };
    }
  }

  /**
   * Получение истории выводов пользователя
   */
  @Get('history/:userId')
  async getUserWithdrawalHistory(@Param('userId') userId: string) {
    try {
      const history = await this.withdrawService.getUserWithdrawalHistory(userId);
      return history;
    } catch (error) {
      this.logger.error(`Ошибка при получении истории выводов: ${error.message}`);
      throw new BadRequestException('Не удалось получить историю выводов');
    }
  }
  

  /**
 * Обновление статуса заявки на вывод (для админки)
 */
@Put('admin/withdrawal/:id/status')
async updateWithdrawalStatus(
  @Param('id') withdrawalId: string,
  @Body() updateStatusDto: UpdateWithdrawalStatusDto
) {
  try {
    this.logger.log(`Обновление статуса вывода ID: ${withdrawalId} на ${updateStatusDto.status}`);
    
    // Проверяем существование заявки
    const withdrawal = await this.withdrawService.getWithdrawalById(withdrawalId);
    
    if (!withdrawal) {
      throw new NotFoundException(`Заявка на вывод с ID ${withdrawalId} не найдена`);
    }
    
    // Обновляем статус
    const updatedWithdrawal = await this.withdrawService.updateWithdrawalStatus(
      withdrawalId, 
      updateStatusDto.status, 
      updateStatusDto.rejectionReason
    );
    
    return {
      status: 'SUCCESS',
      message: `Статус заявки на вывод успешно обновлен на ${updateStatusDto.status}`,
      withdrawal: {
        id: updatedWithdrawal.id,
        status: updatedWithdrawal.status,
        updatedAt: updatedWithdrawal.updatedAt
      }
    };
  } catch (error) {
    this.logger.error(`Ошибка при обновлении статуса вывода: ${error.message}`);
    
    if (error instanceof NotFoundException) {
      throw error;
    }
    
    throw new BadRequestException(`Ошибка при обновлении статуса: ${error.message}`);
  }
}



/**
 * Получение всех заявок на вывод (для админки)
 */
@Get('admin/all')
async getAllWithdrawals() {
  try {
    const withdrawals = await this.withdrawService.getAllWithdrawalsForAdmin();
    
    return {
      status: 'SUCCESS',
      message: 'Список всех заявок на вывод получен',
      count: withdrawals.length,
      data: withdrawals
    };
  } catch (error) {
    this.logger.error(`Ошибка при получении всех заявок на вывод: ${error.message}`);
    throw new BadRequestException('Не удалось получить список заявок на вывод');
  }
}

/**
 * Получение всех заявок на вывод с пагинацией (для админки)
 */
@Get('admin/all-paginated')
async getAllWithdrawalsPaginated(
  @Query('page') page: number = 1,
  @Query('limit') limit: number = 20,
  @Query('status') status?: WithdrawalStatus,
  @Query('userId') userId?: string
) {
  try {
    // Валидация параметров
    const pageNum = Math.max(1, Number(page) || 1);
    const limitNum = Math.min(100, Math.max(1, Number(limit) || 20));
    
    const result = await this.withdrawService.getAllWithdrawalsPaginated({
      page: pageNum,
      limit: limitNum,
      status,
      userId: userId ? parseInt(userId) : undefined
    });
    
    return {
      status: 'SUCCESS',
      message: 'Список заявок на вывод получен',
      pagination: {
        page: pageNum,
        limit: limitNum,
        total: result.total,
        totalPages: Math.ceil(result.total / limitNum)
      },
      data: result.data
    };
  } catch (error) {
    this.logger.error(`Ошибка при получении заявок с пагинацией: ${error.message}`);
    throw new BadRequestException('Не удалось получить список заявок на вывод');
  }
}
}