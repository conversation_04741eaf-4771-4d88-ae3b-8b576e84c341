import { Modu<PERSON> } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { WithdrawModule } from './withdraw/withdraw.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

import * as dotenv from 'dotenv';

dotenv.config();

@Module({
  imports: [
    WithdrawModule,
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: process.env.DB_HOST,
      port: Number(process.env.DB_PORT),
      username: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      autoLoadEntities: true,
      synchronize: true,
      ssl: {
        rejectUnauthorized: false, // 🔥 AWS требует SSL
      },
    }),
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
