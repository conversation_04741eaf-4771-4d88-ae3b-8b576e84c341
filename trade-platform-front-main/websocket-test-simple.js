// Simple WebSocket test using browser WebSocket API in Node.js
// Run with: bun run websocket-test-simple.js

const WS_URL = 'wss://trading-ws.eu.ngrok.io/api/ws';
const TEST_USER_ID = '13';

let messageCount = 0;
const startTime = Date.now();

console.log('🚀 Starting Trading WebSocket Test');
console.log(`📍 URL: ${WS_URL}/${TEST_USER_ID}`);
console.log('⏱️  Test will run for 30 seconds\n');

try {
  const ws = new WebSocket(`${WS_URL}/${TEST_USER_ID}`);
  
  ws.onopen = function(event) {
    console.log('✅ WebSocket connected successfully');
    console.log('📅 Connected at:', new Date().toISOString());
    
    // Send connection info request
    setTimeout(() => {
      console.log('🔍 Requesting connection info...');
      ws.send(JSON.stringify({
        action: "get_connection_info"
      }));
    }, 1000);
    
    // Subscribe to position events
    setTimeout(() => {
      console.log('🔍 Subscribing to position events...');
      ws.send(JSON.stringify({
        action: "subscribe_position_events",
        data: { user_id: TEST_USER_ID }
      }));
    }, 2000);
    
    // Subscribe to liquidation alerts
    setTimeout(() => {
      console.log('🔍 Subscribing to liquidation alerts...');
      ws.send(JSON.stringify({
        action: "subscribe_liquidation_alerts",
        data: { user_id: TEST_USER_ID }
      }));
    }, 3000);
    
    // Send ping
    setTimeout(() => {
      console.log('🔍 Sending ping...');
      const pingTime = Date.now();
      ws.send(JSON.stringify({
        action: "ping",
        timestamp: pingTime
      }));
    }, 4000);
    
    // Test invalid message
    setTimeout(() => {
      console.log('🔍 Testing error handling...');
      ws.send(JSON.stringify({
        action: "invalid_action",
        data: { test: "error_handling" }
      }));
    }, 5000);
    
    // Close after 30 seconds
    setTimeout(() => {
      console.log('\n🔚 Test completed, closing connection...');
      ws.close(1000, 'Test completed');
    }, 30000);
  };
  
  ws.onmessage = function(event) {
    try {
      const message = JSON.parse(event.data);
      messageCount++;
      
      const timestamp = new Date().toISOString();
      console.log(`📨 [${messageCount}] ${timestamp}: ${message.action || message.type || 'unknown'}`);
      
      if (message.action === 'pong') {
        console.log('   🏓 Pong received - connection healthy');
      } else if (message.action === 'connection_info') {
        console.log('   ℹ️  Connection info:', JSON.stringify(message.data, null, 2));
      } else if (message.action === 'positions') {
        console.log(`   📊 Positions: ${message.data?.length || 0} items`);
      } else if (message.type) {
        console.log(`   ⚡ Event: ${message.type} (${message.event_type || 'no event type'})`);
      } else {
        console.log('   📝 Data:', JSON.stringify(message, null, 2));
      }
      
    } catch (error) {
      console.error('❌ Error parsing message:', error.message);
      console.log('   Raw:', event.data);
    }
  };
  
  ws.onclose = function(event) {
    const duration = Date.now() - startTime;
    console.log(`\n🔌 WebSocket closed: ${event.code} - ${event.reason}`);
    console.log(`⏱️  Total duration: ${duration}ms`);
    console.log(`📨 Total messages: ${messageCount}`);
    console.log('✅ Test completed');
  };
  
  ws.onerror = function(error) {
    console.error('❌ WebSocket error:', error);
  };
  
} catch (error) {
  console.error('❌ Failed to create WebSocket:', error);
}