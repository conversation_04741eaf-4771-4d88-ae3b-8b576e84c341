"use client";

import { createStore } from "zustand/vanilla";

import { createModuleLogger } from "@/shared/lib/logger";
import { StoreSlice } from "@/shared/lib/store/slice-creator";
import { createStoreProvider } from "@/shared/providers/store-provider";

const logger = createModuleLogger("theme-store");

export type Theme = "light" | "dark" | "system";

// Define the theme state
export interface ThemeState {
  theme: Theme;
  resolvedTheme: "light" | "dark";
  isLoading: boolean;

  setTheme: (theme: Theme) => void;
  toggleTheme: () => void;
  initializeTheme: () => void;
}

// Define the theme store
export interface ThemeStore {
  theme: ThemeState;
}

// Storage key for theme persistence
const THEME_STORAGE_KEY = "theme";

// Helper functions
const getStoredTheme = (): Theme => {
  if (typeof window === "undefined") return "light";

  try {
    const stored = localStorage.getItem(THEME_STORAGE_KEY);
    if (stored && ["light", "dark", "system"].includes(stored)) {
      return stored as Theme;
    }
  } catch (error) {
    logger.error("Failed to get stored theme", { error });
  }

  return "light"; // Default to light theme
};

const storeTheme = (theme: Theme): void => {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(THEME_STORAGE_KEY, theme);
  } catch (error) {
    logger.error("Failed to store theme", { error });
  }
};

const getSystemTheme = (): "light" | "dark" => {
  if (typeof window === "undefined") return "light";

  try {
    return window.matchMedia("(prefers-color-scheme: dark)").matches ? "dark" : "light";
  } catch (error) {
    logger.error("Failed to get system theme", { error });
    return "light";
  }
};

const resolveTheme = (theme: Theme): "light" | "dark" => {
  if (theme === "system") {
    return getSystemTheme();
  }
  return theme;
};

const applyTheme = (resolvedTheme: "light" | "dark"): void => {
  if (typeof window === "undefined") return;

  try {
    const root = window.document.documentElement;
    root.classList.remove("light", "dark");
    root.classList.add(resolvedTheme);

    // Update meta theme-color for mobile browsers
    const metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (metaThemeColor) {
      metaThemeColor.setAttribute("content", resolvedTheme === "dark" ? "#0a0a0a" : "#ffffff");
    }
  } catch (error) {
    logger.error("Failed to apply theme", { error });
  }
};

// Create the theme slice
export const createThemeSlice: StoreSlice<ThemeStore, { theme: ThemeState }> = (set, get) => {
  // Initialize theme from storage
  const initialTheme = getStoredTheme();
  const initialResolvedTheme = resolveTheme(initialTheme);

  return {
    theme: {
      // Initial state
      theme: initialTheme,
      resolvedTheme: initialResolvedTheme,
      isLoading: true,

      // Actions
      setTheme: (newTheme) => {
        logger.info("Setting theme", { theme: newTheme });

        const resolvedTheme = resolveTheme(newTheme);

        // Store theme preference
        storeTheme(newTheme);

        // Apply theme to DOM
        applyTheme(resolvedTheme);

        // Update state
        set((state) => ({
          theme: {
            ...state.theme,
            theme: newTheme,
            resolvedTheme,
          },
        }));
      },

      toggleTheme: () => {
        const currentState = get();
        const currentResolved = currentState.theme.resolvedTheme;
        const newTheme = currentResolved === "dark" ? "light" : "dark";

        logger.info("Toggling theme", { from: currentResolved, to: newTheme });
        currentState.theme.setTheme(newTheme);
      },

      initializeTheme: () => {
        if (typeof window === "undefined") return;

        const storedTheme = getStoredTheme();
        const resolvedTheme = resolveTheme(storedTheme);

        // Apply theme immediately
        applyTheme(resolvedTheme);

        // Set up system theme listener if using system preference
        if (storedTheme === "system") {
          const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
          const handleSystemThemeChange = () => {
            const currentState = get();
            if (currentState.theme.theme === "system") {
              const newResolvedTheme = getSystemTheme();
              applyTheme(newResolvedTheme);
              set((state) => ({
                theme: {
                  ...state.theme,
                  resolvedTheme: newResolvedTheme,
                },
              }));
            }
          };

          mediaQuery.addEventListener("change", handleSystemThemeChange);
        }

        // Update state and mark as loaded
        set((state) => ({
          theme: {
            ...state.theme,
            theme: storedTheme,
            resolvedTheme,
            isLoading: false,
          },
        }));

        logger.info("Theme initialized", { theme: storedTheme, resolvedTheme });
      },
    },
  };
};

// Create store factory
export const createThemeStore = () =>
  createStore<ThemeStore>((set, get, api) => ({
    ...createThemeSlice(set, get, api),
  }));

// Create provider and hooks
export const {
  Provider: ThemeStoreProvider,
  useStore: useThemeStore,
  useStoreApi: useThemeStoreApi,
} = createStoreProvider(createThemeStore, "ThemeStore");
