import { useCallback } from "react";

import { KlineData } from "@/entities/chart";

import { usePriceUpdates } from "../hooks/use-price-updates";

interface UseChartPriceUpdatesReturn {
  handleKlineUpdate: (symbol: string, kline: KlineData) => void;
  handleChartConnection: (isConnected: boolean) => void;
}

export function useChartPriceUpdates(): UseChartPriceUpdatesReturn {
  const { updatePrice, updateConnection } = usePriceUpdates();

  const handleKlineUpdate = useCallback(
    (symbol: string, kline: KlineData) => {
      if (!kline || !kline.close || kline.close <= 0) return;

      updatePrice(symbol, {
        price: kline.close,
        source: "chart",
        high24h: kline.high,
        low24h: kline.low,
        volume24h: kline.volume,
      });
    },
    [updatePrice]
  );

  const handleChartConnection = useCallback(
    (isConnected: boolean) => {
      updateConnection("chart", isConnected);
    },
    [updateConnection]
  );

  return {
    handleKlineUpdate,
    handleChartConnection,
  };
}
