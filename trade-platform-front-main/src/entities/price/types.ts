export type PriceSource = "priority" | "realtime" | "ticker" | "chart" | "orderbook" | "fallback";

export interface PriceData {
  price: number;
  timestamp: number;
  source: PriceSource;
  symbol: string;
  change24h?: number;
  changePercent24h?: number;
  high24h?: number;
  low24h?: number;
  volume24h?: number;
}

export interface PriceUpdate {
  price: number;
  source: PriceSource;
  change24h?: number;
  changePercent24h?: number;
  high24h?: number;
  low24h?: number;
  volume24h?: number;
}

export interface SymbolPriceData {
  current: PriceData | null;
  sources: Map<PriceSource, PriceData>;
  lastUpdate: number;
  isStale: boolean;
}

export interface PriceConnectionStatus {
  source: PriceSource;
  isConnected: boolean;
  lastPing: number;
  reconnectAttempts: number;
}

export interface PriceStoreState {
  prices: Map<string, SymbolPriceData>;
  connections: Map<PriceSource, PriceConnectionStatus>;
  sourcePriorities: Map<PriceSource, number>;
  updatePrice: (symbol: string, update: PriceUpdate) => void;
  updateConnection: (source: PriceSource, isConnected: boolean) => void;
  getPrice: (symbol: string) => PriceData | null;
  clearStale: () => void;
  reset: () => void;
}

export const SOURCE_PRIORITIES: Record<PriceSource, number> = {
  priority: 12, // Priority price updates - highest priority, no jumps on timeframe switch
  ticker: 10, // Unified ticker - primary source of truth
  realtime: 8, // Real-time price feeds
  chart: 6, // Chart data
  orderbook: 4, // Orderbook prices
  fallback: 2, // Reserved for future fallback mechanisms (currently unused)
};

export const STALE_TIMEOUT = 30000; // 30 seconds
export const CLEANUP_INTERVAL = 60000; // 1 minute
