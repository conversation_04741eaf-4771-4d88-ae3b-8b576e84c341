import { useEffect, useMemo, useRef, useState } from "react";
import { useShallow } from "zustand/react/shallow";

import { usePriceStore } from "../model/store";
import { PriceData } from "../types";

interface UsePriceStabilizedReturn {
  price: number | null;
  priceData: PriceData | null;
  isConnected: boolean;
  isStale: boolean;
  change24h: number;
  changePercent24h: number;
  high24h: number | null;
  low24h: number | null;
  volume24h: number | null;
}

const STABILIZATION_DELAY = 100; // 100ms delay to avoid jumps
const MAX_PRICE_CHANGE_THRESHOLD = 0.1; // 10% max change to consider valid

export function usePriceStabilized(symbol: string): UsePriceStabilizedReturn {
  const [stabilizedPrice, setStabilizedPrice] = useState<number | null>(null);
  const [stabilizedData, setStabilizedData] = useState<PriceData | null>(null);
  const stabilizationTimerRef = useRef<NodeJS.Timeout | null>(null);
  const lastValidPriceRef = useRef<number | null>(null);

  const { priceData, isStale, connectionCount, totalConnections } = usePriceStore(
    useShallow((state) => {
      const symbolData = state.prices.get(symbol);
      const connections = Array.from(state.connections.values());
      const connectedCount = connections.filter((c) => c.isConnected).length;

      return {
        priceData: symbolData?.current || null,
        isStale: symbolData?.isStale || false,
        connectionCount: connectedCount,
        totalConnections: connections.length,
      };
    })
  );

  const isConnected = connectionCount > 0;

  // Stabilization logic
  useEffect(() => {
    if (!priceData?.price) return;

    const newPrice = priceData.price;

    // If we don't have a last valid price, accept immediately
    if (!lastValidPriceRef.current) {
      lastValidPriceRef.current = newPrice;
      setStabilizedPrice(newPrice);
      setStabilizedData(priceData);
      return;
    }

    // Check if price change is reasonable
    const priceChangePercent =
      Math.abs(newPrice - lastValidPriceRef.current) / lastValidPriceRef.current;

    // If price change is too large, wait for stabilization
    if (priceChangePercent > MAX_PRICE_CHANGE_THRESHOLD) {
      // Clear existing timer
      if (stabilizationTimerRef.current) {
        clearTimeout(stabilizationTimerRef.current);
      }

      // Set new stabilization timer
      stabilizationTimerRef.current = setTimeout(() => {
        // Re-check the price after delay
        const currentState = usePriceStore.getState();
        const currentSymbolData = currentState.prices.get(symbol);
        const currentPriceData = currentSymbolData?.current;

        if (currentPriceData?.price) {
          lastValidPriceRef.current = currentPriceData.price;
          setStabilizedPrice(currentPriceData.price);
          setStabilizedData(currentPriceData);
        }

        stabilizationTimerRef.current = null;
      }, STABILIZATION_DELAY);
    } else {
      // Price change is reasonable, update immediately
      lastValidPriceRef.current = newPrice;
      setStabilizedPrice(newPrice);
      setStabilizedData(priceData);

      // Clear any pending stabilization
      if (stabilizationTimerRef.current) {
        clearTimeout(stabilizationTimerRef.current);
        stabilizationTimerRef.current = null;
      }
    }
  }, [priceData?.price, symbol]);

  // Cleanup
  useEffect(() => {
    return () => {
      if (stabilizationTimerRef.current) {
        clearTimeout(stabilizationTimerRef.current);
      }
    };
  }, []);

  const result = useMemo<UsePriceStabilizedReturn>(
    () => ({
      price: stabilizedPrice,
      priceData: stabilizedData,
      isConnected,
      isStale,
      change24h: stabilizedData?.change24h || 0,
      changePercent24h: stabilizedData?.changePercent24h || 0,
      high24h: stabilizedData?.high24h || null,
      low24h: stabilizedData?.low24h || null,
      volume24h: stabilizedData?.volume24h || null,
    }),
    [stabilizedPrice, stabilizedData, isConnected, isStale]
  );

  return result;
}
