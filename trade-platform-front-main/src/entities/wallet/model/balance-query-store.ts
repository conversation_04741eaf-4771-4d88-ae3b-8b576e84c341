"use client";

import { devtools } from "zustand/middleware";
import { createStore } from "zustand/vanilla";

import { FixedBalanceData, BalanceItem } from "@/shared/api/services/balance-service";
import { createModuleLogger } from "@/shared/lib/logger";
import { StoreSlice } from "@/shared/lib/store/slice-creator";
import { createStoreProvider } from "@/shared/providers/store-provider";

// Create module-specific logger
const logger = createModuleLogger("balance-query-store");

// Define the types for the balance slice
export interface BalanceQueryState {
  // State
  availableBalanceUsd: number;
  lockedBalanceUsd: number;
  totalBalanceUsd: number;
  totalEquivalentUsd: string | null;
  balances: BalanceItem[];
  fixedBalance: FixedBalanceData;
  isLoading: boolean;
  isError: boolean;
  error: string | null;
  isWebSocketConnected: boolean;
  userId: string | null;

  // Actions
  setBalanceData: (data: {
    availableBalanceUsd?: number;
    lockedBalanceUsd?: number;
    totalBalanceUsd?: number;
    totalEquivalentUsd?: string | null;
    balances?: BalanceItem[];
    fixedBalance?: FixedBalanceData;
    isLoading?: boolean;
    isError?: boolean;
    error?: string | null;
    isWebSocketConnected?: boolean;
    userId?: string | null;
  }) => void;
  reset: () => void;
}

// Define the full store type
export interface BalanceQueryStore {
  balanceQuery: BalanceQueryState;
}

// Create the balance slice
export const createBalanceQuerySlice: StoreSlice<
  BalanceQueryStore,
  { balanceQuery: BalanceQueryState }
> = (set) => ({
  balanceQuery: {
    // Initial state
    availableBalanceUsd: 0,
    lockedBalanceUsd: 0,
    totalBalanceUsd: 0,
    totalEquivalentUsd: null,
    balances: [],
    fixedBalance: {
      fixed_usd_balance: "0.00",
      available_usd_balance: "0.00",
      locked_usd_balance: "0.00",
    },
    isLoading: false,
    isError: false,
    error: null,
    isWebSocketConnected: false,
    userId: null,

    // Actions
    setBalanceData: (data) => {
      logger.debug("Updating balance data", { data });
      set((state) => ({
        balanceQuery: {
          ...state.balanceQuery,
          ...data,
        },
      }));
    },

    reset: () => {
      logger.info("Resetting balance query store");
      set((state) => ({
        balanceQuery: {
          ...state.balanceQuery,
          availableBalanceUsd: 0,
          lockedBalanceUsd: 0,
          totalBalanceUsd: 0,
          totalEquivalentUsd: null,
          balances: [],
          fixedBalance: {
            fixed_usd_balance: "0.00",
            available_usd_balance: "0.00",
            locked_usd_balance: "0.00",
          },
          isLoading: false,
          isError: false,
          error: null,
        },
      }));
    },
  },
});

// Create store factory function
export const createBalanceQueryStore = () =>
  createStore<BalanceQueryStore>()(
    devtools(
      (set, get, api) => ({
        ...createBalanceQuerySlice(set, get, api),
      }),
      { name: "balance-query-store" }
    )
  );

// Create provider and hooks
export const {
  Provider: BalanceQueryStoreProvider,
  useStore: useBalanceQueryStore,
  useStoreApi: useBalanceQueryStoreApi,
} = createStoreProvider(createBalanceQueryStore, "BalanceQueryStore");

// Export singleton instance for direct access when needed
export const balanceQueryStore = createBalanceQueryStore();
