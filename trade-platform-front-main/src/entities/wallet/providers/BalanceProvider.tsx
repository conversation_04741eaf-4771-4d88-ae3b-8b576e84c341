"use client";

import React, { ReactNode, useEffect, useState } from "react";

import { authUtils } from "@/entities/auth";
import { tokenStore } from "@/entities/auth/model/token-store";
import { useBalanceQuery } from "@/entities/wallet/api/use-balance-query";
import { BalanceQueryStoreProvider } from "@/entities/wallet/model/balance-query-store";
import { createModuleLogger } from "@/shared/lib/logger";

// Create module-specific logger
const logger = createModuleLogger("balance-provider");

interface BalanceProviderProps {
  children: ReactNode;
}

/**
 * Balance Provider Component
 * Initializes the balance fetching and provides the Zustand store context
 * This should be placed high in the component tree where balance data is needed
 */
const BalanceDataFetcher: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Use simple approach - check token directly from authUtils
  // This avoids SSR issues with Zustand
  const token = authUtils.getToken();
  const isAuthenticated = !!(token && token.trim().length > 0);

  // Get user ID from auth utils
  const userId = isAuthenticated ? authUtils.getUserId() : null;

  // Additional validation - don't fetch if no userId
  const shouldFetch =
    isAuthenticated && userId && typeof userId === "string" && userId.trim().length > 0;

  // Debug logging
  logger.debug("BalanceProvider auth check", {
    hasToken: !!token,
    tokenLength: token?.length || 0,
    isAuthenticated,
    userId,
    userIdType: typeof userId,
    shouldFetch,
  });

  // Additional validation - don't fetch if no userId
  const shouldFetchBalance =
    isAuthenticated && userId && typeof userId === "string" && userId.trim().length > 0;

  // Debug logging
  logger.debug("BalanceProvider auth check", {
    hasToken: !!token,
    tokenLength: token?.length || 0,
    isAuthenticated,
    userId,
    userIdType: typeof userId,
    shouldFetch: shouldFetchBalance,
  });

  // Use the balance query hook to fetch and sync data to Zustand store
  // Configure with optimal settings to reduce API calls while maintaining data freshness
  const balanceQuery = useBalanceQuery({
    enabled: shouldFetchBalance || false, // Only enable if authenticated and has userId
    staleTime: 60000, // Consider data fresh for 1 minute
    manualUserId: userId || undefined, // Use actual user ID from auth
  });

  // Extract commonly used fields from the query result
  const { isLoading, isError, error } = balanceQuery;

  // Log the initialization of the balance query
  useEffect(() => {
    logger.info("Balance Provider initialized", {
      authenticated: isAuthenticated,
      userId: userId ? "present" : "not found",
      loading: isLoading,
      error: isError ? String(error) : null,
    });

    // Check if data is properly flowing through the system
    if (!isLoading && !isError && balanceQuery) {
      logger.info("Balance Provider data check", {
        hasFixedBalance: !!balanceQuery.fixedBalance,
        hasBalances: !!balanceQuery.balances && balanceQuery.balances.length > 0,
        balancesCount: balanceQuery.balances?.length || 0,
        availableBalanceUsd: balanceQuery.availableBalanceUsd,
        totalBalanceUsd: balanceQuery.totalBalanceUsd,
        hasTotalEquivalentUsd: !!balanceQuery.totalEquivalentUsd,
        fixedQueryStatus: balanceQuery.fixedBalanceQuery?.status || "unknown",
        balancesQueryStatus: balanceQuery.balancesQuery?.status || "unknown",
      });
    }
  }, [isAuthenticated, userId, isLoading, isError, error, balanceQuery]);

  return <>{children}</>;
};

/**
 * Balance Provider Component with Store
 * Combines the Zustand store provider with the data fetcher
 */
export const BalanceProvider: React.FC<BalanceProviderProps> = ({ children }) => {
  return (
    <BalanceQueryStoreProvider>
      <BalanceDataFetcher>{children}</BalanceDataFetcher>
    </BalanceQueryStoreProvider>
  );
};

export default BalanceProvider;
