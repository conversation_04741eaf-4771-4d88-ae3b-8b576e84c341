/**
 * Utility functions for debugging React Query and other parts of the application
 */

import { createModuleLogger } from "./logger";

// Create a logger for debugging utilities
const logger = createModuleLogger("debug-utils");

/**
 * Adds a debug function to the window object for inspecting React Query cache
 * This should only be used in development
 */
export function setupDebugUtils() {
  if (process.env.NODE_ENV !== "production" && typeof window !== "undefined") {
    // Add a global debugCache function
    (window as any).debugCache = (queryClientObject: unknown, queryKey: unknown) => {
      try {
        if (!queryClientObject || !(queryClientObject as any).getQueryCache) {
          logger.error("Invalid QueryClient provided");
          return;
        }

        // Get query data based on key
        const queryData = (queryClientObject as any).getQueryData(queryKey);

        // Get the query instance
        const query = (queryClientObject as any).getQueryCache().find(queryKey);

        logger.info("==== DEBUG QUERY CACHE DATA ====", {});
        logger.info("QueryKey:", { queryKey });
        logger.info("Has Data:", { hasData: !!queryData });

        if (queryData) {
          logger.info("Data:", { data: queryData });
          logger.info("Data Type:", { type: typeof queryData });
          logger.info("Data Structure:", {
            structure:
              typeof queryData === "object" && queryData ? Object.keys(queryData as object) : [],
          });

          // If data has balances, show some stats
          if ((queryData as any).balances) {
            logger.info(
              "Balances Count:",
              Array.isArray((queryData as any).balances)
                ? (queryData as any).balances.length
                : "Not an array"
            );
            if (
              Array.isArray((queryData as any).balances) &&
              (queryData as any).balances.length > 0
            ) {
              logger.info("First Balance Item:", (queryData as any).balances[0]);
            }
          }

          // Check for total_equivalent_usd
          if ("total_equivalent_usd" in (queryData as object)) {
            logger.info("total_equivalent_usd:", (queryData as any).total_equivalent_usd);
          }
        }

        if (query) {
          logger.info("==== QUERY DETAILS ====", {});
          // logger.info("Status:", { status: query.state.status });
          logger.info("Fetch Status:", { fetchStatus: query.state.fetchStatus });
          logger.info("Data Updated At:", {
            dataUpdatedAt: new Date(query.state.dataUpdatedAt).toISOString(),
          });
          logger.info("Is Fetching:", { isFetching: query.state.isFetching });
          logger.info("Is Stale:", { isStale: query.state.isStale });
          logger.info("Update Count:", { updateCount: query.state.dataUpdateCount });
          logger.info("Error:", { error: query.state.error });

          // logger.info("==== QUERY OPTIONS ====", {});
          logger.info("Stale Time:", { staleTime: query.options.staleTime });
          logger.info("GC Time:", { gcTime: query.options.gcTime });
          logger.info("Refetch On Window Focus:", {
            refetchOnWindowFocus: query.options.refetchOnWindowFocus,
          });
          logger.info("Retry:", { retry: query.options.retry });
        } else {
          logger.info("Query not found for key:", { queryKey });
        }
      } catch (error) {
        logger.error("Error inspecting cache:", { error });
      }
    };

    // Add a shortcut to debug balance queries
    (window as any).debugBalanceQueries = (queryClientObject: unknown) => {
      try {
        if (!queryClientObject || !(queryClientObject as any).getQueryCache) {
          logger.error("Invalid QueryClient provided");
          return;
        }

        const allQueries = (queryClientObject as any).getQueryCache().getAll();
        const balanceQueries = allQueries.filter((query: any) => {
          const queryKey = query.queryKey;
          return Array.isArray(queryKey) && queryKey.length > 0 && queryKey[0] === "balance";
        });

        logger.info("==== ALL BALANCE QUERIES ====");
        // logger.info("Total Balance Queries:", balanceQueries.length);

        balanceQueries.forEach((query: any, index: number) => {
          // logger.info(`\n[QUERY ${index + 1}] Key: ${JSON.stringify(query.queryKey)}`, {});
          // logger.info("Status:", { status: query.state.status });
          logger.info("Has Data:", { hasData: !!query.state.data });

          if (query.state.data) {
            // logger.info("Data Keys:", Object.keys(query.state.data));
            if ("balances" in query.state.data) {
              logger.info(
                "Balances Count:",
                Array.isArray(query.state.data.balances)
                  ? query.state.data.balances.length
                  : "Not an array"
              );
            }
            if ("total_equivalent_usd" in query.state.data) {
              logger.info("total_equivalent_usd:", query.state.data.total_equivalent_usd);
            }
          }
        });

        logger.info("\nTo inspect a specific query, use:");
        logger.info('window.debugCache(queryClient, ["balance", "all", "13"])');
        logger.info('window.debugCache(queryClient, ["balance", "fixed", "13"])');
      } catch (error) {
        logger.error("Error listing balance queries:", { error });
      }
    };

    // logger.info("Debug utilities added to window object", {
    //   availableFunctions: [
    //     "window.debugCache(queryClient, queryKey)",
    //     "window.debugBalanceQueries(queryClient)",
    //   ],
    // });
  }
}

export default setupDebugUtils;
