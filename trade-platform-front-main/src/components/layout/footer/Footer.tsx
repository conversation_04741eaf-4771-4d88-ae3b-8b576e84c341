"use client";

import { motion, useAnimation, Variants, useScroll, useTransform } from "framer-motion";
import { Send, Twitter, Facebook, Instagram, ArrowRight, Mail, ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useEffect, useState, useRef } from "react";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

import styles from "./Footer.module.css";

export function Footer() {
  const [isInView, setIsInView] = useState(false);
  const [currentYear, setCurrentYear] = useState(2025); // Default year to prevent hydration mismatch
  const controls = useAnimation();
  const ref = useRef<HTMLElement>(null);
  const emailRef = useRef<HTMLInputElement>(null);

  // For scroll-based parallax effect on curved lines
  const { scrollYProgress } = useScroll({
    target: ref,
    offset: ["start end", "end start"],
  });

  const parallaxY1 = useTransform(scrollYProgress, [0, 1], [0, -30]);
  const parallaxY2 = useTransform(scrollYProgress, [0, 1], [0, -15]);
  const parallaxY3 = useTransform(scrollYProgress, [0, 1], [0, -45]);

  // Set current year after hydration to prevent mismatch
  useEffect(() => {
    setCurrentYear(new Date().getFullYear());
  }, []);

  // Check if the footer is in view
  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsInView(entry.isIntersecting);
      },
      { threshold: 0.1 }
    );

    const currentRef = ref.current;

    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, []);

  // Start animations when in view
  useEffect(() => {
    if (isInView) {
      controls.start("visible");
    }
  }, [isInView, controls]);

  const handleSubscribe = (e: React.FormEvent) => {
    e.preventDefault();

    // Get email value
    const email = emailRef.current?.value;

    if (email) {
      // In a real app, you would send this to your API
      // For now, just clear the input

      // Clear the input
      if (emailRef.current) {
        emailRef.current.value = "";
      }
    }
  };

  // Animation variants
  const containerVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5,
        staggerChildren: 0.08,
      },
    },
  };

  const itemVariants: Variants = {
    hidden: { opacity: 0, y: 15 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.4,
        ease: [0.22, 1, 0.36, 1], // Custom ease curve for smoother animation
      },
    },
  };

  const socialIconVariants: Variants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3, ease: "easeOut" },
    },
    hover: {
      scale: 1.15,
      backgroundColor: "rgba(59, 130, 246, 0.3)",
      boxShadow: "0 0 12px rgba(59, 130, 246, 0.3)",
      transition: {
        duration: 0.2,
        type: "spring",
        stiffness: 400,
        damping: 10,
      },
    },
  };

  const linkVariants: Variants = {
    hover: {
      x: 5,
      color: "#3b82f6",
      transition: {
        type: "spring",
        stiffness: 400,
        damping: 10,
      },
    },
  };

  const backgroundCurveVariants: Variants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 0.05,
      transition: {
        duration: 1.2,
        ease: "easeOut",
      },
    },
  };

  return (
    <motion.footer
      ref={ref}
      className={styles.footer}
      initial="hidden"
      animate={controls}
      variants={containerVariants}
    >
      {/* Background decoration elements */}
      <div className={styles.footerBackground}>
        <motion.div
          className={styles.curve1}
          variants={backgroundCurveVariants}
          style={{ y: parallaxY1 }}
        ></motion.div>
        <motion.div
          className={styles.curve2}
          variants={backgroundCurveVariants}
          style={{ y: parallaxY2 }}
        ></motion.div>
        <motion.div
          className={styles.curve3}
          variants={backgroundCurveVariants}
          style={{ y: parallaxY3 }}
        ></motion.div>
      </div>

      <div className={styles.footerContent}>
        <div className={styles.footerInner}>
          <div className={styles.footerTop}>
            <motion.div variants={itemVariants} className={styles.footerLeft}>
              <div className={styles.logoSection}>
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: -10 }}
                  transition={{
                    duration: 0.6,
                    ease: [0.22, 1, 0.36, 1],
                    delay: 0.1,
                  }}
                >
                  <Link href="/">
                    <Image
                      src="/LogoWhite.svg"
                      alt="BitMei"
                      width={140}
                      height={40}
                      className={styles.footerLogo}
                      priority
                    />
                  </Link>
                </motion.div>
                <motion.p variants={itemVariants} className={styles.footerDescription}>
                  BitMei provides a comprehensive trading platform for cryptocurrency enthusiasts,
                  with real-time market data and secure transactions.
                </motion.p>
                <motion.div className={styles.socialIcons} variants={itemVariants}>
                  <motion.div
                    variants={socialIconVariants}
                    whileHover="hover"
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href="https://twitter.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="Twitter"
                    >
                      <div className={styles.socialIcon}>
                        <Twitter size={16} />
                      </div>
                    </Link>
                  </motion.div>
                  <motion.div
                    variants={socialIconVariants}
                    whileHover="hover"
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href="https://facebook.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="Facebook"
                    >
                      <div className={styles.socialIcon}>
                        <Facebook size={16} />
                      </div>
                    </Link>
                  </motion.div>
                  <motion.div
                    variants={socialIconVariants}
                    whileHover="hover"
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href="https://instagram.com"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="Instagram"
                    >
                      <div className={styles.socialIcon}>
                        <Instagram size={16} />
                      </div>
                    </Link>
                  </motion.div>
                  <motion.div
                    variants={socialIconVariants}
                    whileHover="hover"
                    whileTap={{ scale: 0.95 }}
                  >
                    <Link
                      href="https://t.me"
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label="Telegram"
                    >
                      <div className={styles.socialIcon}>
                        <Send size={16} />
                      </div>
                    </Link>
                  </motion.div>
                </motion.div>
              </div>
            </motion.div>

            <motion.div variants={itemVariants} className={styles.footerLinks}>
              <motion.div variants={itemVariants} className={styles.linksColumn}>
                <motion.h3 variants={itemVariants} className={styles.columnTitle}>
                  Platform
                </motion.h3>
                <ul className={styles.linksList}>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/trade" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Trade</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/dashboard/deposit" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Deposit/Withdraw</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/affiliate" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Affiliate Program</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/dashboard" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Account</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                </ul>
              </motion.div>

              <motion.div variants={itemVariants} className={styles.linksColumn}>
                <motion.h3 variants={itemVariants} className={styles.columnTitle}>
                  Resources
                </motion.h3>
                <ul className={styles.linksList}>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/faq" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>FAQ</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/guide" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Trading Guide</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/blog" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Blog</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                </ul>
              </motion.div>

              <motion.div variants={itemVariants} className={styles.linksColumn}>
                <motion.h3 variants={itemVariants} className={styles.columnTitle}>
                  Help
                </motion.h3>
                <ul className={styles.linksList}>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/support" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Customer Support</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/terms" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Terms & Conditions</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/privacy" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>Privacy Policy</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                  <motion.li variants={itemVariants}>
                    <motion.div whileHover="hover" variants={linkVariants}>
                      <Link href="/about" className={styles.footerLink}>
                        <ChevronRight className={styles.linkIcon} size={14} />
                        <span>About us</span>
                      </Link>
                    </motion.div>
                  </motion.li>
                </ul>
              </motion.div>
            </motion.div>
          </div>

          <Separator className={styles.footerSeparator} />

          {/* Newsletter subscription section */}
          <motion.div className={styles.subscribeSection} variants={itemVariants}>
            {/* Decorative elements */}
            <div className={styles.subscribeDecoration}>
              <motion.div
                className={styles.decorCircle1}
                animate={{
                  scale: [1, 1.05, 1],
                  opacity: [0.1, 0.15, 0.1],
                }}
                transition={{
                  duration: 5,
                  repeat: Infinity,
                  ease: "easeInOut",
                }}
              />
              <motion.div
                className={styles.decorCircle2}
                animate={{
                  scale: [1, 1.1, 1],
                  opacity: [0.07, 0.12, 0.07],
                }}
                transition={{
                  duration: 6,
                  repeat: Infinity,
                  ease: "easeInOut",
                  delay: 0.5,
                }}
              />
            </div>
            <motion.div className={styles.subscribeContent} variants={itemVariants}>
              <div className={styles.subscribeText}>
                <motion.h3 variants={itemVariants} className={styles.subscribeTitle}>
                  <Mail className={styles.columnIcon} size={16} />
                  Subscribe to Newsletter
                </motion.h3>
                <motion.p variants={itemVariants}>
                  Get the latest updates, news and product announcements
                </motion.p>
              </div>
              <motion.div className={styles.subscribeForm} variants={itemVariants}>
                <form onSubmit={handleSubscribe} className="w-full">
                  <div className={styles.inputContainer}>
                    <Input
                      ref={emailRef}
                      type="email"
                      placeholder="Your email address"
                      className={cn(styles.subscribeInput, "h-10")}
                      required
                    />
                    <motion.div
                      whileHover={{ scale: 1.03 }}
                      whileTap={{ scale: 0.97 }}
                      transition={{ type: "spring", stiffness: 400, damping: 10 }}
                      className="sm:w-auto w-full"
                    >
                      <Button
                        type="submit"
                        variant="default"
                        size="default"
                        className={styles.subscribeButton}
                        onClick={(e) => {
                          e.preventDefault();
                          if (emailRef.current?.value) {
                            // In a production app, you would send this to your API
                            alert("Thank you for subscribing to our newsletter!");
                            if (emailRef.current) {
                              emailRef.current.value = "";
                            }
                          }
                        }}
                      >
                        <span>Subscribe</span>
                        <ArrowRight size={16} className="ml-1" />
                      </Button>
                    </motion.div>
                  </div>
                </form>
              </motion.div>
            </motion.div>
          </motion.div>

          <Separator className={styles.footerSeparator} style={{ marginTop: 24 }} />

          <motion.div className={styles.footerBottom} variants={itemVariants}>
            <motion.div className={styles.copyrightSection} variants={itemVariants}>
              <p className={styles.copyright}>© {currentYear} BitMei. All Rights Reserved.</p>
              <div className={styles.legalLinks}>
                <Link href="/terms" className={styles.legalLink}>
                  Terms
                </Link>
                <span className={styles.legalDivider}>•</span>
                <Link href="/privacy" className={styles.legalLink}>
                  Privacy
                </Link>
                <span className={styles.legalDivider}>•</span>
                <Link href="/cookies" className={styles.legalLink}>
                  Cookies
                </Link>
                <span className={styles.legalDivider}>•</span>
                <Link href="/sitemap" className={styles.legalLink}>
                  Sitemap
                </Link>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </motion.footer>
  );
}

export default Footer;
