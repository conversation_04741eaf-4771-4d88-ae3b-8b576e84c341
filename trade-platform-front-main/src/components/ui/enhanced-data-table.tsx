"use client";

import { ColumnDef } from "@tanstack/react-table";
import { Filter } from "lucide-react";
import * as React from "react";
import { DateRange } from "react-day-picker";

import { DataTable } from "@/components/ui/data-table";
import { DatePickerWithRange } from "@/components/ui/date-range-picker";
import { cn } from "@/lib/utils";

interface EnhancedDataTableProps<TData, TValue> {
  columns: ColumnDef<TData, TValue>[];
  data: TData[];
  tableId?: string;
  dateRange?: DateRange | undefined;
  setDateRange?: React.Dispatch<React.SetStateAction<DateRange | undefined>>;
  filterableColumns?: {
    id: string;
    title: string;
    options: {
      value: string;
      label: string;
    }[];
  }[];
  searchableColumns?: {
    id: string;
    title: string;
  }[];
  filterTabs?: {
    id: string;
    label: string;
    count?: number;
    filterFn: (item: TData) => boolean;
  }[];
  exportable?: boolean;
  dateField?: string;
  customActions?: React.ReactNode;
  rowSelection?: Record<string, boolean>;
  onRowSelectionChange?: (selection: Record<string, boolean>) => void;
  getRowId?: (row: TData) => string;
  defaultHiddenColumns?: string[];
  onRowClick?: (row: TData) => void;
  onColumnVisibilityChange?: () => void;

  // External state management
  externalActiveFilterTab?: string;
  onFilterTabChange?: (tab: string) => void;
  externalColumnVisibility?: Record<string, boolean>;
  onColumnVisibilityExternalChange?: (visibility: Record<string, boolean>) => void;
  externalPageSize?: number;
  onPageSizeChange?: (size: number) => void;
  externalSorting?: Array<{ id: string; desc: boolean }>;
  onSortingChange?: (sorting: Array<{ id: string; desc: boolean }>) => void;
}

export function EnhancedDataTable<TData, TValue>({
  columns,
  data,
  tableId,
  dateRange,
  setDateRange,
  filterableColumns = [],
  searchableColumns = [],
  filterTabs = [],
  exportable = false,
  dateField = "timestamp",
  customActions,
  rowSelection,
  onRowSelectionChange,
  getRowId,
  defaultHiddenColumns = [],
  onRowClick,
  onColumnVisibilityChange,
  externalActiveFilterTab,
  onFilterTabChange,
  externalColumnVisibility,
  onColumnVisibilityExternalChange,
  externalPageSize,
  onPageSizeChange,
  externalSorting,
  onSortingChange,
}: EnhancedDataTableProps<TData, TValue>) {
  // Apply date range filter if provided
  const filteredData = React.useMemo(() => {
    if (!dateRange?.from) return data;

    return data.filter((item: any) => {
      const itemDate = new Date(item[dateField]);

      // Filter by start date
      if (dateRange.from && itemDate < dateRange.from) {
        return false;
      }

      // Filter by end date (if provided)
      if (dateRange.to && itemDate > new Date(dateRange.to.setHours(23, 59, 59, 999))) {
        return false;
      }

      return true;
    });
  }, [data, dateRange, dateField]);

  return (
    <div className="h-full flex flex-col">
      {/* Date range picker */}
      {setDateRange && (
        <div className="mb-3">
          <div className="flex items-center gap-2 mb-2">
            <Filter className="h-3 w-3 text-muted-foreground" />
            <span className="text-xs font-medium">Date Range Filter</span>
          </div>
          <DatePickerWithRange
            date={dateRange}
            setDate={setDateRange}
            className={cn("w-full sm:w-auto")}
          />
        </div>
      )}

      {/* Standard data table */}
      <div className="flex-1 overflow-hidden">
        <DataTable
          columns={columns}
          data={filteredData}
          tableId={tableId}
          filterableColumns={filterableColumns}
          searchableColumns={searchableColumns}
          filterTabs={filterTabs}
          exportable={exportable}
          customActions={customActions}
          rowSelection={rowSelection}
          onRowSelectionChange={onRowSelectionChange}
          getRowId={getRowId}
          defaultHiddenColumns={defaultHiddenColumns}
          onRowClick={onRowClick}
          onColumnVisibilityChange={onColumnVisibilityChange}
          externalActiveFilterTab={externalActiveFilterTab}
          onFilterTabChange={onFilterTabChange}
          externalColumnVisibility={externalColumnVisibility}
          onColumnVisibilityExternalChange={onColumnVisibilityExternalChange}
          externalPageSize={externalPageSize}
          onPageSizeChange={onPageSizeChange}
          externalSorting={externalSorting}
          onSortingChange={onSortingChange}
        />
      </div>
    </div>
  );
}
