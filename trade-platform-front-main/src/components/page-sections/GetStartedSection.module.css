/* GetStartedSection styles */
.container {
  width: 100%;
  background: linear-gradient(90deg, #001047, #001e86, #001047);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  margin: 60px auto;
  box-shadow:
    0 10px 30px rgba(0, 0, 0, 0.3),
    0 0 0 1px rgba(59, 130, 246, 0.2),
    0 0 40px rgba(0, 10, 50, 0.2);
  max-width: 1200px;
  padding: 0;
  height: 320px;
  display: flex;
  align-items: center;
  justify-content: center;
}

:global(.dark) .container {
  background: linear-gradient(90deg, #000c2e, #001370, #000c2e);
  box-shadow:
    0 10px 40px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(59, 130, 246, 0.3),
    0 0 60px rgba(0, 10, 50, 0.3);
}

.background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}

/* Curved lines styling */
.curveLine1,
.curveLine2,
.curveLine3,
.curveLine4,
.curveLine5,
.curveLine6,
.curveLine7,
.curveLine8,
.curveLine9,
.curveLine10 {
  position: absolute;
  border: 1px solid rgba(255, 255, 255, 0.07);
  border-radius: 50%;
}

.curveLine1 {
  width: 1400px;
  height: 1400px;
  left: -500px;
  top: -1050px;
}

.curveLine2 {
  width: 1300px;
  height: 1300px;
  left: -450px;
  top: -950px;
}

.curveLine3 {
  width: 1200px;
  height: 1200px;
  left: -400px;
  top: -900px;
}

.curveLine4 {
  width: 1100px;
  height: 1100px;
  left: -375px;
  top: -825px;
}

.curveLine5 {
  width: 1000px;
  height: 1000px;
  left: -350px;
  top: -750px;
}

.curveLine6 {
  width: 900px;
  height: 900px;
  left: -325px;
  top: -675px;
}

.curveLine7 {
  width: 800px;
  height: 800px;
  left: -300px;
  top: -600px;
}

.curveLine8 {
  width: 700px;
  height: 700px;
  left: -275px;
  top: -525px;
}

.curveLine9 {
  width: 600px;
  height: 600px;
  left: -250px;
  top: -450px;
}

.curveLine10 {
  width: 500px;
  height: 500px;
  left: -225px;
  top: -375px;
}

.content {
  padding: 40px 20px;
  text-align: center;
  position: relative;
  z-index: 2;
  width: 100%;
  max-width: 600px;
}

.title {
  font-size: 36px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 16px;
}

.description {
  font-size: 16px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 32px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.storeButtons {
  display: flex;
  gap: 12px;
  justify-content: center;
  align-items: center;
  flex-wrap: wrap;
}

.storeLink {
  transition:
    transform 0.2s ease,
    opacity 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  padding: 0;
  cursor: pointer;
}

.storeLink:hover {
  transform: translateY(-5px);
  opacity: 0.9;
}

.storeBadge {
  height: 40px;
  width: auto;
  min-width: 130px;
  object-fit: contain;
}

.tradeButton {
  background: linear-gradient(to right, #1e88e5, #1565c0);
  color: white;
  font-weight: 600;
  font-size: 18px;
  padding: 12px 32px;
  border-radius: 8px;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(21, 101, 192, 0.3);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tradeButton:hover {
  background: linear-gradient(to right, #2196f3, #1976d2);
  box-shadow: 0 6px 16px rgba(33, 150, 243, 0.4);
  transform: translateY(-5px);
}

@media (max-width: 768px) {
  .container {
    margin: 40px auto;
    border-radius: 12px;
    height: auto;
    padding: 60px 0;
  }

  .title {
    font-size: 28px;
  }

  .description {
    font-size: 14px;
    margin-bottom: 24px;
  }

  /* Adjust curved lines for mobile */
  .curveLine1,
  .curveLine2,
  .curveLine3,
  .curveLine4,
  .curveLine5 {
    left: -450px;
  }
}

@media (max-width: 480px) {
  .container {
    margin: 30px auto;
    padding: 40px 0;
  }

  .title {
    font-size: 24px;
  }

  .storeButtons {
    flex-direction: column;
    align-items: center;
    gap: 12px;
  }

  /* Further adjust curved lines for smaller screens */
  .curveLine1,
  .curveLine2,
  .curveLine3,
  .curveLine4,
  .curveLine5 {
    left: -500px;
  }
}
