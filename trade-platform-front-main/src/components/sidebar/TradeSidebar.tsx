"use client";

import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod";
import { motion, AnimatePresence } from "framer-motion";
import {
  AlertTriangle,
  BarChart4,
  Info,
  Settings,
  TrendingDown,
  TrendingUp,
  Wallet,
  X,
  Zap,
} from "lucide-react";
import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import * as z from "zod";

// UI Components
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
} from "@/components/ui/drawer";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

// Utils & Services
import { useAuthStore } from "@/entities/auth/model/store";
import { usePrice, useUnifiedTickerForPair } from "@/entities/price";
import {
  TradeCalculationRequest,
  TradeCalculationResponse,
  TradeExecutionRequest,
} from "@/entities/trade/types";
import { useOptimisticBalanceV2, useBalanceSync } from "@/entities/wallet";
import { calculateTradeHTTP } from "@/features/trade/api/analysis-service";
import { useExecuteTrade } from "@/features/trade/api/hooks/usePositions";
import { createModuleLogger } from "@/lib/logger";
import { showErrorToast } from "@/lib/toast-utils";
import { cn } from "@/lib/utils";

// Import components
import { AnimatedBalance } from "./AnimatedBalance";
import { AnimatedPrice } from "./AnimatedPrice";
import { CoinIcon } from "./CoinIcon";
import { SliderWithDrag } from "./SliderWithDrag";
import styles from "./TradeSidebar.module.css";
import { PairInfo } from "./types";

// Component props
interface TradeSidebarProps {
  currentPrice?: number;
  selectedPair?: PairInfo;
  priceChange?: "up" | "down" | null;
  onSidebarStateChange?: (isExpanded: boolean) => void;
}

// Create logger
const logger = createModuleLogger("trade-sidebar");

// Format number
const formatNumber = (num: number, decimals: number = 2): string => {
  return num.toLocaleString("en-US", {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals,
  });
};

// Form schema
const tradeFormSchema = z.object({
  orderType: z.enum(["Market", "Limit"]),
  orderSide: z.enum(["Long", "Short"]),
  price: z.number().positive("Price must be positive"),
  amount: z.string().refine((val) => {
    const num = parseFloat(val);
    return !isNaN(num) && num > 0;
  }, "Enter a valid amount"),
  leverage: z.number().min(1).max(100),
  stopLoss: z.number().positive().optional().nullable(),
  takeProfit: z.number().positive().optional().nullable(),
  advancedMode: z.boolean(),
});

type TradeFormValues = z.infer<typeof tradeFormSchema>;

// Balance skeleton component
const BalanceSkeleton = ({
  isLoading,
  children,
  skeletonClass = "w-20 h-4",
}: {
  isLoading: boolean;
  children: React.ReactNode;
  skeletonClass?: string;
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) return <>{children}</>;
  if (isLoading) return <Skeleton className={skeletonClass} />;
  return <>{children}</>;
};

// Parameter row component - Memoized to prevent re-renders
const ParameterRow = React.memo<{
  label: string;
  value: string | number | React.ReactNode;
  valueColor?: string;
  tooltip?: string;
  icon?: React.ReactNode;
}>(({ label, value, valueColor, tooltip, icon }) => (
  <div className="flex justify-between items-center py-1">
    <div className="flex items-center gap-1">
      {icon}
      <span className="text-xs text-muted-foreground font-medium">{label}</span>
      {tooltip && (
        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Info size={10} className="text-muted-foreground/70 cursor-help" />
            </TooltipTrigger>
            <TooltipContent className="max-w-[250px] text-xs p-2">
              <p>{tooltip}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      )}
    </div>
    <span className={cn("text-xs font-semibold", valueColor)}>{value}</span>
  </div>
));

ParameterRow.displayName = "ParameterRow";

// Loading spinner
const LoadingSpinner = ({ size = 14 }: { size?: number }) => (
  <svg
    className="animate-spin"
    width={size}
    height={size}
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    viewBox="0 0 24 24"
  >
    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
    <path
      className="opacity-75"
      fill="currentColor"
      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
    />
  </svg>
);

// Percentage button component
const PercentButton = React.memo<{
  percent: number;
  onClick: () => void;
  active?: boolean;
}>(({ percent, onClick, active }) => (
  <Button
    type="button"
    variant={active ? "default" : "outline"}
    size="sm"
    onClick={onClick}
    className={cn("h-7 text-xs font-medium", active && "bg-primary")}
  >
    {percent}%
  </Button>
));

PercentButton.displayName = "PercentButton";

// Main component
export const TradeSidebar: React.FC<TradeSidebarProps> = ({
  currentPrice: propCurrentPrice,
  selectedPair,
  priceChange,
  onSidebarStateChange,
}) => {
  // Connect to unified ticker for this pair
  useUnifiedTickerForPair(selectedPair?.symbol);

  // Get real-time price without stabilization for immediate updates
  const { price: unifiedPrice } = usePrice(selectedPair?.symbol || "");

  // Use unified price with fallback to prop price
  const currentPrice = unifiedPrice ?? propCurrentPrice ?? 0;

  // Balance state and sync
  const {
    availableBalance,
    totalBalanceEquivalentUsd,
    formattedTotalBalanceEquivalentUsd,
    isInitialLoading: balanceLoading,
    isOptimistic,
    optimisticAction,
  } = useOptimisticBalanceV2();
  const { invalidateBalance } = useBalanceSync();

  // State
  const [analysisResult, setAnalysisResult] = useState<TradeCalculationResponse | null>(null);
  const [calculating, setCalculating] = useState(false);
  const [activePercent, setActivePercent] = useState<number | null>(null);
  const [sliderDragging, setSliderDragging] = useState(false);
  const [feePercentage] = useState(0.1);
  const [lastClickedSide, setLastClickedSide] = useState<"Long" | "Short" | null>(null);

  // Trade execution
  const executeTradeOptimistic = useExecuteTrade();
  const [isExecutingTrade, setIsExecutingTrade] = useState(false);

  // Auth
  const user = useAuthStore((state) => state.auth.user);

  // UI states
  const [isMobile, setIsMobile] = useState(false);
  const [showMobilePanel, setShowMobilePanel] = useState(false);

  // Refs to prevent unnecessary re-renders
  const priceInputRef = useRef<HTMLInputElement>(null);
  const lastPriceRef = useRef<number>(currentPrice || 0);

  // Trading pair for API (keeps USDT for backend)
  const tradingPairForAPI = useMemo(() => {
    return selectedPair ? `${selectedPair.baseAsset}/${selectedPair.quoteAsset}` : "ETH/USDT";
  }, [selectedPair]);

  // Trading pair for display (shows USD to user)
  const tradingPairDisplay = useMemo(() => {
    return selectedPair ? selectedPair.displayName || `${selectedPair.baseAsset}/USD` : "ETH/USD";
  }, [selectedPair]);

  // Form setup
  const form = useForm<TradeFormValues>({
    resolver: zodResolver(tradeFormSchema),
    defaultValues: {
      orderType: "Market",
      orderSide: "Long",
      price: currentPrice || 0,
      amount: "",
      leverage: 20,
      stopLoss: null,
      takeProfit: null,
      advancedMode: true,
    },
    mode: "onChange",
  });

  const {
    watch,
    setValue,
    formState: { errors, isValid },
  } = form;
  const watchedValues = watch();

  // Insufficient balance check - memoized
  const insufficientBalance = useMemo(() => {
    if (!watchedValues.amount) return false;
    const amount = parseFloat(watchedValues.amount);
    const balance = totalBalanceEquivalentUsd || availableBalance;
    return amount > balance;
  }, [watchedValues.amount, totalBalanceEquivalentUsd, availableBalance]);

  // Check if trade is ready - more specific than form validation
  const isTradeReady = useMemo(() => {
    const amount = watchedValues.amount;
    const amountNum = parseFloat(amount || "0");

    // Check if amount is valid and positive
    const hasValidAmount = amount && !isNaN(amountNum) && amountNum > 0;

    // Check if price is valid (for limit orders)
    const hasValidPrice = watchedValues.orderType === "Market" || watchedValues.price > 0;

    return hasValidAmount && hasValidPrice && !insufficientBalance;
  }, [watchedValues.amount, watchedValues.price, watchedValues.orderType, insufficientBalance]);

  // Quick access values
  const positionSize = analysisResult?.position_size || 0;
  const liquidationPrice = analysisResult?.liquidation_price || 0;
  const distanceToLiquidation = analysisResult?.distance_to_liquidation_percent || 0;

  // Check mobile
  useEffect(() => {
    const checkIfMobile = () => setIsMobile(window.innerWidth < 1024);
    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  // Update price when currentPrice changes - optimized to prevent input clearing
  useEffect(() => {
    if (currentPrice !== undefined && watchedValues.orderType === "Market") {
      // Only update if price actually changed and input is not focused
      if (
        Math.abs(lastPriceRef.current - currentPrice) > 0.01 &&
        document.activeElement !== priceInputRef.current
      ) {
        setValue("price", currentPrice, { shouldValidate: false });
        lastPriceRef.current = currentPrice;
      }
    }
  }, [currentPrice, watchedValues.orderType, setValue]);

  // Reset form on pair change - don't reset if only price changes
  useEffect(() => {
    if (selectedPair) {
      const currentAmount = form.getValues("amount");
      form.reset({
        orderType: "Market",
        orderSide: "Long",
        price: currentPrice || 0,
        amount: currentAmount, // Preserve amount
        leverage: 20,
        stopLoss: null,
        takeProfit: null,
        advancedMode: true,
      });
      lastPriceRef.current = currentPrice || 0;
    }
  }, [selectedPair]); // Remove currentPrice from dependencies

  // Notify parent of sidebar state
  useEffect(() => {
    if (onSidebarStateChange) {
      onSidebarStateChange(showMobilePanel);
    }
  }, [showMobilePanel, onSidebarStateChange]);

  // Calculate trade parameters - debounced and optimized
  useEffect(() => {
    const calculateTradeParameters = async () => {
      const amount = watchedValues.amount;
      if (!amount || parseFloat(amount) <= 0) {
        setAnalysisResult(null);
        setCalculating(false);
        return;
      }

      try {
        setCalculating(true);

        const tradeRequest: TradeCalculationRequest = {
          entry_price: watchedValues.price,
          current_price: watchedValues.price,
          order_type: watchedValues.orderSide,
          fee_percentage: feePercentage,
          leverage: watchedValues.leverage,
          margin: parseFloat(amount),
          stop_loss: watchedValues.stopLoss,
          take_profit: watchedValues.takeProfit,
        };

        const result = await calculateTradeHTTP(tradeRequest);
        setAnalysisResult(result);
      } catch (error) {
        logger.error("Error calculating trade parameters", { error });
      } finally {
        setCalculating(false);
      }
    };

    const timer = setTimeout(calculateTradeParameters, 200); // Fast debounce for better UX
    return () => clearTimeout(timer);
  }, [
    watchedValues.orderSide,
    watchedValues.price,
    watchedValues.amount,
    watchedValues.leverage,
    watchedValues.stopLoss,
    watchedValues.takeProfit,
    feePercentage,
  ]);

  // Clear SL/TP when Advanced Mode is toggled off
  useEffect(() => {
    if (!watchedValues.advancedMode) {
      setValue("stopLoss", null, { shouldValidate: false });
      setValue("takeProfit", null, { shouldValidate: false });
    }
  }, [watchedValues.advancedMode, setValue]);

  // Validate SL/TP
  const validateSLTP = useCallback(
    (side: "Long" | "Short") => {
      const { price, stopLoss, takeProfit } = watchedValues;
      const errors: string[] = [];

      if (stopLoss) {
        if (side === "Long" && stopLoss >= price) {
          errors.push("Stop Loss must be lower than entry price for Long positions");
        } else if (side === "Short" && stopLoss <= price) {
          errors.push("Stop Loss must be higher than entry price for Short positions");
        }
      }

      if (takeProfit) {
        if (side === "Long" && takeProfit <= price) {
          errors.push("Take Profit must be higher than entry price for Long positions");
        } else if (side === "Short" && takeProfit >= price) {
          errors.push("Take Profit must be lower than entry price for Short positions");
        }
      }

      return errors;
    },
    [watchedValues]
  );

  // Execute trade with enhanced UX
  const executeTrade = useCallback(
    async (side: "Long" | "Short") => {
      // Set the clicked side for UI feedback
      setLastClickedSide(side);
      setValue("orderSide", side);

      const isFormValid = await form.trigger();
      if (!isFormValid) {
        setLastClickedSide(null);
        return;
      }

      const data = form.getValues();
      const userId = user?.id;

      if (!userId) {
        showErrorToast("Authentication Error", {
          description: "Please log in again",
        });
        setLastClickedSide(null);
        return;
      }

      // Validate SL/TP
      const validationErrors = validateSLTP(side);
      if (validationErrors.length > 0) {
        validationErrors.forEach((error) =>
          showErrorToast("Validation Error", { description: error })
        );
        setLastClickedSide(null);
        return;
      }

      // Check balance
      if (insufficientBalance) {
        showErrorToast("Insufficient Balance", {
          description: `You need $${parseFloat(data.amount).toFixed(2)} but only have $${(totalBalanceEquivalentUsd || availableBalance).toFixed(2)} available`,
        });
        setLastClickedSide(null);
        return;
      }

      const entryPrice = data.orderType === "Limit" ? data.price : currentPrice || data.price;

      const tradeRequest: TradeExecutionRequest = {
        user_id: userId,
        trading_pair: tradingPairForAPI,
        entry_price: entryPrice,
        current_price: currentPrice || data.price,
        order_type: side,
        order_execution_type: data.orderType.toUpperCase(),
        fee_percentage: feePercentage,
        leverage: data.leverage,
        margin: parseFloat(data.amount),
        stop_loss: data.stopLoss,
        take_profit: data.takeProfit,
      };

      try {
        setIsExecutingTrade(true);

        // Show immediate optimistic feedback with toast
        await toast.promise(executeTradeOptimistic.mutateAsync(tradeRequest), {
          loading: `Opening ${side} position...`,
          success: `${side} position opened successfully!`,
          error: "Failed to open position",
        });

        // Immediately invalidate balance to ensure user sees updated balance after trade execution
        invalidateBalance("Trade executed");

        // Reset form on success with smooth animation
        form.reset({
          ...form.getValues(),
          amount: "",
        });
        setActivePercent(null);
        if (isMobile) setShowMobilePanel(false);
      } catch (error) {
        logger.error("Trade execution error", { error });
      } finally {
        setIsExecutingTrade(false);
        setLastClickedSide(null);
      }
    },
    [
      setValue,
      form,
      user,
      validateSLTP,
      insufficientBalance,
      currentPrice,
      tradingPairForAPI,
      feePercentage,
      executeTradeOptimistic,
      totalBalanceEquivalentUsd,
      availableBalance,
      isMobile,
      invalidateBalance,
    ]
  );

  // Handle percentage
  const handlePercentage = useCallback(
    (percent: number) => {
      const totalBalance = totalBalanceEquivalentUsd || availableBalance;
      const calculatedAmount = ((totalBalance * percent) / 100).toFixed(2);
      setValue("amount", calculatedAmount, { shouldValidate: true });
      setActivePercent(percent);
    },
    [totalBalanceEquivalentUsd, availableBalance, setValue]
  );

  // Toggle mobile panel
  const toggleMobilePanel = useCallback(
    (side?: "Long" | "Short") => {
      if (side) setValue("orderSide", side);
      setShowMobilePanel(!showMobilePanel);
    },
    [setValue, showMobilePanel]
  );

  // Mobile version
  if (isMobile) {
    return (
      <>
        {/* Fixed bottom bar with Buy/Sell buttons */}
        <div
          className="fixed bottom-0 left-0 right-0 bg-background/95 backdrop-blur-sm border-t p-4 z-[1050]"
          style={{ paddingBottom: `calc(1rem + env(safe-area-inset-bottom, 0px))` }}
        >
          <div className="flex gap-3 w-full">
            <Button
              onClick={() => toggleMobilePanel("Long")}
              className={cn("flex-1 font-semibold rounded-xl h-[50px] text-base", styles.buyButton)}
            >
              <span className="flex items-center justify-center gap-2">
                <TrendingUp size={20} />
                <span className="font-semibold">Buy/Long</span>
              </span>
            </Button>
            <Button
              onClick={() => toggleMobilePanel("Short")}
              className={cn(
                "flex-1 font-semibold rounded-xl h-[50px] text-base",
                styles.sellButton
              )}
            >
              <span className="flex items-center justify-center gap-2">
                <TrendingDown size={20} />
                <span className="font-semibold">Sell/Short</span>
              </span>
            </Button>
          </div>
        </div>

        {/* Mobile drawer */}
        <Drawer open={showMobilePanel} onOpenChange={setShowMobilePanel}>
          <DrawerContent className="max-h-[85vh] bg-background border-t-2">
            <div className="mx-auto w-12 h-1.5 bg-muted-foreground/30 rounded-full mt-3 mb-4" />
            <DrawerHeader className="px-6 pb-4 border-b">
              <div className="flex items-center justify-between">
                <DrawerTitle className="text-xl font-bold flex items-center gap-2">
                  <Wallet size={20} />
                  {watchedValues.orderSide === "Long" ? "Buy/Long" : "Sell/Short"} Position
                </DrawerTitle>
                <DrawerClose asChild>
                  <Button variant="ghost" size="icon" className="rounded-full">
                    <X className="h-5 w-5" />
                  </Button>
                </DrawerClose>
              </div>
            </DrawerHeader>

            <div className="flex-1 overflow-y-auto px-6 pb-6">
              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit((data) => executeTrade(data.orderSide))}
                  className="space-y-6"
                >
                  {/* Order Type Tabs */}
                  <FormField
                    control={form.control}
                    name="orderType"
                    render={({ field }) => (
                      <Tabs value={field.value} onValueChange={field.onChange}>
                        <TabsList className="w-full grid grid-cols-2 h-12">
                          <TabsTrigger
                            value="Market"
                            className="data-[state=active]:bg-background text-base font-medium"
                          >
                            Market Order
                          </TabsTrigger>
                          <TabsTrigger
                            value="Limit"
                            className="data-[state=active]:bg-background text-base font-medium"
                          >
                            Limit Order
                          </TabsTrigger>
                        </TabsList>
                      </Tabs>
                    )}
                  />

                  {/* Trading Pair & Balance Info */}
                  <div className="rounded-xl border p-4 bg-muted/20 space-y-3">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <CoinIcon
                          symbol={selectedPair?.baseAsset?.toLowerCase() || "btc"}
                          size={40}
                          fallback={
                            <div className="h-10 w-10 rounded-full flex items-center justify-center bg-orange-500">
                              <span className="font-bold text-white text-sm">₿</span>
                            </div>
                          }
                        />
                        <div>
                          <div className="font-semibold text-lg">
                            {selectedPair?.baseAsset || "BTC"}
                          </div>
                          <div className="text-sm text-muted-foreground">{tradingPairDisplay}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-xs text-muted-foreground mb-1">Available Balance</div>
                        <AnimatedBalance
                          balance={formattedTotalBalanceEquivalentUsd}
                          isLoading={balanceLoading}
                          isOptimistic={isOptimistic}
                          lastAction={optimisticAction}
                          className="text-lg font-bold"
                        />
                      </div>
                    </div>

                    {/* Current Price Display */}
                    <div className="border-t pt-3">
                      <div className="text-xs text-muted-foreground uppercase tracking-wider mb-1">
                        {watchedValues.orderType === "Market" ? "Current Price" : "Limit Price"}
                      </div>
                      {watchedValues.orderType === "Market" ? (
                        <AnimatedPrice
                          price={currentPrice || 0}
                          priceChange={priceChange}
                          size="lg"
                          showTrend={true}
                          className="text-2xl font-bold"
                        />
                      ) : (
                        <FormField
                          control={form.control}
                          name="price"
                          render={({ field }) => (
                            <FormItem>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    type="number"
                                    {...field}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value ? parseFloat(e.target.value) : 0
                                      )
                                    }
                                    className="h-12 pr-16 text-lg font-semibold"
                                    placeholder="0.00"
                                  />
                                  <div className="absolute right-0 top-0 h-full flex items-center px-4 text-sm text-muted-foreground">
                                    USD
                                  </div>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  </div>

                  {/* Leverage Slider */}
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label className="text-base font-medium flex items-center gap-2">
                        <Zap size={16} />
                        Leverage
                      </Label>
                      <Badge variant="outline" className="h-8 px-3 text-base font-bold">
                        {watchedValues.leverage}x
                      </Badge>
                    </div>

                    <div className="relative">
                      {sliderDragging && (
                        <div
                          className="absolute -top-8 transform -translate-x-1/2 bg-primary text-white text-sm px-3 py-1 rounded-lg font-semibold z-10"
                          style={{ left: `${((watchedValues.leverage - 1) / 99) * 100}%` }}
                        >
                          {watchedValues.leverage}x
                        </div>
                      )}
                      <FormField
                        control={form.control}
                        name="leverage"
                        render={({ field }) => (
                          <SliderWithDrag
                            value={[field.value]}
                            onValueChange={([value]) => field.onChange(value)}
                            min={1}
                            max={100}
                            step={1}
                            onSliderDragStart={() => setSliderDragging(true)}
                            onSliderDragEnd={() => setSliderDragging(false)}
                            className="w-full py-2"
                          />
                        )}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground px-1">
                      <span>1x</span>
                      <span>25x</span>
                      <span>50x</span>
                      <span>75x</span>
                      <span>100x</span>
                    </div>
                  </div>

                  {/* Amount Input */}
                  <FormField
                    control={form.control}
                    name="amount"
                    render={({ field }) => (
                      <FormItem className="space-y-2">
                        <FormLabel className="text-base font-medium">Position Size (USD)</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type="number"
                              {...field}
                              onChange={(e) => {
                                field.onChange(e);
                                setActivePercent(null);
                              }}
                              placeholder="Enter amount..."
                              className={cn(
                                "h-12 pr-16 text-lg",
                                insufficientBalance &&
                                  "border-destructive focus:ring-destructive/20"
                              )}
                            />
                            <div className="absolute right-0 top-0 h-full flex items-center px-4 text-sm text-muted-foreground">
                              USD
                            </div>
                          </div>
                        </FormControl>
                        {insufficientBalance && (
                          <p className="text-sm text-destructive flex items-center gap-2">
                            <AlertTriangle size={16} />
                            Insufficient balance
                          </p>
                        )}
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Percentage Buttons */}
                  <div className="space-y-2">
                    <Label className="text-sm text-muted-foreground">Quick Amount</Label>
                    <div className="grid grid-cols-2 gap-3">
                      {[25, 50, 75, 100].map((percent) => (
                        <PercentButton
                          key={percent}
                          percent={percent}
                          onClick={() => handlePercentage(percent)}
                          active={activePercent === percent}
                        />
                      ))}
                    </div>
                  </div>

                  {/* Advanced Mode Toggle */}
                  <FormField
                    control={form.control}
                    name="advancedMode"
                    render={({ field }) => (
                      <div className="flex items-center justify-between py-3 px-4 rounded-xl bg-muted/20 border">
                        <div className="flex items-center gap-2">
                          <Settings size={16} />
                          <span className="text-base font-medium">Advanced Settings</span>
                          <TooltipProvider>
                            <Tooltip>
                              <TooltipTrigger asChild>
                                <Info size={14} className="text-muted-foreground cursor-help" />
                              </TooltipTrigger>
                              <TooltipContent side="top">
                                <p>Set Stop Loss and Take Profit levels</p>
                              </TooltipContent>
                            </Tooltip>
                          </TooltipProvider>
                        </div>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          className="scale-110"
                        />
                      </div>
                    )}
                  />

                  {/* Advanced Settings - SL/TP */}
                  {watchedValues.advancedMode && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: "auto" }}
                      exit={{ opacity: 0, height: 0 }}
                      transition={{ duration: 0.3 }}
                      className="space-y-4"
                    >
                      <div className="grid grid-cols-1 gap-4">
                        <FormField
                          control={form.control}
                          name="stopLoss"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-base font-medium flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-destructive" />
                                Stop Loss
                              </FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    type="number"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value ? parseFloat(e.target.value) : null
                                      )
                                    }
                                    placeholder="Optional"
                                    className="h-12 pr-16"
                                  />
                                  <div className="absolute right-0 top-0 h-full flex items-center px-4 text-sm text-muted-foreground">
                                    USD
                                  </div>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name="takeProfit"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-base font-medium flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-green-500" />
                                Take Profit
                              </FormLabel>
                              <FormControl>
                                <div className="relative">
                                  <Input
                                    type="number"
                                    {...field}
                                    value={field.value || ""}
                                    onChange={(e) =>
                                      field.onChange(
                                        e.target.value ? parseFloat(e.target.value) : null
                                      )
                                    }
                                    placeholder="Optional"
                                    className="h-12 pr-16"
                                  />
                                  <div className="absolute right-0 top-0 h-full flex items-center px-4 text-sm text-muted-foreground">
                                    USD
                                  </div>
                                </div>
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                    </motion.div>
                  )}

                  {/* Position Summary */}
                  {watchedValues.amount &&
                    parseFloat(watchedValues.amount) > 0 &&
                    analysisResult && (
                      <motion.div
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="rounded-xl border p-4 bg-card/50 space-y-3"
                      >
                        <div className="flex items-center gap-2 mb-3">
                          <BarChart4 size={16} className="text-primary" />
                          <span className="font-semibold">Position Summary</span>
                          {calculating && (
                            <div className="flex items-center gap-1">
                              <LoadingSpinner size={12} />
                              <span className="text-xs text-muted-foreground">Calculating...</span>
                            </div>
                          )}
                        </div>

                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div>
                            <div className="text-muted-foreground mb-1">Position Size</div>
                            <div className="font-bold">${formatNumber(positionSize)}</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground mb-1">Entry Price</div>
                            <div className="font-bold">${formatNumber(watchedValues.price)}</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground mb-1">Liquidation</div>
                            <div className="font-bold">${formatNumber(liquidationPrice)}</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground mb-1">Distance</div>
                            <div
                              className={cn(
                                "font-bold",
                                distanceToLiquidation < 10
                                  ? "text-destructive"
                                  : distanceToLiquidation < 25
                                    ? "text-amber-500"
                                    : "text-green-500"
                              )}
                            >
                              {formatNumber(distanceToLiquidation)}%
                            </div>
                          </div>
                          <div>
                            <div className="text-muted-foreground mb-1">Trading Fee</div>
                            <div className="font-bold">${formatNumber(analysisResult.fee)}</div>
                          </div>
                          <div>
                            <div className="text-muted-foreground mb-1">Leverage</div>
                            <div className="font-bold">{watchedValues.leverage}x</div>
                          </div>
                        </div>

                        {(watchedValues.stopLoss || watchedValues.takeProfit) && (
                          <div className="border-t pt-3 mt-3">
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              {watchedValues.stopLoss && (
                                <div>
                                  <div className="text-destructive text-xs mb-1 flex items-center gap-1">
                                    <div className="w-1.5 h-1.5 rounded-full bg-destructive" />
                                    Stop Loss
                                  </div>
                                  <div className="font-bold">
                                    ${formatNumber(watchedValues.stopLoss)}
                                  </div>
                                </div>
                              )}
                              {watchedValues.takeProfit && (
                                <div>
                                  <div className="text-green-500 text-xs mb-1 flex items-center gap-1">
                                    <div className="w-1.5 h-1.5 rounded-full bg-green-500" />
                                    Take Profit
                                  </div>
                                  <div className="font-bold">
                                    ${formatNumber(watchedValues.takeProfit)}
                                  </div>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </motion.div>
                    )}

                  {/* Execute Button */}
                  <div className="pt-4">
                    <motion.div
                      whileTap={{ scale: 0.98 }}
                      animate={{
                        scale:
                          lastClickedSide === watchedValues.orderSide && isExecutingTrade
                            ? [1, 1.02, 1]
                            : 1,
                      }}
                      transition={{ duration: 0.15 }}
                    >
                      <Button
                        type="submit"
                        disabled={
                          !isTradeReady || isExecutingTrade || executeTradeOptimistic.isPending
                        }
                        className={cn(
                          "w-full h-14 font-bold text-lg rounded-xl transition-all duration-200",
                          watchedValues.orderSide === "Long" ? styles.buyButton : styles.sellButton,
                          "disabled:opacity-50 disabled:cursor-not-allowed"
                        )}
                      >
                        <AnimatePresence mode="wait">
                          {(isExecutingTrade || executeTradeOptimistic.isPending) &&
                          lastClickedSide === watchedValues.orderSide ? (
                            <motion.div
                              key="loading"
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              exit={{ opacity: 0, scale: 0.8 }}
                              transition={{ duration: 0.2 }}
                              className="flex items-center gap-3"
                            >
                              <LoadingSpinner size={16} />
                              Opening Position...
                            </motion.div>
                          ) : (
                            <motion.div
                              key="default"
                              initial={{ opacity: 0, scale: 0.8 }}
                              animate={{ opacity: 1, scale: 1 }}
                              exit={{ opacity: 0, scale: 0.8 }}
                              transition={{ duration: 0.2 }}
                              className="flex items-center gap-3"
                            >
                              {watchedValues.orderSide === "Long" ? (
                                <TrendingUp size={18} />
                              ) : (
                                <TrendingDown size={18} />
                              )}
                              {watchedValues.orderSide === "Long" ? "Buy/Long" : "Sell/Short"}
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </Button>
                    </motion.div>
                  </div>
                </form>
              </Form>
            </div>
          </DrawerContent>
        </Drawer>
      </>
    );
  }

  // Desktop version - optimized layout
  return (
    <Card className="w-full border shadow-sm flex flex-col min-w-[300px] max-w-[320px] mb-sm:min-w-[210px] mb-sm:max-w-[230px] mb-xs:min-w-[195px] mb-xs:max-w-[215px] h-full max-h-[calc(100vh-160px)] mb-sm:max-h-[calc(100vh-140px)] mb-xs:max-h-[calc(100vh-120px)] p-0">
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit((data) => executeTrade(data.orderSide))}
          className="flex flex-col h-full"
        >
          {/* Header with Tabs - reduced padding */}
          <CardHeader className="p-2 pb-1.5 mb-sm:p-1.5 mb-sm:pb-1 mb-xs:p-1 mb-xs:pb-0.5 flex-shrink-0">
            <FormField
              control={form.control}
              name="orderType"
              render={({ field }) => (
                <Tabs value={field.value} onValueChange={field.onChange}>
                  <TabsList className="w-full grid grid-cols-2 h-8">
                    <TabsTrigger
                      value="Market"
                      className="data-[state=active]:bg-background text-xs"
                    >
                      Market
                    </TabsTrigger>
                    <TabsTrigger
                      value="Limit"
                      className="data-[state=active]:bg-background text-xs"
                    >
                      Limit
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              )}
            />
          </CardHeader>

          <CardContent className="p-2 pt-0 mb-sm:p-1.5 mb-sm:pt-0 mb-xs:p-1 mb-xs:pt-0 flex flex-col flex-1 overflow-hidden">
            {/* Scrollable content with smaller gaps */}
            <div className="flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent pr-1 space-y-1.5 mb-sm:space-y-1 mb-xs:space-y-0.5">
              {/* Pair & Price Display with Balance */}
              <div className="rounded-lg border p-2 bg-muted/20">
                <div className="flex items-center justify-between mb-1.5">
                  <div className="flex items-center gap-2.5">
                    <CoinIcon
                      symbol={selectedPair?.baseAsset?.toLowerCase() || "btc"}
                      size={32}
                      fallback={
                        <div className="h-8 w-8 rounded-full flex items-center justify-center bg-orange-500">
                          <span className="font-bold text-white text-xs">₿</span>
                        </div>
                      }
                    />
                    <div>
                      <div className="font-semibold text-sm">
                        {selectedPair?.baseAsset || "BTC"}
                      </div>
                      <div className="text-xs text-muted-foreground">{tradingPairDisplay}</div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-[10px] text-muted-foreground">Balance</div>
                    <AnimatedBalance
                      balance={formattedTotalBalanceEquivalentUsd}
                      isLoading={balanceLoading}
                      isOptimistic={isOptimistic}
                      lastAction={optimisticAction}
                    />
                  </div>
                </div>

                {watchedValues.orderType === "Market" ? (
                  <div>
                    <div className="text-[10px] text-muted-foreground uppercase tracking-wider">
                      Current Price
                    </div>
                    <AnimatedPrice
                      price={currentPrice || 0}
                      priceChange={priceChange}
                      size="lg"
                      showTrend={false}
                    />
                  </div>
                ) : (
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <FormLabel className="text-[10px] uppercase tracking-wider text-muted-foreground">
                          Limit Price
                        </FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input
                              type="number"
                              {...field}
                              ref={(e) => {
                                field.ref(e);
                                priceInputRef.current = e;
                              }}
                              onChange={(e) =>
                                field.onChange(e.target.value ? parseFloat(e.target.value) : 0)
                              }
                              className="h-9 pr-12 text-sm"
                              placeholder="0.00"
                            />
                            <div className="absolute right-0 top-0 h-full flex items-center px-2.5 text-xs text-muted-foreground">
                              USD
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage className="text-[10px]" />
                      </FormItem>
                    )}
                  />
                )}
              </div>

              {/* Leverage Slider */}
              <div className="p-2 rounded-lg border bg-card/50">
                <div className="flex items-center justify-between mb-1.5">
                  <span className="text-xs text-muted-foreground flex items-center gap-1">
                    <Zap size={12} />
                    Leverage
                  </span>
                  <Badge variant="outline" className="h-5 px-1.5 text-xs font-semibold">
                    {watchedValues.leverage}x
                  </Badge>
                </div>

                <div className={cn(styles.leverageSlider, "relative mt-1")}>
                  {sliderDragging && (
                    <div
                      className="absolute -top-6 transform -translate-x-1/2 bg-primary text-white text-xs px-2 py-0.5 rounded"
                      style={{ left: `${((watchedValues.leverage - 1) / 99) * 100}%` }}
                    >
                      {watchedValues.leverage}x
                    </div>
                  )}
                  <FormField
                    control={form.control}
                    name="leverage"
                    render={({ field }) => (
                      <SliderWithDrag
                        value={[field.value]}
                        onValueChange={([value]) => field.onChange(value)}
                        min={1}
                        max={100}
                        step={1}
                        onSliderDragStart={() => setSliderDragging(true)}
                        onSliderDragEnd={() => setSliderDragging(false)}
                      />
                    )}
                  />
                </div>
                <div className="flex justify-between text-[10px] text-muted-foreground mt-1 px-0.5">
                  <span>1x</span>
                  <span>25x</span>
                  <span>50x</span>
                  <span>75x</span>
                  <span>100x</span>
                </div>
              </div>

              {/* Amount Input */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="space-y-1">
                    <FormLabel className="text-xs uppercase tracking-wider text-muted-foreground font-medium flex items-center gap-1">
                      <div className="w-1.5 h-1.5 rounded-full bg-primary" />
                      Amount (USD)
                    </FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          type="number"
                          {...field}
                          onChange={(e) => {
                            field.onChange(e);
                            // Reset active percentage when user types manually
                            setActivePercent(null);
                          }}
                          placeholder="0.00"
                          className={cn(
                            "h-10 pr-12 text-sm",
                            insufficientBalance && "border-destructive focus:ring-destructive/20"
                          )}
                        />
                        <div className="absolute right-0 top-0 h-full flex items-center px-3 text-xs text-muted-foreground">
                          USD
                        </div>
                      </div>
                    </FormControl>
                    {insufficientBalance && (
                      <p className="text-[10px] text-destructive flex items-center gap-1">
                        <AlertTriangle size={10} />
                        Insufficient balance
                      </p>
                    )}
                  </FormItem>
                )}
              />

              {/* Percentage Buttons */}
              <div className="grid grid-cols-4 gap-1.5">
                {[25, 50, 75, 100].map((percent) => (
                  <PercentButton
                    key={percent}
                    percent={percent}
                    onClick={() => handlePercentage(percent)}
                    active={activePercent === percent}
                  />
                ))}
              </div>

              {/* Advanced Mode Toggle */}
              <FormField
                control={form.control}
                name="advancedMode"
                render={({ field }) => (
                  <div className="flex items-center justify-between py-1.5 px-2 rounded-lg bg-muted/20">
                    <div className="flex items-center gap-1.5">
                      <Settings size={12} />
                      <span className="text-xs font-medium">Advanced Mode</span>
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Info size={11} className="text-muted-foreground cursor-help" />
                          </TooltipTrigger>
                          <TooltipContent side="top">
                            <p className="text-xs">Set Stop Loss and Take Profit levels</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="scale-75"
                    />
                  </div>
                )}
              />

              {/* Advanced Settings - SL/TP */}
              {watchedValues.advancedMode && (
                <div className="grid grid-cols-2 gap-2 animate-in slide-in-from-top-1 duration-200">
                  <FormField
                    control={form.control}
                    name="stopLoss"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <FormLabel className="text-[10px] uppercase tracking-wider flex items-center gap-0.5">
                          <div className="w-1.5 h-1.5 rounded-full bg-destructive" />
                          Stop Loss
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) =>
                              field.onChange(e.target.value ? parseFloat(e.target.value) : null)
                            }
                            placeholder="0.00"
                            className="h-8 text-xs"
                          />
                        </FormControl>
                        <FormMessage className="text-[10px]" />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="takeProfit"
                    render={({ field }) => (
                      <FormItem className="space-y-1">
                        <FormLabel className="text-[10px] uppercase tracking-wider flex items-center gap-0.5">
                          <div className="w-1.5 h-1.5 rounded-full bg-green-500" />
                          Take Profit
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            {...field}
                            value={field.value || ""}
                            onChange={(e) =>
                              field.onChange(e.target.value ? parseFloat(e.target.value) : null)
                            }
                            placeholder="0.00"
                            className="h-8 text-xs"
                          />
                        </FormControl>
                        <FormMessage className="text-[10px]" />
                      </FormItem>
                    )}
                  />
                </div>
              )}

              {/* Position Summary - Always visible */}
              <Accordion
                type="single"
                collapsible
                defaultValue="summary"
                className="border rounded-md overflow-hidden"
              >
                <AccordionItem value="summary" className="border-none">
                  <AccordionTrigger className="py-2 px-2.5 bg-muted/20 hover:bg-muted/30 no-underline">
                    <div className="flex items-center justify-between w-full">
                      <span className="text-xs font-medium flex items-center gap-1.5">
                        <motion.div
                          animate={calculating ? { rotate: 360 } : {}}
                          transition={{
                            duration: 1,
                            repeat: calculating ? Infinity : 0,
                            ease: "linear",
                          }}
                        >
                          <BarChart4 size={12} />
                        </motion.div>
                        Position Summary
                        {calculating && (
                          <motion.div
                            animate={{ scale: [0.8, 1.2, 0.8], opacity: [0.5, 1, 0.5] }}
                            transition={{ duration: 1.5, repeat: Infinity }}
                            className="w-1.5 h-1.5 bg-blue-500 rounded-full"
                          />
                        )}
                      </span>
                      {analysisResult &&
                      watchedValues.amount &&
                      parseFloat(watchedValues.amount) > 0 ? (
                        <Badge className="h-5 text-xs px-1.5 bg-primary/10 text-primary hover:bg-primary/15 font-medium">
                          ${formatNumber(positionSize)}
                        </Badge>
                      ) : (
                        <Badge
                          variant="outline"
                          className="h-5 text-xs px-1.5 text-muted-foreground"
                        >
                          --
                        </Badge>
                      )}
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="p-1.5 bg-background/50 space-y-0.5">
                    {analysisResult &&
                    watchedValues.amount &&
                    parseFloat(watchedValues.amount) > 0 ? (
                      <>
                        <ParameterRow
                          label="Position Size"
                          value={`$${formatNumber(positionSize)}`}
                          tooltip="Total size of your position including leverage"
                        />
                        <ParameterRow
                          label="Entry Price"
                          value={`$${formatNumber(watchedValues.price)}`}
                        />
                        <ParameterRow label="Leverage" value={`${watchedValues.leverage}x`} />
                        <Separator className="my-1 bg-border/30" />
                        <ParameterRow
                          label="Liquidation Price"
                          value={`$${formatNumber(liquidationPrice)}`}
                          tooltip="The price at which your position will be automatically closed"
                        />
                        <ParameterRow
                          label="Distance"
                          value={`${formatNumber(distanceToLiquidation)}%`}
                          valueColor={
                            distanceToLiquidation < 10
                              ? "text-destructive"
                              : distanceToLiquidation < 25
                                ? "text-amber-500"
                                : "text-green-500"
                          }
                        />
                        <ParameterRow
                          label="Fees"
                          value={`$${formatNumber(analysisResult.fee)}`}
                          tooltip="Trading fees for opening the position"
                        />

                        {(watchedValues.stopLoss || watchedValues.takeProfit) && (
                          <>
                            <Separator className="my-1 bg-border/30" />
                            {watchedValues.stopLoss && (
                              <ParameterRow
                                label="Stop Loss"
                                value={`$${formatNumber(watchedValues.stopLoss)}`}
                                valueColor="text-destructive"
                                icon={<div className="w-1 h-1 rounded-full bg-destructive" />}
                              />
                            )}
                            {watchedValues.takeProfit && (
                              <ParameterRow
                                label="Take Profit"
                                value={`$${formatNumber(watchedValues.takeProfit)}`}
                                valueColor="text-green-500"
                                icon={<div className="w-1 h-1 rounded-full bg-green-500" />}
                              />
                            )}
                          </>
                        )}
                      </>
                    ) : (
                      <div className="opacity-40 space-y-0.5">
                        <ParameterRow label="Position Size" value="--" />
                        <ParameterRow label="Entry Price" value="--" />
                        <ParameterRow label="Leverage" value={`${watchedValues.leverage}x`} />
                        <Separator className="my-1 bg-border/20" />
                        <ParameterRow label="Liquidation Price" value="--" />
                        <ParameterRow label="Distance" value="--" />
                        <ParameterRow label="Fees" value="--" />
                      </div>
                    )}
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>

            {/* Trading Buttons - Fixed at bottom with enhanced animations */}
            <div className="grid grid-cols-2 gap-2 mt-2 flex-shrink-0">
              <motion.div
                whileTap={{ scale: 0.98 }}
                animate={{
                  scale: lastClickedSide === "Long" && isExecutingTrade ? [1, 1.02, 1] : 1,
                }}
                transition={{ duration: 0.15 }}
              >
                <Button
                  type="button"
                  disabled={!isTradeReady || isExecutingTrade || executeTradeOptimistic.isPending}
                  onClick={() => executeTrade("Long")}
                  className={cn(
                    "h-10 font-semibold text-sm shadow-md transition-all duration-200 w-full",
                    styles.buyButton,
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    (isExecutingTrade || executeTradeOptimistic.isPending) &&
                      lastClickedSide === "Long" &&
                      "bg-green-600 shadow-lg"
                  )}
                >
                  <AnimatePresence mode="wait">
                    {(isExecutingTrade || executeTradeOptimistic.isPending) &&
                    lastClickedSide === "Long" ? (
                      <motion.div
                        key="loading"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                        className="flex items-center gap-2"
                      >
                        <LoadingSpinner size={14} />
                        Opening...
                      </motion.div>
                    ) : (
                      <motion.div
                        key="default"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                        className="flex items-center gap-2"
                      >
                        <TrendingUp size={14} />
                        Buy/Long
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Button>
              </motion.div>

              <motion.div
                whileTap={{ scale: 0.98 }}
                animate={{
                  scale: lastClickedSide === "Short" && isExecutingTrade ? [1, 1.02, 1] : 1,
                }}
                transition={{ duration: 0.15 }}
              >
                <Button
                  type="button"
                  disabled={!isTradeReady || isExecutingTrade || executeTradeOptimistic.isPending}
                  onClick={() => executeTrade("Short")}
                  className={cn(
                    "h-10 font-semibold text-sm shadow-md transition-all duration-200 w-full",
                    styles.sellButton,
                    "disabled:opacity-50 disabled:cursor-not-allowed",
                    (isExecutingTrade || executeTradeOptimistic.isPending) &&
                      lastClickedSide === "Short" &&
                      "bg-red-600 shadow-lg"
                  )}
                >
                  <AnimatePresence mode="wait">
                    {(isExecutingTrade || executeTradeOptimistic.isPending) &&
                    lastClickedSide === "Short" ? (
                      <motion.div
                        key="loading"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                        className="flex items-center gap-2"
                      >
                        <LoadingSpinner size={14} />
                        Opening...
                      </motion.div>
                    ) : (
                      <motion.div
                        key="default"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.2 }}
                        className="flex items-center gap-2"
                      >
                        <TrendingDown size={14} />
                        Sell/Short
                      </motion.div>
                    )}
                  </AnimatePresence>
                </Button>
              </motion.div>
            </div>
          </CardContent>
        </form>
      </Form>
    </Card>
  );
};

export default TradeSidebar;
