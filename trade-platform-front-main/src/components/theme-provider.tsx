"use client";

import { useEffect } from "react";

import { ThemeStoreProvider, useThemeStore } from "@/entities/theme";

type ThemeProviderProps = {
  children: React.ReactNode;
};

// Theme initializer component that runs the initialization logic
function ThemeInitializer({ children }: { children: React.ReactNode }) {
  const { initializeTheme } = useThemeStore((state) => state.theme);

  useEffect(() => {
    initializeTheme();
  }, [initializeTheme]);

  return <>{children}</>;
}

export function ThemeProvider({ children }: ThemeProviderProps) {
  return (
    <ThemeStoreProvider>
      <ThemeInitializer>{children}</ThemeInitializer>
    </ThemeStoreProvider>
  );
}

// Legacy hook for backward compatibility
export const useTheme = () => {
  const { theme, resolvedTheme, toggleTheme } = useThemeStore((state) => state.theme);

  return {
    theme,
    resolvedTheme,
    darkMode: resolvedTheme === "dark",
    toggleTheme,
    setTheme: useThemeStore((state) => state.theme.setTheme),
  };
};
