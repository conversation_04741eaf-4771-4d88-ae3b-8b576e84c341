"use client";

import { motion } from "framer-motion";
import { BanknoteIcon, HelpCircleIcon, LifeBuoyIcon } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";

import { Accordion, AccordionContent, AccordionItem } from "@/components/ui/accordion";
import { cn } from "@/lib/utils";

interface NavItemProps {
  icon: string | React.ReactNode;
  label: string;
  path: string;
  isActive?: boolean;
}

const NavItem: React.FC<NavItemProps> = ({ icon, label, path, isActive = false }) => {
  return (
    <Link href={path} prefetch={true} className="block w-full">
      <motion.div
        className={cn(
          "w-full min-h-[44px] xl:min-h-[48px] mac-sm:min-h-[36px] mac-xs:min-h-[32px] px-2 xl:px-3 mac-sm:px-1.5 mac-xs:px-1 py-2 xl:py-2.5 mac-sm:py-1 mac-xs:py-0.5 rounded-xl mac-sm:rounded-lg mac-xs:rounded-md flex items-center gap-2 mac-sm:gap-1.5 mac-xs:gap-1 cursor-pointer relative overflow-hidden group",
          isActive ? "" : "hover:bg-blue-50/20 hover:translate-x-1"
        )}
        whileHover={{ scale: isActive ? 1 : 1.02 }}
        whileTap={{ scale: 0.98 }}
        transition={{
          type: "spring",
          stiffness: 400,
          damping: 22,
          mass: 0.6,
        }}
      >
        {isActive ? (
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-[#22BDFF]/90 via-[#109FE3]/85 to-[#1B4BA7]/80 rounded-xl shadow-md"
            layoutId="activeBackground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          />
        ) : null}

        <div
          className={cn(
            "flex items-center gap-3 w-full z-10 transition-all duration-200 ease-out",
            isActive ? "pl-1" : ""
          )}
        >
          <motion.div
            className="w-5 xl:w-6 mac-sm:w-4 mac-xs:w-3.5 h-5 xl:h-6 mac-sm:h-4 mac-xs:h-3.5 flex-shrink-0 flex items-center justify-center"
            animate={{ scale: isActive ? 1.1 : 1 }}
            transition={{ type: "spring", stiffness: 300, damping: 20 }}
          >
            {typeof icon === "string" ? (
              <Image
                src={`/icons/${icon}.svg`}
                alt={`${label} icon`}
                width={20}
                height={20}
                className={cn(
                  "xl:w-6 xl:h-6 mac-sm:w-4 mac-sm:h-4 mac-xs:w-3.5 mac-xs:h-3.5 transition-all duration-500 ease-in-out",
                  isActive
                    ? "brightness-0 invert"
                    : "opacity-50 text-foreground group-hover:opacity-90"
                )}
              />
            ) : (
              <div
                className={cn(
                  "transition-all duration-500 ease-in-out",
                  isActive ? "text-white" : "text-muted-foreground group-hover:text-foreground"
                )}
              >
                {icon}
              </div>
            )}
          </motion.div>
          <div
            className={cn(
              "flex-1 flex-none font-['DM_Sans'] text-sm xl:text-base mac-sm:text-xs mac-xs:text-[10px] font-semibold leading-[20px] xl:leading-[22px] mac-sm:leading-[16px] mac-xs:leading-[14px] tracking-[-0.112px] mac-sm:tracking-[-0.08px] mac-xs:tracking-[-0.06px] flex items-center transition-all duration-200 ease-out",
              isActive ? "text-white" : "text-foreground group-hover:translate-x-1"
            )}
          >
            {label}
          </div>

          {isActive && (
            <motion.div
              className="h-full w-1 absolute left-0 top-0 bg-blue-300"
              initial={{ height: 0 }}
              animate={{ height: "100%" }}
              transition={{ duration: 0.2, ease: "easeOut" }}
            />
          )}
        </div>
      </motion.div>
    </Link>
  );
};

interface SettingItemProps {
  label: string;
  path: string;
  isActive: boolean;
}

const SettingItem: React.FC<SettingItemProps> = ({ label, path, isActive }) => {
  return (
    <Link href={path} prefetch={true} className="block w-full">
      <motion.div
        className={cn(
          "flex p-2 w-full cursor-pointer relative overflow-hidden rounded-lg group",
          isActive ? "" : "hover:bg-blue-50/20 hover:translate-x-1"
        )}
        whileHover={{ scale: isActive ? 1 : 1.02 }}
        whileTap={{ scale: 0.97 }}
        transition={{
          type: "spring",
          stiffness: 400,
          damping: 22,
          mass: 0.6,
        }}
      >
        {isActive ? (
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-[#22BDFF]/90 via-[#109FE3]/85 to-[#1B4BA7]/80 rounded-lg shadow-md"
            layoutId="activeSettingBackground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          />
        ) : null}

        <div className="flex items-center w-full z-10">
          <div
            className={cn(
              "w-1.5 h-1.5 rounded-full mr-2.5 transition-colors duration-200",
              isActive ? "bg-white" : "bg-gray-300"
            )}
          ></div>
          <div
            className={cn(
              "flex-1 flex-none font-['DM_Sans'] text-sm xl:text-base mac-sm:text-xs mac-xs:text-[10px] font-medium leading-[20px] xl:leading-[22px] mac-sm:leading-[16px] mac-xs:leading-[14px] tracking-[-0.112px] mac-sm:tracking-[-0.08px] mac-xs:tracking-[-0.06px] flex items-center transition-all duration-200 ease-out",
              isActive ? "text-white pl-1" : "text-foreground group-hover:translate-x-1"
            )}
          >
            {label}
          </div>
        </div>

        {isActive && (
          <motion.div
            className="h-full w-1 absolute left-0 top-0 bg-blue-300"
            initial={{ height: 0 }}
            animate={{ height: "100%" }}
            transition={{ duration: 0.2, ease: "easeOut" }}
          />
        )}
      </motion.div>
    </Link>
  );
};

interface LeftSidebarProps {
  pathname?: string;
  onLoad?: () => void;
}

export const LeftSidebar: React.FC<LeftSidebarProps> = ({ pathname = "", onLoad }) => {
  // Get current path to determine active items
  const isSettingsActive = pathname.includes("/dashboard/settings");
  const isSettingsSection = isSettingsActive && pathname !== "/dashboard/settings";

  // Check specific active states
  const activeNavItem =
    pathname === "/dashboard"
      ? "dashboard"
      : pathname === "/dashboard/deposit"
        ? "deposit"
        : pathname === "/dashboard/withdraw"
          ? "withdraw"
          : pathname === "/dashboard/link"
            ? "link"
            : pathname === "/dashboard/referrals"
              ? "referrals"
              : pathname === "/dashboard/notification"
                ? "notification"
                : pathname === "/dashboard/support"
                  ? "support"
                  : isSettingsActive
                    ? "settings"
                    : null;

  // Check active settings item
  const activeSettingItem =
    pathname === "/dashboard/settings/profile"
      ? "profile"
      : pathname === "/dashboard/settings/security"
        ? "security"
        : null;

  // Add useEffect hook to notify when component is mounted and animations complete
  React.useEffect(() => {
    if (onLoad) {
      // Wait for animation to complete before calling onLoad
      const timer = setTimeout(() => {
        onLoad();
      }, 400); // Match this with the animation duration

      return () => clearTimeout(timer);
    }
  }, [onLoad]);

  return (
    <motion.aside
      className="flex flex-col w-full max-w-[280px] xl:max-w-[320px] mac-sm:max-w-[240px] mac-xs:max-w-[220px] h-[calc(100vh-100px)] mac-sm:h-[calc(100vh-60px)] mac-xs:h-[calc(100vh-50px)] pl-3 xl:pl-4 mac-sm:pl-2 mac-xs:pl-1.5 pr-2 xl:pr-3 mac-sm:pr-1.5 mac-xs:pr-1 py-4 xl:py-6 mac-sm:py-2 mac-xs:py-1.5 bg-card/30 rounded-3xl mac-sm:rounded-2xl mac-xs:rounded-xl border border-border shadow-sm backdrop-blur-md overflow-hidden"
      initial={{ opacity: 0, x: -20 }}
      animate={{ opacity: 1, x: 0 }}
      transition={{
        duration: 0.4,
        type: "spring",
        stiffness: 100,
        damping: 15,
      }}
    >
      <div className="flex flex-col gap-4 mac-sm:gap-2 mac-xs:gap-1.5 w-full items-start">
        <div className="flex flex-col gap-4 mac-sm:gap-2 mac-xs:gap-1.5 w-full">
          {/* Overview */}
          <NavItem
            icon="home"
            label="Overview"
            path="/dashboard"
            isActive={activeNavItem === "dashboard"}
          />

          {/* Deposit */}
          <NavItem
            icon="money-add"
            label="Deposit"
            path="/dashboard/deposit"
            isActive={activeNavItem === "deposit"}
          />

          {/* Withdraw */}
          <NavItem
            icon={<BanknoteIcon size={18} />}
            label="Withdraw"
            path="/dashboard/withdraw"
            isActive={activeNavItem === "withdraw"}
          />

          {/* Link */}
          <NavItem
            icon="link"
            label="Link"
            path="/dashboard/link"
            isActive={activeNavItem === "link"}
          />

          {/* Referrals */}
          <NavItem
            icon="user-c-frame"
            label="Referrals"
            path="/dashboard/referrals"
            isActive={activeNavItem === "referrals"}
          />

          {/* Notification */}
          <NavItem
            icon="notification"
            label="Notification"
            path="/dashboard/notification"
            isActive={activeNavItem === "notification"}
          />

          {/* Support */}
          <NavItem
            icon={<LifeBuoyIcon size={18} />}
            label="Support"
            path="/dashboard/support"
            isActive={activeNavItem === "support"}
          />

          {/* Settings with Accordion */}
          <Accordion
            type="single"
            collapsible
            className="w-full"
            value={isSettingsActive ? "settings" : ""}
          >
            <AccordionItem value="settings" className="border-none">
              <Link href="/dashboard/settings" prefetch={true} className="block w-full">
                <motion.div
                  className={cn(
                    "w-full min-h-[44px] xl:min-h-[48px] mac-sm:min-h-[36px] mac-xs:min-h-[32px] px-2 xl:px-3 mac-sm:px-1.5 mac-xs:px-1 py-2 xl:py-2.5 mac-sm:py-1 mac-xs:py-0.5 rounded-xl mac-sm:rounded-lg mac-xs:rounded-md flex items-center gap-2 mac-sm:gap-1.5 mac-xs:gap-1 cursor-pointer relative overflow-hidden group",
                    activeNavItem === "settings" ? "" : "hover:bg-blue-50/20 hover:translate-x-1"
                  )}
                  whileHover={{ scale: activeNavItem === "settings" ? 1 : 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 20,
                    mass: 0.8,
                  }}
                >
                  {activeNavItem === "settings" && !isSettingsSection ? (
                    <motion.div
                      className="absolute inset-0 bg-gradient-to-r from-[#22BDFF]/90 via-[#109FE3]/85 to-[#1B4BA7]/80 rounded-xl shadow-md"
                      layoutId="activeSettingsBackground"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      transition={{ duration: 0.2, ease: "easeOut" }}
                    />
                  ) : null}

                  <div
                    className={cn(
                      "flex items-center gap-3 w-full z-10 transition-all duration-200 ease-out",
                      activeNavItem === "settings" && !isSettingsSection ? "pl-1" : ""
                    )}
                  >
                    <motion.div
                      className="w-5 xl:w-6 mac-sm:w-4 mac-xs:w-3.5 h-5 xl:h-6 mac-sm:h-4 mac-xs:h-3.5 flex-shrink-0 flex items-center justify-center"
                      animate={{ scale: activeNavItem === "settings" ? 1.1 : 1 }}
                      transition={{ type: "spring", stiffness: 300, damping: 20 }}
                    >
                      <Image
                        src="/icons/settings.svg"
                        alt="Settings icon"
                        width={24}
                        height={24}
                        className={cn(
                          "transition-all duration-500 ease-in-out",
                          activeNavItem === "settings" && !isSettingsSection
                            ? "brightness-0 invert"
                            : "opacity-50 text-foreground group-hover:opacity-90"
                        )}
                      />
                    </motion.div>
                    <div
                      className={cn(
                        "flex-1 flex-none font-['DM_Sans'] text-sm xl:text-base mac-sm:text-xs mac-xs:text-[10px] font-semibold leading-[20px] xl:leading-[22px] mac-sm:leading-[16px] mac-xs:leading-[14px] tracking-[-0.112px] mac-sm:tracking-[-0.08px] mac-xs:tracking-[-0.06px] flex items-center transition-all duration-200 ease-out",
                        activeNavItem === "settings" && !isSettingsSection
                          ? "text-white"
                          : "text-foreground group-hover:translate-x-1"
                      )}
                    >
                      Settings
                    </div>

                    {activeNavItem === "settings" && !isSettingsSection && (
                      <motion.div
                        className="h-full w-1 absolute left-0 top-0 bg-blue-300"
                        initial={{ height: 0 }}
                        animate={{ height: "100%" }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                      />
                    )}
                  </div>
                </motion.div>
              </Link>

              <AccordionContent className="pl-12 mac-sm:pl-8 mac-xs:pl-6 pt-2 mac-sm:pt-1 mac-xs:pt-0.5">
                <motion.div
                  className="flex flex-col gap-2 w-full"
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: "auto" }}
                  exit={{ opacity: 0, height: 0 }}
                  transition={{ duration: 0.3, ease: "easeOut" }}
                >
                  {/* Profile */}
                  <SettingItem
                    label="Profile"
                    path="/dashboard/settings/profile"
                    isActive={activeSettingItem === "profile"}
                  />

                  {/* Security */}
                  <SettingItem
                    label="Security"
                    path="/dashboard/settings/security"
                    isActive={activeSettingItem === "security"}
                  />
                </motion.div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </div>
      </div>
    </motion.aside>
  );
};

// Memoized component to prevent unnecessary re-renders
export default React.memo(LeftSidebar);
