"use client";

import React from "react";

import styles from "./RulesModal.module.css";
import { RulesModalProps } from "./types";

export const RulesModal: React.FC<RulesModalProps> = ({ onAccept }) => {
  return (
    <div className={styles.modalOverlay}>
      <div className={styles.modalContent}>
        <h2>Social Network Rules</h2>
        <p>1. Publishing illegal content is prohibited.</p>
        <p>2. Respect other users.</p>
        <p>3. Insults and discrimination are prohibited.</p>
        <p>4. Comply with laws and moral standards.</p>
        <button className={styles.acceptButton} onClick={onAccept}>
          Accept
        </button>
      </div>
    </div>
  );
};

export default RulesModal;
