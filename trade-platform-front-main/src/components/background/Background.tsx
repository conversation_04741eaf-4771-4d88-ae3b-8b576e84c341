"use client";

import { useTheme as useNextTheme } from "next-themes";
import React from "react";

import { CloudyBackground } from "./CloudyBackground";
import { CloudyBackgroundDark } from "./CloudyBackgroundDark";

export const Background: React.FC = () => {
  const { resolvedTheme } = useNextTheme();
  const isDarkMode = resolvedTheme === "dark";

  // Fixed backgrounds with opacity transitions
  const lightBackgroundStyle: React.CSSProperties = {
    width: "100vw",
    height: "100vh",
    position: "fixed",
    top: 0,
    left: 0,
    zIndex: -10,
    opacity: isDarkMode ? 0 : 1,
    visibility: isDarkMode ? "hidden" : "visible",
    transition: "opacity 0.8s ease-in-out, visibility 0.8s ease-in-out",
  };

  const darkBackgroundStyle: React.CSSProperties = {
    width: "100vw",
    height: "100vh",
    position: "fixed",
    top: 0,
    left: 0,
    zIndex: -10,
    opacity: isDarkMode ? 1 : 0,
    visibility: isDarkMode ? "visible" : "hidden",
    transition: "opacity 1.5s ease-in-out, visibility 1.5s ease-in-out",
  };

  return (
    <>
      <div style={lightBackgroundStyle} data-testid="light-bg">
        <CloudyBackground />
      </div>
      <div style={darkBackgroundStyle} data-testid="dark-bg">
        <CloudyBackgroundDark />
      </div>
    </>
  );
};

export default Background;
