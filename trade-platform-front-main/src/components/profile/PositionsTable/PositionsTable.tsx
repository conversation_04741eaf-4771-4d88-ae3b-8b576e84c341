"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { motion, AnimatePresence } from "framer-motion";
import React, { useCallback, useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Spinner } from "@/components/ui/spinner";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { authUtils } from "@/entities/auth/lib/auth-utils";
import { Position } from "@/entities/trade/types";
import {
  useFilledOrders,
  useOpenOrders,
  usePositionHistory,
  usePositions,
  useClosePosition,
  useCancelOrder,
  useUpdatePositionTPSL,
} from "@/features/trade/api/hooks/usePositions";
import { cn } from "@/lib/utils";

import styles from "./PositionsTable.module.css";
import { PositionsTableProps } from "./types";

// Интерфейс для компонента редактирования TP/SL
interface TPSLEditFormProps {
  position: Position;
  onSave: (data: { takeProfit?: string; stopLoss?: string }) => void;
  onCancel: () => void;
}

// Компонент формы редактирования TP/SL
const TPSLEditForm: React.FC<TPSLEditFormProps> = ({ position, onSave, onCancel }) => {
  const schema = z.object({
    takeProfit: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val || val === "") return true;
          const tpValue = parseFloat(val);
          if (isNaN(tpValue)) return false;

          const currentPrice = position.current_price;
          const minDistance = currentPrice * 0.005;

          if (position.direction === "LONG") {
            return tpValue > currentPrice + minDistance;
          } else {
            return tpValue < currentPrice - minDistance;
          }
        },
        {
          message:
            position.direction === "LONG"
              ? `Take Profit must be at least 0.5% higher than current price (${(position.current_price * 1.005).toFixed(2)})`
              : `Take Profit must be at least 0.5% lower than current price (${(position.current_price * 0.995).toFixed(2)})`,
        }
      ),
    stopLoss: z
      .string()
      .optional()
      .refine(
        (val) => {
          if (!val || val === "") return true;
          const slValue = parseFloat(val);
          if (isNaN(slValue)) return false;

          const currentPrice = position.current_price;
          const minDistance = currentPrice * 0.005;

          if (position.direction === "LONG") {
            return slValue < currentPrice - minDistance && slValue > position.liquidation_price;
          } else {
            return slValue > currentPrice + minDistance && slValue < position.liquidation_price;
          }
        },
        {
          message:
            position.direction === "LONG"
              ? `Stop Loss must be at least 0.5% lower than current price (${(position.current_price * 0.995).toFixed(2)}) and higher than liquidation price (${position.liquidation_price.toFixed(2)})`
              : `Stop Loss must be at least 0.5% higher than current price (${(position.current_price * 1.005).toFixed(2)}) and lower than liquidation price (${position.liquidation_price.toFixed(2)})`,
        }
      ),
  });

  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: {
      takeProfit: position.take_profit ? position.take_profit.toString() : "",
      stopLoss: position.stop_loss ? position.stop_loss.toString() : "",
    },
    mode: "onBlur", // Валидация только при потере фокуса
  });

  const onSubmit = (data: z.infer<typeof schema>) => {
    onSave(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex items-center gap-2">
        <FormField
          control={form.control}
          name="stopLoss"
          render={({ field }) => (
            <FormItem className="relative">
              <FormControl>
                <Input
                  {...field}
                  type="text"
                  placeholder={
                    position.direction === "LONG"
                      ? `SL: < ${(position.current_price * 0.995).toFixed(2)}`
                      : `SL: > ${(position.current_price * 1.005).toFixed(2)}`
                  }
                  className="h-8 w-24 text-xs"
                  onClick={(e) => e.stopPropagation()}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === "" || /^-?\d*\.?\d*$/.test(value)) {
                      field.onChange(value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage className="absolute top-full left-0 text-xs" />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name="takeProfit"
          render={({ field }) => (
            <FormItem className="relative">
              <FormControl>
                <Input
                  {...field}
                  type="text"
                  placeholder={
                    position.direction === "LONG"
                      ? `TP: > ${(position.current_price * 1.005).toFixed(2)}`
                      : `TP: < ${(position.current_price * 0.995).toFixed(2)}`
                  }
                  className="h-8 w-24 text-xs"
                  onClick={(e) => e.stopPropagation()}
                  onChange={(e) => {
                    const value = e.target.value;
                    if (value === "" || /^-?\d*\.?\d*$/.test(value)) {
                      field.onChange(value);
                    }
                  }}
                />
              </FormControl>
              <FormMessage className="absolute top-full left-0 text-xs" />
            </FormItem>
          )}
        />
        <Button type="submit" size="sm" className="h-8 px-2" onClick={(e) => e.stopPropagation()}>
          Save
        </Button>
        <Button
          type="button"
          size="sm"
          variant="outline"
          onClick={(e) => {
            e.stopPropagation();
            onCancel();
          }}
          className="h-8 px-2"
        >
          Cancel
        </Button>
      </form>
    </Form>
  );
};

export const PositionsTable: React.FC<PositionsTableProps> = ({ onPositionSelect }) => {
  const [activeTab, setActiveTab] = useState("positions");
  const [editingPositionId, setEditingPositionId] = useState<number | null>(null);

  // Состояние для отслеживания выбранной позиции
  const [selectedPositionId, setSelectedPositionId] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Состояние для отслеживания новых позиций (для анимации)
  const [newPositionIds, setNewPositionIds] = useState<Set<number>>(new Set());
  const [previousPositionsCount, setPreviousPositionsCount] = useState(0);
  const [closingPositions, setClosingPositions] = useState<Set<number>>(new Set());

  // Используем React Query hooks для получения данных
  const {
    data: openPositions = [],
    isLoading: positionsLoading,
    error: positionsError,
    isFetching: positionsFetching,
  } = usePositions("OPEN");
  const { data: positionHistory = [], isLoading: historyLoading } = usePositionHistory();
  const { data: openOrders = [], isLoading: ordersLoading } = useOpenOrders();
  const { data: filledOrders = [] } = useFilledOrders();

  // Используем мутации
  const closePositionMutation = useClosePosition();
  const cancelOrderMutation = useCancelOrder();
  const updateTPSLMutation = useUpdatePositionTPSL();

  // Отслеживаем новые позиции для анимации
  useEffect(() => {
    if (openPositions.length > previousPositionsCount && previousPositionsCount > 0) {
      // Находим новые позиции (предполагаем, что новые позиции добавляются в начало)
      const newPositions = openPositions.slice(0, openPositions.length - previousPositionsCount);
      const newIds = new Set(newPositions.map((p) => p.id));
      setNewPositionIds(newIds);

      // Убираем подсветку через 3 секунды
      const timeoutId = setTimeout(() => {
        setNewPositionIds(new Set());
      }, 3000);

      // Cleanup timeout on unmount or when effect reruns
      return () => clearTimeout(timeoutId);
    }
  }, [openPositions.length]); // Remove previousPositionsCount from dependencies

  // Update previous count in a separate effect
  useEffect(() => {
    setPreviousPositionsCount(openPositions.length);
  }, [openPositions.length]);

  // Cleanup закрывающихся позиций, которые больше не присутствуют в данных
  useEffect(() => {
    const currentPositionIds = new Set(openPositions.map((p) => p.id));
    setClosingPositions((prev) => {
      // Only update if there are positions in the closing set that no longer exist
      const stillClosing = Array.from(prev).filter((id) => currentPositionIds.has(id));
      if (stillClosing.length !== prev.size) {
        return new Set(stillClosing);
      }
      return prev; // Return the same reference if nothing changed
    });
  }, [openPositions]);

  // Объединяем состояния загрузки
  const loading = positionsLoading || historyLoading || ordersLoading;

  // Обрабатываем ошибки API
  React.useEffect(() => {
    if (positionsError) {
      setError("Failed to load positions. Please try again.");
    }
  }, [positionsError]);

  const handleClosePosition = async (positionId: number) => {
    // Check if user is authenticated
    const userId = authUtils.getUserId();
    if (!userId) {
      setError("User not authenticated");
      return;
    }

    // Add position to closing set for animation
    setClosingPositions((prev) => new Set(prev).add(positionId));

    // Снимаем выделение, если закрытая позиция была выбрана
    if (selectedPositionId === positionId) {
      setSelectedPositionId(null);
      onPositionSelect?.(null);
    }

    try {
      await closePositionMutation.mutateAsync(positionId);
    } catch (error) {
      // Remove from closing set on error
      setClosingPositions((prev) => {
        const newSet = new Set(prev);
        newSet.delete(positionId);
        return newSet;
      });
    }
  };

  const handleCancelOrder = async (orderId: number) => {
    // Снимаем выделение, если отмененный ордер был выбран
    if (selectedPositionId === orderId) {
      setSelectedPositionId(null);
      onPositionSelect?.(null);
    }

    try {
      await cancelOrderMutation.mutateAsync({ orderId });
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  // Функция для обработки действия "Share"
  const handleShare = (positionId: number) => {
    // Sharing position functionality would be implemented here
  };

  // Обработчик выбора позиции для отображения на графике
  const handlePositionSelect = useCallback(
    (position: Position) => {
      // Если позиция уже выбрана, снимаем выделение
      if (selectedPositionId === position.id) {
        setSelectedPositionId(null);
        onPositionSelect?.(null);
      } else {
        // Иначе выбираем эту позицию
        setSelectedPositionId(position.id);
        onPositionSelect?.(position);
      }
    },
    [selectedPositionId, onPositionSelect]
  );

  // Обновление значений TP/SL
  const handleUpdateTPSL = async (
    data: { takeProfit?: string; stopLoss?: string },
    positionId: number
  ) => {
    // Находим текущую позицию
    const position = openPositions.find((p) => p.id === positionId);
    if (!position) {
      setError("Position not found");
      return;
    }

    try {
      await updateTPSLMutation.mutateAsync({
        positionId,
        takeProfit:
          data.takeProfit && data.takeProfit !== "" ? parseFloat(data.takeProfit) : undefined,
        stopLoss: data.stopLoss && data.stopLoss !== "" ? parseFloat(data.stopLoss) : undefined,
      });

      setEditingPositionId(null);

      // Если обновлена выбранная позиция, обновим её в родительском компоненте
      if (selectedPositionId === positionId) {
        const positionWithUpdatedTPSL = {
          ...position,
          take_profit:
            data.takeProfit && data.takeProfit !== "" ? parseFloat(data.takeProfit) : undefined,
          stop_loss: data.stopLoss && data.stopLoss !== "" ? parseFloat(data.stopLoss) : undefined,
        };
        onPositionSelect?.(positionWithUpdatedTPSL);
      }
    } catch (error) {
      // Error is handled by the mutation hook
    }
  };

  // Мемоизированные функции форматирования
  const formatNumber = useCallback((num: number, isPrice: boolean = true): string => {
    if (isPrice) {
      // Для цен - показываем до 2 знаков после запятой, с запятыми для тысяч
      return num.toLocaleString("en-US", {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2,
      });
    } else {
      // Для размеров позиций - показываем до 4 знаков если число маленькое
      if (num < 1) {
        return num.toLocaleString("en-US", {
          minimumFractionDigits: 4,
          maximumFractionDigits: 4,
        });
      } else if (num < 100) {
        return num.toLocaleString("en-US", {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2,
        });
      } else {
        return num.toLocaleString("en-US", {
          minimumFractionDigits: 0,
          maximumFractionDigits: 0,
        });
      }
    }
  }, []);

  const formatDate = useCallback((dateStr: string): string => {
    const date = new Date(dateStr);
    return date.toLocaleString();
  }, []);

  const getCryptoIcon = useCallback((tradingPair: string) => {
    const baseAsset = tradingPair.split("/")[0];

    let backgroundColor = "#627EEA";

    if (baseAsset === "BTC") backgroundColor = "#F7931A";
    else if (baseAsset === "ETH") backgroundColor = "#627EEA";
    else if (baseAsset === "SOL") backgroundColor = "#00FFBD";
    else if (baseAsset === "BNB") backgroundColor = "#F3BA2F";
    else if (baseAsset === "DOGE") backgroundColor = "#C3A634";
    else if (baseAsset === "XRP") backgroundColor = "#23292F";

    return (
      <div className={styles.cryptoIcon} style={{ backgroundColor }}>
        {baseAsset}
      </div>
    );
  }, []);

  // Компонент анимированной строки таблицы
  const AnimatedTableRow = ({
    position,
    children,
    isNew = false,
    isClosing = false,
  }: {
    position: Position;
    children: React.ReactNode;
    isNew?: boolean;
    isClosing?: boolean;
  }) => {
    return (
      <motion.tr
        layout
        initial={
          isNew
            ? {
                opacity: 0,
                scale: 0.95,
                backgroundColor: "rgb(34 197 94 / 0.1)", // green glow
              }
            : false
        }
        animate={{
          opacity: isClosing ? 0.5 : 1,
          scale: isClosing ? 0.98 : 1,
          x: isClosing ? -10 : 0,
          backgroundColor: isNew
            ? "rgb(34 197 94 / 0.05)"
            : isClosing
              ? "rgb(248 113 113 / 0.1)" // red glow for closing
              : "transparent",
        }}
        exit={{
          opacity: 0,
          scale: 0.95,
          x: -50,
          height: 0,
          transition: { duration: 0.6, ease: "easeInOut" },
        }}
        transition={{
          duration: isClosing ? 0.5 : 0.3,
          ease: "easeOut",
        }}
        className={cn(
          "cursor-pointer transition-all hover:bg-muted/50",
          selectedPositionId === position.id && "bg-primary/5 border-l-4 border-l-primary",
          isNew && "ring-2 ring-green-500/20 bg-green-50/50 dark:bg-green-950/20",
          isClosing && "bg-red-50/30 dark:bg-red-950/20 ring-1 ring-red-500/20"
        )}
        onClick={() => !isClosing && handlePositionSelect(position)}
      >
        {children}
      </motion.tr>
    );
  };

  // Компонент для индикатора статуса (pending/confirmed)
  const StatusIndicator = ({ isOptimistic }: { isOptimistic?: boolean }) => {
    if (!isOptimistic) return null;

    return (
      <motion.div
        initial={{ opacity: 0, scale: 0 }}
        animate={{ opacity: 1, scale: 1 }}
        className="absolute -top-1 -right-1 h-3 w-3 bg-yellow-500 rounded-full flex items-center justify-center"
      >
        <motion.div
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
          className="h-2 w-2 border border-white border-t-transparent rounded-full"
        />
      </motion.div>
    );
  };

  // Считаем количество элементов для каждой вкладки
  const openPositionsCount = openPositions.length;
  const openOrdersCount = openOrders.length;
  const filledOrdersCount = filledOrders.length;
  const orderHistoryCount = positionHistory.length;

  return (
    <Card className="h-[600px] flex flex-col p-0">
      <Tabs
        defaultValue="positions"
        value={activeTab}
        onValueChange={setActiveTab}
        className="h-full flex flex-col"
      >
        <div className="flex-shrink-0 px-2 pb-2">
          <TabsList className="grid w-full grid-cols-3 bg-muted/30">
            <TabsTrigger
              value="positions"
              className="text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
            >
              <div className="flex items-center gap-2">
                Positions
                {positionsFetching && !positionsLoading && (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="h-3 w-3 border border-current border-t-transparent rounded-full opacity-60"
                  />
                )}
                <Badge variant="secondary" className="h-5 px-1.5 text-xs">
                  {openPositionsCount}
                </Badge>
              </div>
            </TabsTrigger>
            <TabsTrigger
              value="openOrders"
              className="text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
            >
              Open Orders
              <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                {openOrdersCount}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="history"
              className="text-sm font-medium transition-all data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm"
            >
              Order History
              <Badge variant="secondary" className="ml-2 h-5 px-1.5 text-xs">
                {orderHistoryCount}
              </Badge>
            </TabsTrigger>
          </TabsList>
        </div>

        <CardContent className="flex-1 flex flex-col p-0">
          {loading ? (
            <div className="flex-1 flex flex-col items-center justify-center p-8">
              <Spinner size="large" className="text-primary" />
              <p className="mt-4 text-sm text-muted-foreground">Loading trading data...</p>
            </div>
          ) : error ? (
            <div className="flex-1 flex flex-col items-center justify-center p-8">
              <div className="text-center space-y-3">
                <div className="mx-auto w-12 h-12 rounded-full bg-destructive/10 flex items-center justify-center">
                  <svg
                    className="w-6 h-6 text-destructive"
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="12" cy="12" r="10"></circle>
                    <line x1="12" y1="8" x2="12" y2="12"></line>
                    <line x1="12" y1="16" x2="12.01" y2="16"></line>
                  </svg>
                </div>
                <div className="space-y-1">
                  <h3 className="text-sm font-semibold">Error loading data</h3>
                  <p className="text-xs text-destructive">{error}</p>
                  <p className="text-xs text-muted-foreground">Please try again later</p>
                </div>
              </div>
            </div>
          ) : (
            <>
              <TabsContent value="positions" className="flex-1 flex flex-col m-0">
                {openPositions.length > 0 ? (
                  <ScrollArea className="h-[520px]">
                    <div className="min-w-full pb-4">
                      <Table>
                        <TableHeader className="sticky top-0 bg-background/95 backdrop-blur-sm">
                          <TableRow>
                            <TableHead className="w-12"></TableHead>
                            <TableHead className="w-16 text-center">ID</TableHead>
                            <TableHead>Position</TableHead>
                            <TableHead className="text-right">Size</TableHead>
                            <TableHead className="text-center">Type</TableHead>
                            <TableHead className="text-right">Entry Price</TableHead>
                            <TableHead className="text-right">Liq Price</TableHead>
                            <TableHead className="text-center">SL/TP</TableHead>
                            <TableHead className="text-right">PnL</TableHead>
                            <TableHead className="text-center">Actions</TableHead>
                            <TableHead className="text-center">Share</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          <AnimatePresence mode="popLayout">
                            {openPositions.map((position) => {
                              const isNew = newPositionIds.has(position.id);
                              // Проверяем, является ли позиция оптимистической (например, по id меньше 0)
                              const isOptimistic = position.id < 0;

                              return (
                                <AnimatedTableRow
                                  key={position.id}
                                  position={position}
                                  isNew={isNew || isOptimistic}
                                  isClosing={closingPositions.has(position.id)}
                                >
                                  <TableCell onClick={(e) => e.stopPropagation()}>
                                    <div className="relative">
                                      <input
                                        type="radio"
                                        name="position"
                                        checked={selectedPositionId === position.id}
                                        onChange={() => handlePositionSelect(position)}
                                        className="h-4 w-4"
                                      />
                                      <StatusIndicator isOptimistic={isOptimistic} />
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-center">
                                    <div
                                      className={cn(
                                        styles.idBadge,
                                        isOptimistic
                                          ? styles.idBadgeOptimistic
                                          : position.id < 0
                                            ? styles.idBadgeTemporary
                                            : styles.idBadgeNormal
                                      )}
                                    >
                                      {position.id < 0
                                        ? `TEMP-${Math.abs(position.id)}`
                                        : position.id}
                                    </div>
                                  </TableCell>
                                  <TableCell>
                                    <div className="flex items-center gap-3">
                                      {getCryptoIcon(position.trading_pair)}
                                      <div>
                                        <div className="font-medium">{position.trading_pair}</div>
                                        <div className="text-xs text-muted-foreground">
                                          Perpetual {position.leverage}x
                                        </div>
                                      </div>
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-right font-mono">
                                    {formatNumber(position.position_size, false)}
                                  </TableCell>
                                  <TableCell className="text-center">
                                    <div className="relative">
                                      <Badge
                                        variant={
                                          position.direction === "LONG" ? "default" : "destructive"
                                        }
                                        className={cn(
                                          "text-xs font-semibold transition-all",
                                          position.direction === "LONG"
                                            ? "bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400"
                                            : "bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400",
                                          isOptimistic && "opacity-70 animate-pulse"
                                        )}
                                      >
                                        {position.direction}
                                        {isOptimistic && (
                                          <motion.span
                                            initial={{ opacity: 0 }}
                                            animate={{ opacity: 1 }}
                                            className="ml-1 text-xs"
                                          >
                                            •
                                          </motion.span>
                                        )}
                                      </Badge>
                                      {isNew && !isOptimistic && (
                                        <motion.div
                                          initial={{ scale: 0 }}
                                          animate={{ scale: [0, 1.2, 1] }}
                                          transition={{ duration: 0.5 }}
                                          className="absolute -top-1 -right-1 h-2 w-2 bg-green-500 rounded-full"
                                        />
                                      )}
                                    </div>
                                  </TableCell>
                                  <TableCell className="text-right font-mono">
                                    {formatNumber(position.entry_price)}
                                  </TableCell>
                                  <TableCell className="text-right font-mono">
                                    {formatNumber(position.liquidation_price)}
                                  </TableCell>
                                  <TableCell
                                    onClick={(e) => e.stopPropagation()}
                                    className="text-center"
                                  >
                                    {editingPositionId === position.id ? (
                                      <TPSLEditForm
                                        position={position}
                                        onSave={(data) => handleUpdateTPSL(data, position.id)}
                                        onCancel={() => setEditingPositionId(null)}
                                      />
                                    ) : (
                                      <div className="flex items-center gap-2 justify-center">
                                        <div className="text-xs text-muted-foreground">
                                          {position.stop_loss
                                            ? `SL: ${formatNumber(position.stop_loss)}`
                                            : "-"}{" "}
                                          /
                                          {position.take_profit
                                            ? ` TP: ${formatNumber(position.take_profit)}`
                                            : " -"}
                                        </div>
                                        <Button
                                          size="sm"
                                          variant="ghost"
                                          onClick={(e) => {
                                            e.stopPropagation();
                                            setEditingPositionId(position.id);
                                          }}
                                          className="h-7 w-7 p-0"
                                        >
                                          <svg
                                            className="h-3 w-3"
                                            xmlns="http://www.w3.org/2000/svg"
                                            viewBox="0 0 24 24"
                                            fill="none"
                                            stroke="currentColor"
                                            strokeWidth="2"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                          >
                                            <path d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                          </svg>
                                        </Button>
                                      </div>
                                    )}
                                  </TableCell>
                                  <TableCell className="text-right">
                                    <motion.div
                                      key={`${position.id}-${position.pnl}-${position.return_percentage}`}
                                      initial={false}
                                      animate={
                                        positionsFetching && !positionsLoading
                                          ? {
                                              scale: [1, 1.02, 1],
                                              opacity: [1, 0.8, 1],
                                            }
                                          : {}
                                      }
                                      transition={{ duration: 0.3 }}
                                      className="space-y-0.5"
                                    >
                                      <div
                                        className={cn(
                                          "font-mono text-sm font-semibold transition-colors",
                                          position.pnl >= 0
                                            ? "text-green-600 dark:text-green-400"
                                            : "text-red-600 dark:text-red-400"
                                        )}
                                      >
                                        {position.pnl >= 0 ? "+" : "-"}$
                                        {Math.abs(position.pnl).toLocaleString("en-US", {
                                          minimumFractionDigits: 2,
                                          maximumFractionDigits: 2,
                                        })}
                                      </div>
                                      <div
                                        className={cn(
                                          "text-xs font-medium transition-colors",
                                          position.pnl >= 0
                                            ? "text-green-600/80 dark:text-green-400/80"
                                            : "text-red-600/80 dark:text-red-400/80"
                                        )}
                                      >
                                        ({position.return_percentage >= 0 ? "+" : "-"}
                                        {Math.abs(position.return_percentage).toFixed(2)}%)
                                      </div>
                                    </motion.div>
                                  </TableCell>
                                  <TableCell
                                    onClick={(e) => e.stopPropagation()}
                                    className="text-center"
                                  >
                                    <Button
                                      size="sm"
                                      variant="destructive"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleClosePosition(position.id);
                                      }}
                                      disabled={
                                        closingPositions.has(position.id) ||
                                        closePositionMutation.isPending
                                      }
                                      className={cn(
                                        "h-8 px-3",
                                        closingPositions.has(position.id) &&
                                          "opacity-50 cursor-not-allowed"
                                      )}
                                    >
                                      {closingPositions.has(position.id) ? (
                                        <motion.div
                                          initial={{ opacity: 0 }}
                                          animate={{ opacity: 1 }}
                                          className="flex items-center gap-2"
                                        >
                                          <motion.div
                                            animate={{ rotate: 360 }}
                                            transition={{
                                              duration: 1,
                                              repeat: Infinity,
                                              ease: "linear",
                                            }}
                                            className="h-3 w-3 border border-white border-t-transparent rounded-full"
                                          />
                                          Closing...
                                        </motion.div>
                                      ) : (
                                        "Close"
                                      )}
                                    </Button>
                                  </TableCell>
                                  <TableCell
                                    onClick={(e) => e.stopPropagation()}
                                    className="text-center"
                                  >
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={(e) => {
                                        e.stopPropagation();
                                        handleShare(position.id);
                                      }}
                                      className="h-8 w-8 p-0"
                                    >
                                      <svg
                                        className="h-4 w-4"
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 24 24"
                                        fill="none"
                                        stroke="currentColor"
                                        strokeWidth="2"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                      >
                                        <path d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                                      </svg>
                                    </Button>
                                  </TableCell>
                                </AnimatedTableRow>
                              );
                            })}
                          </AnimatePresence>
                        </TableBody>
                      </Table>
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="flex-1 flex flex-col items-center justify-center p-8">
                    <div className="text-center space-y-3">
                      <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                        <svg
                          className="w-8 h-8 text-muted-foreground"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M3 3v18h18" />
                          <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
                        </svg>
                      </div>
                      <div className="space-y-1">
                        <h3 className="text-sm font-semibold">No open positions</h3>
                        <p className="text-xs text-muted-foreground">
                          Open a position to start trading
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="openOrders" className="flex-1 flex flex-col m-0">
                {openOrders.length > 0 ? (
                  <ScrollArea className="h-[520px]">
                    <div className="min-w-full pb-4">
                      <Table>
                        <TableHeader className="sticky top-0 bg-background/95 backdrop-blur-sm">
                          <TableRow>
                            <TableHead className="w-12"></TableHead>
                            <TableHead className="w-16 text-center">ID</TableHead>
                            <TableHead>Order</TableHead>
                            <TableHead className="text-center">Type</TableHead>
                            <TableHead className="text-right">Size</TableHead>
                            <TableHead className="text-right">Price</TableHead>
                            <TableHead className="text-center">Date</TableHead>
                            <TableHead className="text-center">Actions</TableHead>
                            <TableHead className="text-center">Share</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {openOrders.map((order) => (
                            <TableRow
                              key={order.id}
                              className={cn(
                                "cursor-pointer transition-all hover:bg-muted/50",
                                selectedPositionId === order.id &&
                                  "bg-primary/5 border-l-4 border-l-primary"
                              )}
                              onClick={() => handlePositionSelect(order)}
                            >
                              <TableCell onClick={(e) => e.stopPropagation()}>
                                <input
                                  type="radio"
                                  name="position"
                                  checked={selectedPositionId === order.id}
                                  onChange={() => handlePositionSelect(order)}
                                  className="h-4 w-4"
                                />
                              </TableCell>
                              <TableCell className="text-center">
                                <div className={cn(styles.idBadge, styles.idBadgeOrder)}>
                                  {order.id}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  {getCryptoIcon(order.trading_pair)}
                                  <div>
                                    <div className="font-medium">{order.trading_pair}</div>
                                    <div className="text-xs text-muted-foreground">
                                      Limit {order.leverage}x
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="text-center">
                                <Badge
                                  variant={order.direction === "LONG" ? "default" : "destructive"}
                                  className={cn(
                                    "text-xs font-semibold",
                                    order.direction === "LONG"
                                      ? "bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400"
                                      : "bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400"
                                  )}
                                >
                                  {order.direction}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right font-mono">
                                {formatNumber(order.position_size, false)}
                              </TableCell>
                              <TableCell className="text-right font-mono">
                                {formatNumber(order.entry_price)}
                              </TableCell>
                              <TableCell className="text-center text-xs text-muted-foreground">
                                {formatDate(order.opened_at)}
                              </TableCell>
                              <TableCell
                                onClick={(e) => e.stopPropagation()}
                                className="text-center"
                              >
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleCancelOrder(order.id);
                                  }}
                                  className="h-8 px-3"
                                >
                                  Cancel
                                </Button>
                              </TableCell>
                              <TableCell
                                onClick={(e) => e.stopPropagation()}
                                className="text-center"
                              >
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleShare(order.id);
                                  }}
                                  className="h-8 w-8 p-0"
                                >
                                  <svg
                                    className="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <path d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                                  </svg>
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="flex-1 flex flex-col items-center justify-center p-8">
                    <div className="text-center space-y-3">
                      <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                        <svg
                          className="w-8 h-8 text-muted-foreground"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" />
                          <rect x="8" y="2" width="8" height="4" rx="1" ry="1" />
                        </svg>
                      </div>
                      <div className="space-y-1">
                        <h3 className="text-sm font-semibold">No open orders</h3>
                        <p className="text-xs text-muted-foreground">
                          You don't have any pending orders
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="history" className="flex-1 flex flex-col m-0">
                {positionHistory.length > 0 ? (
                  <ScrollArea className="h-[520px]">
                    <div className="min-w-full pb-4">
                      <Table>
                        <TableHeader className="sticky top-0 bg-background/95 backdrop-blur-sm">
                          <TableRow>
                            <TableHead className="w-12"></TableHead>
                            <TableHead className="w-16 text-center">ID</TableHead>
                            <TableHead>Position</TableHead>
                            <TableHead className="text-center">Type</TableHead>
                            <TableHead className="text-right">Size</TableHead>
                            <TableHead className="text-right">Entry/Exit Price</TableHead>
                            <TableHead className="text-right">PnL</TableHead>
                            <TableHead className="text-center">Status</TableHead>
                            <TableHead className="text-center">Opened/Closed At</TableHead>
                            <TableHead className="text-center">Share</TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {positionHistory.map((position) => (
                            <TableRow
                              key={position.id}
                              className={cn(
                                "cursor-pointer transition-all hover:bg-muted/50",
                                selectedPositionId === position.id &&
                                  "bg-primary/5 border-l-4 border-l-primary"
                              )}
                              onClick={() => handlePositionSelect(position)}
                            >
                              <TableCell onClick={(e) => e.stopPropagation()}>
                                <input
                                  type="radio"
                                  name="position"
                                  checked={selectedPositionId === position.id}
                                  onChange={() => handlePositionSelect(position)}
                                  className="h-4 w-4"
                                />
                              </TableCell>
                              <TableCell className="text-center">
                                <div className={cn(styles.idBadge, styles.idBadgeNormal)}>
                                  {position.id}
                                </div>
                              </TableCell>
                              <TableCell>
                                <div className="flex items-center gap-3">
                                  {getCryptoIcon(position.trading_pair)}
                                  <div>
                                    <div className="font-medium">{position.trading_pair}</div>
                                    <div className="text-xs text-muted-foreground">
                                      {position.leverage}x{" "}
                                      {position.close_reason ? `(${position.close_reason})` : ""}
                                    </div>
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="text-center">
                                <Badge
                                  variant={
                                    position.direction === "LONG" ? "default" : "destructive"
                                  }
                                  className={cn(
                                    "text-xs font-semibold",
                                    position.direction === "LONG"
                                      ? "bg-green-100 text-green-800 hover:bg-green-100 dark:bg-green-900/20 dark:text-green-400"
                                      : "bg-red-100 text-red-800 hover:bg-red-100 dark:bg-red-900/20 dark:text-red-400"
                                  )}
                                >
                                  {position.direction}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-right font-mono">
                                {formatNumber(position.position_size, false)}
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="space-y-0.5">
                                  <div className="font-mono text-sm">
                                    {formatNumber(position.entry_price)}
                                  </div>
                                  <div className="font-mono text-xs text-muted-foreground">
                                    {formatNumber(position.current_price)}
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="text-right">
                                <div className="space-y-0.5">
                                  <div
                                    className={cn(
                                      "font-mono text-sm font-semibold",
                                      position.pnl >= 0
                                        ? "text-green-600 dark:text-green-400"
                                        : "text-red-600 dark:text-red-400"
                                    )}
                                  >
                                    {position.pnl >= 0 ? "+" : "-"}$
                                    {Math.abs(position.pnl).toLocaleString("en-US", {
                                      minimumFractionDigits: 2,
                                      maximumFractionDigits: 2,
                                    })}
                                  </div>
                                  <div
                                    className={cn(
                                      "text-xs font-medium",
                                      position.pnl >= 0
                                        ? "text-green-600/80 dark:text-green-400/80"
                                        : "text-red-600/80 dark:text-red-400/80"
                                    )}
                                  >
                                    ({position.return_percentage >= 0 ? "+" : "-"}
                                    {Math.abs(position.return_percentage).toFixed(2)}%)
                                  </div>
                                </div>
                              </TableCell>
                              <TableCell className="text-center">
                                <Badge
                                  variant={
                                    position.status === "LIQUIDATED" ? "destructive" : "secondary"
                                  }
                                  className="text-xs"
                                >
                                  {position.status}
                                </Badge>
                              </TableCell>
                              <TableCell className="text-center">
                                <div className="space-y-0.5">
                                  <div className="text-xs text-muted-foreground">
                                    {formatDate(position.opened_at)}
                                  </div>
                                  {position.closed_at && (
                                    <div className="text-xs text-muted-foreground">
                                      {formatDate(position.closed_at)}
                                    </div>
                                  )}
                                </div>
                              </TableCell>
                              <TableCell
                                onClick={(e) => e.stopPropagation()}
                                className="text-center"
                              >
                                <Button
                                  size="sm"
                                  variant="ghost"
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    handleShare(position.id);
                                  }}
                                  className="h-8 w-8 p-0"
                                >
                                  <svg
                                    className="h-4 w-4"
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    stroke="currentColor"
                                    strokeWidth="2"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                  >
                                    <path d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z" />
                                  </svg>
                                </Button>
                              </TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </ScrollArea>
                ) : (
                  <div className="flex-1 flex flex-col items-center justify-center p-8">
                    <div className="text-center space-y-3">
                      <div className="mx-auto w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                        <svg
                          className="w-8 h-8 text-muted-foreground"
                          xmlns="http://www.w3.org/2000/svg"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <circle cx="12" cy="12" r="3" />
                          <path d="M12 1v6m0 6v6" />
                          <path d="m21 12-6-3-6 3-6-3" />
                        </svg>
                      </div>
                      <div className="space-y-1">
                        <h3 className="text-sm font-semibold">No position history</h3>
                        <p className="text-xs text-muted-foreground">
                          Your trading history will appear here
                        </p>
                      </div>
                    </div>
                  </div>
                )}
              </TabsContent>
            </>
          )}
        </CardContent>
      </Tabs>
    </Card>
  );
};

export default PositionsTable;
