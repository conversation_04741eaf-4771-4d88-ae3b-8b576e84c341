"use client";

import React from "react";

import { cn } from "@/lib/utils";

interface StatItemProps {
  label: string;
  value: string;
  className?: string;
}

export const StatItem: React.FC<StatItemProps> = ({ label, value, className }) => {
  return (
    <div
      className={cn(
        "stat-item flex flex-col p-3 rounded-lg bg-muted/30 dark:bg-secondary/10 border border-border/10",
        "min-w-0",
        className
      )}
    >
      <span className="stat-label text-xs font-medium text-muted-foreground mb-1 truncate">
        {label}
      </span>
      <span className="stat-value text-sm font-semibold truncate">{value}</span>
    </div>
  );
};

export default StatItem;
