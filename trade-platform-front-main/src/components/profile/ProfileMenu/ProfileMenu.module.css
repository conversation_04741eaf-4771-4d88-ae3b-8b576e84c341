.avatar {
  width: 40px !important;
  height: 40px !important;
  background: linear-gradient(135deg, #00aeff 0%, #0096e0 100%) !important;
  border: 1px solid rgba(0, 174, 255, 0.2) !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  flex-shrink: 0;
}

.avatar:hover {
  border-color: rgba(0, 174, 255, 0.3) !important;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 174, 255, 0.2);
}

.menuContent {
  width: 260px;
  padding: 12px 8px;
  background-color: var(--menu-bg, rgb(241, 241, 241));
  color: var(--text-primary, rgb(17, 17, 17));
  border-radius: 12px;
  border: 1px solid var(--border-color, rgba(0, 0, 0, 0.08));
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease;
}

/* User info section styles */
.userInfo {
  display: flex;
  align-items: center;
  padding: 8px 12px 16px 12px;
  gap: 12px;
}

.menuAvatar {
  width: 42px !important;
  height: 42px !important;
  background: linear-gradient(135deg, #00aeff 0%, #0096e0 100%) !important;
  border: 1px solid rgba(0, 174, 255, 0.2) !important;
}

.userInfoText {
  display: flex;
  flex-direction: column;
  gap: 4px;
  max-width: 180px;
}

.userEmail {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}

.userIdContainer {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 2px 4px;
  border-radius: 4px;
}

.userId {
  font-size: 12px;
  color: var(--text-secondary, rgba(17, 17, 17, 0.6));
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.noId {
  font-style: italic;
  color: var(--text-secondary, rgba(17, 17, 17, 0.4));
}

/* Удалены неиспользуемые стили, так как мы используем shadcn/ui компоненты */

/* Menu item styles */
.menuItem {
  padding: 10px 12px;
  font-size: 14px;
  display: flex;
  align-items: center;
  cursor: pointer;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  gap: 12px;
  position: relative;
}

.menuItem:hover {
  background-color: var(--accent-light, rgba(0, 128, 255, 0.1));
}

.menuIcon {
  width: 18px;
  height: 18px;
  color: var(--text-primary, rgb(17, 17, 17));
  opacity: 0.8;
}

/* Notification marker */
.notification {
  position: absolute;
  right: 12px;
  width: 8px;
  height: 8px;
  background-color: #f54242;
  border-radius: 50%;
}

/* Theme toggle styles */
.themeToggleContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 12px;
  font-size: 14px;
}

.themeSwitch {
  height: 20px !important;
}

/* Language selector styles */
.langLabel {
  flex: 1;
}

.langValue {
  color: var(--text-secondary, rgba(17, 17, 17, 0.6));
  font-size: 14px;
  margin-left: 8px;
}

/* Logout button styles */
.logoutMenuItem {
  color: #f54242 !important;
  padding: 10px 12px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-radius: 6px;
}

.logoutMenuItem:hover {
  background-color: rgba(245, 66, 66, 0.1);
}

.logoutIcon {
  width: 18px;
  height: 18px;
  color: #f54242;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .menuContent {
    width: 240px;
  }

  .userInfoText {
    max-width: 160px;
  }
}

/* Dark mode adjustments will be handled by ThemeContext and CSS variables */
