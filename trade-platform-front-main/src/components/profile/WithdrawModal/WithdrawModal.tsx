"use client";

import { <PERSON><PERSON><PERSON><PERSON><PERSON>, CheckCircle, Info, Wallet } from "lucide-react";
import React, { useEffect, useState } from "react";

import {
  getUserFixedBalance,
  getUserSocialProfileById,
  subscribeToBalanceUpdates,
} from "@/app/utils/api";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuthStore } from "@/entities/auth/model/store";
import { cn } from "@/lib/utils";
import { getWithdrawService } from "@/shared/api/services/withdraw-service";

import {
  BalanceItem,
  FixedBalanceData,
  WithdrawModalProps,
  WithdrawResponse,
  WithdrawResponseStatus,
} from "./types";

export const WithdrawModal: React.FC<WithdrawModalProps> = ({ isOpen, onClose }) => {
  // Auth store for user data
  const user = useAuthStore((state) => state.auth.user);

  // Состояния
  const [availableBalance, setAvailableBalance] = useState(0);
  const [balanceLoading, setBalanceLoading] = useState(true);
  const [withdrawAmount, setWithdrawAmount] = useState("");
  const [feePercentage] = useState(0.1); // Withdrawal fee (0.1%)
  const [feeAmount, setFeeAmount] = useState(0);
  const [finalAmount, setFinalAmount] = useState(0);
  const [withdrawAddress, setWithdrawAddress] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  // Новые состояния
  const [step, setStep] = useState<"form" | "open-positions" | "2fa" | "success">("form");
  const [verificationCode, setVerificationCode] = useState("");
  const [emailAddress, setEmailAddress] = useState("");
  const [codeSent, setCodeSent] = useState(false);
  const [withdrawId, setWithdrawId] = useState<string | null>(null);
  const [codeError, setCodeError] = useState<string | null>(null);

  // Загрузка данных пользователя при монтировании компонента
  useEffect(() => {
    const loadUserData = async () => {
      try {
        // First check if we have a valid email from auth store (not the fallback)
        if (user?.id && user?.email && user.email !== "<EMAIL>") {
          setEmailAddress(user.email);
        } else if (user?.id) {
          // Always try to fetch fresh profile data if email is missing or fallback
          const userProfile = await getUserSocialProfileById();
          if (userProfile && userProfile.email) {
            setEmailAddress(userProfile.email);
          } else {
            // Set placeholder if no email found
            setEmailAddress("<EMAIL>");
          }
        }
      } catch (error) {
        console.error("Error loading user profile:", error);
        // Set placeholder on error
        setEmailAddress("<EMAIL>");
      }
    };

    loadUserData();
  }, [user]);

  // Загрузка баланса пользователя
  useEffect(() => {
    if (!isOpen) return;

    let storeUnsubscribe: (() => void) | null = null;

    // Immediately load balance data using the REST API
    const fetchBalance = async () => {
      try {
        setBalanceLoading(true);
        const data = await getUserFixedBalance();

        if (data && data.available_usd_balance !== undefined) {
          setAvailableBalance(parseFloat(data.available_usd_balance));
        } else if (data && data.total_equivalent_usd !== undefined) {
          setAvailableBalance(parseFloat(data.total_equivalent_usd));
        } else {
          setAvailableBalance(25000);
        }
      } catch {
        // Fallback to default balance on error
        setAvailableBalance(25000);
      } finally {
        setBalanceLoading(false);
      }
    };

    // Start loading balance data immediately
    fetchBalance();

    // Dynamically import the store to avoid circular dependencies
    import("@/entities/wallet/model/balance-query-store")
      .then(({ balanceQueryStore }) => {
        // Initial state from store
        const initialState = balanceQueryStore.getState().balanceQuery;

        // If there's already data in the store, use it immediately
        if (initialState.availableBalanceUsd > 0) {
          setAvailableBalance(initialState.availableBalanceUsd);
          setBalanceLoading(false);
        }

        // Subscribe to store changes
        storeUnsubscribe = balanceQueryStore.subscribe((state) => {
          const balanceState = state.balanceQuery;

          // Update component state when store changes
          if (balanceState.availableBalanceUsd !== undefined) {
            setAvailableBalance(balanceState.availableBalanceUsd);
          }

          // Update loading state
          setBalanceLoading(balanceState.isLoading);
        });
      })
      .catch(() => {
        // Fallback if import fails
        // console.error("Failed to import balance query store");
      });

    // Subscribe to balance updates
    // Note: This now uses REST API polling instead of WebSocket
    const id = user?.id;
    let balanceUnsubscribe: (() => void) | null = null;

    if (id) {
      balanceUnsubscribe = subscribeToBalanceUpdates(
        (updatedBalances: BalanceItem[], fixedBalanceData?: FixedBalanceData) => {
          if (fixedBalanceData && fixedBalanceData.available_usd_balance !== undefined) {
            setAvailableBalance(parseFloat(fixedBalanceData.available_usd_balance));
          }
        },
        id
      );
    }

    // Clean up all subscriptions
    return () => {
      if (balanceUnsubscribe) {
        balanceUnsubscribe();
      }
      if (storeUnsubscribe) {
        storeUnsubscribe();
      }
    };
  }, [isOpen]);

  // Расчет комиссии и финальной суммы при изменении суммы вывода
  useEffect(() => {
    if (!withdrawAmount || isNaN(parseFloat(withdrawAmount)) || parseFloat(withdrawAmount) <= 0) {
      setFeeAmount(0);
      setFinalAmount(0);
      return;
    }

    const amountValue = parseFloat(withdrawAmount);
    const calculatedFee = amountValue * (feePercentage / 100); // 0.1% = 0.001
    setFeeAmount(calculatedFee);
    setFinalAmount(amountValue - calculatedFee);
  }, [withdrawAmount, feePercentage]);

  // Обработчик изменения суммы вывода
  const handleAmountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value === "" || (/^\d*\.?\d{0,2}$/.test(value) && !isNaN(parseFloat(value)))) {
      setWithdrawAmount(value);
    }
  };

  // Обработчик изменения адреса вывода
  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setWithdrawAddress(e.target.value);
  };

  // Handle percentage selection
  const handlePercentageAmount = (percentage: number) => {
    const amount = ((availableBalance * percentage) / 100).toFixed(2);
    setWithdrawAmount(amount);
  };

  // Валидация адреса кошелька TRC20
  const isValidTrc20Address = (address: string): boolean => {
    // Базовая проверка на формат адреса TRC20
    return /^T[A-Za-z1-9]{33}$/.test(address);
  };

  // Отправка кода 2FA на email
  const handleSendVerificationCode = async () => {
    if (!withdrawId) {
      alert("Error: Withdrawal request not found");
      return;
    }

    try {
      setIsProcessing(true);
      setCodeError(null);

      // Отправляем запрос на получение кода верификации
      const withdrawService = getWithdrawService();
      const response = await withdrawService.sendVerificationCode(withdrawId);

      if (response.success) {
        setCodeSent(true);
      } else {
        setCodeError(response.message || "Failed to send verification code");
      }
    } catch {
      setCodeError("An error occurred while sending the code");
    } finally {
      setIsProcessing(false);
    }
  };

  // Обработчик запроса на вывод средств
  const handleSubmit = async () => {
    if (!withdrawAmount || parseFloat(withdrawAmount) <= 0) {
      alert("Please enter a valid amount");
      return;
    }

    if (!withdrawAddress) {
      alert("Please enter a withdrawal address");
      return;
    }

    if (!isValidTrc20Address(withdrawAddress)) {
      alert("Invalid TRC20 address format");
      return;
    }

    if (parseFloat(withdrawAmount) > availableBalance) {
      alert("Insufficient funds for withdrawal");
      return;
    }

    if (!user?.id) {
      alert("Error: Could not retrieve user ID");
      return;
    }

    if (!emailAddress) {
      alert("Error: Could not retrieve email address");
      return;
    }

    try {
      setIsProcessing(true);

      // Отправка запроса на вывод средств с включением email
      // Note: API expects the original amount BEFORE fee deduction and calculates fee automatically
      const withdrawService = getWithdrawService();
      const response = await withdrawService.requestWithdrawal({
        userId: user.id,
        amount: parseFloat(withdrawAmount), // This is the original amount the user wants to withdraw
        destinationWallet: withdrawAddress,
        fee: feeAmount, // This is for display purposes, but API will calculate its own fee
        email: emailAddress,
        currency: "USDT",
        network: "TRC20",
      });

      // Обработка ответа от сервера согласно API документации
      if (response.status === "SUCCESS") {
        // Сохраняем ID заявки для дальнейшего использования
        if (response.withdrawId) {
          setWithdrawId(response.withdrawId);
        }
        // Если успех, переходим к 2FA верификации
        setStep("2fa");
        setCodeSent(false); // Сбрасываем статус отправки кода
      } else if (response.status === "OPEN_POSITIONS") {
        // Сохраняем ID заявки для дальнейшего использования
        if (response.withdrawId) {
          setWithdrawId(response.withdrawId);
        }
        // Если есть открытые позиции, переходим к окну предупреждения
        setStep("open-positions");
      } else {
        // Обработка других ошибок
        alert(response.message || "An error occurred while creating the request");
      }
    } catch {
      alert("An error occurred while preparing the request");
    } finally {
      setIsProcessing(false);
    }
  };

  // Игнорирование открытых позиций и переход к 2FA
  const handleContinueDespitePositions = async () => {
    if (!withdrawId) {
      alert("Error: Withdrawal request not found");
      return;
    }

    try {
      setIsProcessing(true);

      // Отправляем запрос на продолжение вывода с открытыми позициями
      const withdrawService = getWithdrawService();
      const response = await withdrawService.continueWithPositions(withdrawId);

      if (response.status === "SUCCESS") {
        // Переход к 2FA верификации
        setStep("2fa");
        setCodeSent(false); // Сбрасываем статус отправки кода
      } else {
        alert(response.message || "An error occurred while processing the request");
      }
    } catch {
      alert("An error occurred while processing the request");
    } finally {
      setIsProcessing(false);
    }
  };

  // Перенаправление на страницу торговли для закрытия позиций
  const handleClosePositions = () => {
    // В реальной реализации здесь был бы редирект или колбэк
    window.location.href = "/trade"; // Исправлено на правильный роут
  };

  // Подтверждение вывода с кодом 2FA
  const handleConfirmWithdrawal = async () => {
    if (!withdrawId) {
      alert("Error: Withdrawal request not found");
      return;
    }

    if (!verificationCode) {
      setCodeError("Please enter the verification code");
      return;
    }

    if (verificationCode.length !== 6 || !/^\d+$/.test(verificationCode)) {
      setCodeError("The verification code must contain 6 digits");
      return;
    }

    try {
      setIsProcessing(true);
      setCodeError(null);

      // Отправляем запрос на подтверждение вывода с кодом
      const withdrawService = getWithdrawService();
      const response = await withdrawService.verifyWithdrawal({
        withdrawId,
        verificationCode,
      });

      if (response.status === "SUCCESS") {
        // После успешного подтверждения
        setStep("success");
      } else {
        setCodeError(response.message || "Error confirming withdrawal");
      }
    } catch {
      setCodeError("An error occurred while confirming the withdrawal");
    } finally {
      setIsProcessing(false);
    }
  };

  // Закрытие модального окна и сброс состояний
  const handleCloseModal = () => {
    setWithdrawAmount("");
    setWithdrawAddress("");
    setVerificationCode("");
    setStep("form");
    setCodeSent(false);
    setWithdrawId(null);
    setCodeError(null);
    onClose();
  };

  // Format currency
  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount);
  };

  // Валидация полей формы
  const isSubmitDisabled =
    !withdrawAmount ||
    parseFloat(withdrawAmount) <= 0 ||
    !withdrawAddress ||
    !isValidTrc20Address(withdrawAddress) ||
    parseFloat(withdrawAmount) > availableBalance ||
    isProcessing ||
    !user?.id ||
    !emailAddress;

  return (
    <Dialog open={isOpen} onOpenChange={handleCloseModal}>
      <DialogContent className="max-w-md mx-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl font-semibold">
            <Wallet className="h-5 w-5 text-blue-500" />
            {step === "form"
              ? "Withdraw Funds"
              : step === "open-positions"
                ? "Warning"
                : step === "2fa"
                  ? "Verification"
                  : "Success"}
          </DialogTitle>
        </DialogHeader>

        {step === "form" && (
          <div className="space-y-5">
            {/* Available Balance */}
            <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm text-muted-foreground">Available Balance</span>
                <Badge variant="secondary" className="text-xs">
                  USDT
                </Badge>
              </div>
              {balanceLoading ? (
                <div className="flex items-center gap-2">
                  <div className="w-4 h-4 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading...</span>
                </div>
              ) : (
                <p className="text-2xl font-bold">{formatCurrency(availableBalance)}</p>
              )}
            </div>

            {/* Amount Input */}
            <div className="space-y-2">
              <Label htmlFor="amount">Amount</Label>
              <div className="relative">
                <Input
                  id="amount"
                  type="text"
                  value={withdrawAmount}
                  onChange={handleAmountChange}
                  placeholder="0.00"
                  className={cn(
                    "pr-12 text-lg",
                    parseFloat(withdrawAmount || "0") > availableBalance &&
                      "border-red-500 focus:border-red-500"
                  )}
                />
                <span className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                  USD
                </span>
              </div>

              {/* Quick percentage buttons */}
              <div className="grid grid-cols-4 gap-2 mt-2">
                {[25, 50, 75, 100].map((percent) => (
                  <Button
                    key={percent}
                    variant="outline"
                    size="sm"
                    onClick={() => handlePercentageAmount(percent)}
                    className="text-xs"
                  >
                    {percent}%
                  </Button>
                ))}
              </div>

              {parseFloat(withdrawAmount || "0") > availableBalance && (
                <p className="text-sm text-red-500">Insufficient funds</p>
              )}
            </div>

            {/* Withdrawal Address */}
            <div className="space-y-2">
              <Label htmlFor="address">Withdrawal Address (TRC20)</Label>
              <Input
                id="address"
                type="text"
                value={withdrawAddress}
                onChange={handleAddressChange}
                placeholder="TRC20 wallet address"
                className={cn(
                  withdrawAddress &&
                    !isValidTrc20Address(withdrawAddress) &&
                    "border-red-500 focus:border-red-500"
                )}
              />
              {withdrawAddress && !isValidTrc20Address(withdrawAddress) && (
                <p className="text-sm text-red-500">Invalid TRC20 address format</p>
              )}
            </div>

            {/* Fee Summary */}
            {withdrawAmount && parseFloat(withdrawAmount) > 0 && (
              <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Amount:</span>
                  <span>{formatCurrency(parseFloat(withdrawAmount))}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-muted-foreground">Fee ({feePercentage}%):</span>
                  <span className="text-red-500">-{formatCurrency(feeAmount)}</span>
                </div>
                <div className="h-px bg-gray-200 dark:bg-gray-700" />
                <div className="flex justify-between font-medium">
                  <span>You'll receive:</span>
                  <span className="text-green-600">{formatCurrency(finalAmount)}</span>
                </div>
              </div>
            )}

            {/* Submit Button */}
            <Button
              onClick={handleSubmit}
              disabled={isSubmitDisabled}
              className="w-full h-11"
              size="lg"
            >
              {isProcessing ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Processing...
                </>
              ) : (
                "Withdraw"
              )}
            </Button>

            {/* Info */}
            <div className="flex items-start gap-2 p-3 bg-blue-50 dark:bg-blue-950/30 rounded-lg">
              <Info className="h-4 w-4 text-blue-500 mt-0.5" />
              <p className="text-xs text-muted-foreground">
                Minimum withdrawal: $50.00 • Processing time: 10-30 minutes
              </p>
            </div>
          </div>
        )}

        {step === "open-positions" && (
          <div className="space-y-5">
            <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-950/50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription>
                <div className="space-y-2">
                  <p className="font-medium">You have open positions</p>
                  <p className="text-sm">
                    For faster processing, we recommend closing your positions before withdrawing.
                  </p>
                </div>
              </AlertDescription>
            </Alert>

            <div className="flex gap-3">
              <Button variant="default" onClick={handleClosePositions} className="flex-1">
                Go to Trading
              </Button>
              <Button
                variant="outline"
                onClick={handleContinueDespitePositions}
                disabled={isProcessing}
                className="flex-1"
              >
                {isProcessing ? "Processing..." : "Continue Anyway"}
              </Button>
            </div>
          </div>
        )}

        {step === "2fa" && (
          <div className="space-y-5">
            <div className="text-center">
              <p className="text-sm text-muted-foreground">We'll send a verification code to:</p>
              <p className="font-medium mt-1">{emailAddress}</p>
            </div>

            <div className="space-y-3">
              <Label htmlFor="verification-code">Verification Code</Label>
              <div className="flex gap-2">
                <Input
                  id="verification-code"
                  type="text"
                  className="text-center text-lg tracking-widest font-mono"
                  placeholder="000000"
                  value={verificationCode}
                  onChange={(e) =>
                    setVerificationCode(e.target.value.replace(/\D/g, "").slice(0, 6))
                  }
                  maxLength={6}
                />
                <Button
                  variant="outline"
                  onClick={handleSendVerificationCode}
                  disabled={isProcessing || codeSent}
                >
                  {codeSent ? "Sent" : "Send Code"}
                </Button>
              </div>

              {codeSent && !codeError && (
                <Alert className="border-green-200 bg-green-50 dark:bg-green-950/50">
                  <CheckCircle className="h-4 w-4 text-green-600" />
                  <AlertDescription>
                    Code sent to your email. Please check your inbox.
                  </AlertDescription>
                </Alert>
              )}

              {codeError && (
                <Alert className="border-red-200 bg-red-50 dark:bg-red-950/50">
                  <AlertTriangle className="h-4 w-4 text-red-600" />
                  <AlertDescription>{codeError}</AlertDescription>
                </Alert>
              )}
            </div>

            {/* Summary */}
            <div className="p-4 bg-gray-50 dark:bg-gray-800/50 rounded-lg space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Amount:</span>
                <span className="font-medium">{formatCurrency(finalAmount)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-muted-foreground">To:</span>
                <span className="font-mono text-xs">
                  {withdrawAddress.slice(0, 6)}...{withdrawAddress.slice(-6)}
                </span>
              </div>
            </div>

            <Button
              onClick={handleConfirmWithdrawal}
              disabled={
                !verificationCode || verificationCode.length !== 6 || isProcessing || !codeSent
              }
              className="w-full h-11"
              size="lg"
            >
              {isProcessing ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2" />
                  Confirming...
                </>
              ) : (
                "Confirm Withdrawal"
              )}
            </Button>
          </div>
        )}

        {step === "success" && (
          <div className="space-y-5">
            <div className="text-center space-y-3">
              <div className="mx-auto w-16 h-16 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">Withdrawal Submitted</h3>
                <p className="text-sm text-muted-foreground mt-1">
                  Your withdrawal of {formatCurrency(finalAmount)} has been submitted for
                  processing.
                </p>
              </div>
            </div>

            <Card>
              <CardContent className="pt-4 space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Amount:</span>
                  <span className="font-medium">{formatCurrency(finalAmount)}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">To:</span>
                  <span className="font-mono text-xs">
                    {withdrawAddress.slice(0, 8)}...{withdrawAddress.slice(-8)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Network:</span>
                  <Badge variant="outline" className="text-xs">
                    TRC20
                  </Badge>
                </div>
              </CardContent>
            </Card>

            <Button onClick={handleCloseModal} className="w-full h-11" size="lg">
              Done
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default WithdrawModal;
