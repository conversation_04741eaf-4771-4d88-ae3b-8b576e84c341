"use client";

import { createModuleLogger } from "@/lib/logger";

// Create module-specific loggers with better naming
// These will automatically use the right implementation based on environment
const utilsLogger = createModuleLogger("api-utils");
const apiLogger = createModuleLogger("api");
const wsLogger = createModuleLogger("websocket");

// Types for the request logging
export interface RequestLogData {
  method: string;
  url: string;
  headers?: Record<string, string>;
  body?: any;
  startTime: number;
}

// Types for the response logging
export interface ResponseLogData {
  status: number;
  statusText: string;
  headers?: Record<string, string>;
  body?: any;
  duration: number;
}

// Function to sanitize sensitive data like tokens
export function sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
  const sanitized = { ...headers };

  // Hide sensitive information in authorization headers
  if (sanitized["Authorization"]) {
    sanitized["Authorization"] = sanitized["Authorization"].replace(
      /Bearer\s+(.{6}).*/,
      "Bearer $1***"
    );
  }

  return sanitized;
}

// Function to sanitize potentially sensitive request body data
export function sanitizeBody(body: any): any {
  if (!body) return body;

  // Make a copy to avoid modifying the original
  const sanitized = { ...body };

  // Replace sensitive fields with masked values
  const sensitiveFields = ["password", "token", "secret", "key", "authentication"];
  for (const field of sensitiveFields) {
    if (sanitized[field]) {
      sanitized[field] = "***";
    }
  }

  return sanitized;
}

/**
 * Log an API request before it's sent
 */
export function logRequest(method: string, url: string, options?: RequestInit): RequestLogData {
  const startTime = performance.now();
  const headers = options?.headers ? (options.headers as Record<string, string>) : {};

  let body: any = undefined;

  try {
    // Parse the request body if it exists
    if (options?.body) {
      if (typeof options.body === "string") {
        try {
          body = JSON.parse(options.body);
        } catch {
          // Not JSON, use as-is
          body = options.body;
        }
      } else {
        body = options.body;
      }
    }
  } catch (error) {
    // If body can't be processed, just use as-is
    utilsLogger.debug("Failed to process request body", { error });
    body = options?.body;
  }

  // Log the request details
  apiLogger.debug("API Request", {
    method,
    url,
    headers: sanitizeHeaders(headers),
    body: sanitizeBody(body),
  });

  return {
    method,
    url,
    headers,
    body,
    startTime,
  };
}

/**
 * Log an API response after it's received
 */
export function logResponse(
  requestData: RequestLogData,
  response: Response,
  responseBody?: any
): ResponseLogData {
  const endTime = performance.now();
  const duration = endTime - requestData.startTime;

  // Extract response headers
  const headers: Record<string, string> = {};
  response.headers.forEach((value, name) => {
    headers[name] = value;
  });

  // Create response log data
  const responseData: ResponseLogData = {
    status: response.status,
    statusText: response.statusText,
    headers,
    body: responseBody,
    duration,
  };

  // Log based on status code
  if (response.ok) {
    apiLogger.info("API Response Success", {
      method: requestData.method,
      url: requestData.url,
      status: response.status,
      duration: `${duration.toFixed(2)}ms`,
    });

    apiLogger.debug("API Response Details", responseData);
  } else {
    apiLogger.error("API Response Error", {
      method: requestData.method,
      url: requestData.url,
      status: response.status,
      statusText: response.statusText,
      body: responseBody,
      duration: `${duration.toFixed(2)}ms`,
    });
  }

  return responseData;
}

/**
 * Log a network or other API error
 */
export function logApiError(requestData: RequestLogData, error: any): void {
  const endTime = performance.now();
  const duration = endTime - requestData.startTime;

  apiLogger.error("API Request Failed", {
    method: requestData.method,
    url: requestData.url,
    error: error instanceof Error ? error.message : String(error),
    duration: `${duration.toFixed(2)}ms`,
    stack: error instanceof Error ? error.stack : undefined,
  });
}

/**
 * Enhanced fetch with logging
 */
export async function loggedFetch(url: string, options?: RequestInit): Promise<Response> {
  const method = options?.method || "GET";
  const requestData = logRequest(method, url, options);

  try {
    const response = await fetch(url, options);

    // Clone the response to read its body without consuming it
    const clonedResponse = response.clone();

    try {
      // Try to parse response as JSON
      const responseBody = await clonedResponse.json();
      logResponse(requestData, response, responseBody);
    } catch {
      // If not JSON, log without body
      logResponse(requestData, response);
    }

    return response;
  } catch (error) {
    logApiError(requestData, error);
    throw error;
  }
}

/**
 * Log WebSocket events
 */
export function logWebSocketEvent(url: string, event: string, data?: any): void {
  switch (event) {
    case "open":
      wsLogger.info("WebSocket Connected", { url });
      break;
    case "message":
      wsLogger.debug("WebSocket Message Received", {
        url,
        data: typeof data === "string" ? tryParseJson(data) : data,
      });
      break;
    case "send":
      wsLogger.debug("WebSocket Message Sent", {
        url,
        data: typeof data === "string" ? tryParseJson(data) : data,
      });
      break;
    case "error":
      wsLogger.error("WebSocket Error", {
        url,
        error: data instanceof Error ? data.message : String(data),
      });
      break;
    case "close":
      wsLogger.info("WebSocket Closed", {
        url,
        code: data?.code,
        reason: data?.reason,
      });
      break;
    default:
      wsLogger.debug(`WebSocket ${event}`, { url, data });
  }
}

// Helper to try parsing JSON without throwing
function tryParseJson(str: string): any {
  try {
    return JSON.parse(str);
  } catch (error) {
    utilsLogger.debug("Failed to parse JSON string", { error });
    return str;
  }
}

// Create a WebSocket wrapper with logging
export class LoggedWebSocket {
  private ws: WebSocket;
  private wsUrl: string;

  constructor(url: string, protocols?: string | string[]) {
    this.wsUrl = url;
    this.ws = new WebSocket(url, protocols);

    // Add event listeners for logging
    this.ws.addEventListener("open", this.onOpen.bind(this));
    this.ws.addEventListener("message", this.onMessage.bind(this));
    this.ws.addEventListener("error", this.onError.bind(this));
    this.ws.addEventListener("close", this.onClose.bind(this));
  }

  // Event handlers
  private onOpen(event: Event): void {
    logWebSocketEvent(this.wsUrl, "open");

    // Dispatch event to any other listeners
    if (this.ws.onopen) {
      this.ws.onopen(event);
    }
  }

  private onMessage(event: MessageEvent): void {
    logWebSocketEvent(this.wsUrl, "message", event.data);

    // Dispatch event to any other listeners
    if (this.ws.onmessage) {
      this.ws.onmessage(event);
    }
  }

  private onError(event: Event): void {
    logWebSocketEvent(this.wsUrl, "error", event);

    // Dispatch event to any other listeners
    if (this.ws.onerror) {
      this.ws.onerror(event);
    }
  }

  private onClose(event: CloseEvent): void {
    logWebSocketEvent(this.wsUrl, "close", {
      code: event.code,
      reason: event.reason,
    });

    // Dispatch event to any other listeners
    if (this.ws.onclose) {
      this.ws.onclose(event);
    }
  }

  // Proxy all WebSocket properties
  get binaryType(): BinaryType {
    return this.ws.binaryType;
  }

  set binaryType(value: BinaryType) {
    this.ws.binaryType = value;
  }

  get bufferedAmount(): number {
    return this.ws.bufferedAmount;
  }

  get extensions(): string {
    return this.ws.extensions;
  }

  get protocol(): string {
    return this.ws.protocol;
  }

  get readyState(): number {
    return this.ws.readyState;
  }

  get urlProperty(): string {
    return this.ws.url;
  }

  set onopen(listener: ((event: Event) => void) | null) {
    // We want our logging listener to always be called first
    this.ws.onopen = listener;
  }

  set onmessage(listener: ((event: MessageEvent) => void) | null) {
    this.ws.onmessage = listener;
  }

  set onerror(listener: ((event: Event) => void) | null) {
    this.ws.onerror = listener;
  }

  set onclose(listener: ((event: CloseEvent) => void) | null) {
    this.ws.onclose = listener;
  }

  // Methods
  close(code?: number, reason?: string): void {
    this.ws.close(code, reason);
  }

  send(data: string | ArrayBufferLike | Blob | ArrayBufferView): void {
    logWebSocketEvent(this.wsUrl, "send", data);
    this.ws.send(data);
  }

  addEventListener<K extends keyof WebSocketEventMap>(
    type: K,
    listener: (event: WebSocketEventMap[K]) => void,
    options?: boolean | AddEventListenerOptions
  ): void {
    this.ws.addEventListener(type, listener, options);
  }

  removeEventListener<K extends keyof WebSocketEventMap>(
    type: K,
    listener: (event: WebSocketEventMap[K]) => void,
    options?: boolean | EventListenerOptions
  ): void {
    this.ws.removeEventListener(type, listener, options);
  }

  dispatchEvent(event: Event): boolean {
    return this.ws.dispatchEvent(event);
  }
}
