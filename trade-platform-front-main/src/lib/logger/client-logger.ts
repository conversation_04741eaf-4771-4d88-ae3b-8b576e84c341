"use client";

import { Logger, LogLevel, LogMetadata } from "./config";

/**
 * Simple console-based logger for browser use
 * Avoids compatibility issues with <PERSON><PERSON> in the browser
 */
class BrowserLogger {
  name: string;
  level: LogLevel;
  disabled: boolean = false;

  constructor(options?: { name?: string; level?: LogLevel; disabled?: boolean }) {
    this.name = options?.name || "client";
    this.level =
      options?.level ||
      (typeof process !== "undefined" && process.env && process.env.NODE_ENV === "production"
        ? "error"
        : "debug");
    this.disabled = options?.disabled || false;
  }

  log(level: LogLevel, message: string, metadata?: Record<string, unknown>) {
    if (this.disabled) return;

    // Check for silent flag - if true, don't log to console
    if (metadata && metadata.silent === true) {
      return;
    }

    // Check for serverOnly flag - if true, only log in development
    if (metadata && metadata.serverOnly === true && process.env.NODE_ENV === "production") {
      return;
    }

    const logLevels: Record<LogLevel, number> = {
      fatal: 60,
      error: 50,
      warn: 40,
      info: 30,
      debug: 20,
      trace: 10,
    };

    const currentLevelValue = logLevels[this.level] || 30;
    const msgLevelValue = logLevels[level] || 30;

    // Skip if message level is less important than logger level
    if (msgLevelValue < currentLevelValue) return;

    const prefix = `[${this.name}] [${level.toUpperCase()}]`;

    // Safe handling of metadata - checks if it's an empty object or defined
    const hasMetadata = metadata && Object.keys(metadata).length > 0;

    // Make a safe version of the metadata for console
    const safeMeta = hasMetadata ? this.safeConsoleObject(metadata) : null;

    try {
      // In production, suppress error and fatal logs to avoid showing technical errors to users
      if (process.env.NODE_ENV === "production" && (level === "error" || level === "fatal")) {
        return;
      }

      switch (level) {
        case "fatal":
        case "error":
          if (hasMetadata) {
            // console.error(prefix, message, safeMeta);
          } else {
            // console.error(prefix, message);
          }
          break;
        case "warn":
          if (hasMetadata) {
            // console.warn(prefix, message, safeMeta);
          } else {
            // console.warn(prefix, message);
          }
          break;
        case "info":
          // if (hasMetadata) {

          // } else {

          // }
          break;
        case "debug":
        case "trace":
          // if (hasMetadata) {

          // } else {

          // }
          break;
      }
    } catch (e) {
      // Fallback if console method fails with metadata
      // console.error("[Logger Internal Error]", e);
      try {
        // console.error(prefix, message, hasMetadata ? "(metadata available but caused error)" : "");
      } catch {
        // Last resort
        // console.error(prefix, message);
      }
    }
  }

  // Create a safe object for console output
  private safeConsoleObject(obj: Record<string, unknown>): Record<string, unknown> {
    try {
      // For basic objects, we can just return them
      if (!obj || typeof obj !== "object") return { value: String(obj) };

      // Create a new object with safe values
      const result: Record<string, unknown> = {};

      // Process each property
      Object.keys(obj).forEach((key) => {
        const value = obj[key];
        if (value === null || value === undefined) {
          result[key] = String(value);
        } else if (typeof value === "object") {
          // Handle objects safely - avoid circular references
          try {
            // For Events or DOM objects that can't be stringified
            if (value instanceof Event || value.toString === Object.prototype.toString) {
              result[key] = value.constructor ? value.constructor.name : "Object";
            } else {
              result[key] = String(value);
            }
          } catch (err) {
            result[key] = "[Complex Object]";
          }
        } else {
          // Primitives should be safe
          result[key] = value;
        }
      });

      return result;
    } catch (error) {
      // If anything fails, return a simple object
      return { error: "Error creating console-safe object" };
    }
  }

  fatal(message: string, metadata?: Record<string, any>) {
    this.log("fatal", message, metadata);
  }

  error(message: string, metadata?: Record<string, any>) {
    this.log("error", message, metadata);
  }

  warn(message: string, metadata?: Record<string, any>) {
    this.log("warn", message, metadata);
  }

  info(message: string, metadata?: Record<string, any>) {
    this.log("info", message, metadata);
  }

  debug(message: string, metadata?: Record<string, any>) {
    this.log("debug", message, metadata);
  }

  trace(message: string, metadata?: Record<string, any>) {
    this.log("trace", message, metadata);
  }

  child(bindings: Record<string, any>): BrowserLogger {
    return new BrowserLogger({
      name: bindings.name ? `${this.name}:${bindings.name}` : this.name,
      level: this.level,
      disabled: this.disabled,
    });
  }
}

// Singleton instance
let browserLoggerInstance: BrowserLogger | null = null;

/**
 * Get or create a browser logger instance
 */
export const getClientLogger = (options?: {
  name?: string;
  level?: LogLevel;
  customOptions?: Record<string, any>;
}): BrowserLogger => {
  if (!browserLoggerInstance) {
    browserLoggerInstance = new BrowserLogger({
      name: options?.name || "client",
      level: options?.level,
    });
  }

  return browserLoggerInstance;
};

/**
 * Client logger class that implements our Logger interface
 * Uses the simpler BrowserLogger implementation to avoid Pino issues in browser
 */
export class ClientLogger implements Logger {
  private logger: BrowserLogger;
  private level: LogLevel;

  constructor(options?: { name?: string; level?: LogLevel; customOptions?: Record<string, any> }) {
    this.level =
      options?.level ||
      (typeof process !== "undefined" && process.env && process.env.NODE_ENV === "production"
        ? "error"
        : "debug");
    this.logger = getClientLogger(options);
  }

  getLevel(): LogLevel {
    return this.level;
  }

  fatal(message: string, metadata?: LogMetadata): void {
    try {
      this.logger.fatal(message, metadata);
    } catch (e) {
      // console.error("[Logger Error]", e);
      // console.error("[FATAL]", message, metadata || "");
    }
  }

  error(message: string, metadata?: LogMetadata): void {
    try {
      // Safer error logging with better metadata handling
      if (!metadata || Object.keys(metadata).length === 0) {
        this.logger.error(message);
      } else {
        try {
          // Convert metadata to safe format before logging
          const safeMetadata = this.makeSafeMetadata(metadata);
          this.logger.error(message, safeMetadata);
        } catch (innerError) {
          // If error with metadata fails, try without metadata
          // console.error("[Logger Error]", innerError);
          this.logger.error(message);
        }
      }
    } catch (e) {
      // Complete fallback when logger itself fails
      // console.error("[Logger Error]", e);
      try {
        // console.error("[ERROR]", message, metadata ? JSON.stringify(metadata) : "");
      } catch (jsonError) {
        // If JSON.stringify fails
        // console.error("[ERROR]", message, "(metadata logging failed)");
      }
    }
  }

  // Helper method to create safe metadata object
  private makeSafeMetadata(metadata: LogMetadata): Record<string, string | number | boolean> {
    try {
      const safeObj: Record<string, string | number | boolean> = {};

      // Only include primitive values that can be safely logged
      Object.keys(metadata).forEach((key) => {
        const value = metadata[key];
        const type = typeof value;

        if (type === "string" || type === "number" || type === "boolean") {
          safeObj[key] = value;
        } else if (value === null) {
          safeObj[key] = "null";
        } else if (value === undefined) {
          safeObj[key] = "undefined";
        } else if (type === "object") {
          // Try to safely stringify objects
          try {
            safeObj[key] = JSON.stringify(value);
          } catch (e) {
            safeObj[key] = "[Object conversion failed]";
          }
        } else {
          safeObj[key] = `[${type}]`;
        }
      });

      return safeObj;
    } catch (e) {
      // Return a simple error object if anything fails
      return { error: "Error creating safe metadata" };
    }
  }

  warn(message: string, metadata?: LogMetadata): void {
    try {
      this.logger.warn(message, metadata);
    } catch (e) {
      // console.error("[Logger Error]", e);
      // console.warn("[WARN]", message, metadata || "");
    }
  }

  info(message: string, metadata?: LogMetadata): void {
    try {
      this.logger.info(message, metadata);
    } catch (e) {
      // console.error("[Logger Error]", e);
      // console.info("[INFO]", message, metadata || "");
    }
  }

  debug(message: string, metadata?: LogMetadata): void {
    try {
      this.logger.debug(message, metadata);
    } catch (e) {
      // console.error("[Logger Error]", e);
      // console.debug("[DEBUG]", message, metadata || "");
    }
  }

  trace(message: string, metadata?: LogMetadata): void {
    try {
      this.logger.trace(message, metadata);
    } catch (e) {
      // console.error("[Logger Error]", e);
      // console.debug("[TRACE]", message, metadata || "");
    }
  }

  child(bindings: Record<string, any>): Logger {
    try {
      const childLogger = this.logger.child(bindings);

      // Return a new ClientLogger using the child
      return new ClientLogger({
        name: bindings.name ? `${this.logger.name}:${bindings.name}` : this.logger.name,
        level: this.level,
      });
    } catch (error) {
      // If child() fails, create a new ClientLogger with the same options
      // console.warn("Failed to create child logger, creating new logger instance:", error);
      return new ClientLogger({
        name: `${this.logger.name || "client"}:${bindings.name || "child"}`,
        level: this.level,
      });
    }
  }
}
