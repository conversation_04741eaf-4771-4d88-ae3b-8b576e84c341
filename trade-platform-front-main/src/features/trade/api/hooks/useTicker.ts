"use client";

import { useQuery } from "@tanstack/react-query";

import { useTickerPriceUpdates } from "@/entities/price";
import { useWebSocketQuery } from "@/shared/api/websocket/useWebSocketQuery";
import { createModuleLogger } from "@/shared/lib/logger";

import { marketKeys, marketQueries } from "../query-keys";
import { TickerData, marketWebSocketClient } from "../websocket/market-websocket";

const logger = createModuleLogger("use-ticker");

/**
 * Hook for accessing and updating ticker data via REST and WebSocket
 *
 * @param symbol Trading pair symbol (e.g., "BTC/USDT")
 * @returns Query result with ticker data
 */
export function useTicker(symbol: string): {
  data: TickerData | undefined;
  isLoading: boolean;
  error: Error | null;
  [key: string]: unknown;
} {
  // Get unified price update handlers
  const { handleTickerUpdate, handleMarketConnection } = useTickerPriceUpdates();

  // Get initial data from REST API
  const query = useQuery({
    ...marketQueries.ticker(symbol),
    staleTime: Infinity, // Never mark as stale since we'll update via WebSocket
  });

  // Set up WebSocket connection and updates
  useWebSocketQuery<TickerData, unknown>({
    wsClient: marketWebSocketClient,
    queryKey: marketKeys.withParams("ticker", symbol) as unknown as unknown[],
    processMessage: (message: unknown) => {
      const typedMessage = message as any;
      // Only process messages for the symbol we're interested in
      if (typedMessage?.type === "ticker" && typedMessage?.data?.symbol === symbol) {
        const tickerData = typedMessage.data;

        // Update unified price store
        if (tickerData && tickerData.price) {
          handleTickerUpdate({
            symbol: tickerData.symbol,
            c: tickerData.price,
            pc: tickerData.priceChange || 0,
            h: tickerData.high || tickerData.price,
            l: tickerData.low || tickerData.price,
            v: tickerData.volume || 0,
          });
        }

        return tickerData;
      }
      return undefined;
    },
    eventName: "ticker",
    onConnected: () => {
      // Subscribe to ticker updates on connection
      marketWebSocketClient.subscribeToTicker(symbol);
      handleMarketConnection(true);
      logger.info("Subscribed to ticker updates", { symbol });
    },
    initialData: query.data as TickerData | undefined,
  });

  // Ensure proper return type casting
  const result = query as unknown as {
    data: TickerData | undefined;
    isLoading: boolean;
    error: Error | null;
    [key: string]: unknown;
  };

  return result;
}
