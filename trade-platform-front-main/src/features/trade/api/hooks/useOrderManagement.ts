"use client";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { useAuthStore } from "@/entities/auth";
import { Position } from "@/entities/trade/types";
import { createModuleLogger } from "@/lib/logger";
import { BALANCE_QUERY_KEY } from "@/shared/api/hooks/useBalance";

import { cancelPendingOrder, updateOrderEntryPrice } from "../position-service";
import { positionKeys } from "../query-keys";

const logger = createModuleLogger("order-management");

interface UpdateOrderEntryPriceParams {
  orderId: number;
  newEntryPrice: number;
}

interface CancelOrderParams {
  orderId: number;
}

/**
 * Hook for managing limit orders (edit entry price, cancel orders)
 * Provides optimistic updates and proper error handling
 */
export function useOrderManagement() {
  const queryClient = useQueryClient();
  const userId = useAuthStore((state) => state.auth.user?.id);

  /**
   * Update entry price of a pending limit order
   */
  const updateEntryPriceMutation = useMutation({
    mutationFn: async ({ orderId, newEntryPrice }: UpdateOrderEntryPriceParams) => {
      if (!userId) throw new Error("User not authenticated");
      
      return updateOrderEntryPrice({
        user_id: userId,
        order_id: orderId,
        entry_price: newEntryPrice,
      });
    },
    onMutate: async ({ orderId, newEntryPrice }) => {
      if (!userId) return;

      // Cancel any outgoing refetches
      const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
      await queryClient.cancelQueries({ queryKey: pendingOrdersKey });

      // Get snapshot of previous value
      const previousOrders = queryClient.getQueryData<Position[]>(pendingOrdersKey);

      // Optimistically update the order
      queryClient.setQueryData<Position[]>(pendingOrdersKey, (old = []) =>
        old.map((order) =>
          order.id === orderId
            ? { ...order, entry_price: newEntryPrice }
            : order
        )
      );

      logger.info("Optimistically updated entry price", { orderId, newEntryPrice });

      return { previousOrders, orderId, newEntryPrice };
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousOrders && userId) {
        const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
        queryClient.setQueryData(pendingOrdersKey, context.previousOrders);
        logger.error("Rolled back entry price update", { 
          orderId: context.orderId, 
          error: error.message 
        });
      }

      toast.error(`Failed to update entry price: ${error.message}`);
    },
    onSuccess: (updatedOrder, { orderId, newEntryPrice }) => {
      logger.info("Entry price updated successfully", { 
        orderId, 
        newEntryPrice,
        actualPrice: updatedOrder.entry_price 
      });

      toast.success(`Entry price updated to $${newEntryPrice.toLocaleString()}`);

      // Ensure the updated order is in the cache
      if (userId) {
        const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
        queryClient.setQueryData<Position[]>(pendingOrdersKey, (old = []) =>
          old.map((order) =>
            order.id === orderId ? updatedOrder : order
          )
        );
      }
    },
    onSettled: () => {
      // Always refetch to ensure consistency
      if (userId) {
        const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
        queryClient.invalidateQueries({ queryKey: pendingOrdersKey });
      }
    },
  });

  /**
   * Cancel a pending order
   */
  const cancelOrderMutation = useMutation({
    mutationFn: async ({ orderId }: CancelOrderParams) => {
      if (!userId) throw new Error("User not authenticated");
      
      return cancelPendingOrder({
        user_id: userId,
        order_id: orderId,
      });
    },
    onMutate: async ({ orderId }) => {
      if (!userId) return;

      // Cancel any outgoing refetches
      const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
      await queryClient.cancelQueries({ queryKey: pendingOrdersKey });

      // Get snapshot of previous value
      const previousOrders = queryClient.getQueryData<Position[]>(pendingOrdersKey);

      // Find the order being cancelled for the toast message
      const orderToCancel = previousOrders?.find(order => order.id === orderId);

      // Optimistically remove the order
      queryClient.setQueryData<Position[]>(pendingOrdersKey, (old = []) =>
        old.filter((order) => order.id !== orderId)
      );

      logger.info("Optimistically removed cancelled order", { orderId });

      return { previousOrders, orderId, orderToCancel };
    },
    onError: (error, variables, context) => {
      // Rollback optimistic update
      if (context?.previousOrders && userId) {
        const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
        queryClient.setQueryData(pendingOrdersKey, context.previousOrders);
        logger.error("Rolled back order cancellation", { 
          orderId: context.orderId, 
          error: error.message 
        });
      }

      toast.error(`Failed to cancel order: ${error.message}`);
    },
    onSuccess: (result, { orderId }, context) => {
      logger.info("Order cancelled successfully", { 
        orderId, 
        result 
      });

      const orderInfo = context?.orderToCancel;
      const pairInfo = orderInfo ? ` (${orderInfo.trading_pair})` : '';
      toast.success(`Order cancelled successfully${pairInfo}`);

      // Invalidate balance since margin is returned
      queryClient.invalidateQueries({ queryKey: [BALANCE_QUERY_KEY] });
    },
    onSettled: () => {
      // Always refetch to ensure consistency
      if (userId) {
        const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
        queryClient.invalidateQueries({ queryKey: pendingOrdersKey });
      }
    },
  });

  /**
   * Batch cancel multiple orders
   */
  const batchCancelOrdersMutation = useMutation({
    mutationFn: async (orderIds: number[]) => {
      if (!userId) throw new Error("User not authenticated");
      
      const results = await Promise.allSettled(
        orderIds.map(orderId =>
          cancelPendingOrder({
            user_id: userId,
            order_id: orderId,
          })
        )
      );

      // Check for any failures
      const failures = results
        .map((result, index) => ({ result, orderId: orderIds[index] }))
        .filter(({ result }) => result.status === 'rejected');

      if (failures.length > 0) {
        const failedOrderIds = failures.map(({ orderId }) => orderId);
        throw new Error(`Failed to cancel orders: ${failedOrderIds.join(', ')}`);
      }

      return results.map(result => 
        result.status === 'fulfilled' ? result.value : null
      ).filter(Boolean);
    },
    onMutate: async (orderIds) => {
      if (!userId) return;

      const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
      await queryClient.cancelQueries({ queryKey: pendingOrdersKey });

      const previousOrders = queryClient.getQueryData<Position[]>(pendingOrdersKey);

      // Optimistically remove all orders
      queryClient.setQueryData<Position[]>(pendingOrdersKey, (old = []) =>
        old.filter((order) => !orderIds.includes(order.id))
      );

      return { previousOrders, orderIds };
    },
    onError: (error, variables, context) => {
      if (context?.previousOrders && userId) {
        const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
        queryClient.setQueryData(pendingOrdersKey, context.previousOrders);
      }

      toast.error(`Batch cancellation failed: ${error.message}`);
    },
    onSuccess: (results, orderIds) => {
      logger.info("Batch order cancellation completed", { 
        cancelled: orderIds.length,
        results: results.length 
      });

      toast.success(`${orderIds.length} orders cancelled successfully`);
      
      // Invalidate balance since margin is returned
      queryClient.invalidateQueries({ queryKey: [BALANCE_QUERY_KEY] });
    },
    onSettled: () => {
      if (userId) {
        const pendingOrdersKey = positionKeys.withParams(userId, "PENDING");
        queryClient.invalidateQueries({ queryKey: pendingOrdersKey });
      }
    },
  });

  return {
    /**
     * Update entry price of a pending limit order
     */
    updateEntryPrice: updateEntryPriceMutation.mutate,
    updateEntryPriceAsync: updateEntryPriceMutation.mutateAsync,
    isUpdatingEntryPrice: updateEntryPriceMutation.isPending,

    /**
     * Cancel a single pending order
     */
    cancelOrder: cancelOrderMutation.mutate,
    cancelOrderAsync: cancelOrderMutation.mutateAsync,
    isCancellingOrder: cancelOrderMutation.isPending,

    /**
     * Cancel multiple orders at once
     */
    batchCancelOrders: batchCancelOrdersMutation.mutate,
    batchCancelOrdersAsync: batchCancelOrdersMutation.mutateAsync,
    isBatchCancelling: batchCancelOrdersMutation.isPending,

    /**
     * Combined loading state
     */
    isLoading: updateEntryPriceMutation.isPending || 
               cancelOrderMutation.isPending || 
               batchCancelOrdersMutation.isPending,
  };
}

/**
 * Hook for quick order actions with confirmation
 */
export function useOrderActions() {
  const orderManagement = useOrderManagement();

  const updateEntryPriceWithConfirmation = async (
    orderId: number, 
    currentPrice: number, 
    newPrice: number
  ) => {
    const priceChange = ((newPrice - currentPrice) / currentPrice * 100).toFixed(2);
    const changeDirection = newPrice > currentPrice ? 'increase' : 'decrease';
    
    if (Math.abs(Number(priceChange)) > 10) {
      const confirmed = window.confirm(
        `You're about to ${changeDirection} the entry price by ${Math.abs(Number(priceChange))}%. Are you sure?`
      );
      
      if (!confirmed) return;
    }

    return orderManagement.updateEntryPriceAsync({ orderId, newEntryPrice: newPrice });
  };

  const cancelOrderWithConfirmation = async (
    orderId: number, 
    tradingPair: string,
    margin: number
  ) => {
    const confirmed = window.confirm(
      `Cancel order for ${tradingPair}? Your margin of $${margin.toLocaleString()} will be returned.`
    );
    
    if (!confirmed) return;

    return orderManagement.cancelOrderAsync({ orderId });
  };

  const batchCancelWithConfirmation = async (
    orderIds: number[],
    totalMargin: number
  ) => {
    const confirmed = window.confirm(
      `Cancel ${orderIds.length} orders? Total margin of $${totalMargin.toLocaleString()} will be returned.`
    );
    
    if (!confirmed) return;

    return orderManagement.batchCancelOrdersAsync(orderIds);
  };

  return {
    ...orderManagement,
    updateEntryPriceWithConfirmation,
    cancelOrderWithConfirmation,
    batchCancelWithConfirmation,
  };
}