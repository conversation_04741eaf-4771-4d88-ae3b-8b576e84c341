"use client";

import { createModuleLogger } from "@/lib/logger";
import { BaseWebSocketClient } from "@/shared/api/websocket-client";
import { TRADING_CHART_WS_URL } from "@/shared/config/service-urls";

const logger = createModuleLogger("chart-websocket");

export interface ChartTick {
  volume: number;
  timestamp: number;
  open: number;
  high: number;
  low: number;
  close: number;
  tradesCount: number;
  isFinal: boolean;
}

export interface ChartStats {
  indexPrice: string;
  oraclePrice: string;
  change24h: string;
  changePercent: string;
  volume24h: string;
  high24h: string;
  low24h: string;
  volBase24h: string;
  volUsd24h: string;
  isPositiveChange: boolean;
}

export interface ChartHistoryData {
  time_period_start: string;
  time_period_end: string;
  time_open: string;
  time_close: string;
  price_open: number;
  price_high: number;
  price_low: number;
  price_close: number;
  volume_traded: number;
  trades_count: number;
}

interface ChartWebSocketMessage {
  type: string;
  symbol?: string;
  timeframe?: string;
  data?: ChartTick | ChartStats | ChartHistoryData[];
  message?: string;
}

/**
 * WebSocket client for real-time chart data
 * Handles price ticks, stats updates, and historical data
 */
export class ChartWebSocketClient extends BaseWebSocketClient {
  private activeSubscriptions = new Map<string, { symbol: string; timeframe: string }>();
  private subscriptionPromises = new Map<string, Promise<void>>();

  constructor() {
    const wsUrl = TRADING_CHART_WS_URL;
    logger.info("ChartWebSocketClient initializing", {
      wsUrl,
      urlLength: wsUrl?.length || 0,
      urlType: typeof wsUrl,
      environment: typeof window !== "undefined" ? "browser" : "server",
    });
    super(wsUrl);
    this.maxReconnectAttempts = 8; // Increased for better reliability
    this.reconnectTimeout = 3000; // Faster reconnect
  }

  protected handleMessage(event: MessageEvent): void {
    try {
      const data: ChartWebSocketMessage = JSON.parse(event.data);

      switch (data.type) {
        case "kline":
          this.notifyListeners("kline", {
            symbol: data.symbol,
            timeframe: data.timeframe,
            tick: data.data as ChartTick,
          });
          break;

        case "stats":
          this.notifyListeners("stats", {
            symbol: data.symbol,
            stats: data.data as ChartStats,
          });
          break;

        case "history":
          this.notifyListeners("history", {
            symbol: data.symbol,
            timeframe: data.timeframe,
            data: data.data as ChartHistoryData[],
          });
          break;

        case "error":
          logger.warn("Server error", {
            message: data.message,
            symbol: data.symbol,
            timeframe: data.timeframe,
          });
          this.notifyListeners("error", {
            message: data.message,
            symbol: data.symbol,
            timeframe: data.timeframe,
          });
          break;

        case "ping":
          // Respond to ping to keep connection alive
          if (this.ws?.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify({ type: "pong" }));
          }
          break;

        case "subscription_confirmed":
          // Handle subscription confirmation
          const key = this.getSubscriptionKey(data.symbol!, data.timeframe!);
          logger.info("Subscription confirmed", { symbol: data.symbol, timeframe: data.timeframe });
          break;

        default:
          logger.debug("Unknown message type", { type: data.type });
      }
    } catch (error) {
      logger.error("Error parsing WebSocket message", { error });
    }
  }

  /**
   * Subscribe to real-time chart updates
   */
  public async subscribe(
    symbol: string,
    timeframe: string,
    fetchHistory: boolean = true
  ): Promise<void> {
    const key = this.getSubscriptionKey(symbol, timeframe);

    // Check if already subscribing
    if (this.subscriptionPromises.has(key)) {
      return this.subscriptionPromises.get(key);
    }

    // Check if already subscribed
    if (this.activeSubscriptions.has(key)) {
      logger.debug("Already subscribed", { symbol, timeframe });
      return Promise.resolve();
    }

    logger.info("Starting subscription", { symbol, timeframe, fetchHistory, key });

    // Create subscription promise
    const subscriptionPromise = this.connect()
      .then(() => {
        logger.debug("Connection established, sending subscription", {
          symbol,
          timeframe,
          wsState: this.ws?.readyState,
          wsOpen: this.ws?.readyState === WebSocket.OPEN,
        });

        if (this.ws?.readyState === WebSocket.OPEN) {
          const subscribeMessage = {
            type: "subscribe",
            symbol,
            timeframe,
            fetchHistory,
          };

          logger.debug("Sending subscription message", subscribeMessage);
          this.ws.send(JSON.stringify(subscribeMessage));

          this.activeSubscriptions.set(key, { symbol, timeframe });
          logger.info("Subscribed to chart updates", { symbol, timeframe });
        }
      })
      .catch((error) => {
        logger.error("Failed to subscribe", { error, symbol, timeframe });
        throw error;
      })
      .finally(() => {
        this.subscriptionPromises.delete(key);
      });

    this.subscriptionPromises.set(key, subscriptionPromise);
    return subscriptionPromise;
  }

  /**
   * Unsubscribe from chart updates
   */
  public unsubscribe(symbol: string, timeframe: string): void {
    const key = this.getSubscriptionKey(symbol, timeframe);

    if (!this.activeSubscriptions.has(key)) {
      return;
    }

    if (this.ws?.readyState === WebSocket.OPEN) {
      this.ws.send(
        JSON.stringify({
          type: "unsubscribe",
          symbol,
          timeframe,
        })
      );
    }

    this.activeSubscriptions.delete(key);
    logger.info("Unsubscribed from chart updates", { symbol, timeframe });
  }

  /**
   * Fetch pair statistics
   */
  public async fetchPairStats(symbol: string): Promise<ChartStats> {
    await this.connect();

    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error("Timeout fetching pair stats"));
      }, 10000);

      const handleStats = (data: any) => {
        if (data.symbol === symbol && data.stats) {
          clearTimeout(timeout);
          this.removeEventListener("stats", handleStats);
          resolve(data.stats);
        }
      };

      this.addEventListener("stats", handleStats);

      if (this.ws && this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(
          JSON.stringify({
            type: "fetch_pair_stats",
            symbol,
          })
        );
      } else {
        reject(new Error("WebSocket not connected"));
      }
    });
  }

  /**
   * Override connect to resubscribe on reconnection
   */
  public async connect(): Promise<void> {
    await super.connect();

    // Resubscribe to all active subscriptions
    for (const [key, subscription] of this.activeSubscriptions) {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.send(
          JSON.stringify({
            type: "subscribe",
            symbol: subscription.symbol,
            timeframe: subscription.timeframe,
            fetchHistory: false, // Don't refetch history on reconnect
          })
        );
      }
    }
  }

  /**
   * Override disconnect to clean up subscriptions
   */
  public disconnect(): void {
    this.activeSubscriptions.clear();
    this.subscriptionPromises.clear();
    super.disconnect();
  }

  private getSubscriptionKey(symbol: string, timeframe: string): string {
    return `${symbol}_${timeframe}`;
  }
}

// Singleton instance
let chartWebSocketInstance: ChartWebSocketClient | null = null;

/**
 * Get or create ChartWebSocketClient instance
 */
export function getChartWebSocketClient(): ChartWebSocketClient {
  if (!chartWebSocketInstance) {
    logger.info("Creating new chart WebSocket client", {
      url: TRADING_CHART_WS_URL,
      urlDefined: !!TRADING_CHART_WS_URL,
    });
    chartWebSocketInstance = new ChartWebSocketClient();
  }
  return chartWebSocketInstance;
}
