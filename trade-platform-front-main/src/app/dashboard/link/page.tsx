"use client";

import {
  <PERSON><PERSON>,
  Link2,
  <PERSON>r<PERSON><PERSON>,
  Share,
  Users,
  ExternalLink,
  Download,
  Check,
  X,
  Eye,
  Edit3,
  Save,
} from "lucide-react";
import React, { useState, useRef } from "react";
import { toast } from "sonner";

import { <PERSON>ert, AlertDescription } from "@/components/ui/alert";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuthStore } from "@/entities/auth";
import { cn } from "@/lib/utils";
import { useProfileData, useUpdateProfile } from "@/shared/api/hooks/useProfile";

// Generate referral link using the user's referral code
const generateReferralLink = (referralCode?: string) => {
  if (!referralCode) return "https://app.bitmei.com/auth?ref=your-code";
  return `https://app.bitmei.com/auth?ref=${referralCode}`;
};

// Copy text to clipboard
const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text);
    return true;
  } catch (error) {
    console.error("Failed to copy to clipboard:", error);
    return false;
  }
};

// Generate QR code with BitMei logo
const generateQRCodeWithLogo = (referralLink: string, size: number = 300): string => {
  // Use QR Server API with better styling and logo
  const logoUrl = encodeURIComponent("/LogoWhite.svg");
  return `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(referralLink)}&format=png&margin=20&color=1E293B&bgcolor=FFFFFF&qzone=2`;
};

// QR Code Modal Component
function QRCodeModal({
  isOpen,
  onClose,
  referralLink,
  referralCode,
}: {
  isOpen: boolean;
  onClose: () => void;
  referralLink: string;
  referralCode?: string;
}) {
  const [isDownloading, setIsDownloading] = useState(false);
  const [copySuccess, setCopySuccess] = useState(false);

  const handleDownload = async () => {
    setIsDownloading(true);
    try {
      const qrCodeUrl = generateQRCodeWithLogo(referralLink, 400);
      const response = await fetch(qrCodeUrl);
      const blob = await response.blob();

      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.download = `bitmei-referral-qr-${referralCode || "code"}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Error downloading QR code:", error);
    } finally {
      setIsDownloading(false);
    }
  };

  const handleCopyLink = async () => {
    const success = await copyToClipboard(referralLink);
    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5 text-[#475569] dark:text-gray-400" />
            QR Code for Your Referral Link
          </DialogTitle>
          <DialogDescription>
            Share this QR code for easy mobile access to your referral link
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col items-center space-y-4 py-4">
          {/* QR Code Display */}
          <div className="relative bg-white p-6 rounded-xl border-2 border-gray-200 shadow-lg">
            <img
              src={generateQRCodeWithLogo(referralLink, 280)}
              alt="Referral QR Code"
              className="w-70 h-70 rounded-lg"
              loading="lazy"
            />
            {/* Logo overlay with better positioning and styling */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="bg-white rounded-xl p-3 shadow-lg border border-gray-100">
                <img src="/LogoDark.svg" alt="BitMei Logo" className="w-10 h-10" />
              </div>
            </div>
            {/* Brand text below logo */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2">
              <div className="bg-white/90 backdrop-blur-sm rounded-full px-3 py-1 shadow-md">
                <span className="text-xs font-semibold text-gray-800">BitMei</span>
              </div>
            </div>
          </div>

          {/* Referral Info */}
          <div className="text-center space-y-2">
            <p className="text-sm text-[#475569] dark:text-gray-400">
              Referral Code:{" "}
              <span className="font-mono font-bold text-[#1E293B] dark:text-white">
                {referralCode}
              </span>
            </p>
            <p className="text-xs text-gray-500 dark:text-gray-500 max-w-sm">
              Scan this QR code with any smartphone camera to access your referral link
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 w-full">
            <Button
              variant="outline"
              onClick={handleCopyLink}
              className="flex-1 border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800"
            >
              {copySuccess ? (
                <>
                  <Check className="h-4 w-4 mr-2 text-green-600" />
                  Copied!
                </>
              ) : (
                <>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy Link
                </>
              )}
            </Button>

            <Button
              onClick={handleDownload}
              disabled={isDownloading}
              className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            >
              {isDownloading ? (
                <>
                  <div className="h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  Downloading...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Download PNG
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Page header component
function PageHeader() {
  return (
    <div className="mb-6">
      <h1 className="text-[#1E293B] dark:text-white text-[28px] font-bold leading-[1.2] tracking-[-0.2px] mb-2">
        Your Referral Link
      </h1>
      <p className="text-[#475569] dark:text-gray-300 text-[16px] font-normal leading-[160%]">
        Share your referral link and earn commissions when others trade
      </p>
    </div>
  );
}

// Referral link display card
function ReferralLinkCard({
  referralCode,
  onUpdateCode,
}: {
  referralCode?: string;
  onUpdateCode?: (newCode: string) => void;
}) {
  const [copySuccess, setCopySuccess] = useState(false);
  const [isEditingCode, setIsEditingCode] = useState(false);
  const [editCodeValue, setEditCodeValue] = useState(referralCode || "");
  const inputRef = useRef<HTMLInputElement>(null);

  const referralLink = generateReferralLink(referralCode);

  const handleCopyLink = async () => {
    const success = await copyToClipboard(referralLink);
    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    }
  };

  const handleSelectAll = () => {
    if (inputRef.current) {
      inputRef.current.select();
    }
  };

  const handleStartEdit = () => {
    setEditCodeValue(referralCode || "");
    setIsEditingCode(true);
  };

  const handleSaveCode = () => {
    if (!editCodeValue.trim()) {
      toast.error("Referral code cannot be empty");
      return;
    }

    // Validate referral code format
    const codeRegex = /^[a-zA-Z0-9_-]{3,20}$/;
    if (!codeRegex.test(editCodeValue.trim())) {
      toast.error(
        "Referral code must be 3-20 characters and can only contain letters, numbers, underscores, and hyphens"
      );
      return;
    }

    if (onUpdateCode) {
      onUpdateCode(editCodeValue.trim());
    }
    setIsEditingCode(false);
  };

  const handleCancelEdit = () => {
    setEditCodeValue(referralCode || "");
    setIsEditingCode(false);
  };

  return (
    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-100 dark:border-gray-700">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Link2 className="h-5 w-5 text-[#475569] dark:text-gray-400" />
          Your Referral Link
        </CardTitle>
        <CardDescription>
          Share this link with friends to earn 20% commission on their trading fees
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="referral-link" className="text-sm font-medium">
            Referral Link
          </Label>
          <div className="relative">
            <Input
              ref={inputRef}
              id="referral-link"
              type="text"
              value={referralLink}
              readOnly
              onClick={handleSelectAll}
              className="pr-10 bg-gray-50 dark:bg-gray-900/50 font-mono text-sm cursor-pointer"
            />
            <button
              type="button"
              onClick={handleCopyLink}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 transition-colors"
              aria-label="Copy referral link"
            >
              {copySuccess ? (
                <Check className="h-4 w-4 text-green-600" />
              ) : (
                <Copy className="h-4 w-4" />
              )}
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <Label className="text-sm font-medium">Referral Code</Label>
          {isEditingCode ? (
            <div className="space-y-3">
              <Input
                value={editCodeValue}
                onChange={(e) => setEditCodeValue(e.target.value)}
                placeholder="Enter new referral code"
                className="font-mono"
                maxLength={20}
                onKeyDown={(e) => {
                  if (e.key === "Enter") handleSaveCode();
                  if (e.key === "Escape") handleCancelEdit();
                }}
              />
              <div className="flex gap-2">
                <Button variant="outline" size="sm" onClick={handleSaveCode} className="flex-1">
                  <Save className="h-4 w-4 mr-2" />
                  Save
                </Button>
                <Button variant="ghost" size="sm" onClick={handleCancelEdit} className="flex-1">
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
              </div>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                3-20 characters, letters, numbers, underscores, and hyphens only
              </p>
            </div>
          ) : (
            <div className="flex items-center gap-2">
              <div className="bg-gray-50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-600 rounded-lg px-4 py-3 flex-1">
                <span className="font-mono text-lg font-bold text-[#1E293B] dark:text-white">
                  {referralCode || "Loading..."}
                </span>
              </div>
              {onUpdateCode && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleStartEdit}
                  className="h-11 border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800"
                  title="Edit referral code"
                >
                  <Edit3 className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        {copySuccess && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-3">
            <p className="text-sm text-green-700 dark:text-green-300 font-medium">
              ✓ Copied to clipboard!
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Quick actions card
function QuickActionsCard({ referralCode }: { referralCode?: string }) {
  const [showQRModal, setShowQRModal] = useState(false);
  const referralLink = generateReferralLink(referralCode);

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: "Join BitMei Trading Platform",
          text: "Start trading crypto with low fees and earn together!",
          url: referralLink,
        });
      } catch (error) {
        console.error("Error sharing:", error);
      }
    } else {
      // Fallback to copy
      await copyToClipboard(referralLink);
    }
  };

  return (
    <>
      <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-100 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Share className="h-5 w-5 text-[#475569] dark:text-gray-400" />
            Quick Actions
          </CardTitle>
          <CardDescription>Easy ways to share your referral link</CardDescription>
        </CardHeader>
        <CardContent className="space-y-3">
          <Button
            variant="default"
            className="w-full justify-start h-12 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white"
            onClick={handleShare}
          >
            <Share className="h-4 w-4 mr-3" />
            Share Link
          </Button>

          <Button
            variant="outline"
            className="w-full justify-start h-12 border-gray-200 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-800"
            onClick={() => setShowQRModal(true)}
          >
            <QrCode className="h-4 w-4 mr-3" />
            Show QR Code
          </Button>
        </CardContent>
      </Card>

      <QRCodeModal
        isOpen={showQRModal}
        onClose={() => setShowQRModal(false)}
        referralLink={referralLink}
        referralCode={referralCode}
      />
    </>
  );
}

// Referral info card
function ReferralInfoCard() {
  return (
    <Card className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border border-gray-100 dark:border-gray-700">
      <CardHeader>
        <CardTitle className="text-[#1E293B] dark:text-white">How It Works</CardTitle>
        <CardDescription className="text-[#475569] dark:text-gray-300">
          Earn money by referring new traders to BitMei
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-4">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
              1
            </div>
            <div>
              <p className="font-semibold text-[#1E293B] dark:text-white mb-1">Share Your Link</p>
              <p className="text-sm text-[#475569] dark:text-gray-300">
                Send your referral link to friends, family, or social media
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-gradient-to-r from-purple-600 to-pink-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
              2
            </div>
            <div>
              <p className="font-semibold text-[#1E293B] dark:text-white mb-1">
                They Sign Up & Trade
              </p>
              <p className="text-sm text-[#475569] dark:text-gray-300">
                When someone uses your link to register and starts trading
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <div className="w-10 h-10 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
              3
            </div>
            <div>
              <p className="font-semibold text-[#1E293B] dark:text-white mb-1">
                Earn 20% Commission
              </p>
              <p className="text-sm text-[#475569] dark:text-gray-300">
                Get 20% of all trading fees from your referred users
              </p>
            </div>
          </div>
        </div>

        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 mt-6 border border-gray-100 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-3">
            <Users className="h-4 w-4 text-[#475569] dark:text-gray-400" />
            <span className="font-semibold text-[#1E293B] dark:text-white">Key Benefits</span>
          </div>
          <ul className="text-sm text-[#475569] dark:text-gray-300 space-y-2">
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-blue-600 rounded-full"></div>
              Lifetime commissions on all referrals
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-purple-600 rounded-full"></div>
              No limit on number of referrals
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-green-600 rounded-full"></div>
              Weekly automatic payouts
            </li>
            <li className="flex items-center gap-2">
              <div className="w-1.5 h-1.5 bg-pink-600 rounded-full"></div>
              Real-time earnings tracking
            </li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

// Loading skeleton
function LoadingSkeleton() {
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <Skeleton className="h-8 w-64" />
        <Skeleton className="h-4 w-96" />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-40" />
            <Skeleton className="h-4 w-64" />
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-4 w-48" />
          </CardHeader>
          <CardContent className="space-y-3">
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
            <Skeleton className="h-12 w-full" />
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function LinkPage() {
  const { profile, isLoading: profileLoading } = useProfileData();
  const updateProfileMutation = useUpdateProfile();

  // Handle referral code update
  const handleUpdateReferralCode = async (newCode: string) => {
    try {
      const updatedProfile = await updateProfileMutation.mutateAsync({
        data: { referralCode: newCode },
      });

      // Show success message
      toast.success("Referral code updated successfully!", {
        description: `Your new referral code is: ${newCode}`,
      });

      // Log for debugging
      console.log("Referral code updated:", {
        oldCode: profile?.referralCode,
        newCode,
        updatedProfile,
      });
    } catch (error) {
      console.error("Failed to update referral code:", error);

      // Handle specific error cases
      if (error instanceof Error) {
        if (error.message.includes("already exists") || error.message.includes("taken")) {
          toast.error("Referral code already taken", {
            description: "Please choose a different referral code.",
          });
        } else if (error.message.includes("invalid") || error.message.includes("format")) {
          toast.error("Invalid referral code format", {
            description: "Use only letters, numbers, underscores, and hyphens (3-20 characters).",
          });
        } else if (
          error.message.includes("not supported") ||
          error.message.includes("referralCode")
        ) {
          toast.error("Feature not available", {
            description: "Referral code editing is not supported by the backend yet.",
          });
        } else {
          toast.error("Update failed", {
            description: error.message || "Failed to update referral code. Please try again.",
          });
        }
      } else {
        toast.error("Update failed", {
          description: "An unexpected error occurred. Please try again.",
        });
      }
    }
  };

  if (profileLoading) {
    return (
      <div className="w-full max-w-[1240px] mx-auto px-4">
        <LoadingSkeleton />
      </div>
    );
  }

  return (
    <div className="w-full max-w-[1240px] mx-auto px-4">
      <PageHeader />

      <div className="space-y-6">
        {/* Main referral link section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <ReferralLinkCard
            referralCode={profile?.referralCode}
            onUpdateCode={handleUpdateReferralCode}
          />
          <QuickActionsCard referralCode={profile?.referralCode} />
        </div>

        {/* How it works section */}
        <ReferralInfoCard />
      </div>
    </div>
  );
}
