"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import {
  Calendar,
  Camera,
  Check,
  ChevronDown,
  User,
  X,
  Shield,
  Globe,
  Clock,
  Hash,
  Link2,
  UserCheck,
  Phone,
  Mail,
  MapPin,
  Fingerprint,
  Activity,
  UserCog,
  Copy,
  Edit3,
  Save,
  Users,
} from "lucide-react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { z } from "zod";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { WorkingCountrySelect, WorkingPhoneInput } from "@/components/ui/working-country-select";
import { cn } from "@/lib/utils";
import { useProfileData, useUploadAvatar, useUpdateProfile } from "@/shared/api/hooks/useProfile";
import { getUserIpAddress } from "@/shared/lib/ip-utils";
import { createModuleLogger } from "@/shared/lib/logger";

const logger = createModuleLogger("profile-editor");

// Placeholder avatar URL constant - empty avatar
const PLACEHOLDER_AVATAR_URL = "";

// Storage keys
const PROFILE_STORAGE_KEY = "bitmei_user_profile";
const PROFILE_IMAGE_KEY = "bitmei_profile_image";

import { UpdateProfileRequest } from "@/shared/api/services/profile-service";

// Validation schema
const profileSchema = z.object({
  username: z
    .string()
    .transform((val) => val?.trim() || "")
    .refine((val) => {
      if (!val || val === "") return true; // Empty username is allowed
      if (val.length < 3) return false;
      if (val.length > 30) return false;
      // Username can contain letters, numbers, underscores, and hyphens
      const usernameRegex = /^[a-zA-Z0-9_-]+$/;
      return usernameRegex.test(val);
    }, "Username must be 3-30 characters and can only contain letters, numbers, underscores, and hyphens"),
  firstName: z
    .string()
    .min(1, "First name is required")
    .min(2, "First name must be at least 2 characters")
    .max(50, "First name must be less than 50 characters")
    .regex(
      /^[a-zA-Z\s-']+$/,
      "First name can only contain letters, spaces, hyphens, and apostrophes"
    ),
  lastName: z
    .string()
    .min(1, "Last name is required")
    .min(2, "Last name must be at least 2 characters")
    .max(50, "Last name must be less than 50 characters")
    .regex(
      /^[a-zA-Z\s-']+$/,
      "Last name can only contain letters, spaces, hyphens, and apostrophes"
    ),
  email: z
    .string()
    .min(1, "Email is required")
    .email("Please enter a valid email address")
    .max(100, "Email must be less than 100 characters"),
  phoneNumber: z
    .string()
    .min(1, "Phone number is required")
    .regex(/^[\d\s\-\+\(\)]+$/, "Please enter a valid phone number")
    .min(10, "Phone number must be at least 10 digits")
    .max(20, "Phone number must be less than 20 characters"),
  country: z
    .string()
    .min(1, "Country is required")
    .max(100, "Country name must be less than 100 characters"),
  birthDate: z
    .string()
    .min(1, "Birth date is required")
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      const age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        return age - 1 >= 18;
      }
      return age >= 18;
    }, "You must be at least 18 years old")
    .refine((date) => {
      const birthDate = new Date(date);
      const today = new Date();
      return birthDate <= today;
    }, "Birth date cannot be in the future"),
});

type ProfileFormData = z.infer<typeof profileSchema>;

// Function to get user's current IP address
const getUserIPAddress = async (): Promise<string | null> => {
  try {
    const response = await fetch("https://api.ipify.org?format=json");
    const data = await response.json();
    return data.ip;
  } catch (error) {
    logger.error("Failed to get IP address", { error });
    return null;
  }
};

interface UserProfile {
  // Editable fields
  username?: string;
  email: string;
  firstName: string;
  lastName: string;
  birthDate: string;
  phoneNumber: string;
  country: string;
  avatarUrl?: string | null;

  // View-only fields
  id?: number;
  ipAddress?: string;
  isActive?: boolean;
  isVerified?: boolean;
  twoFactorEnabled?: boolean;
  lastLoginAt?: string;
  createdAt?: string;
  referralCode?: string;
  referrerCode?: string;
}

export default function ProfileEditor() {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { profile, avatar, isLoading: profileLoading, isError, error } = useProfileData();

  // State for referrer code editing
  const [isEditingReferrer, setIsEditingReferrer] = useState(false);
  const [referrerCodeInput, setReferrerCodeInput] = useState("");
  const [copySuccess, setCopySuccess] = useState(false);

  // Debug logging
  useEffect(() => {
    if (error) {
      logger.error("Profile loading error", {
        error,
        message: error?.message,
        stack: error?.stack,
      });
    }
  }, [error]);
  const uploadAvatarMutation = useUploadAvatar();
  const updateProfileMutation = useUpdateProfile();

  const [profileImage, setProfileImage] = useState<string | null>(null);
  const [isProfileError, setIsProfileError] = useState(false);

  // Initialize form with validation
  const form = useForm<ProfileFormData>({
    resolver: zodResolver(profileSchema),
    defaultValues: {
      username: "",
      firstName: "",
      lastName: "",
      email: "",
      phoneNumber: "",
      country: "",
      birthDate: "",
    },
    mode: "onChange", // Validate on change for better UX
  });

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors, isValid, isDirty },
  } = form;

  // Debug form state
  useEffect(() => {
    logger.info("Form state update", {
      isValid,
      isDirty,
      errors: Object.keys(errors),
      errorDetails: errors,
    });
  }, [isValid, isDirty, errors]);

  // Update form when profile data loads
  useEffect(() => {
    logger.info("Profile data update", { profile, avatar, isError, error });

    if (profile) {
      // Update form values
      setValue("username", profile.username || "");
      setValue("firstName", profile.firstName || "");
      setValue("lastName", profile.lastName || "");
      setValue("email", profile.email || "");
      setValue("phoneNumber", profile.phone || "");
      setValue("country", profile.country || "");
      setValue("birthDate", profile.birthDate || "");

      // Set profile image from profile data
      if (profile.avatarUrl) {
        logger.info("Setting profile image from profile", { avatarUrl: profile.avatarUrl });
        setProfileImage(profile.avatarUrl);
      }

      // Check if IP address is empty and update it
      if (!profile.ipAddress && profile.id) {
        logger.info("IP address is empty, fetching current IP");
        getUserIpAddress().then((ip) => {
          if (ip) {
            logger.info("Got user IP, updating profile", { ip });

            // Update profile on backend
            updateProfileMutation.mutate(
              { data: { ipAddress: ip } },
              {
                onSuccess: () => {
                  logger.info("IP address updated on server", { ip });
                },
                onError: (error) => {
                  logger.error("Failed to update IP address", { error });
                },
              }
            );
          }
        });
      }
    }

    if (avatar && !profileImage) {
      logger.info("Setting profile image from avatar", { avatar });
      setProfileImage(avatar);
    }
  }, [profile, avatar, setValue]);

  // Handle error state - show notification once
  useEffect(() => {
    if (isError && !profileLoading) {
      setIsProfileError(true);

      // Show error notification
      toast.error("Failed to load profile", {
        description: error?.message || "Unable to load your profile data. Please try again later.",
      });
    }
  }, [isError, error, profileLoading]);

  // Handle profile image upload
  const handleImageUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    uploadAvatarMutation.mutate(
      { file },
      {
        onSuccess: ({ avatarUrl }) => {
          logger.info("Avatar upload successful", { avatarUrl });
          setProfileImage(avatarUrl);
          // Clear the file input
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
        },
      }
    );
  };

  // Trigger file input click
  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  // Remove profile image by updating with placeholder
  const removeProfileImage = () => {
    updateProfileMutation.mutate(
      {
        data: { avatarUrl: PLACEHOLDER_AVATAR_URL },
      },
      {
        onSuccess: () => {
          logger.info("Avatar removed - updated with placeholder");
          setProfileImage(null);
          if (fileInputRef.current) {
            fileInputRef.current.value = "";
          }
          toast.success("Avatar removed successfully");
        },
        onError: (error) => {
          logger.error("Failed to remove avatar", { error });
          toast.error("Failed to remove avatar");
        },
      }
    );
  };

  // Handle form submission
  const onSubmit = (data: ProfileFormData) => {
    const updateData: UpdateProfileRequest = {
      username: data.username,
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      phone: data.phoneNumber,
      country: data.country,
      birthDate: data.birthDate,
    };

    updateProfileMutation.mutate(
      { data: updateData },
      {
        onSuccess: (updatedProfile) => {
          logger.info("Profile saved successfully", { updatedProfile });

          // Update form values with fresh data
          setValue("username", updatedProfile.username || "");
          setValue("firstName", updatedProfile.firstName || "");
          setValue("lastName", updatedProfile.lastName || "");
          setValue("email", updatedProfile.email || "");
          setValue("phoneNumber", updatedProfile.phone || "");
          setValue("country", updatedProfile.country || "");
          setValue("birthDate", updatedProfile.birthDate || "");

          toast.success("Profile Updated", {
            description: "Your profile has been updated successfully.",
          });
        },
        onError: (error) => {
          logger.error("Failed to save profile", { error });
          toast.error("Error Saving Profile", {
            description: "There was an error saving your profile. Please try again.",
          });
        },
      }
    );
  };

  // Reset form to original values
  const handleCancel = () => {
    if (profile) {
      setValue("username", profile.username || "");
      setValue("firstName", profile.firstName || "");
      setValue("lastName", profile.lastName || "");
      setValue("email", profile.email || "");
      setValue("phoneNumber", profile.phone || "");
      setValue("country", profile.country || "");
      setValue("birthDate", profile.birthDate || "");
    }
    setProfileImage(profile?.avatarUrl || avatar || null);
  };

  // Watch form values for avatar fallback
  const username = watch("username");
  const firstName = watch("firstName");
  const lastName = watch("lastName");
  const phoneNumber = watch("phoneNumber");
  const country = watch("country");

  // Initialize referrer code input when profile loads
  useEffect(() => {
    if (profile?.referrerCode) {
      setReferrerCodeInput(profile.referrerCode);
    }
  }, [profile?.referrerCode]);

  // Get first letter of name for avatar fallback
  const firstLetter = firstName ? firstName.charAt(0).toUpperCase() : "U";
  const lastLetter = lastName ? lastName.charAt(0).toUpperCase() : "";

  // Helper function to format dates
  const formatDate = (dateString?: string) => {
    if (!dateString) return "Not available";
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "2-digit",
        minute: "2-digit",
      });
    } catch {
      return dateString;
    }
  };

  // Helper function to format date for input field
  const formatDateForInput = (dateString?: string) => {
    if (!dateString) return "";
    try {
      const date = new Date(dateString);
      return date.toISOString().split("T")[0];
    } catch {
      return "";
    }
  };

  // Generate referral link using the user's referral code
  const generateReferralLink = (referralCode?: string) => {
    if (!referralCode) return "https://app.bitmei.com/auth?ref=your-code";
    return `https://app.bitmei.com/auth?ref=${referralCode}`;
  };

  // Handle copying referral link
  const handleCopyReferralLink = async () => {
    if (!profile?.referralCode) return;

    const referralLink = generateReferralLink(profile.referralCode);

    try {
      await navigator.clipboard.writeText(referralLink);
      setCopySuccess(true);
      toast.success("Referral link copied to clipboard");

      setTimeout(() => {
        setCopySuccess(false);
      }, 2000);
    } catch {
      toast.error("Failed to copy referral link");
    }
  };

  // Check if user can still edit referrer code (within 1 month of registration)
  const canEditReferrer = () => {
    if (!profile?.createdAt) return false;
    if (profile?.referrerCode) return false; // Can't edit if already set

    const createdAt = new Date(profile.createdAt);
    const now = new Date();
    const oneMonthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000); // 30 days ago

    return createdAt > oneMonthAgo;
  };

  // Handle saving referrer code
  const handleSaveReferrerCode = async () => {
    if (!referrerCodeInput.trim()) {
      toast.error("Please enter a referrer code");
      return;
    }

    // Validate referrer code format
    const codeRegex = /^[a-zA-Z0-9_-]{3,20}$/;
    if (!codeRegex.test(referrerCodeInput.trim())) {
      toast.error(
        "Referrer code must be 3-20 characters and can only contain letters, numbers, underscores, and hyphens"
      );
      return;
    }

    updateProfileMutation.mutate(
      { data: { referrerCode: referrerCodeInput.trim() } },
      {
        onSuccess: () => {
          setIsEditingReferrer(false);
          toast.success("Referrer code updated successfully");
          logger.info("Referrer code updated", { referrerCode: referrerCodeInput.trim() });
        },
        onError: (error) => {
          logger.error("Failed to update referrer code", { error });
          toast.error("Failed to update referrer code. Please try again.");
        },
      }
    );
  };

  // Handle canceling referrer code edit
  const handleCancelReferrerEdit = () => {
    setReferrerCodeInput(profile?.referrerCode || "");
    setIsEditingReferrer(false);
  };

  // Show loading state while profile data is being fetched
  if (profileLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-12 w-12 border-4 border-gray-300 border-t-blue-600" />
      </div>
    );
  }

  return (
    <div className="space-y-5 w-full max-w-[1200px] px-2 sm:px-0">
      {/* Hidden file input for profile image */}
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleImageUpload}
        accept="image/*"
        className="hidden"
      />

      {/* Profile Info Card */}
      <Card className="border-slate-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle>Personal Information</CardTitle>
          <CardDescription>Update your personal details and profile picture</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Avatar Section */}
          <div>
            <Label className="text-sm font-medium text-slate-700 dark:text-gray-300 mb-4 block">
              Change Avatar
            </Label>
            <div className="flex flex-wrap items-center gap-5">
              <div className="relative group">
                {profileImage ? (
                  <div className="h-16 w-16 rounded-full overflow-hidden ring-2 ring-offset-2 ring-slate-200 dark:ring-gray-700 dark:ring-offset-gray-900">
                    <img
                      src={profileImage}
                      alt="Profile"
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        logger.error("Failed to load avatar image", { url: profileImage });
                        setProfileImage(null);
                      }}
                    />
                  </div>
                ) : (
                  <Avatar className="h-16 w-16 flex items-center justify-center ring-2 ring-offset-2 ring-slate-200 dark:ring-gray-700 dark:ring-offset-gray-900">
                    <AvatarFallback className="bg-[#3B82F6] text-white text-xl font-medium w-full h-full">
                      {firstLetter}
                      {lastLetter}
                    </AvatarFallback>
                  </Avatar>
                )}
                <div
                  className="absolute inset-0 flex items-center justify-center bg-black/40 opacity-0 group-hover:opacity-100 transition-opacity rounded-full cursor-pointer"
                  onClick={triggerFileInput}
                >
                  {uploadAvatarMutation.isPending ? (
                    <div className="animate-spin rounded-full h-6 w-6 border-2 border-white border-t-transparent" />
                  ) : (
                    <Camera className="w-6 h-6 text-white" />
                  )}
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-3 mt-3 sm:mt-0">
                <Button
                  variant="outline"
                  className="h-11 px-5 rounded-xl border-slate-200 text-slate-500 dark:text-gray-300 dark:border-gray-700 font-medium text-sm bg-slate-50 dark:bg-gray-800/50 hover:bg-slate-100 dark:hover:bg-gray-700"
                  onClick={removeProfileImage}
                  disabled={!profileImage || updateProfileMutation.isPending}
                >
                  {updateProfileMutation.isPending ? "Removing..." : "Remove Photo"}
                </Button>
                <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                  Click on avatar to upload
                </p>
              </div>
            </div>
          </div>

          {/* Form Fields */}
          <form onSubmit={handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-2">
              {/* Username */}
              <div className="flex flex-col gap-2">
                <Label className="text-sm font-medium text-slate-700 dark:text-gray-300">
                  Username
                </Label>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                    <UserCog className="h-5 w-5" />
                  </div>
                  <Input
                    {...register("username")}
                    className={cn(
                      "h-12 pl-10 pr-3 rounded-xl border-slate-200 dark:border-gray-600 focus-visible:ring-0 focus:border-blue-500 text-slate-700 dark:text-gray-200",
                      errors.username && "border-red-500 dark:border-red-500"
                    )}
                    placeholder="Enter your username"
                  />
                </div>
                {errors.username && (
                  <p className="text-sm text-red-500 dark:text-red-400">
                    {errors.username.message}
                  </p>
                )}
              </div>

              {/* First Name */}
              <div className="flex flex-col gap-2">
                <Label className="text-sm font-medium text-slate-700 dark:text-gray-300">
                  First Name
                </Label>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                    <User className="h-5 w-5" />
                  </div>
                  <Input
                    {...register("firstName")}
                    className={cn(
                      "h-12 pl-10 pr-3 rounded-xl border-slate-200 dark:border-gray-600 focus-visible:ring-0 focus:border-blue-500 text-slate-700 dark:text-gray-200",
                      errors.firstName && "border-red-500 dark:border-red-500"
                    )}
                    placeholder="Enter your first name"
                  />
                </div>
                {errors.firstName && (
                  <p className="text-sm text-red-500 dark:text-red-400">
                    {errors.firstName.message}
                  </p>
                )}
              </div>

              {/* Last Name */}
              <div className="flex flex-col gap-2">
                <Label className="text-sm font-medium text-slate-700 dark:text-gray-300">
                  Last Name
                </Label>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                    <User className="h-5 w-5" />
                  </div>
                  <Input
                    {...register("lastName")}
                    className={cn(
                      "h-12 pl-10 pr-3 rounded-xl border-slate-200 dark:border-gray-600 focus-visible:ring-0 focus:border-blue-500 text-slate-700 dark:text-gray-200",
                      errors.lastName && "border-red-500 dark:border-red-500"
                    )}
                    placeholder="Enter your last name"
                  />
                </div>
                {errors.lastName && (
                  <p className="text-sm text-red-500 dark:text-red-400">
                    {errors.lastName.message}
                  </p>
                )}
              </div>

              {/* Date of Birth */}
              <div className="flex flex-col gap-2">
                <Label className="text-sm font-medium text-slate-700 dark:text-gray-300">
                  Date of Birth
                </Label>
                <div className="relative">
                  <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                    <Calendar className="h-5 w-5" />
                  </div>
                  <Input
                    {...register("birthDate")}
                    type="date"
                    className={cn(
                      "h-12 pl-10 pr-3 rounded-xl border-slate-200 dark:border-gray-600 focus-visible:ring-0 focus:border-blue-500 text-slate-700 dark:text-gray-200",
                      errors.birthDate && "border-red-500 dark:border-red-500"
                    )}
                  />
                </div>
                {errors.birthDate && (
                  <p className="text-sm text-red-500 dark:text-red-400">
                    {errors.birthDate.message}
                  </p>
                )}
              </div>

              {/* Country */}
              <WorkingCountrySelect
                value={country}
                onValueChange={(value) => setValue("country", value)}
                error={errors.country?.message}
                disabled={updateProfileMutation.isPending}
              />

              {/* Phone Number - spans 2 columns */}
              <div className="md:col-span-2">
                <WorkingPhoneInput
                  phoneValue={phoneNumber}
                  countryValue={country}
                  onPhoneChange={(value) => setValue("phoneNumber", value)}
                  onCountryChange={(value) => setValue("country", value)}
                  phoneError={errors.phoneNumber?.message}
                  disabled={updateProfileMutation.isPending}
                  placeholder="Enter your phone number"
                />
              </div>
            </div>
          </form>
        </CardContent>
      </Card>

      {/* Contact Information Card */}
      <Card className="border-slate-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle>Contact Information</CardTitle>
          <CardDescription>Manage your email and phone verification</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Email Section */}
          <div className="flex flex-col gap-2">
            <Label className="text-sm font-medium text-slate-700 dark:text-gray-300">Email</Label>
            <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
              <div className="relative flex-1 w-full">
                <div className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500 dark:text-gray-400">
                  <Mail className="h-5 w-5" />
                </div>
                <Input
                  {...register("email")}
                  className={cn(
                    "h-12 pl-10 pr-3 rounded-xl border-slate-200 dark:border-gray-600 focus-visible:ring-0 focus:border-blue-500 text-slate-700 dark:text-gray-200 w-full",
                    errors.email && "border-red-500 dark:border-red-500"
                  )}
                  type="email"
                  placeholder="Enter your email address"
                />
              </div>
              <Button
                variant="outline"
                type="button"
                className="h-12 px-4 rounded-xl border-slate-200 dark:border-gray-600 bg-slate-50 dark:bg-gray-800/50 text-blue-600 hover:bg-slate-100 dark:hover:bg-gray-700 w-full sm:w-auto"
                onClick={() => {
                  // Show verification prompt or dialog in a real implementation
                  toast.info("Email verification sent", {
                    description: "Please check your inbox for verification link",
                  });
                }}
              >
                Verify Email
              </Button>
            </div>
            {errors.email && (
              <p className="text-sm text-red-500 dark:text-red-400">{errors.email.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Account Information Card - View Only */}
      <Card className="border-slate-200 dark:border-gray-700">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserCog className="h-5 w-5" />
            Account Information
          </CardTitle>
          <CardDescription>System-managed account details and security information</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Primary Information */}
          <div className="space-y-4">
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              {/* User ID */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-1">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Hash className="h-4 w-4" />
                  <span>User ID</span>
                </div>
                <p className="font-semibold text-gray-900 dark:text-gray-100">
                  {profile?.id || "N/A"}
                </p>
              </div>

              {/* IP Address */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-1">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Globe className="h-4 w-4" />
                  <span>IP Address</span>
                </div>
                <p className="font-mono text-gray-900 dark:text-gray-100">
                  {profile?.ipAddress || "Not available"}
                </p>
              </div>
            </div>

            {/* Status Badges */}
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              {/* Account Status */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <UserCheck className="h-4 w-4" />
                  <span>Account Status</span>
                </div>
                {profile?.isActive ? (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30">
                    Active
                  </Badge>
                ) : (
                  <Badge variant="destructive">Inactive</Badge>
                )}
              </div>

              {/* Verification Status */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Shield className="h-4 w-4" />
                  <span>Verification Status</span>
                </div>
                {profile?.isVerified ? (
                  <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400 hover:bg-blue-100 dark:hover:bg-blue-900/30">
                    Verified
                  </Badge>
                ) : (
                  <Badge
                    variant="secondary"
                    className="bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"
                  >
                    Unverified
                  </Badge>
                )}
              </div>

              {/* 2FA Status */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Fingerprint className="h-4 w-4" />
                  <span>Two-Factor Auth</span>
                </div>
                {profile?.twoFactorEnabled ? (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400 hover:bg-green-100 dark:hover:bg-green-900/30">
                    Enabled
                  </Badge>
                ) : (
                  <Badge variant="outline" className="text-gray-600 dark:text-gray-400">
                    Disabled
                  </Badge>
                )}
              </div>
            </div>

            {/* Time Information */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              {/* Last Login */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-1">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Clock className="h-4 w-4" />
                  <span>Last Login</span>
                </div>
                <p className="text-gray-900 dark:text-gray-100">
                  {formatDate(profile?.lastLoginAt)}
                </p>
              </div>

              {/* Account Created */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-1">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Clock className="h-4 w-4" />
                  <span>Account Created</span>
                </div>
                <p className="text-gray-900 dark:text-gray-100">{formatDate(profile?.createdAt)}</p>
              </div>
            </div>

            {/* Referral Information */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mt-4">
              {/* Referral Code - Clickable */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Link2 className="h-4 w-4" />
                  <span>Your Referral Code</span>
                </div>
                <div className="flex items-center gap-2">
                  <p className="font-mono font-semibold text-gray-900 dark:text-gray-100 flex-1">
                    {profile?.referralCode || "Not available"}
                  </p>
                  {profile?.referralCode && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={handleCopyReferralLink}
                      className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-600"
                      title="Copy referral link"
                    >
                      {copySuccess ? (
                        <Check className="h-4 w-4 text-green-600" />
                      ) : (
                        <Copy className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                      )}
                    </Button>
                  )}
                </div>
                {profile?.referralCode && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Click to copy referral link
                  </p>
                )}
              </div>

              {/* Who Invited You - Editable if conditions are met */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-xl p-4 space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400">
                  <Users className="h-4 w-4" />
                  <span>Who Invited You</span>
                </div>

                {isEditingReferrer ? (
                  <div className="space-y-2">
                    <Input
                      value={referrerCodeInput}
                      onChange={(e) => setReferrerCodeInput(e.target.value)}
                      placeholder="Enter referral code"
                      className="h-8 text-sm"
                      maxLength={20}
                    />
                    <div className="flex gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={handleSaveReferrerCode}
                        disabled={updateProfileMutation.isPending || !referrerCodeInput.trim()}
                        className="h-7 px-2 text-xs"
                      >
                        {updateProfileMutation.isPending ? (
                          <div className="h-3 w-3 rounded-full border border-gray-400 border-t-transparent animate-spin" />
                        ) : (
                          <>
                            <Save className="h-3 w-3 mr-1" />
                            Save
                          </>
                        )}
                      </Button>
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={handleCancelReferrerEdit}
                        disabled={updateProfileMutation.isPending}
                        className="h-7 px-2 text-xs"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center gap-2">
                    <p className="font-mono font-semibold text-gray-900 dark:text-gray-100 flex-1">
                      {profile?.referrerCode || "Not set"}
                    </p>
                    {canEditReferrer() && (
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => setIsEditingReferrer(true)}
                        className="h-8 w-8 p-0 hover:bg-gray-200 dark:hover:bg-gray-600"
                        title="Add referrer code"
                      >
                        <Edit3 className="h-4 w-4 text-gray-500 dark:text-gray-400" />
                      </Button>
                    )}
                  </div>
                )}

                {canEditReferrer() && !isEditingReferrer && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    You can add a referrer code within 1 month of registration
                  </p>
                )}

                {!canEditReferrer() && !profile?.referrerCode && (
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Time limit expired (1 month after registration)
                  </p>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex flex-col-reverse sm:flex-row items-center justify-center sm:justify-end gap-3 sm:gap-5 py-4 mt-2">
        <Button
          type="button"
          variant="outline"
          className="h-11 px-8 rounded-xl border-slate-200 dark:border-gray-600 text-slate-700 dark:text-gray-300 font-medium w-full sm:w-auto"
          onClick={handleCancel}
          disabled={!isDirty || updateProfileMutation.isPending || isProfileError}
        >
          Cancel <X className="h-4 w-4 ml-1" />
        </Button>
        <Button
          type="submit"
          variant="gradient"
          className="h-11 px-8 text-white font-medium w-full sm:w-auto rounded-xl"
          onClick={handleSubmit(onSubmit)}
          disabled={!isDirty || !isValid || updateProfileMutation.isPending || isProfileError}
        >
          {updateProfileMutation.isPending ? (
            <>
              <span className="mr-2">Saving</span>
              <div className="h-4 w-4 rounded-full border-2 border-white border-t-transparent animate-spin"></div>
            </>
          ) : (
            <>
              Save <Check className="h-4 w-4 ml-1" />
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
