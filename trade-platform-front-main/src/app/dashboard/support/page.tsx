"use client";

import { motion } from "framer-motion";
import { Search } from "lucide-react";
import { useState } from "react";

import DashboardCard from "@/components/dashboard/DashboardCard";
import { TicketTracker, SupportForm } from "@/components/support";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useSupportTicketTracker } from "@/shared/api/hooks/useSupport";
import { SupportRequest } from "@/shared/api/services/support-service";

// Page header component
function SupportPageHeader() {
  return (
    <div className="mb-6 px-2 sm:px-0">
      <h1 className="text-[#1E293B] text-[24px] sm:text-[28px] font-bold leading-tight mb-2 dark:text-white">
        Support Center
      </h1>
      <p className="text-[#475569] text-[14px] sm:text-[16px] font-normal dark:text-gray-300">
        Get help with your account, transactions, or technical issues
      </p>
    </div>
  );
}

// Quick access tickets component
function QuickAccessTickets({
  savedTickets,
  onSelectTicket,
}: {
  savedTickets: string[];
  onSelectTicket: (ticketId: string) => void;
}) {
  if (savedTickets.length === 0) return null;

  return (
    <div className="border-t pt-6">
      <h4 className="font-medium text-foreground mb-3">Your Recent Tickets</h4>
      <div className="grid gap-2">
        {savedTickets.slice(0, 3).map((ticketId) => (
          <Button
            key={ticketId}
            variant="ghost"
            size="sm"
            className="justify-start h-auto p-3 text-left"
            onClick={() => onSelectTicket(ticketId)}
          >
            <div>
              <div className="font-medium">#{ticketId.slice(0, 8)}</div>
              <div className="text-xs text-muted-foreground">Click to track this ticket</div>
            </div>
          </Button>
        ))}
      </div>
    </div>
  );
}

// Track request tab content
function TrackRequestTab({
  trackingId,
  setTrackingId,
  savedTickets,
}: {
  trackingId: string;
  setTrackingId: (id: string) => void;
  savedTickets: string[];
}) {
  return (
    <DashboardCard>
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold text-foreground mb-2">Track Your Support Request</h3>
          <p className="text-sm text-muted-foreground mb-4">
            Enter your ticket ID to check the status and response from our support team.
          </p>

          <div className="flex gap-2 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                type="text"
                placeholder="Enter ticket ID (e.g., cd1073a5-3bba-4f52...)"
                value={trackingId}
                onChange={(e) => setTrackingId(e.target.value)}
                className="pl-10"
              />
            </div>
            <Button
              variant="outline"
              disabled={!trackingId.trim()}
              onClick={() => {
                // Trigger re-render by updating the key
                setTrackingId(trackingId.trim());
              }}
            >
              Track
            </Button>
          </div>
        </div>

        {trackingId && <TicketTracker key={trackingId} ticketId={trackingId} />}

        <QuickAccessTickets savedTickets={savedTickets} onSelectTicket={setTrackingId} />
      </div>
    </DashboardCard>
  );
}

export default function SupportPage() {
  const [selectedTab, setSelectedTab] = useState<string>("new");
  const [trackingId, setTrackingId] = useState("");
  const { getSavedTicketIds } = useSupportTicketTracker();

  // Handle real ticket creation
  const handleTicketCreated = (ticket: SupportRequest) => {
    setTrackingId(ticket.id);
    setSelectedTab("track");
  };

  // Get saved tickets
  const savedTickets = getSavedTicketIds();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.4 }}
    >
      <SupportPageHeader />

      <Tabs value={selectedTab} onValueChange={setSelectedTab} className="w-full mb-6">
        <TabsList className="bg-gray-100 dark:bg-gray-800 p-1 rounded-lg w-full sm:w-auto">
          <TabsTrigger
            value="new"
            className="rounded-md text-sm px-4 py-1.5 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700"
          >
            New Request
          </TabsTrigger>
          <TabsTrigger
            value="track"
            className="rounded-md text-sm px-4 py-1.5 data-[state=active]:bg-white dark:data-[state=active]:bg-gray-700"
          >
            Track Request
          </TabsTrigger>
        </TabsList>

        <TabsContent value="new" className="pt-4">
          <SupportForm onTicketCreated={handleTicketCreated} />
        </TabsContent>

        <TabsContent value="track" className="pt-4">
          <TrackRequestTab
            trackingId={trackingId}
            setTrackingId={setTrackingId}
            savedTickets={savedTickets}
          />
        </TabsContent>
      </Tabs>
    </motion.div>
  );
}
