.statsCard {
  transition: all 0.3s ease;
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.statsCard:hover {
  transform: translateY(-5px);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.05),
    0 8px 10px -6px rgba(0, 0, 0, 0.01);
}

.statsCard::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(59, 130, 246, 0.05), rgba(59, 130, 246, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.statsCard:hover::after {
  opacity: 1;
}

.referralLink {
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.referralLink:hover {
  transform: scale(1.005);
  box-shadow:
    0 10px 25px -5px rgba(0, 0, 0, 0.05),
    0 8px 10px -6px rgba(0, 0, 0, 0.01);
}

.copyButton {
  position: relative;
  overflow: hidden;
}

.copyButton::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.copyButton:hover::before {
  transform: translateX(100%);
}

.materialItem {
  transition: all 0.2s ease;
  cursor: pointer;
}

.materialItem:hover {
  background-color: rgba(59, 130, 246, 0.05);
}

:global(.dark) .materialItem:hover {
  background-color: rgba(59, 130, 246, 0.1);
}

.downloadButton {
  transition: all 0.2s ease;
}

.downloadButton:hover {
  transform: translateY(-1px);
}

.iconContainer {
  width: 42px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background-color: rgba(59, 130, 246, 0.1);
  transition: all 0.2s ease;
}

:global(.dark) .iconContainer {
  background-color: rgba(59, 130, 246, 0.15);
}

/* Coming soon styles */
.comingSoon {
  opacity: 0.6;
  pointer-events: none;
}

.comingSoonBadge {
  font-size: 0.75rem;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: rgba(251, 146, 60, 0.1);
  color: rgb(234, 88, 12);
}

:global(.dark) .comingSoonBadge {
  background-color: rgba(251, 146, 60, 0.2);
  color: rgb(251, 146, 60);
}

/* History table styling */
.historyTable {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
}

.historyTable th {
  font-weight: 500;
  text-align: left;
  padding: 12px 16px;
  border-bottom: 1px solid rgba(229, 231, 235, 0.5);
}

:global(.dark) .historyTable th {
  border-bottom: 1px solid rgba(51, 65, 85, 0.5);
}

.historyTable td {
  padding: 12px 16px;
  transition: background-color 0.2s ease;
}

.historyTable tr:hover td {
  background-color: rgba(243, 244, 246, 0.5);
}

:global(.dark) .historyTable tr:hover td {
  background-color: rgba(51, 65, 85, 0.3);
}

.historyTable tr td:first-child {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.historyTable tr td:last-child {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.statusBadge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.75rem;
  line-height: 1.5;
  transition: all 0.2s ease;
}

.statusBadge.completed {
  background-color: rgba(34, 197, 94, 0.1);
  color: rgb(22, 163, 74);
}

:global(.dark) .statusBadge.completed {
  background-color: rgba(34, 197, 94, 0.2);
  color: rgb(74, 222, 128);
}

.infoBox {
  padding: 16px;
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
  transition: all 0.2s ease;
}

:global(.dark) .infoBox {
  background-color: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
}

.infoBox:hover {
  background-color: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

:global(.dark) .infoBox:hover {
  background-color: rgba(59, 130, 246, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .statsCard {
    margin-bottom: 12px;
  }

  .historyTable th,
  .historyTable td {
    padding: 10px 12px;
  }
}
