"use client";

import { AnimatePresence } from "framer-motion";
import React from "react";

import { Background } from "@/components/background";
import { Navbar } from "@/components/layout/navbar";

// Unified spinner component - for consistency throughout the application
const LoadingSpinner = () => (
  <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
);

export default function SupportLayout({ children }: { children: React.ReactNode }) {
  return (
    <div className="w-full max-w-full overflow-x-hidden relative flex flex-col min-h-screen">
      {/* Background */}
      <div className="fixed inset-0 z-0">
        <Background />
      </div>

      {/* Fixed navbar for support page */}
      <div className="fixed top-0 left-0 right-0 z-50">
        <Navbar />
      </div>

      {/* Main content */}
      <div className="flex w-full relative z-10 flex-grow pt-16">
        <div className="flex-1">{children}</div>
      </div>
    </div>
  );
}
