<svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2035_20673)">
<rect x="-3" y="-1" width="44" height="33" rx="2" fill="white"/>
<mask id="mask0_2035_20673" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="-3" y="-1" width="44" height="33">
<rect x="-3" y="-1" width="44" height="33" rx="2" fill="white"/>
</mask>
<g mask="url(#mask0_2035_20673)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M41 -1H-3V1.2H41V-1ZM41 3.40027H-3V5.60027H41V3.40027ZM-3 7.8H41V10H-3V7.8ZM41 12.2H-3V14.4H41V12.2ZM-3 16.5997H41V18.7997H-3V16.5997ZM41 21H-3V23.2H41V21ZM-3 25.4003H41V27.6003H-3V25.4003ZM41 29.8H-3V32H41V29.8Z" fill="#D02F44"/>
<rect x="-3" y="-1" width="18.8571" height="15.4" fill="#46467F"/>
<g filter="url(#filter0_d_2035_20673)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M1.19094 2.30013C1.19094 2.90765 0.721906 3.40013 0.143322 3.40013C-0.435262 3.40013 -0.904297 2.90765 -0.904297 2.30013C-0.904297 1.69262 -0.435262 1.20013 0.143322 1.20013C0.721906 1.20013 1.19094 1.69262 1.19094 2.30013ZM5.38142 2.30013C5.38142 2.90765 4.91238 3.40013 4.3338 3.40013C3.75521 3.40013 3.28618 2.90765 3.28618 2.30013C3.28618 1.69262 3.75521 1.20013 4.3338 1.20013C4.91238 1.20013 5.38142 1.69262 5.38142 2.30013ZM8.52427 3.40013C9.10286 3.40013 9.57189 2.90765 9.57189 2.30013C9.57189 1.69262 9.10286 1.20013 8.52427 1.20013C7.94569 1.20013 7.47666 1.69262 7.47666 2.30013C7.47666 2.90765 7.94569 3.40013 8.52427 3.40013ZM13.7624 2.30013C13.7624 2.90765 13.2933 3.40013 12.7148 3.40013C12.1362 3.40013 11.6671 2.90765 11.6671 2.30013C11.6671 1.69262 12.1362 1.20013 12.7148 1.20013C13.2933 1.20013 13.7624 1.69262 13.7624 2.30013ZM2.23872 5.60027C2.8173 5.60027 3.28634 5.10778 3.28634 4.50027C3.28634 3.89276 2.8173 3.40027 2.23872 3.40027C1.66014 3.40027 1.1911 3.89276 1.1911 4.50027C1.1911 5.10778 1.66014 5.60027 2.23872 5.60027ZM7.4768 4.50027C7.4768 5.10778 7.00776 5.60027 6.42918 5.60027C5.8506 5.60027 5.38156 5.10778 5.38156 4.50027C5.38156 3.89276 5.8506 3.40027 6.42918 3.40027C7.00776 3.40027 7.4768 3.89276 7.4768 4.50027ZM10.6195 5.60014C11.1981 5.60014 11.6671 5.10765 11.6671 4.50014C11.6671 3.89262 11.1981 3.40013 10.6195 3.40013C10.0409 3.40013 9.57189 3.89262 9.57189 4.50014C9.57189 5.10765 10.0409 5.60014 10.6195 5.60014ZM13.7624 6.7004C13.7624 7.30792 13.2933 7.8004 12.7148 7.8004C12.1362 7.8004 11.6671 7.30792 11.6671 6.7004C11.6671 6.09289 12.1362 5.6004 12.7148 5.6004C13.2933 5.6004 13.7624 6.09289 13.7624 6.7004ZM8.52415 7.8004C9.10273 7.8004 9.57177 7.30792 9.57177 6.7004C9.57177 6.09289 9.10273 5.6004 8.52415 5.6004C7.94556 5.6004 7.47653 6.09289 7.47653 6.7004C7.47653 7.30792 7.94556 7.8004 8.52415 7.8004ZM5.3814 6.7004C5.3814 7.30792 4.91237 7.8004 4.33378 7.8004C3.7552 7.8004 3.28616 7.30792 3.28616 6.7004C3.28616 6.09289 3.7552 5.6004 4.33378 5.6004C4.91237 5.6004 5.3814 6.09289 5.3814 6.7004ZM0.143322 7.8004C0.721906 7.8004 1.19094 7.30792 1.19094 6.7004C1.19094 6.09289 0.721906 5.6004 0.143322 5.6004C-0.435262 5.6004 -0.904297 6.09289 -0.904297 6.7004C-0.904297 7.30792 -0.435262 7.8004 0.143322 7.8004ZM3.28605 8.89986C3.28605 9.50738 2.81702 9.99986 2.23843 9.99986C1.65985 9.99986 1.19081 9.50738 1.19081 8.89986C1.19081 8.29235 1.65985 7.79987 2.23843 7.79987C2.81702 7.79987 3.28605 8.29235 3.28605 8.89986ZM6.42918 9.99986C7.00776 9.99986 7.4768 9.50738 7.4768 8.89986C7.4768 8.29235 7.00776 7.79987 6.42918 7.79987C5.8506 7.79987 5.38156 8.29235 5.38156 8.89986C5.38156 9.50738 5.8506 9.99986 6.42918 9.99986ZM11.6673 8.89986C11.6673 9.50738 11.1982 9.99986 10.6196 9.99986C10.0411 9.99986 9.57202 9.50738 9.57202 8.89986C9.57202 8.29235 10.0411 7.79987 10.6196 7.79987C11.1982 7.79987 11.6673 8.29235 11.6673 8.89986ZM12.7148 12.2001C13.2933 12.2001 13.7624 11.7077 13.7624 11.1001C13.7624 10.4926 13.2933 10.0001 12.7148 10.0001C12.1362 10.0001 11.6671 10.4926 11.6671 11.1001C11.6671 11.7077 12.1362 12.2001 12.7148 12.2001ZM9.57189 11.1001C9.57189 11.7077 9.10286 12.2001 8.52427 12.2001C7.94569 12.2001 7.47666 11.7077 7.47666 11.1001C7.47666 10.4926 7.94569 10.0001 8.52427 10.0001C9.10286 10.0001 9.57189 10.4926 9.57189 11.1001ZM4.3338 12.2001C4.91238 12.2001 5.38142 11.7077 5.38142 11.1001C5.38142 10.4926 4.91238 10.0001 4.3338 10.0001C3.75521 10.0001 3.28618 10.4926 3.28618 11.1001C3.28618 11.7077 3.75521 12.2001 4.3338 12.2001ZM1.19094 11.1001C1.19094 11.7077 0.721906 12.2001 0.143322 12.2001C-0.435262 12.2001 -0.904297 11.7077 -0.904297 11.1001C-0.904297 10.4926 -0.435262 10.0001 0.143322 10.0001C0.721906 10.0001 1.19094 10.4926 1.19094 11.1001Z" fill="url(#paint0_linear_2035_20673)"/>
</g>
</g>
</g>
<defs>
<filter id="filter0_d_2035_20673" x="-0.904297" y="1.20013" width="14.667" height="12" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2035_20673"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2035_20673" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2035_20673" x1="-0.904297" y1="1.20013" x2="-0.904297" y2="12.2001" gradientUnits="userSpaceOnUse">
<stop stop-color="white"/>
<stop offset="1" stop-color="#F0F0F0"/>
</linearGradient>
<clipPath id="clip0_2035_20673">
<rect width="22" height="22" rx="11" fill="white"/>
</clipPath>
</defs>
</svg>
