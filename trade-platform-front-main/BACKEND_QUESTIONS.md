# Questions for Backend Team

## 🔄 Real-time Updates Architecture

### Current Understanding:
1. Frontend connects to WebSocket at `wss://trading-ws.eu.ngrok.io/api/ws/{user_id}`
2. On connection, we receive all positions and history
3. We subscribe to `position_events` and `liquidation_alerts`
4. Backend has a PnLUpdater that runs every 5 seconds

### Questions:

**1. Are PnL updates pushed automatically via WebSocket?**
- We expected to receive `realtime_pnl_update` events every 5 seconds
- Currently not seeing these automatic updates
- Should we poll the REST API instead?

**2. What triggers position update events?**
We need to know which actions trigger WebSocket events:
- [ ] Position opened (limit order activated)
- [ ] Position closed manually
- [ ] Position liquidated
- [ ] Take Profit triggered
- [ ] Stop Loss triggered
- [ ] Position PnL updated (every 5 sec?)
- [ ] Entry price modified
- [ ] TP/SL levels modified

**3. WebSocket Message Format Clarification**

We see different message formats in responses:
```json
// Format 1 - with action
{
  "action": "positions",
  "data": [...]
}

// Format 2 - with type
{
  "type": "position_event",
  "event_type": "position_opened",
  "data": {...}
}

// Format 3 - status messages
{
  "status": "connected",
  "message": "Connected as user 13"
}
```

Which format should we expect for each event type?

**4. Optimistic Updates Strategy**

For better UX, we're implementing optimistic updates. Need to confirm:
- Is position ID returned immediately when opening a position?
- Can we rely on position IDs being sequential?
- What's the typical delay between trade execution and WebSocket event?

**5. Balance Integration**

How should we handle balance updates?
- Are balance changes pushed via WebSocket?
- Should we use a separate Balance WebSocket endpoint?
- Or should we invalidate balance cache on every position event?

---

## 🚀 Feature Requests

### 1. Batch Operations Support
Can you add endpoints for:
- `POST /api/orders/cancel-batch` - Cancel multiple orders
- `POST /api/positions/close-batch` - Close multiple positions

### 2. WebSocket Enhancements
Would be helpful to have:
- `subscribe_price_updates` - For specific trading pairs
- `get_subscriptions` - List active subscriptions
- Connection quality metrics in `connection_info`

### 3. Position Calculations
Can the API return pre-calculated values?
- Distance to liquidation (price and percentage)
- Required price for break-even
- Price impact of closing position

---

## 📊 Performance Considerations

**Current Load:**
- User 13 has 111+ positions
- Some users might have 1000+ positions

**Questions:**
1. Is there a limit on positions per user?
2. Should we implement pagination for WebSocket initial data?
3. Can we subscribe to updates for specific positions only?
4. What's the recommended strategy for users with many positions?

---

## 🔐 Security Questions

1. **WebSocket Authentication:**
   - Currently using user_id in URL path
   - Should we also send JWT token in connection headers?
   - How long do WebSocket connections stay authenticated?

2. **Rate Limiting:**
   - What happens if a user opens multiple browser tabs?
   - Is rate limiting per connection or per user?
   - Best practice for handling rate limit errors?

---

Please let us know your thoughts and recommendations. We want to ensure optimal integration with your backend services.

Thank you! 🙏