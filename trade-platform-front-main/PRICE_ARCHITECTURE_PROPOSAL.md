# Price Data Architecture Proposal

## 🎯 Problem Statement

**Critical Issue:** Multiple components showing different prices simultaneously leads to:
- User confusion and trust issues
- Inconsistent PnL calculations
- UI state inconsistencies
- Poor user experience

**Current State:**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Positions     │    │  Trading View   │    │   Sidebar       │
│   BTC: $43,000  │    │  BTC: $43,100   │    │   BTC: $42,950  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         ↑                       ↑                       ↑
   REST API call          WebSocket msg          Cached data
```

## 🎯 Identified Source of Truth: trading-chart-ws

**We have discovered that `trading-chart-ws.eu.ngrok.io` (*************:8765) is the authoritative price source.**

**Critical Requirement:** All price-dependent operations must use trading-chart-ws data:
- ✅ PnL calculations in backend services
- ✅ Liquidation monitoring
- ✅ Frontend price displays
- ✅ Trading execution pricing
- ✅ Real-time WebSocket updates

---

## 🏗️ Proposed Architecture: trading-chart-ws Integration

### Core Principle: trading-chart-ws as Single Source of Truth

```javascript
// Central price management system with trading-chart-ws integration
class UnifiedPriceManager {
  constructor() {
    this.prices = new Map();           // Current prices from trading-chart-ws
    this.priceHistory = new Map();     // Price history for charts
    this.subscribers = new Set();      // Components listening to updates
    this.lastUpdate = new Map();       // Timestamps for each pair
    this.tradingWS = null;            // Main trading WebSocket
    this.chartWS = null;              // Direct trading-chart-ws connection (fallback)
    this.fallbackInterval = null;     // REST API fallback
  }
}
```

### Architecture Flow with trading-chart-ws

```
┌─────────────────────────────────┐
│      trading-chart-ws           │  ← AUTHORITATIVE SOURCE
│   *************:8765           │
└─────────────┬───────────────────┘
              │
              ▼
┌─────────────────────────────────┐
│     Trading Service             │  ← PRICE DISTRIBUTOR
│  - Subscribes to chart-ws       │
│  - Calculates PnL               │
│  - Distributes via WebSocket    │
└─────────────┬───────────────────┘
              │
              ▼
┌─────────────────────────────────┐
│     Unified Price Manager       │  ← FRONTEND CONSUMER
│  (Single Source of Truth)       │
└─────────────┬───────────────────┘
              │
    ┌─────────┼─────────┐
    │         │         │
    ▼         ▼         ▼
┌─────────┐ ┌─────────┐ ┌─────────┐
│Position │ │Trading  │ │Sidebar  │
│Component│ │Component│ │Component│
└─────────┘ └─────────┘ └─────────┘
BTC: $43,000 BTC: $43,000 BTC: $43,000
✅ Consistent ✅ Consistent ✅ Consistent
```

---

## 💡 Implementation Options with trading-chart-ws

### Option 1: Trading Service as Price Hub (Recommended)

**Trading Service subscribes to trading-chart-ws and redistributes:**

```javascript
// Backend integrates with trading-chart-ws and adds to position WebSocket
{
  "type": "price_update",
  "data": {
    "BTC/USDT": {
      "price": 43000.50,
      "timestamp": "2025-06-08T12:00:00Z",
      "source": "trading-chart-ws",
      "chart_timestamp": "2025-06-08T12:00:00.123Z",
      "volume_24h": 1500000,
      "change_24h": 2.5
    },
    "ETH/USDT": {
      "price": 2800.25,
      "timestamp": "2025-06-08T12:00:00Z", 
      "source": "trading-chart-ws",
      "chart_timestamp": "2025-06-08T12:00:00.125Z",
      "volume_24h": 800000,
      "change_24h": -1.2
    }
  },
  "version": 12345
}
```

**Advantages:**
- ✅ Single integration point with trading-chart-ws
- ✅ Trading service can calculate PnL with same prices frontend sees
- ✅ Consistent versioning system
- ✅ Lower complexity for frontend
- ✅ Built-in fallback mechanisms

**Questions for Backend:**
1. Can Trading Service subscribe to trading-chart-ws?
2. What's the trading-chart-ws API format?
3. How to handle trading-chart-ws authentication?
4. What update frequency from trading-chart-ws?

### Option 2: Direct Frontend Connection to trading-chart-ws

```javascript
// Frontend connects directly to trading-chart-ws (fallback option)
const chartWS = new WebSocket('wss://trading-chart-ws.eu.ngrok.io');

// Subscribe to specific pairs
chartWS.send({
  action: "subscribe_prices",
  pairs: ["BTC/USDT", "ETH/USDT", "SOL/USDT"]
});
```

**Advantages:**
- ✅ Direct price source access
- ✅ Reduced latency
- ✅ Can work independently of Trading Service

**Disadvantages:**
- ❌ Additional connection management
- ❌ Potential authentication complexity
- ❌ May overload trading-chart-ws with many clients
- ❌ No integration with backend PnL calculations

**Questions:**
- Is trading-chart-ws designed for multiple frontend connections?
- What's the authentication mechanism for trading-chart-ws?
- Can it handle 100+ concurrent connections?

### Option 3: Hybrid Approach with Fallback

```javascript
class HybridPriceManager {
  constructor() {
    this.prices = new Map();
    this.tradingWS = null;     // Primary: Trading Service WebSocket
    this.chartWS = null;       // Fallback: Direct trading-chart-ws
    this.restInterval = null;  // Last resort: REST polling
  }
  
  async startPriceUpdates() {
    // Primary: Get prices from Trading Service WebSocket
    this.tradingWS.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === 'price_update') {
        this.updatePricesFromTradingService(data.data);
      }
    };
    
    // Fallback: Connect directly to trading-chart-ws if Trading Service fails
    this.tradingWS.onclose = () => {
      this.connectToChartWS();
    };
  }
  
  connectToChartWS() {
    this.chartWS = new WebSocket('wss://trading-chart-ws.eu.ngrok.io');
    // Handle direct chart data
  }
}
```

**Advantages:**
- ✅ Maximum reliability with multiple fallbacks
- ✅ Direct access to source of truth when needed
- ✅ Graceful degradation

**Disadvantages:**
- ❌ Complex connection management
- ❌ Potential price source conflicts
- ❌ Higher development complexity

---

## 🔧 Detailed Implementation

### 1. Core Price Manager

```javascript
export class UnifiedPriceManager {
  constructor() {
    this.prices = new Map();
    this.subscribers = new Set();
    this.lastUpdate = new Map();
    this.initialized = false;
  }
  
  // Subscribe to price updates
  subscribe(callback) {
    this.subscribers.add(callback);
    
    // Send current prices to new subscriber
    if (this.initialized) {
      this.prices.forEach((data, pair) => {
        callback(pair, data);
      });
    }
    
    // Return unsubscribe function
    return () => this.subscribers.delete(callback);
  }
  
  // Update price and notify all subscribers
  updatePrice(pair, price, timestamp = Date.now()) {
    const priceData = {
      price,
      timestamp,
      change: this.calculateChange(pair, price)
    };
    
    this.prices.set(pair, priceData);
    this.lastUpdate.set(pair, timestamp);
    
    // Notify all subscribers
    this.subscribers.forEach(callback => {
      callback(pair, priceData);
    });
  }
  
  // Get current price for a pair
  getPrice(pair) {
    return this.prices.get(pair)?.price || null;
  }
  
  // Get price with metadata
  getPriceData(pair) {
    return this.prices.get(pair) || null;
  }
  
  // Check if price is stale (older than 30 seconds)
  isPriceStale(pair) {
    const lastUpdate = this.lastUpdate.get(pair);
    return !lastUpdate || (Date.now() - lastUpdate) > 30000;
  }
}

// Global instance
export const priceManager = new UnifiedPriceManager();
```

### 2. React Hook for Components

```javascript
export function usePrice(tradingPair) {
  const [priceData, setPriceData] = useState(null);
  const [isStale, setIsStale] = useState(false);
  
  useEffect(() => {
    // Subscribe to price updates
    const unsubscribe = priceManager.subscribe((pair, data) => {
      if (pair === tradingPair) {
        setPriceData(data);
        setIsStale(false);
      }
    });
    
    // Check for initial data
    const initialData = priceManager.getPriceData(tradingPair);
    if (initialData) {
      setPriceData(initialData);
      setIsStale(priceManager.isPriceStale(tradingPair));
    }
    
    // Check for stale data periodically
    const staleCheck = setInterval(() => {
      setIsStale(priceManager.isPriceStale(tradingPair));
    }, 5000);
    
    return () => {
      unsubscribe();
      clearInterval(staleCheck);
    };
  }, [tradingPair]);
  
  return {
    price: priceData?.price,
    change: priceData?.change,
    timestamp: priceData?.timestamp,
    isStale
  };
}
```

### 3. PnL Calculation Hook

```javascript
export function useRealtimePnL(position) {
  const { price: currentPrice } = usePrice(position.trading_pair);
  
  const pnl = useMemo(() => {
    if (!currentPrice || !position) return null;
    
    const priceChange = currentPrice - position.entry_price;
    const multiplier = position.direction === 'LONG' ? 1 : -1;
    
    return {
      pnl: position.position_size * priceChange * multiplier,
      returnPercentage: ((priceChange / position.entry_price) * 100) * multiplier,
      currentPrice
    };
  }, [currentPrice, position]);
  
  return pnl;
}
```

### 4. Component Usage Examples

```javascript
// Position component
function PositionRow({ position }) {
  const { price, isStale } = usePrice(position.trading_pair);
  const pnl = useRealtimePnL(position);
  
  return (
    <tr>
      <td>{position.trading_pair}</td>
      <td className={isStale ? 'price-stale' : 'price-fresh'}>
        ${price?.toLocaleString()}
      </td>
      <td className={pnl?.pnl >= 0 ? 'profit' : 'loss'}>
        ${pnl?.pnl.toFixed(2)} ({pnl?.returnPercentage.toFixed(2)}%)
      </td>
    </tr>
  );
}

// Trading sidebar
function TradingSidebar({ tradingPair }) {
  const { price, change } = usePrice(tradingPair);
  
  return (
    <div>
      <h2>{tradingPair}</h2>
      <div className="price">${price?.toLocaleString()}</div>
      <div className={change >= 0 ? 'positive' : 'negative'}>
        {change >= 0 ? '+' : ''}{change?.toFixed(2)}%
      </div>
    </div>
  );
}
```

---

## 📊 Performance Considerations

### Memory Management
```javascript
class PriceManager {
  constructor() {
    this.maxHistoryLength = 1000; // Keep last 1000 price points
    this.cleanupInterval = null;
  }
  
  startCleanup() {
    // Clean old price history every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.priceHistory.forEach((history, pair) => {
        if (history.length > this.maxHistoryLength) {
          this.priceHistory.set(pair, history.slice(-this.maxHistoryLength));
        }
      });
    }, 300000);
  }
}
```

### Bandwidth Optimization
```javascript
// Only subscribe to pairs user is actually trading
class SmartSubscriptionManager {
  constructor(priceManager) {
    this.priceManager = priceManager;
    this.activePairs = new Set();
  }
  
  addPair(pair) {
    if (!this.activePairs.has(pair)) {
      this.activePairs.add(pair);
      this.subscribeToBackend(pair);
    }
  }
  
  removePair(pair) {
    this.activePairs.delete(pair);
    if (this.activePairs.size === 0) {
      this.unsubscribeFromBackend(pair);
    }
  }
}
```

---

## 🔗 Integration with Existing Code

### Enhanced Position Hook
```javascript
export function useEnhancedPositions() {
  const { connectionStats, wsHealthy, refreshAll } = useBasePositions();
  
  // Integrate with price manager
  useEffect(() => {
    if (wsHealthy) {
      // WebSocket is healthy, prices come from WS
      priceManager.setSource('websocket');
    } else {
      // Fallback to REST polling
      priceManager.setSource('rest');
      priceManager.startRestPolling();
    }
  }, [wsHealthy]);
  
  return {
    connectionStats,
    wsHealthy,
    refreshAll,
    priceManager
  };
}
```

---

## ❓ Questions for Backend Team

### Technical Implementation
1. **Which option do you prefer?** (WebSocket integration vs dedicated price WS vs hybrid)
2. **Update frequency:** What's optimal for real-time trading? (1s, 5s, 10s?)
3. **Data format:** Should we include volume, 24h change, etc.?
4. **Bandwidth:** How much additional load would price streaming add?

### Scalability
1. **Pair filtering:** Can users subscribe to specific trading pairs only?
2. **Connection limits:** Does price streaming count towards the 5 connection limit?
3. **Fallback strategy:** What if price WebSocket fails but position WS works?

### Data Consistency
1. **Sync guarantee:** How do you ensure price data matches position calculations?
2. **Timestamp precision:** What timestamp format for price updates?
3. **Market hours:** How are prices handled when markets are closed?

---

## 🎯 Recommended Implementation Plan

### Phase 1: Core Price Manager (Week 1)
- Implement UnifiedPriceManager class
- Create usePrice and useRealtimePnL hooks
- Update 2-3 key components to test

### Phase 2: WebSocket Integration (Week 2)
- Integrate with backend price streaming solution
- Add fallback REST polling
- Performance testing and optimization

### Phase 3: Full Rollout (Week 3)
- Update all components to use unified prices
- Add price staleness indicators
- Comprehensive testing across all trading scenarios

**Would this plan work with your backend development timeline?**

Looking forward to your feedback and guidance! 🚀