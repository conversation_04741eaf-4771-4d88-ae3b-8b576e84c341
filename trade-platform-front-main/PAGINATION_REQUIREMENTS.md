# Pagination Requirements for Position Data

## 🎯 Problem Statement

**Current Issue:** Loading all positions at once creates performance problems:
- User 13 has 111+ positions loaded instantly
- Heavy users might have 1000+ positions
- Large payload on initial WebSocket connection
- Browser memory usage increases with position count
- Table rendering becomes slow with hundreds of rows

**Impact:**
- Slow initial page load
- High memory consumption
- Poor UX for heavy traders
- Increased server load

---

## 📊 Performance Analysis

### Current Load Times (User 13 - 111 positions)
- **WebSocket Initial Load:** ~1.2s for all positions
- **Memory Usage:** ~2MB for position data
- **Table Render:** ~300ms for complete table

### Projected Load Times (1000+ positions)
- **WebSocket Initial Load:** ~10-15s (estimated)
- **Memory Usage:** ~18MB for position data  
- **Table Render:** ~3-5s for complete table
- **Browser Impact:** Potential UI freezing

---

## 🏗️ Proposed Pagination Architecture

### 1. Backend API Pagination

#### Enhanced Position Endpoints
```http
GET /api/positions?user_id={user_id}&page={page}&limit={limit}&status={status}
```

**Request Parameters:**
```json
{
  "user_id": "13",
  "page": 1,           // Page number (1-based)
  "limit": 50,         // Items per page (default: 50, max: 100)
  "status": "OPEN",    // Optional filter
  "sort_by": "opened_at", // opened_at, pnl, trading_pair
  "sort_order": "desc"    // asc, desc
}
```

**Response Format:**
```json
{
  "data": [
    {
      "id": 123,
      "trading_pair": "BTC/USDT",
      // ... position fields
    }
  ],
  "pagination": {
    "current_page": 1,
    "total_pages": 23,
    "total_items": 1127,
    "items_per_page": 50,
    "has_next": true,
    "has_prev": false
  },
  "summary": {
    "total_pnl": 1250.45,
    "total_margin": 50000.0,
    "open_positions": 45,
    "pending_orders": 5
  }
}
```

#### WebSocket Initial Data Optimization
```javascript
// On WebSocket connection, send only first page + summary
{
  "action": "positions",
  "data": {
    "positions": [...], // First 50 positions
    "pagination": {
      "current_page": 1,
      "total_pages": 23,
      "total_items": 1127
    },
    "summary": {
      "total_pnl": 1250.45,
      "total_positions": 1127
    }
  }
}
```

### 2. Frontend Implementation

#### Enhanced Position Service
```typescript
// src/features/trade/api/position-service.ts

export interface PositionPaginationParams {
  page: number;
  limit: number;
  status?: PositionStatus;
  sortBy?: 'opened_at' | 'pnl' | 'trading_pair' | 'margin';
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedPositionsResponse {
  data: Position[];
  pagination: {
    current_page: number;
    total_pages: number;
    total_items: number;
    items_per_page: number;
    has_next: boolean;
    has_prev: boolean;
  };
  summary: {
    total_pnl: number;
    total_margin: number;
    open_positions: number;
    pending_orders: number;
  };
}

/**
 * Get paginated positions
 */
export async function getPaginatedPositions(
  userId: string,
  params: PositionPaginationParams
): Promise<PaginatedPositionsResponse> {
  logger.info("Fetching paginated positions", { userId, params });

  try {
    const queryParams = new URLSearchParams({
      user_id: userId,
      page: params.page.toString(),
      limit: params.limit.toString(),
      ...(params.status && { status: params.status }),
      ...(params.sortBy && { sort_by: params.sortBy }),
      ...(params.sortOrder && { sort_order: params.sortOrder }),
    });

    return await httpClient.get<PaginatedPositionsResponse>(
      `/api/positions/paginated?${queryParams}`,
      { auth: true }
    );
  } catch (error) {
    logger.error("Error fetching paginated positions", { userId, params, error });
    throw error;
  }
}

/**
 * Get position summary only (for dashboard widgets)
 */
export async function getPositionSummary(userId: string): Promise<{
  total_pnl: number;
  total_margin: number;
  open_positions: number;
  pending_orders: number;
  top_performers: Position[];
  worst_performers: Position[];
}> {
  return await httpClient.get(`/api/positions/summary/${userId}`, { auth: true });
}
```

#### Enhanced React Hook
```typescript
// src/features/trade/api/hooks/usePaginatedPositions.ts

export interface UsePaginatedPositionsOptions {
  enabled?: boolean;
  pageSize?: number;
  initialPage?: number;
  status?: PositionStatus;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export function usePaginatedPositions({
  enabled = true,
  pageSize = 50,
  initialPage = 1,
  status,
  sortBy = 'opened_at',
  sortOrder = 'desc'
}: UsePaginatedPositionsOptions = {}) {
  const userId = useAuthStore((state) => state.auth.user?.id);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [allLoadedPositions, setAllLoadedPositions] = useState<Map<number, Position[]>>(new Map());
  
  // Main paginated query
  const {
    data: paginatedData,
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['positions-paginated', userId, currentPage, pageSize, status, sortBy, sortOrder],
    queryFn: () => getPaginatedPositions(userId!, {
      page: currentPage,
      limit: pageSize,
      status,
      sortBy,
      sortOrder
    }),
    enabled: enabled && !!userId,
    staleTime: 10000, // 10 seconds
    keepPreviousData: true // Keep previous page while loading new one
  });

  // Summary query (independent of pagination)
  const {
    data: summary,
    isLoading: isSummaryLoading
  } = useQuery({
    queryKey: ['positions-summary', userId],
    queryFn: () => getPositionSummary(userId!),
    enabled: enabled && !!userId,
    staleTime: 30000, // 30 seconds
  });

  // Cache management
  useEffect(() => {
    if (paginatedData?.data) {
      setAllLoadedPositions(prev => {
        const updated = new Map(prev);
        updated.set(currentPage, paginatedData.data);
        return updated;
      });
    }
  }, [paginatedData, currentPage]);

  // Navigation helpers
  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= (paginatedData?.pagination.total_pages || 1)) {
      setCurrentPage(page);
    }
  }, [paginatedData?.pagination.total_pages]);

  const goToNextPage = useCallback(() => {
    if (paginatedData?.pagination.has_next) {
      setCurrentPage(prev => prev + 1);
    }
  }, [paginatedData?.pagination.has_next]);

  const goToPrevPage = useCallback(() => {
    if (paginatedData?.pagination.has_prev) {
      setCurrentPage(prev => prev - 1);
    }
  }, [paginatedData?.pagination.has_prev]);

  // Get all loaded positions (for search/filter operations)
  const getAllLoadedPositions = useCallback(() => {
    const allPositions: Position[] = [];
    for (const positions of allLoadedPositions.values()) {
      allPositions.push(...positions);
    }
    return allPositions;
  }, [allLoadedPositions]);

  return {
    // Data
    positions: paginatedData?.data || [],
    pagination: paginatedData?.pagination,
    summary,
    
    // State
    currentPage,
    isLoading,
    isSummaryLoading,
    error,
    
    // Navigation
    goToPage,
    goToNextPage,
    goToPrevPage,
    
    // Utilities
    refetch,
    getAllLoadedPositions,
    
    // Cache info
    loadedPages: Array.from(allLoadedPositions.keys()),
    totalLoadedPositions: getAllLoadedPositions().length
  };
}
```

#### Enhanced Positions Table Component
```typescript
// src/components/trade/PositionsDataTable/PaginatedPositionsTable.tsx

export function PaginatedPositionsTable() {
  const [activeTab, setActiveTab] = useState<'open' | 'pending' | 'history'>('open');
  const [pageSize, setPageSize] = useState(50);
  const [sortConfig, setSortConfig] = useState({ 
    sortBy: 'opened_at', 
    sortOrder: 'desc' as const 
  });

  const {
    positions,
    pagination,
    summary,
    currentPage,
    isLoading,
    goToPage,
    goToNextPage,
    goToPrevPage
  } = usePaginatedPositions({
    status: getStatusFromTab(activeTab),
    pageSize,
    ...sortConfig
  });

  const handleSort = (field: string) => {
    setSortConfig(prev => ({
      sortBy: field,
      sortOrder: prev.sortBy === field && prev.sortOrder === 'desc' ? 'asc' : 'desc'
    }));
  };

  return (
    <div className="paginated-positions-table">
      {/* Summary Stats */}
      <div className="summary-cards">
        <SummaryCard 
          title="Total P&L" 
          value={summary?.total_pnl} 
          format="currency" 
        />
        <SummaryCard 
          title="Open Positions" 
          value={summary?.open_positions} 
        />
        <SummaryCard 
          title="Pending Orders" 
          value={summary?.pending_orders} 
        />
      </div>

      {/* Tabs */}
      <div className="tab-container">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="open">
              Open ({summary?.open_positions || 0})
            </TabsTrigger>
            <TabsTrigger value="pending">
              Pending ({summary?.pending_orders || 0})
            </TabsTrigger>
            <TabsTrigger value="history">
              History
            </TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Table Controls */}
      <div className="table-controls">
        <div className="page-size-selector">
          <Select value={pageSize.toString()} onValueChange={(v) => setPageSize(Number(v))}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="25">25</SelectItem>
              <SelectItem value="50">50</SelectItem>
              <SelectItem value="100">100</SelectItem>
            </SelectContent>
          </Select>
          <span className="text-sm text-muted-foreground">per page</span>
        </div>

        <div className="pagination-info">
          {pagination && (
            <span className="text-sm text-muted-foreground">
              Showing {((currentPage - 1) * pageSize) + 1} to{' '}
              {Math.min(currentPage * pageSize, pagination.total_items)} of{' '}
              {pagination.total_items} positions
            </span>
          )}
        </div>
      </div>

      {/* Data Table */}
      <div className="table-container">
        {isLoading && currentPage === 1 ? (
          <PositionsTableSkeleton />
        ) : (
          <DataTable
            data={positions}
            columns={getColumnsWithSort(handleSort, sortConfig)}
            loading={isLoading}
          />
        )}
      </div>

      {/* Pagination Controls */}
      {pagination && pagination.total_pages > 1 && (
        <div className="pagination-controls">
          <Pagination>
            <PaginationContent>
              <PaginationItem>
                <PaginationPrevious 
                  onClick={goToPrevPage}
                  disabled={!pagination.has_prev}
                />
              </PaginationItem>
              
              {/* Page numbers */}
              {Array.from({ length: Math.min(pagination.total_pages, 7) }, (_, i) => {
                const pageNum = getPageNumber(i, currentPage, pagination.total_pages);
                return (
                  <PaginationItem key={pageNum}>
                    <PaginationLink
                      onClick={() => goToPage(pageNum)}
                      isActive={pageNum === currentPage}
                    >
                      {pageNum}
                    </PaginationLink>
                  </PaginationItem>
                );
              })}
              
              <PaginationItem>
                <PaginationNext
                  onClick={goToNextPage}
                  disabled={!pagination.has_next}
                />
              </PaginationItem>
            </PaginationContent>
          </Pagination>
        </div>
      )}
    </div>
  );
}
```

### 3. WebSocket Integration with Pagination

#### Smart Position Updates
```typescript
// Enhanced WebSocket handler for paginated data
class PaginatedPositionManager {
  constructor(queryClient: QueryClient) {
    this.queryClient = queryClient;
    this.currentPages = new Map(); // Track what pages are loaded
  }

  handlePositionUpdate(positionUpdate: Position) {
    const userId = positionUpdate.user_id;
    
    // Update position in all relevant paginated queries
    this.queryClient.setQueriesData(
      { queryKey: ['positions-paginated', userId] },
      (oldData: PaginatedPositionsResponse | undefined) => {
        if (!oldData) return oldData;
        
        const updatedPositions = oldData.data.map(pos => 
          pos.id === positionUpdate.id ? positionUpdate : pos
        );
        
        return {
          ...oldData,
          data: updatedPositions
        };
      }
    );

    // Update summary if needed
    this.queryClient.invalidateQueries({
      queryKey: ['positions-summary', userId]
    });
  }

  handleNewPosition(newPosition: Position) {
    const userId = newPosition.user_id;
    
    // If new position matches current filters, add to first page
    this.queryClient.setQueriesData(
      { queryKey: ['positions-paginated', userId, 1] },
      (oldData: PaginatedPositionsResponse | undefined) => {
        if (!oldData) return oldData;

        return {
          ...oldData,
          data: [newPosition, ...oldData.data.slice(0, -1)], // Add to top, remove last
          pagination: {
            ...oldData.pagination,
            total_items: oldData.pagination.total_items + 1
          }
        };
      }
    );

    // Invalidate summary
    this.queryClient.invalidateQueries({
      queryKey: ['positions-summary', userId]
    });
  }

  handlePositionClosed(closedPosition: Position) {
    const userId = closedPosition.user_id;
    
    // Remove from current pages
    this.queryClient.setQueriesData(
      { queryKey: ['positions-paginated', userId] },
      (oldData: PaginatedPositionsResponse | undefined) => {
        if (!oldData) return oldData;
        
        return {
          ...oldData,
          data: oldData.data.filter(pos => pos.id !== closedPosition.id),
          pagination: {
            ...oldData.pagination,
            total_items: oldData.pagination.total_items - 1
          }
        };
      }
    );

    // Update summary
    this.queryClient.invalidateQueries({
      queryKey: ['positions-summary', userId]
    });
  }
}
```

### 4. Performance Optimizations

#### Virtual Scrolling Option
```typescript
// For very large datasets, implement virtual scrolling
import { FixedSizeList as List } from 'react-window';

export function VirtualizedPositionsTable({ positions }: { positions: Position[] }) {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <PositionRow position={positions[index]} />
    </div>
  );

  return (
    <List
      height={600} // Table height
      itemCount={positions.length}
      itemSize={60} // Row height
      width="100%"
    >
      {Row}
    </List>
  );
}
```

#### Intelligent Prefetching
```typescript
// Prefetch next page when user is near the bottom
export function usePositionPrefetch(currentPage: number, hasNext: boolean) {
  const queryClient = useQueryClient();
  const userId = useAuthStore(state => state.auth.user?.id);

  const prefetchNextPage = useCallback(() => {
    if (hasNext && userId) {
      queryClient.prefetchQuery({
        queryKey: ['positions-paginated', userId, currentPage + 1],
        queryFn: () => getPaginatedPositions(userId, {
          page: currentPage + 1,
          limit: 50
        }),
        staleTime: 5 * 60 * 1000 // 5 minutes
      });
    }
  }, [currentPage, hasNext, userId, queryClient]);

  // Prefetch when user scrolls to 80% of current page
  useEffect(() => {
    const handleScroll = throttle(() => {
      const scrollPercent = (window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100;
      if (scrollPercent > 80) {
        prefetchNextPage();
      }
    }, 200);

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [prefetchNextPage]);
}
```

---

## 📊 Expected Performance Improvements

### Load Time Improvements
| Scenario | Current | With Pagination | Improvement |
|----------|---------|-----------------|-------------|
| 111 positions | 1.2s | 0.3s | 75% faster |
| 500 positions | ~5s | 0.3s | 94% faster |
| 1000+ positions | ~10s | 0.3s | 97% faster |

### Memory Usage
| Scenario | Current | With Pagination | Savings |
|----------|---------|-----------------|---------|
| 111 positions | 2MB | 1MB | 50% less |
| 500 positions | 9MB | 1MB | 89% less |
| 1000+ positions | 18MB | 1MB | 94% less |

---

## 🔧 Backend Requirements

### New Endpoints Needed
```http
GET /api/positions/paginated?user_id={id}&page={p}&limit={l}&status={s}
GET /api/positions/summary/{user_id}
GET /api/positions/search?user_id={id}&query={q}&page={p}&limit={l}
```

### WebSocket Enhancements
- Send pagination metadata with initial position data
- Smart position update routing (only update loaded pages)
- Summary updates via WebSocket

### Database Optimizations
- Add indexes for sorting columns (opened_at, pnl, trading_pair)
- Optimize queries for large result sets
- Consider database-level pagination optimization

---

## 🎯 Implementation Priority

### Phase 1: Basic Pagination (Week 1)
- Backend pagination endpoints
- Frontend paginated hooks
- Basic table with pagination controls

### Phase 2: Performance (Week 2)
- WebSocket integration with pagination
- Smart caching and prefetching
- Summary widgets

### Phase 3: Advanced Features (Week 3)
- Search functionality
- Advanced filtering
- Virtual scrolling for extreme cases

This pagination system will dramatically improve performance for heavy users while maintaining real-time functionality!