
/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/about/page.tsx
  31:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/dashboard/deposit/page.tsx
  309:31  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
  370:39  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
  565:53  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  570:51  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                 @typescript-eslint/no-explicit-any
  580:17  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
  646:19  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
  681:27  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                                                                                                          react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/dashboard/layout.tsx
  59:14  warning  'decodeError' is defined but never used  no-unused-vars
  59:14  warning  'decodeError' is defined but never used  @typescript-eslint/no-unused-vars
  64:12  warning  'error' is defined but never used        no-unused-vars
  64:12  warning  'error' is defined but never used        @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/dashboard/link/page.tsx
  94:25  warning  '_value' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/dashboard/settings/profile/ProfileEditor.tsx
  203:17  warning  Visible, non-interactive elements with click handlers must have at least one keyboard listener                                                                                                     jsx-a11y/click-events-have-key-events
  203:17  warning  Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element  jsx-a11y/no-static-element-interactions

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/dashboard/settings/security/page.tsx
   16:60  warning  'otp' is defined but never used                                                                                                                                                                                                                                                                                                                                    no-unused-vars
  102:11  warning  The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md  jsx-a11y/anchor-is-valid
  172:28  warning  'otp' is defined but never used                                                                                                                                                                                                                                                                                                                                    no-unused-vars
  172:28  warning  'otp' is defined but never used                                                                                                                                                                                                                                                                                                                                    @typescript-eslint/no-unused-vars
  200:19  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                                                                                                                                                                                    react/no-unescaped-entities
  244:19  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                                                                                                                                                                                    react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/dashboard/support/page.tsx
  218:14  warning  '_value' is defined but never used                                                                                                                                                                 no-unused-vars
  221:5   warning  Visible, non-interactive elements with click handlers must have at least one keyboard listener                                                                                                     jsx-a11y/click-events-have-key-events
  221:5   warning  Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element  jsx-a11y/no-static-element-interactions
  615:28  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/dashboard/withdraw/page.tsx
    7:8   warning  Using exported name 'WithdrawModal' as identifier for default import                                                                                                                                                                                                                     import/no-named-as-default
   70:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                        no-unused-vars
   70:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                        @typescript-eslint/no-unused-vars
  178:21  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
  187:21  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/faq/page.tsx
  143:71  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  159:29  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  163:33  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  163:49  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  166:31  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  170:35  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  257:43  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  257:52  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  529:90  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  732:50  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  751:19  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/guide/page.tsx
   24:16  warning  'TabsList' is defined but never used                                                                                                                                                               no-unused-vars
   24:16  warning  'TabsList' is defined but never used                                                                                                                                                               @typescript-eslint/no-unused-vars
   24:26  warning  'TabsTrigger' is defined but never used                                                                                                                                                            no-unused-vars
   24:26  warning  'TabsTrigger' is defined but never used                                                                                                                                                            @typescript-eslint/no-unused-vars
   72:5   warning  Visible, non-interactive elements with click handlers must have at least one keyboard listener                                                                                                     jsx-a11y/click-events-have-key-events
   72:5   warning  Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element  jsx-a11y/no-static-element-interactions
  370:26  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  380:43  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  389:83  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  389:90  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  460:41  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  460:57  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  461:37  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  461:54  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  474:41  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  474:57  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  475:37  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  475:46  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  528:39  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  528:48  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  529:35  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  529:68  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  532:30  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  582:41  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  582:50  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  583:37  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  583:62  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  584:60  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  597:41  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  597:50  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  598:38  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  598:70  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  633:96  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  643:74  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  648:50  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  657:66  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities
  763:57  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                    react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/page.tsx
    9:10  warning  'Button' is defined but never used                               no-unused-vars
    9:10  warning  'Button' is defined but never used                               @typescript-eslint/no-unused-vars
   10:10  warning  'Input' is defined but never used                                no-unused-vars
   10:10  warning  'Input' is defined but never used                                @typescript-eslint/no-unused-vars
  106:9   warning  Unexpected console statement                                     no-console
  109:7   warning  Unexpected console statement                                     no-console
  371:49  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/privacy/page.tsx
   40:25  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   40:29  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   40:31  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   40:35  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   40:40  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   40:44  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   43:17  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   43:26  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
   71:30  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  321:21  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  321:32  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  333:34  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  333:46  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  337:73  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  337:84  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  394:79  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  416:32  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  416:47  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/privacy/short/page.tsx
  144:34  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  188:91  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities
  189:19  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`  react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/settings/layout.tsx
  3:10  warning  'jwtDecode' is defined but never used  no-unused-vars
  3:10  warning  'jwtDecode' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/sitemap/page.tsx
   66:15  warning  Missing an explicit type attribute for button                    react/button-has-type
  267:27  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities
  267:43  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/support/layout.tsx
   3:10  warning  'AnimatePresence' is defined but never used          no-unused-vars
   3:10  warning  'AnimatePresence' is defined but never used          @typescript-eslint/no-unused-vars
  10:7   warning  'LoadingSpinner' is assigned a value but never used  no-unused-vars
  10:7   warning  'LoadingSpinner' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/support/page.tsx
  210:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  253:16  warning  '_' is defined but never used             no-unused-vars
  253:16  warning  '_' is defined but never used             @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/trade/page.tsx
  164:9   warning  Unexpected console statement              no-console
  181:43  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  311:25  warning  Unexpected console statement              no-console
  418:35  warning  Unexpected console statement              no-console

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/utils/api.ts
     4:10  warning  'HttpClient' is defined but never used                 no-unused-vars
     4:10  warning  'HttpClient' is defined but never used                 @typescript-eslint/no-unused-vars
     9:1   warning  There should be no empty line within import group      import/order
    16:7   warning  'BALANCE_WS_URL' is assigned a value but never used    no-unused-vars
    16:7   warning  'BALANCE_WS_URL' is assigned a value but never used    @typescript-eslint/no-unused-vars
    52:13  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
    62:54  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
    65:14  warning  'e' is defined but never used                          no-unused-vars
    65:14  warning  'e' is defined but never used                          @typescript-eslint/no-unused-vars
    99:31  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   141:9   warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   474:11  warning  'BalanceUpdateMessage' is defined but never used       no-unused-vars
   474:11  warning  'BalanceUpdateMessage' is defined but never used       @typescript-eslint/no-unused-vars
   484:5   warning  'balanceWebSocket' is assigned a value but never used  no-unused-vars
   484:5   warning  'balanceWebSocket' is assigned a value but never used  @typescript-eslint/no-unused-vars
   486:3   warning  'balances' is defined but never used                   no-unused-vars
   487:3   warning  'fixedBalanceData' is defined but never used           no-unused-vars
   509:14  warning  'balances' is defined but never used                   no-unused-vars
   509:39  warning  'fixedBalanceData' is defined but never used           no-unused-vars
   622:10  warning  'initBalanceWebSocket' is defined but never used       no-unused-vars
   622:10  warning  'initBalanceWebSocket' is defined but never used       @typescript-eslint/no-unused-vars
   716:5   warning  Unexpected console statement                           no-console
   908:66  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
   973:12  warning  'error' is defined but never used                      no-unused-vars
   973:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
   998:12  warning  'error' is defined but never used                      no-unused-vars
   998:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1041:12  warning  'error' is defined but never used                      no-unused-vars
  1041:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1089:12  warning  'error' is defined but never used                      no-unused-vars
  1089:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1112:12  warning  'error' is defined but never used                      no-unused-vars
  1112:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1124:12  warning  'error' is defined but never used                      no-unused-vars
  1124:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1135:12  warning  'error' is defined but never used                      no-unused-vars
  1135:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1168:16  warning  'decodeError' is defined but never used                no-unused-vars
  1168:16  warning  'decodeError' is defined but never used                @typescript-eslint/no-unused-vars
  1187:20  warning  'atobError' is defined but never used                  no-unused-vars
  1187:20  warning  'atobError' is defined but never used                  @typescript-eslint/no-unused-vars
  1194:14  warning  'error' is defined but never used                      no-unused-vars
  1194:14  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1244:3   warning  'address' is defined but never used                    no-unused-vars
  1244:3   warning  'address' is defined but never used                    @typescript-eslint/no-unused-vars
  1263:12  warning  'error' is defined but never used                      no-unused-vars
  1263:12  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1292:14  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1319:10  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1371:12  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1422:14  warning  'err' is defined but never used                        no-unused-vars
  1422:14  warning  'err' is defined but never used                        @typescript-eslint/no-unused-vars
  1475:82  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1491:38  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1613:18  warning  'err' is defined but never used                        no-unused-vars
  1613:18  warning  'err' is defined but never used                        @typescript-eslint/no-unused-vars
  1647:22  warning  'err' is defined but never used                        no-unused-vars
  1647:22  warning  'err' is defined but never used                        @typescript-eslint/no-unused-vars
  1705:28  warning  'error' is defined but never used                      no-unused-vars
  1705:28  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1745:45  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1796:17  warning  'error' is defined but never used                      no-unused-vars
  1796:17  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1799:14  warning  'error' is defined but never used                      no-unused-vars
  1799:14  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1823:14  warning  'err' is defined but never used                        no-unused-vars
  1823:14  warning  'err' is defined but never used                        @typescript-eslint/no-unused-vars
  1849:18  warning  'err' is defined but never used                        no-unused-vars
  1849:18  warning  'err' is defined but never used                        @typescript-eslint/no-unused-vars
  1884:53  warning  'error' is defined but never used                      no-unused-vars
  1884:53  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  1892:54  warning  'data' is defined but never used                       no-unused-vars
  1892:60  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1900:57  warning  'data' is defined but never used                       no-unused-vars
  1900:63  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1910:51  warning  Unexpected any. Specify a different type               @typescript-eslint/no-explicit-any
  1948:19  warning  '_' is defined but never used                          no-unused-vars
  1948:19  warning  '_' is defined but never used                          @typescript-eslint/no-unused-vars
  2024:14  warning  '_' is defined but never used                          no-unused-vars
  2024:14  warning  '_' is defined but never used                          @typescript-eslint/no-unused-vars
  2083:14  warning  '_' is defined but never used                          no-unused-vars
  2083:14  warning  '_' is defined but never used                          @typescript-eslint/no-unused-vars
  2137:14  warning  '_' is defined but never used                          no-unused-vars
  2137:14  warning  '_' is defined but never used                          @typescript-eslint/no-unused-vars
  2188:17  warning  'error' is defined but never used                      no-unused-vars
  2188:17  warning  'error' is defined but never used                      @typescript-eslint/no-unused-vars
  2288:19  warning  '_' is defined but never used                          no-unused-vars
  2288:19  warning  '_' is defined but never used                          @typescript-eslint/no-unused-vars
  2345:19  warning  '_' is defined but never used                          no-unused-vars
  2345:19  warning  '_' is defined but never used                          @typescript-eslint/no-unused-vars
  2379:19  warning  '_' is defined but never used                          no-unused-vars
  2379:19  warning  '_' is defined but never used                          @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/app/utils/theme-checker.ts
  31:7  warning  'THEME_VARIABLES' is assigned a value but never used  no-unused-vars
  31:7  warning  'THEME_VARIABLES' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/charts/asset-allocation-chart.tsx
   31:35  warning  Unexpected any. Specify a different type   @typescript-eslint/no-explicit-any
   43:5   warning  'name' is assigned a value but never used  no-unused-vars
   43:5   warning  'name' is assigned a value but never used  @typescript-eslint/no-unused-vars
  132:26  warning  Unexpected any. Specify a different type   @typescript-eslint/no-explicit-any
  140:26  warning  Unexpected any. Specify a different type   @typescript-eslint/no-explicit-any
  188:32  warning  'entry' is defined but never used          no-unused-vars
  188:32  warning  'entry' is defined but never used          @typescript-eslint/no-unused-vars
  188:39  warning  'index' is defined but never used          no-unused-vars
  188:39  warning  'index' is defined but never used          @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/charts/asset-performance-chart.tsx
  3:10  warning  'ChevronDown' is defined but never used  no-unused-vars
  3:10  warning  'ChevronDown' is defined but never used  @typescript-eslint/no-unused-vars
  3:23  warning  'ChevronUp' is defined but never used    no-unused-vars
  3:23  warning  'ChevronUp' is defined but never used    @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/charts/balance-history-chart.tsx
   19:16  warning  'TabsContent' is defined but never used              no-unused-vars
   19:16  warning  'TabsContent' is defined but never used              @typescript-eslint/no-unused-vars
   33:43  warning  'label' is defined but never used                    no-unused-vars
   33:43  warning  'label' is defined but never used                    @typescript-eslint/no-unused-vars
  131:22  warning  'percent' is defined but never used                  no-unused-vars
  160:24  warning  'prevData' is defined but never used                 no-unused-vars
  160:24  warning  'prevData' is defined but never used                 @typescript-eslint/no-unused-vars
  320:31  warning  'summaryLoading' is assigned a value but never used  no-unused-vars
  320:31  warning  'summaryLoading' is assigned a value but never used  @typescript-eslint/no-unused-vars
  345:13  warning  'formattedDate' is assigned a value but never used   no-unused-vars
  345:13  warning  'formattedDate' is assigned a value but never used   @typescript-eslint/no-unused-vars
  426:67  warning  Unexpected any. Specify a different type             @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/charts/transaction-chart.tsx
  257:36  warning  'entry' is defined but never used  no-unused-vars
  257:36  warning  'entry' is defined but never used  @typescript-eslint/no-unused-vars
  257:43  warning  'index' is defined but never used  no-unused-vars
  257:43  warning  'index' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/dashboard/LeftSidebar.tsx
  4:24  warning  'HelpCircleIcon' is defined but never used  no-unused-vars
  4:24  warning  'HelpCircleIcon' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/dashboard/MobileLeftSidebar.tsx
  9:3  warning  'HelpCircleIcon' is defined but never used  no-unused-vars
  9:3  warning  'HelpCircleIcon' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/header/BalanceIndicator.tsx
  30:7  warning  Unexpected console statement  no-console

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/layout/navbar/Navbar.tsx
  105:7  warning  Unexpected console statement                                    no-console
  106:7  warning  Unexpected console statement                                    no-console
  112:5  warning  'formattedTotalBalance' is assigned a value but never used      no-unused-vars
  112:5  warning  'formattedTotalBalance' is assigned a value but never used      @typescript-eslint/no-unused-vars
  113:5  warning  'formattedAvailableBalance' is assigned a value but never used  no-unused-vars
  113:5  warning  'formattedAvailableBalance' is assigned a value but never used  @typescript-eslint/no-unused-vars
  116:5  warning  'isAuthenticated' is assigned a value but never used            no-unused-vars
  116:5  warning  'isAuthenticated' is assigned a value but never used            @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/modals/DepositModal.tsx
  145:15  warning  'err' is defined but never used  no-unused-vars
  145:15  warning  'err' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/modals/rules/RulesModal.tsx
  17:9  warning  Missing an explicit type attribute for button  react/button-has-type

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/page-sections/InfoPageHeader.tsx
  4:22  warning  'LogOut' is defined but never used  no-unused-vars
  4:22  warning  'LogOut' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/OrderSidebar/OrderSidebar.tsx
   10:3   warning  'analysisService' is defined but never used                  no-unused-vars
   10:3   warning  'analysisService' is defined but never used                  @typescript-eslint/no-unused-vars
   18:3   warning  'tradingService' is defined but never used                   no-unused-vars
   18:3   warning  'tradingService' is defined but never used                   @typescript-eslint/no-unused-vars
  213:28  warning  Unexpected any. Specify a different type                     @typescript-eslint/no-explicit-any
  231:63  warning  Unexpected any. Specify a different type                     @typescript-eslint/no-explicit-any
  607:9   warning  'handlePriceChange' is assigned a value but never used       no-unused-vars
  607:9   warning  'handlePriceChange' is assigned a value but never used       @typescript-eslint/no-unused-vars
  613:9   warning  'handleAmountChange' is assigned a value but never used      no-unused-vars
  613:9   warning  'handleAmountChange' is assigned a value but never used      @typescript-eslint/no-unused-vars
  632:9   warning  'handleStopLossChange' is assigned a value but never used    no-unused-vars
  632:9   warning  'handleStopLossChange' is assigned a value but never used    @typescript-eslint/no-unused-vars
  646:9   warning  'handleTakeProfitChange' is assigned a value but never used  no-unused-vars
  646:9   warning  'handleTakeProfitChange' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/OrderSidebar/types.ts
  15:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  40:27  warning  'isExpanded' is defined but never used    no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/PairSelector/CryptoPairItem.tsx
  13:13  warning  'pair' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/PairSelector/PairSelector.tsx
  168:13  warning  Missing an explicit type attribute for button  react/button-has-type

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/PairSelector/types.ts
  10:18  warning  'selectedPair' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/PositionsTable/PositionsTable.tsx
   86:14  warning  'err' is defined but never used                                  no-unused-vars
   86:14  warning  'err' is defined but never used                                  @typescript-eslint/no-unused-vars
  117:24  warning  'positionId' is defined but never used                           no-unused-vars
  117:24  warning  'positionId' is defined but never used                           @typescript-eslint/no-unused-vars
  271:14  warning  '_' is defined but never used                                    no-unused-vars
  271:14  warning  '_' is defined but never used                                    @typescript-eslint/no-unused-vars
  332:9   warning  'filledOrdersCount' is assigned a value but never used           no-unused-vars
  332:9   warning  'filledOrdersCount' is assigned a value but never used           @typescript-eslint/no-unused-vars
  882:34  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`  react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/PositionsTable/types.ts
   6:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  17:23  warning  'position' is defined but never used      no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/ProfileContent/ProfileContent.tsx
   51:10  warning  'is2FAEnabled' is assigned a value but never used     no-unused-vars
   51:10  warning  'is2FAEnabled' is assigned a value but never used     @typescript-eslint/no-unused-vars
   51:24  warning  'setIs2FAEnabled' is assigned a value but never used  no-unused-vars
   51:24  warning  'setIs2FAEnabled' is assigned a value but never used  @typescript-eslint/no-unused-vars
   63:21  warning  Unexpected any. Specify a different type              @typescript-eslint/no-explicit-any
   88:19  warning  Unexpected any. Specify a different type              @typescript-eslint/no-explicit-any
  103:19  warning  Unexpected any. Specify a different type              @typescript-eslint/no-explicit-any
  120:19  warning  Unexpected any. Specify a different type              @typescript-eslint/no-explicit-any
  145:19  warning  Unexpected any. Specify a different type              @typescript-eslint/no-explicit-any
  168:19  warning  Unexpected any. Specify a different type              @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/ProfileMenu/ProfileMenu.tsx
   69:7   warning  Unexpected console statement       no-console
   70:7   warning  Unexpected console statement       no-console
  116:20  warning  'error' is defined but never used  no-unused-vars
  116:20  warning  'error' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/SendTipsWidget/types.ts
  12:13  warning  'amount' is defined but never used    no-unused-vars
  12:29  warning  'userId' is defined but never used    no-unused-vars
  12:45  warning  'currency' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/Sidebar/types.ts
  7:18  warning  'tab' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/TradingPair/TradingPair.tsx
   58:5   warning  Visible, non-interactive elements with click handlers must have at least one keyboard listener                                                                                                     jsx-a11y/click-events-have-key-events
   58:5   warning  Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element  jsx-a11y/no-static-element-interactions
  127:13  warning  Unexpected console statement                                                                                                                                                                       no-console

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/TradingView/TradingView.tsx
     5:3   warning  'ChartOptions' is defined but never used                                                                                                                                                                                                                                           no-unused-vars
     5:3   warning  'ChartOptions' is defined but never used                                                                                                                                                                                                                                           @typescript-eslint/no-unused-vars
    46:10  warning  'Card' is defined but never used                                                                                                                                                                                                                                                   no-unused-vars
    46:10  warning  'Card' is defined but never used                                                                                                                                                                                                                                                   @typescript-eslint/no-unused-vars
    46:16  warning  'CardHeader' is defined but never used                                                                                                                                                                                                                                             no-unused-vars
    46:16  warning  'CardHeader' is defined but never used                                                                                                                                                                                                                                             @typescript-eslint/no-unused-vars
    46:28  warning  'CardTitle' is defined but never used                                                                                                                                                                                                                                              no-unused-vars
    46:28  warning  'CardTitle' is defined but never used                                                                                                                                                                                                                                              @typescript-eslint/no-unused-vars
    46:39  warning  'CardContent' is defined but never used                                                                                                                                                                                                                                            no-unused-vars
    46:39  warning  'CardContent' is defined but never used                                                                                                                                                                                                                                            @typescript-eslint/no-unused-vars
    61:10  warning  'Tabs' is defined but never used                                                                                                                                                                                                                                                   no-unused-vars
    61:10  warning  'Tabs' is defined but never used                                                                                                                                                                                                                                                   @typescript-eslint/no-unused-vars
    61:16  warning  'TabsList' is defined but never used                                                                                                                                                                                                                                               no-unused-vars
    61:16  warning  'TabsList' is defined but never used                                                                                                                                                                                                                                               @typescript-eslint/no-unused-vars
    61:26  warning  'TabsTrigger' is defined but never used                                                                                                                                                                                                                                            no-unused-vars
    61:26  warning  'TabsTrigger' is defined but never used                                                                                                                                                                                                                                            @typescript-eslint/no-unused-vars
    68:28  warning  'PairInfo' is defined but never used                                                                                                                                                                                                                                               no-unused-vars
    68:28  warning  'PairInfo' is defined but never used                                                                                                                                                                                                                                               @typescript-eslint/no-unused-vars
    68:49  warning  'OrderLineInfo' is defined but never used                                                                                                                                                                                                                                          no-unused-vars
    68:49  warning  'OrderLineInfo' is defined but never used                                                                                                                                                                                                                                          @typescript-eslint/no-unused-vars
    84:11  warning  'TimeframeHandler' is defined but never used                                                                                                                                                                                                                                       no-unused-vars
    84:11  warning  'TimeframeHandler' is defined but never used                                                                                                                                                                                                                                       @typescript-eslint/no-unused-vars
    87:28  warning  'timestamp' is defined but never used                                                                                                                                                                                                                                              no-unused-vars
    88:25  warning  'timestamp' is defined but never used                                                                                                                                                                                                                                              no-unused-vars
   149:10  warning  'computeBollingerBands' is defined but never used                                                                                                                                                                                                                                  no-unused-vars
   149:10  warning  'computeBollingerBands' is defined but never used                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   483:10  warning  'showMobileControls' is assigned a value but never used                                                                                                                                                                                                                            no-unused-vars
   483:10  warning  'showMobileControls' is assigned a value but never used                                                                                                                                                                                                                            @typescript-eslint/no-unused-vars
   483:30  warning  'setShowMobileControls' is assigned a value but never used                                                                                                                                                                                                                         no-unused-vars
   483:30  warning  'setShowMobileControls' is assigned a value but never used                                                                                                                                                                                                                         @typescript-eslint/no-unused-vars
   485:10  warning  'showDrawingTools' is assigned a value but never used                                                                                                                                                                                                                              no-unused-vars
   485:10  warning  'showDrawingTools' is assigned a value but never used                                                                                                                                                                                                                              @typescript-eslint/no-unused-vars
   485:28  warning  'setShowDrawingTools' is assigned a value but never used                                                                                                                                                                                                                           no-unused-vars
   485:28  warning  'setShowDrawingTools' is assigned a value but never used                                                                                                                                                                                                                           @typescript-eslint/no-unused-vars
   506:10  warning  'oraclePrice' is assigned a value but never used                                                                                                                                                                                                                                   no-unused-vars
   506:10  warning  'oraclePrice' is assigned a value but never used                                                                                                                                                                                                                                   @typescript-eslint/no-unused-vars
   641:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
   641:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   650:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
   650:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   659:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
   659:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   686:14  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
   686:14  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   704:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
   704:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   723:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
   723:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   735:9   warning  Unexpected console statement                                                                                                                                                                                                                                                       no-console
   762:13  warning  Unexpected console statement                                                                                                                                                                                                                                                       no-console
   771:13  warning  Unexpected console statement                                                                                                                                                                                                                                                       no-console
   782:21  warning  'errorMessage' is assigned a value but never used                                                                                                                                                                                                                                  no-unused-vars
   782:21  warning  'errorMessage' is assigned a value but never used                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   795:20  warning  'err' is defined but never used                                                                                                                                                                                                                                                    no-unused-vars
   795:20  warning  'err' is defined but never used                                                                                                                                                                                                                                                    @typescript-eslint/no-unused-vars
   806:7   warning  Unexpected console statement                                                                                                                                                                                                                                                       no-console
   812:6   warning  React Hook useCallback has a missing dependency: 'handleWebSocketMessage'. Either include it or remove the dependency array                                                                                                                                                        react-hooks/exhaustive-deps
   874:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
   874:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
   940:24  warning  Forbidden non-null assertion                                                                                                                                                                                                                                                       @typescript-eslint/no-non-null-assertion
  1005:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
  1005:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
  1038:11  warning  'isTablet' is assigned a value but never used                                                                                                                                                                                                                                      no-unused-vars
  1038:11  warning  'isTablet' is assigned a value but never used                                                                                                                                                                                                                                      @typescript-eslint/no-unused-vars
  1053:11  warning  'mainChartProportion' is assigned a value but never used                                                                                                                                                                                                                           no-unused-vars
  1053:11  warning  'mainChartProportion' is assigned a value but never used                                                                                                                                                                                                                           @typescript-eslint/no-unused-vars
  1425:16  warning  'e' is defined but never used                                                                                                                                                                                                                                                      no-unused-vars
  1425:16  warning  'e' is defined but never used                                                                                                                                                                                                                                                      @typescript-eslint/no-unused-vars
  1441:6   warning  React Hook useCallback has a missing dependency: 'userInteracted'. Either include it or remove the dependency array                                                                                                                                                                react-hooks/exhaustive-deps
  1454:6   warning  React Hook useEffect has missing dependencies: 'chartData.length', 'initializeChart', and 'updateOrderLines'. Either include them or remove the dependency array                                                                                                                   react-hooks/exhaustive-deps
  1707:6   warning  React Hook useCallback has missing dependencies: 'isRealTimeMode' and 'userInteracted'. Either include them or remove the dependency array                                                                                                                                         react-hooks/exhaustive-deps
  1743:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
  1743:16  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
  1791:48  warning  The ref value 'chartContainerRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'chartContainerRef.current' to a variable inside the effect, and use that variable in the cleanup function  react-hooks/exhaustive-deps
  1801:39  warning  The ref value 'scrollTimeoutRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'scrollTimeoutRef.current' to a variable inside the effect, and use that variable in the cleanup function    react-hooks/exhaustive-deps
  1807:18  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
  1807:18  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
  1816:18  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
  1816:18  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
  1879:14  warning  'error' is defined but never used                                                                                                                                                                                                                                                  no-unused-vars
  1879:14  warning  'error' is defined but never used                                                                                                                                                                                                                                                  @typescript-eslint/no-unused-vars
  1886:9   warning  'ma7Value' is assigned a value but never used                                                                                                                                                                                                                                      no-unused-vars
  1886:9   warning  'ma7Value' is assigned a value but never used                                                                                                                                                                                                                                      @typescript-eslint/no-unused-vars
  2447:19  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                                                                                                                                                                    react/no-unescaped-entities

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/TradingView/apiService.ts
   10:23  warning  'event' is defined but never used         no-unused-vars
   11:23  warning  'error' is defined but never used         no-unused-vars
   23:25  warning  'tick' is defined but never used          no-unused-vars
  119:16  warning  'error' is defined but never used         no-unused-vars
  119:16  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars
  204:20  warning  'callbackErr' is defined but never used   no-unused-vars
  204:20  warning  'callbackErr' is defined but never used   @typescript-eslint/no-unused-vars
  206:13  warning  Unexpected console statement              no-console
  209:16  warning  'err' is defined but never used           no-unused-vars
  209:16  warning  'err' is defined but never used           @typescript-eslint/no-unused-vars
  211:9   warning  Unexpected console statement              no-console
  322:14  warning  'error' is defined but never used         no-unused-vars
  322:14  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars
  337:17  warning  'data' is defined but never used          no-unused-vars
  337:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  338:21  warning  'data' is defined but never used          no-unused-vars
  338:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  379:20  warning  'e' is defined but never used             no-unused-vars
  379:20  warning  'e' is defined but never used             @typescript-eslint/no-unused-vars
  429:20  warning  'err' is defined but never used           no-unused-vars
  429:20  warning  'err' is defined but never used           @typescript-eslint/no-unused-vars
  430:13  warning  Unexpected console statement              no-console
  452:32  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  455:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  510:27  warning  Forbidden non-null assertion              @typescript-eslint/no-non-null-assertion
  515:14  warning  'error' is defined but never used         no-unused-vars
  515:14  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars
  521:47  warning  'timeframe' is defined but never used     no-unused-vars
  521:47  warning  'timeframe' is defined but never used     @typescript-eslint/no-unused-vars
  523:14  warning  Forbidden non-null assertion              @typescript-eslint/no-non-null-assertion
  531:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  533:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/TradingView/timeframeHandlers.ts
   13:16  warning  'message' is defined but never used       no-unused-vars
   34:20  warning  'message' is defined but never used       no-unused-vars
   53:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  101:32  warning  'message' is defined but never used       no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/TradingView/types.ts
  26:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  36:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  49:20  warning  'price' is defined but never used         no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/WithdrawModal/WithdrawModal.tsx
   87:14  warning  'error' is defined but never used                                                                                                                                                                  no-unused-vars
   87:14  warning  'error' is defined but never used                                                                                                                                                                  @typescript-eslint/no-unused-vars
  172:9   warning  Unexpected console statement                                                                                                                                                                       no-console
  454:11  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  484:21  warning  Visible, non-interactive elements with click handlers must have at least one keyboard listener                                                                                                     jsx-a11y/click-events-have-key-events
  484:21  warning  Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element  jsx-a11y/no-static-element-interactions
  533:21  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  554:19  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  570:15  warning  A form label must be associated with a control                                                                                                                                                     jsx-a11y/label-has-associated-control
  588:17  warning  A form label must be associated with a control                                                                                                                                                     jsx-a11y/label-has-associated-control
  632:23  warning  Visible, non-interactive elements with click handlers must have at least one keyboard listener                                                                                                     jsx-a11y/click-events-have-key-events
  632:23  warning  Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element  jsx-a11y/no-static-element-interactions
  704:13  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  730:15  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  736:15  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  773:19  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  813:15  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type
  836:60  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  836:79  warning  `"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`                                                                                                                                    react/no-unescaped-entities
  859:15  warning  Missing an explicit type attribute for button                                                                                                                                                      react/button-has-type

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/profile/WithdrawModal/types.ts
  12:18  warning  Unexpected any. Specify a different type        @typescript-eslint/no-explicit-any
  28:3   warning  'SUCCESS' is defined but never used             no-unused-vars
  29:3   warning  'OPEN_POSITIONS' is defined but never used      no-unused-vars
  30:3   warning  'INSUFFICIENT_FUNDS' is defined but never used  no-unused-vars
  31:3   warning  'INVALID_ADDRESS' is defined but never used     no-unused-vars
  32:3   warning  'ERROR' is defined but never used               no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/sidebar/CoinIcon.tsx
  24:7  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/sidebar/SliderWithDrag.tsx
  12:19  warning  'value' is defined but never used                                                                                                                                                                  no-unused-vars
  38:5   warning  Avoid non-native interactive elements. If using native HTML is not possible, add an appropriate role and support for tabbing, mouse, keyboard, and touch inputs to an interactive content element  jsx-a11y/no-static-element-interactions

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/sidebar/TradeSidebar.tsx
   39:1   warning  There should be no empty line within import group                                                                                                                                                                                                               import/order
   68:27  warning  'isExpanded' is defined but never used                                                                                                                                                                                                                          no-unused-vars
  288:14  warning  'percent' is defined but never used                                                                                                                                                                                                                             no-unused-vars
  341:14  warning  'balanceError' is assigned a value but never used                                                                                                                                                                                                               no-unused-vars
  341:14  warning  'balanceError' is assigned a value but never used                                                                                                                                                                                                               @typescript-eslint/no-unused-vars
  354:10  warning  'previousAnalysisResult' is assigned a value but never used                                                                                                                                                                                                     no-unused-vars
  354:10  warning  'previousAnalysisResult' is assigned a value but never used                                                                                                                                                                                                     @typescript-eslint/no-unused-vars
  485:6   warning  React Hook useEffect has a missing dependency: 'validateStopLossAndTakeProfit'. Either include it or remove the dependency array                                                                                                                                react-hooks/exhaustive-deps
  500:6   warning  React Hook useEffect has a missing dependency: 'validateStopLossAndTakeProfit'. Either include it or remove the dependency array                                                                                                                                react-hooks/exhaustive-deps
  572:6   warning  React Hook useEffect has a missing dependency: 'analysisResult'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setPreviousAnalysisResult' needs the current value of 'analysisResult'  react-hooks/exhaustive-deps

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/ui/carousel.tsx
  19:13  warning  'api' is defined but never used           no-unused-vars
  19:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/ui/chart.tsx
  12:4  warning  'k' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/ui/data-table.tsx
  105:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  113:49  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/ui/date-range-picker.tsx
  3:10  warning  'addDays' is defined but never used  no-unused-vars
  3:10  warning  'addDays' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/components/ui/enhanced-data-table.tsx
  47:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/auth/api/auth-api.ts
   67:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  105:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  175:12  warning  'error' is defined but never used         no-unused-vars
  175:12  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/auth/api/query-keys.ts
   5:1  warning  '@/shared/api/services/auth-service' import is duplicated  no-duplicate-imports
  11:3  warning  'RegisterResponse' is defined but never used               no-unused-vars
  11:3  warning  'RegisterResponse' is defined but never used               @typescript-eslint/no-unused-vars
  12:3  warning  'LoginResponse' is defined but never used                  no-unused-vars
  12:3  warning  'LoginResponse' is defined but never used                  @typescript-eslint/no-unused-vars
  13:3  warning  'ConfirmLoginResponse' is defined but never used           no-unused-vars
  13:3  warning  'ConfirmLoginResponse' is defined but never used           @typescript-eslint/no-unused-vars
  14:3  warning  'MessageResponse' is defined but never used                no-unused-vars
  14:3  warning  'MessageResponse' is defined but never used                @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/auth/lib/auth-utils.ts
   1:34  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `auth-utils.ts` to same directory as `../../../shared/lib` or consider making `../../../shared/lib` a package    import/no-relative-parent-imports
   2:28  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `auth-utils.ts` to same directory as `../model/token-store` or consider making `../model/token-store` a package  import/no-relative-parent-imports
  13:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                            @typescript-eslint/no-explicit-any
  52:14  warning  'error' is defined but never used                                                                                                                                                                                                                                   no-unused-vars
  52:14  warning  'error' is defined but never used                                                                                                                                                                                                                                   @typescript-eslint/no-unused-vars
  65:14  warning  'error' is defined but never used                                                                                                                                                                                                                                   no-unused-vars
  65:14  warning  'error' is defined but never used                                                                                                                                                                                                                                   @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/auth/model/store.ts
  30:13  warning  'user' is defined but never used         no-unused-vars
  31:14  warning  'token' is defined but never used        no-unused-vars
  32:18  warning  'isLoading' is defined but never used    no-unused-vars
  33:14  warning  'error' is defined but never used        no-unused-vars
  34:19  warning  'rememberMe' is defined but never used   no-unused-vars
  35:20  warning  'deviceToken' is defined but never used  no-unused-vars
  36:11  warning  'token' is defined but never used        no-unused-vars
  36:26  warning  'rememberMe' is defined but never used   no-unused-vars
  38:22  warning  'token' is defined but never used        no-unused-vars
  52:13  warning  'step' is defined but never used         no-unused-vars
  53:14  warning  'email' is defined but never used        no-unused-vars
  54:17  warning  'password' is defined but never used     no-unused-vars
  55:28  warning  'isLoading' is defined but never used    no-unused-vars
  56:26  warning  'error' is defined but never used        no-unused-vars
  67:18  warning  'isEnabled' is defined but never used    no-unused-vars
  70:25  warning  'step' is defined but never used         no-unused-vars
  71:23  warning  'error' is defined but never used        no-unused-vars
  83:9   warning  'fn' is defined but never used           no-unused-vars
  83:14  warning  'state' is defined but never used        no-unused-vars
  95:20  warning  Forbidden non-null assertion             @typescript-eslint/no-non-null-assertion

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/auth/model/token-store.ts
  32:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  50:14  warning  'token' is defined but never used         no-unused-vars
  52:24  warning  'key' is defined but never used           no-unused-vars
  52:37  warning  'defaultValue' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/forms/model/store.ts
   19:13  warning  'step' is defined but never used      no-unused-vars
   20:20  warning  'currency' is defined but never used  no-unused-vars
   21:15  warning  'amount' is defined but never used    no-unused-vars
   36:13  warning  'step' is defined but never used      no-unused-vars
   37:20  warning  'currency' is defined but never used  no-unused-vars
   38:15  warning  'amount' is defined but never used    no-unused-vars
   39:16  warning  'address' is defined but never used   no-unused-vars
   40:13  warning  'memo' is defined but never used      no-unused-vars
   57:18  warning  'tab' is defined but never used       no-unused-vars
   69:9   warning  'fn' is defined but never used        no-unused-vars
   69:14  warning  'state' is defined but never used     no-unused-vars
  126:12  warning  'fn' is defined but never used        no-unused-vars
  126:17  warning  'state' is defined but never used     no-unused-vars
  235:9   warning  'fn' is defined but never used        no-unused-vars
  235:14  warning  'state' is defined but never used     no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/market/model/store.ts
   32:23  warning  'pairs' is defined but never used      no-unused-vars
   33:21  warning  'pair' is defined but never used       no-unused-vars
   34:18  warning  'isLoading' is defined but never used  no-unused-vars
   35:14  warning  'error' is defined but never used      no-unused-vars
   53:13  warning  'bids' is defined but never used       no-unused-vars
   54:13  warning  'asks' is defined but never used       no-unused-vars
   55:21  warning  'data' is defined but never used       no-unused-vars
   60:25  warning  'isLoading' is defined but never used  no-unused-vars
   76:18  warning  'type' is defined but never used       no-unused-vars
   77:18  warning  'side' is defined but never used       no-unused-vars
   78:14  warning  'price' is defined but never used      no-unused-vars
   79:15  warning  'amount' is defined but never used     no-unused-vars
   80:14  warning  'total' is defined but never used      no-unused-vars
   93:9   warning  'fn' is defined but never used         no-unused-vars
   93:14  warning  'state' is defined but never used      no-unused-vars
  148:9   warning  'fn' is defined but never used         no-unused-vars
  148:14  warning  'state' is defined but never used      no-unused-vars
  200:9   warning  'fn' is defined but never used         no-unused-vars
  200:14  warning  'state' is defined but never used      no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/theme/model/store.ts
  19:14  warning  'theme' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/trade/model/position-store.ts
   6:26  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `position-store.ts` to same directory as `../types` or consider making `../types` a package  import/no-relative-parent-imports
  29:20  warning  'position' is defined but never used                                                                                                                                                                                                            no-unused-vars
  32:26  warning  'isClosing' is defined but never used                                                                                                                                                                                                           no-unused-vars
  33:23  warning  'isUpdating' is defined but never used                                                                                                                                                                                                          no-unused-vars
  34:26  warning  'isCancelling' is defined but never used                                                                                                                                                                                                        no-unused-vars
  37:23  warning  'filter' is defined but never used                                                                                                                                                                                                              no-unused-vars
  40:16  warning  'sortBy' is defined but never used                                                                                                                                                                                                              no-unused-vars
  40:49  warning  'sortOrder' is defined but never used                                                                                                                                                                                                           no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/trade/types.ts
  40:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/user/api/query-keys.ts
  15:3  warning  'ProfileData' is defined but never used  no-unused-vars
  15:3  warning  'ProfileData' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/api/query-keys.ts
  8:31  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `query-keys.ts` to same directory as `../types` or consider making `../types` a package  import/no-relative-parent-imports

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/api/transaction-api.ts
    7:29  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `transaction-api.ts` to same directory as `../types` or consider making `../types` a package  import/no-relative-parent-imports
   63:66  warning  Unexpected any. Specify a different type                                                                                                                                                                                                         @typescript-eslint/no-explicit-any
  319:3   warning  'address' is defined but never used                                                                                                                                                                                                              no-unused-vars
  319:3   warning  'address' is defined but never used                                                                                                                                                                                                              @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/api/use-balance-query.ts
  143:6  warning  React Hook useEffect has missing dependencies: 'balanceQuery.balancesQuery', 'balanceQuery.fixedBalanceQuery?.status', and 'balanceQuery.isRefetching'. Either include them or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/api/wallet-api.ts
  1:32  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `wallet-api.ts` to same directory as `../../../shared/config/service-urls` or consider making `../../../shared/config/service-urls` a package  import/no-relative-parent-imports
  2:27  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `wallet-api.ts` to same directory as `../../../shared/lib/browser` or consider making `../../../shared/lib/browser` a package                  import/no-relative-parent-imports
  3:27  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `wallet-api.ts` to same directory as `../../auth/lib/auth-utils` or consider making `../../auth/lib/auth-utils` a package                      import/no-relative-parent-imports
  4:31  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `wallet-api.ts` to same directory as `../types` or consider making `../types` a package                                                        import/no-relative-parent-imports

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/model/balance-query-store.ts
  30:20  warning  'data' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/model/balance-store.ts
  29:15  warning  'userId' is defined but never used        no-unused-vars
  30:17  warning  'balances' is defined but never used      no-unused-vars
  31:21  warning  'fixedBalance' is defined but never used  no-unused-vars
  32:16  warning  'isLoading' is defined but never used     no-unused-vars
  33:14  warning  'error' is defined but never used         no-unused-vars
  34:27  warning  'isConnected' is defined but never used   no-unused-vars
  38:19  warning  'currency' is defined but never used      no-unused-vars
  41:5   warning  'onUpdate' is defined but never used      no-unused-vars
  41:17  warning  'balances' is defined but never used      no-unused-vars
  41:42  warning  'fixedBalance' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/model/use-balance-data.ts
  45:14  warning  'e' is defined but never used  no-unused-vars
  45:14  warning  'e' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/model/use-balance.ts
   3:10  warning  'authUtils' is defined but never used               no-unused-vars
   3:10  warning  'authUtils' is defined but never used               @typescript-eslint/no-unused-vars
   5:10  warning  'balanceService' is defined but never used          no-unused-vars
   5:10  warning  'balanceService' is defined but never used          @typescript-eslint/no-unused-vars
  27:9   warning  'formatBalance' is assigned a value but never used  no-unused-vars
  27:9   warning  'formatBalance' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/providers/BalanceProvider.tsx
  3:39  warning  'useState' is defined but never used    no-unused-vars
  3:39  warning  'useState' is defined but never used    @typescript-eslint/no-unused-vars
  6:10  warning  'tokenStore' is defined but never used  no-unused-vars
  6:10  warning  'tokenStore' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/entities/wallet/ui/TransactionHistoryTable.tsx
   95:20  warning  'formattedItems' is assigned a value but never used  no-unused-vars
   95:20  warning  'formattedItems' is assigned a value but never used  @typescript-eslint/no-unused-vars
  106:7   warning  Unexpected console statement                         no-console
  116:14  warning  'error' is defined but never used                    no-unused-vars
  116:14  warning  'error' is defined but never used                    @typescript-eslint/no-unused-vars
  139:9   warning  'truncateHash' is assigned a value but never used    no-unused-vars
  139:9   warning  'truncateHash' is assigned a value but never used    @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/auth/ui/AuthPage.tsx
  14:10  warning  'SignUpForm' is defined but never used  no-unused-vars
  14:10  warning  'SignUpForm' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/auth/ui/ResetPasswordForm.tsx
  53:9  warning  'setIsLoading' is assigned a value but never used  no-unused-vars
  53:9  warning  'setIsLoading' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/auth/ui/SignInForm.tsx
   62:9   warning  'setIsLoading' is assigned a value but never used                                                                                     no-unused-vars
   62:9   warning  'setIsLoading' is assigned a value but never used                                                                                     @typescript-eslint/no-unused-vars
   66:11  warning  'shouldSkipOTP' is assigned a value but never used                                                                                    no-unused-vars
   66:11  warning  'shouldSkipOTP' is assigned a value but never used                                                                                    @typescript-eslint/no-unused-vars
   66:63  warning  'setRememberMe' is assigned a value but never used                                                                                    no-unused-vars
   66:63  warning  'setRememberMe' is assigned a value but never used                                                                                    @typescript-eslint/no-unused-vars
  152:6   warning  React Hook useEffect has missing dependencies: 'authState' and 'handleAutoLogin'. Either include them or remove the dependency array  react-hooks/exhaustive-deps
  446:18  warning  `'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`                                                                       react/no-unescaped-entities
  447:15  warning  Missing an explicit type attribute for button                                                                                         react/button-has-type

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/auth/ui/SignUpForm.tsx
   21:10  warning  'cn' is defined but never used                                                                                                                                                                                                                                                           no-unused-vars
   21:10  warning  'cn' is defined but never used                                                                                                                                                                                                                                                           @typescript-eslint/no-unused-vars
   28:7   warning  'logger' is assigned a value but never used                                                                                                                                                                                                                                              no-unused-vars
   28:7   warning  'logger' is assigned a value but never used                                                                                                                                                                                                                                              @typescript-eslint/no-unused-vars
   60:9   warning  'setIsLoading' is assigned a value but never used                                                                                                                                                                                                                                        no-unused-vars
   60:9   warning  'setIsLoading' is assigned a value but never used                                                                                                                                                                                                                                        @typescript-eslint/no-unused-vars
  260:11  warning  Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element  @next/next/no-img-element
  342:13  warning  Missing an explicit type attribute for button                                                                                                                                                                                                                                            react/button-has-type

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/trade/api/analysis-service.ts
    1:67  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `analysis-service.ts` to same directory as `../../../entities/trade/types` or consider making `../../../entities/trade/types` a package                import/no-relative-parent-imports
    2:36  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `analysis-service.ts` to same directory as `../../../lib/logger` or consider making `../../../lib/logger` a package                                    import/no-relative-parent-imports
    3:37  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `analysis-service.ts` to same directory as `../../../shared/api/websocket-client` or consider making `../../../shared/api/websocket-client` a package  import/no-relative-parent-imports
    4:51  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `analysis-service.ts` to same directory as `../../../shared/config/service-urls` or consider making `../../../shared/config/service-urls` a package    import/no-relative-parent-imports
    5:35  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `analysis-service.ts` to same directory as `../../../shared/types/api` or consider making `../../../shared/types/api` a package                        import/no-relative-parent-imports
   77:12  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                  @typescript-eslint/no-explicit-any
  181:82  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                  @typescript-eslint/no-explicit-any
  197:38  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                                  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/trade/api/hooks/useOrderBook.ts
   4:21  warning  'useRef' is defined but never used                                                                                                                                                                                                                                                      no-unused-vars
   4:21  warning  'useRef' is defined but never used                                                                                                                                                                                                                                                      @typescript-eslint/no-unused-vars
   9:49  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `useOrderBook.ts` to same directory as `../query-keys` or consider making `../query-keys` a package                                  import/no-relative-parent-imports
  11:3   warning  'MarketWebSocketClient' is defined but never used                                                                                                                                                                                                                                       no-unused-vars
  11:3   warning  'MarketWebSocketClient' is defined but never used                                                                                                                                                                                                                                       @typescript-eslint/no-unused-vars
  14:8   warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `useOrderBook.ts` to same directory as `../websocket/market-websocket` or consider making `../websocket/market-websocket` a package  import/no-relative-parent-imports
  32:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                @typescript-eslint/no-explicit-any
  34:9   warning  'queryClient' is assigned a value but never used                                                                                                                                                                                                                                        no-unused-vars
  34:9   warning  'queryClient' is assigned a value but never used                                                                                                                                                                                                                                        @typescript-eslint/no-unused-vars
  43:36  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                @typescript-eslint/no-explicit-any
  75:20  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                                @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/trade/api/hooks/usePositions.ts
   7:10  warning  'Position' is defined but never used                                                                                                                                                                                                                                no-unused-vars
   7:10  warning  'Position' is defined but never used                                                                                                                                                                                                                                @typescript-eslint/no-unused-vars
  16:8   warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `usePositions.ts` to same directory as `../position-service` or consider making `../position-service` a package  import/no-relative-parent-imports
  17:72  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `usePositions.ts` to same directory as `../query-keys` or consider making `../query-keys` a package              import/no-relative-parent-imports

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/trade/api/hooks/useTicker.ts
   8:43  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `useTicker.ts` to same directory as `../query-keys` or consider making `../query-keys` a package                                  import/no-relative-parent-imports
   9:51  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `useTicker.ts` to same directory as `../websocket/market-websocket` or consider making `../websocket/market-websocket` a package  import/no-relative-parent-imports
  23:18  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                             @typescript-eslint/no-explicit-any
  32:33  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                             @typescript-eslint/no-explicit-any
  56:20  warning  Unexpected any. Specify a different type                                                                                                                                                                                                                                             @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/trade/api/trading-service.ts
   61:67  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   61:81  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  127:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  153:63  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  172:61  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  283:38  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/trade/api/websocket/market-websocket.ts
  148:68  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  201:62  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/features/withdrawal/api/withdrawal-api.ts
  1:27  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `withdrawal-api.ts` to same directory as `../../../entities/auth/lib/auth-utils` or consider making `../../../entities/auth/lib/auth-utils` a package  import/no-relative-parent-imports
  2:34  warning  Relative imports from parent directories are not allowed. Please either pass what you're importing through at runtime (dependency injection), move `withdrawal-api.ts` to same directory as `../../../shared/config/service-urls` or consider making `../../../shared/config/service-urls` a package      import/no-relative-parent-imports

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/api-logger.ts
   16:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   25:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   45:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   45:42  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   69:13  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  114:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  161:65  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  206:70  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  242:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  336:26  warning  'event' is defined but never used         no-unused-vars
  341:29  warning  'event' is defined but never used         no-unused-vars
  345:27  warning  'event' is defined but never used         no-unused-vars
  349:27  warning  'event' is defined but never used         no-unused-vars
  365:16  warning  'event' is defined but never used         no-unused-vars
  373:16  warning  'event' is defined but never used         no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/browser-logger.ts
   24:10  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   40:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   41:12  warning  Unexpected console statement              no-console
   42:14  warning  Unexpected console statement              no-console
   43:13  warning  Unexpected console statement              no-console
   44:13  warning  Unexpected console statement              no-console
   45:14  warning  Unexpected console statement              no-console
   51:7   warning  Unexpected console statement              no-console
   51:44  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   76:35  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  114:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  147:12  warning  'error' is defined but never used         no-unused-vars
  147:12  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars
  161:12  warning  'error' is defined but never used         no-unused-vars
  161:12  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars
  172:12  warning  'error' is defined but never used         no-unused-vars
  172:12  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/http-client.ts
   65:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  118:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  120:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  164:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  166:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  210:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  314:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/logger/client-logger.ts
   55:13  warning  Unexpected console statement                      no-console
   57:13  warning  Unexpected console statement                      no-console
   62:13  warning  Unexpected console statement                      no-console
   64:13  warning  Unexpected console statement                      no-console
   69:13  warning  Unexpected console statement                      no-console
   71:13  warning  Unexpected console statement                      no-console
   77:13  warning  Unexpected console statement                      no-console
   79:13  warning  Unexpected console statement                      no-console
   85:7   warning  Unexpected console statement                      no-console
   87:9   warning  Unexpected console statement                      no-console
   90:9   warning  Unexpected console statement                      no-console
  118:20  warning  'err' is defined but never used                   no-unused-vars
  118:20  warning  'err' is defined but never used                   @typescript-eslint/no-unused-vars
  128:14  warning  'error' is defined but never used                 no-unused-vars
  128:14  warning  'error' is defined but never used                 @typescript-eslint/no-unused-vars
  134:52  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  138:52  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  142:51  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  146:51  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  150:52  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  154:52  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  158:34  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  176:34  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  196:91  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  213:7   warning  Unexpected console statement                      no-console
  214:7   warning  Unexpected console statement                      no-console
  230:11  warning  Unexpected console statement                      no-console
  236:7   warning  Unexpected console statement                      no-console
  238:9   warning  Unexpected console statement                      no-console
  239:16  warning  'jsonError' is defined but never used             no-unused-vars
  239:16  warning  'jsonError' is defined but never used             @typescript-eslint/no-unused-vars
  241:9   warning  Unexpected console statement                      no-console
  266:20  warning  'e' is defined but never used                     no-unused-vars
  266:20  warning  'e' is defined but never used                     @typescript-eslint/no-unused-vars
  275:14  warning  'e' is defined but never used                     no-unused-vars
  275:14  warning  'e' is defined but never used                     @typescript-eslint/no-unused-vars
  285:7   warning  Unexpected console statement                      no-console
  286:7   warning  Unexpected console statement                      no-console
  294:7   warning  Unexpected console statement                      no-console
  295:7   warning  Unexpected console statement                      no-console
  303:7   warning  Unexpected console statement                      no-console
  304:7   warning  Unexpected console statement                      no-console
  312:7   warning  Unexpected console statement                      no-console
  313:7   warning  Unexpected console statement                      no-console
  317:34  warning  Unexpected any. Specify a different type          @typescript-eslint/no-explicit-any
  319:13  warning  'childLogger' is assigned a value but never used  no-unused-vars
  319:13  warning  'childLogger' is assigned a value but never used  @typescript-eslint/no-unused-vars
  328:7   warning  Unexpected console statement                      no-console

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/logger/config.ts
  13:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  18:11  warning  'message' is defined but never used       no-unused-vars
  18:28  warning  'metadata' is defined but never used      no-unused-vars
  19:11  warning  'message' is defined but never used       no-unused-vars
  19:28  warning  'metadata' is defined but never used      no-unused-vars
  20:10  warning  'message' is defined but never used       no-unused-vars
  20:27  warning  'metadata' is defined but never used      no-unused-vars
  21:10  warning  'message' is defined but never used       no-unused-vars
  21:27  warning  'metadata' is defined but never used      no-unused-vars
  22:11  warning  'message' is defined but never used       no-unused-vars
  22:28  warning  'metadata' is defined but never used      no-unused-vars
  23:11  warning  'message' is defined but never used       no-unused-vars
  23:28  warning  'metadata' is defined but never used      no-unused-vars
  24:11  warning  'bindings' is defined but never used      no-unused-vars
  24:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  33:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/logger/env-detection.ts
  82:3  warning  'CLIENT' is defined but never used     no-unused-vars
  85:3  warning  'SERVER' is defined but never used     no-unused-vars
  88:3  warning  'TURBOPACK' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/logger/index.ts
   4:28  warning  'LogMetadata' is defined but never used  no-unused-vars
   4:28  warning  'LogMetadata' is defined but never used  @typescript-eslint/no-unused-vars
  10:3   warning  'isBrowser' is defined but never used    no-unused-vars
  10:3   warning  'isBrowser' is defined but never used    @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/logger/server-imports.ts
  24:5   warning  Unexpected console statement              no-console
  34:5   warning  Unexpected console statement              no-console
  38:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  38:41  warning  Unexpected console statement              no-console
  39:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  39:41  warning  Unexpected console statement              no-console
  40:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  40:40  warning  Unexpected console statement              no-console
  41:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  41:40  warning  Unexpected console statement              no-console
  42:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  42:41  warning  Unexpected console statement              no-console
  43:20  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  43:41  warning  Unexpected console statement              no-console
  47:15  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/logger/server-logger.ts
    5:11  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
    6:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
    7:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   27:7   warning  Unexpected console statement              no-console
   33:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   43:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   44:5   warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   67:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   68:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   69:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   70:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   71:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   72:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   73:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   77:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   78:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   79:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   80:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   81:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   82:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   87:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   91:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   98:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  113:28  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  152:9   warning  Unexpected console statement              no-console
  167:79  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  169:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  170:7   warning  Unexpected console statement              no-console
  171:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  172:7   warning  Unexpected console statement              no-console
  173:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  174:7   warning  Unexpected console statement              no-console
  175:17  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  176:7   warning  Unexpected console statement              no-console
  177:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  178:7   warning  Unexpected console statement              no-console
  179:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  180:7   warning  Unexpected console statement              no-console
  181:23  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  188:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  196:19  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  226:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  244:48  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  256:7   warning  Unexpected console statement              no-console

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/logger/turbopack-logger.ts
  54:7   warning  Unexpected console statement              no-console
  60:7   warning  Unexpected console statement              no-console
  66:7   warning  Unexpected console statement              no-console
  72:7   warning  Unexpected console statement              no-console
  78:7   warning  Unexpected console statement              no-console
  84:7   warning  Unexpected console statement              no-console
  88:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/lib/server-utils.ts
  14:44  warning  'args' is defined but never used          no-unused-vars
  14:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  14:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  16:8   warning  'args' is defined but never used          no-unused-vars
  30:44  warning  'args' is defined but never used          no-unused-vars
  30:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  30:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  32:8   warning  'args' is defined but never used          no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/providers/query-logger.ts
  133:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  134:42  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  137:40  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  151:46  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/providers/query-provider.tsx
  82:18  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/api-client.ts
   73:53  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   93:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   97:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   98:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  199:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  202:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  217:25  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  219:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  221:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  242:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  244:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  246:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  261:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  263:12  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  265:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  279:27  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  282:14  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  283:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/error-handling.ts
  13:3  warning  'UNKNOWN_ERROR' is defined but never used              no-unused-vars
  14:3  warning  'NETWORK_ERROR' is defined but never used              no-unused-vars
  15:3  warning  'TIMEOUT_ERROR' is defined but never used              no-unused-vars
  16:3  warning  'SERVER_ERROR' is defined but never used               no-unused-vars
  17:3  warning  'VALIDATION_ERROR' is defined but never used           no-unused-vars
  18:3  warning  'NOT_FOUND' is defined but never used                  no-unused-vars
  21:3  warning  'AUTH_REQUIRED' is defined but never used              no-unused-vars
  22:3  warning  'INVALID_CREDENTIALS' is defined but never used        no-unused-vars
  23:3  warning  'TOKEN_EXPIRED' is defined but never used              no-unused-vars
  24:3  warning  'ACCESS_DENIED' is defined but never used              no-unused-vars
  27:3  warning  'EMAIL_ALREADY_USED' is defined but never used         no-unused-vars
  28:3  warning  'INVALID_VERIFICATION_CODE' is defined but never used  no-unused-vars
  29:3  warning  'USER_NOT_FOUND' is defined but never used             no-unused-vars
  30:3  warning  'ACCOUNT_NOT_VERIFIED' is defined but never used       no-unused-vars
  31:3  warning  'CODE_EXPIRED' is defined but never used               no-unused-vars
  32:3  warning  'TOO_MANY_ATTEMPTS' is defined but never used          no-unused-vars
  35:3  warning  'RESOURCE_NOT_FOUND' is defined but never used         no-unused-vars
  36:3  warning  'DUPLICATE_RESOURCE' is defined but never used         no-unused-vars
  39:3  warning  'SERVICE_UNAVAILABLE' is defined but never used        no-unused-vars
  40:3  warning  'RATE_LIMITED' is defined but never used               no-unused-vars
  43:3  warning  'INSUFFICIENT_FUNDS' is defined but never used         no-unused-vars
  44:3  warning  'INVALID_ADDRESS' is defined but never used            no-unused-vars
  45:3  warning  'TRANSACTION_FAILED' is defined but never used         no-unused-vars
  48:3  warning  'MARKET_CLOSED' is defined but never used              no-unused-vars
  49:3  warning  'INVALID_ORDER' is defined but never used              no-unused-vars
  50:3  warning  'PRICE_CHANGED' is defined but never used              no-unused-vars
  53:3  warning  'PROFILE_NOT_FOUND' is defined but never used          no-unused-vars
  54:3  warning  'EMAIL_ALREADY_EXISTS' is defined but never used       no-unused-vars
  55:3  warning  'INVALID_PROFILE_DATA' is defined but never used       no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/hooks/use-api-service.ts
   55:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   64:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   93:51  warning  'methodArgs' is defined but never used    no-unused-vars
  143:30  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  163:49  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  163:57  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  164:20  warning  'args' is defined but never used          no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/hooks/use-service-websocket.ts
   14:3   warning  'BALANCE' is defined but never used       no-unused-vars
   15:3   warning  'TRADING' is defined but never used       no-unused-vars
   16:3   warning  'ANALYSIS' is defined but never used      no-unused-vars
   32:41  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   76:39  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  126:37  warning  'data' is defined but never used          no-unused-vars
  144:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/hooks/useBalanceHistory.ts
  150:6  warning  React Hook useCallback has a missing dependency: 'historyQuery'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/hooks/useBalanceSummary.ts
  144:6  warning  React Hook useCallback has a missing dependency: 'summaryQuery'. Either include it or remove the dependency array  react-hooks/exhaustive-deps

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/hooks/useBalanceWebSocket.ts
  30:14  warning  'error' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/request.ts
  17:9  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/service-check.ts
   49:16  warning  'e' is defined but never used  no-unused-vars
   49:16  warning  'e' is defined but never used  @typescript-eslint/no-unused-vars
  178:12  warning  'e' is defined but never used  no-unused-vars
  178:12  warning  'e' is defined but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/services/analysis-service.ts
  169:26  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  170:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  182:56  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  189:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  213:55  warning  'data' is defined but never used          no-unused-vars
  216:36  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  244:37  warning  'data' is defined but never used          no-unused-vars
  286:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  299:47  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/services/auth-service.ts
  12:40  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/services/trading-service.ts
  260:46  warning  'data' is defined but never used          no-unused-vars
  263:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  292:49  warning  'data' is defined but never used          no-unused-vars
  295:37  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  324:46  warning  'data' is defined but never used          no-unused-vars
  327:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  355:34  warning  'data' is defined but never used          no-unused-vars
  405:45  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  418:47  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/websocket-client.ts
   19:41  warning  'data' is defined but never used          no-unused-vars
   19:47  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  143:36  warning  'event' is defined but never used         no-unused-vars
  189:54  warning  'data' is defined but never used          no-unused-vars
  189:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  211:57  warning  'data' is defined but never used          no-unused-vars
  211:63  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  228:51  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  395:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/api/websocket/useWebSocketQuery.ts
   25:20  warning  'message' is defined but never used       no-unused-vars
   37:14  warning  'error' is defined but never used         no-unused-vars
   37:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   64:63  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  125:33  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  126:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  192:31  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  193:22  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/lib/debug-utils.ts
   17:16  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   17:54  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   17:69  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   20:11  warning  Unexpected console statement              no-console
   30:9   warning  Unexpected console statement              no-console
   31:9   warning  Unexpected console statement              no-console
   32:9   warning  Unexpected console statement              no-console
   35:11  warning  Unexpected console statement              no-console
   36:11  warning  Unexpected console statement              no-console
   37:11  warning  Unexpected console statement              no-console
   41:13  warning  Unexpected console statement              no-console
   46:15  warning  Unexpected console statement              no-console
   52:13  warning  Unexpected console statement              no-console
   57:11  warning  Unexpected console statement              no-console
   58:11  warning  Unexpected console statement              no-console
   59:11  warning  Unexpected console statement              no-console
   60:11  warning  Unexpected console statement              no-console
   61:11  warning  Unexpected console statement              no-console
   62:11  warning  Unexpected console statement              no-console
   63:11  warning  Unexpected console statement              no-console
   64:11  warning  Unexpected console statement              no-console
   66:11  warning  Unexpected console statement              no-console
   67:11  warning  Unexpected console statement              no-console
   68:11  warning  Unexpected console statement              no-console
   69:11  warning  Unexpected console statement              no-console
   70:11  warning  Unexpected console statement              no-console
   72:11  warning  Unexpected console statement              no-console
   75:9   warning  Unexpected console statement              no-console
   80:16  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   80:63  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   83:11  warning  Unexpected console statement              no-console
   88:58  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   93:9   warning  Unexpected console statement              no-console
   94:9   warning  Unexpected console statement              no-console
   96:40  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
   97:11  warning  Unexpected console statement              no-console
   98:11  warning  Unexpected console statement              no-console
   99:11  warning  Unexpected console statement              no-console
  102:13  warning  Unexpected console statement              no-console
  104:15  warning  Unexpected console statement              no-console
  112:15  warning  Unexpected console statement              no-console
  117:9   warning  Unexpected console statement              no-console
  118:9   warning  Unexpected console statement              no-console
  119:9   warning  Unexpected console statement              no-console
  121:9   warning  Unexpected console statement              no-console

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/lib/logger/index.ts
  12:8  warning  Using exported name 'logger' as identifier for default import  import/no-named-as-default

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/lib/server/server-utils.ts
  14:44  warning  'args' is defined but never used          no-unused-vars
  14:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  14:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  16:8   warning  'args' is defined but never used          no-unused-vars
  30:44  warning  'args' is defined but never used          no-unused-vars
  30:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  30:60  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  32:8   warning  'args' is defined but never used          no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/lib/store/slice-creator.ts
  37:18  warning  'selector' is defined but never used      no-unused-vars
  37:29  warning  'state' is defined but never used         no-unused-vars
  37:42  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  37:50  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/lib/useWebSocketManager.ts
  31:14  warning  'error' is defined but never used         no-unused-vars
  31:21  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  70:73  warning  'manager' is defined but never used       no-unused-vars
  70:73  warning  'manager' is defined but never used       @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/lib/websocket-error-handler.ts
   49:49  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  157:15  warning  'ws' is defined but never used            no-unused-vars
  158:18  warning  'event' is defined but never used         no-unused-vars
  159:16  warning  'event' is defined but never used         no-unused-vars
  160:16  warning  'event' is defined but never used         no-unused-vars
  197:18  warning  'event' is defined but never used         no-unused-vars
  197:18  warning  'event' is defined but never used         @typescript-eslint/no-unused-vars
  258:10  warning  'data' is defined but never used          no-unused-vars
  297:13  warning  'data' is defined but never used          no-unused-vars
  298:5   warning  'event' is defined but never used         no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/lib/websocket-manager.ts
   52:43  warning  'data' is defined but never used          no-unused-vars
   70:6   warning  'event' is defined but never used         no-unused-vars
   70:38  warning  'manager' is defined but never used       no-unused-vars
  147:19  warning  'event' is defined but never used         no-unused-vars
  147:19  warning  'event' is defined but never used         @typescript-eslint/no-unused-vars
  236:16  warning  'error' is defined but never used         no-unused-vars
  236:16  warning  'error' is defined but never used         @typescript-eslint/no-unused-vars
  262:16  warning  'e' is defined but never used             no-unused-vars
  262:16  warning  'e' is defined but never used             @typescript-eslint/no-unused-vars
  308:44  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  350:38  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  391:24  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  391:53  warning  'data' is defined but never used          no-unused-vars
  396:22  warning  Forbidden non-null assertion              @typescript-eslint/no-non-null-assertion
  397:29  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  412:34  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any
  494:16  warning  'event' is defined but never used         no-unused-vars
  494:48  warning  'manager' is defined but never used       no-unused-vars
  601:15  warning  'key' is assigned a value but never used  no-unused-vars
  601:15  warning  'key' is assigned a value but never used  @typescript-eslint/no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/shared/providers/store-provider.tsx
  34:31  warning  'state' is defined but never used  no-unused-vars

/Users/<USER>/WebstormProjects/bitmeiapp/trade-platform-front/src/tests/api/balance-token-store-test.ts
  84:16  warning  Unexpected any. Specify a different type  @typescript-eslint/no-explicit-any

✖ 1061 problems (0 errors, 1061 warnings)

