import unittest
from unittest.mock import patch, MagicMock
from fastapi.testclient import TestClient

from app.main import app
from app.database import Base, get_db
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.wallet import Wallet

# Создаем тестовую базу данных в памяти
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Функция для переопределения зависимости базы данных
def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

# Переопределяем зависимость
app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)

class TestWalletService(unittest.TestCase):
    
    def setUp(self):
        # Создаем таблицы в тестовой базе
        Base.metadata.create_all(bind=engine)
    
    def tearDown(self):
        # Удаляем таблицы после тестов
        Base.metadata.drop_all(bind=engine)
    
    @patch('app.services.btc_service.BTCService.generate_address')
    @patch('app.crypto.crypto_utils.encrypt_private_key')
    @patch('app.kafka.producer.send_wallet_created_event')
    def test_create_btc_wallet(self, mock_send_event, mock_encrypt, mock_generate_address):
        # Устанавливаем возвращаемые значения для моков
        mock_generate_address.return_value = ("**********************************", "test_private_key")
        mock_encrypt.return_value = "encrypted_key"
        mock_send_event.return_value = True
        
        # Отправляем запрос на создание кошелька
        response = client.post(
            "/api/wallets/",
            json={"currency": "BTC", "user_id": 123}
        )
        
        # Проверяем ответ
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertEqual(data["currency"], "BTC")
        self.assertEqual(data["address"], "**********************************")
        self.assertEqual(data["network"], "BTC")
        
        # Проверяем, что методы были вызваны
        mock_generate_address.assert_called_once_with(123)
        mock_encrypt.assert_called_once_with("test_private_key")
        mock_send_event.assert_called_once()
    
    @patch('app.services.eth_service.ETHService.generate_address')
    @patch('app.crypto.crypto_utils.encrypt_private_key')
    @patch('app.kafka.producer.send_wallet_created_event')
    def test_create_eth_wallet(self, mock_send_event, mock_encrypt, mock_generate_address):
        # Устанавливаем возвращаемые значения для моков
        mock_generate_address.return_value = ("0xABC123", "eth_private_key")
        mock_encrypt.return_value = "encrypted_key"
        mock_send_event.return_value = True
        
        # Отправляем запрос на создание кошелька
        response = client.post(
            "/api/wallets/",
            json={"currency": "ETH", "user_id": 123}
        )
        
        # Проверяем ответ
        self.assertEqual(response.status_code, 201)
        data = response.json()
        self.assertEqual(data["currency"], "ETH")
        self.assertEqual(data["address"], "0xABC123")
        self.assertEqual(data["network"], "ERC20")
        
        # Проверяем, что методы были вызваны
        mock_generate_address.assert_called_once_with(123)
        mock_encrypt.assert_called_once_with("eth_private_key")
        mock_send_event.assert_called_once()
    
    def test_get_user_wallets_empty(self):
        # Запрос на получение кошельков пользователя, у которого их нет
        response = client.get("/api/wallets/999")
        
        # Проверяем ответ
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertEqual(len(data), 0)
    
    def test_get_nonexistent_wallet(self):
        # Запрос на получение несуществующего кошелька
        response = client.get("/api/wallets/999/BTC")
        
        # Проверяем ответ
        self.assertEqual(response.status_code, 404)
    
    def test_invalid_currency(self):
        # Запрос на создание кошелька с неподдерживаемой валютой
        response = client.post(
            "/api/wallets/",
            json={"currency": "INVALID", "user_id": 123}
        )
        
        # Проверяем ответ
        self.assertEqual(response.status_code, 400)
        
if __name__ == "__main__":
    unittest.main()