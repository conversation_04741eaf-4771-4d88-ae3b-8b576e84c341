import json
import logging
import threading
from typing import Dict, Any
from kafka import KafkaConsumer
from kafka.errors import KafkaError
from sqlalchemy.orm import Session
from sqlalchemy import select, func
from sqlalchemy.sql import or_

from app.config import settings
from app.database import SessionLocal
from app.models.wallet import Wallet
from app.kafka.producer import get_kafka_producer
from app.crypto.crypto_utils import decrypt_private_key, validate_key_address_match


logger = logging.getLogger("app.kafka.consumer")

def start_consumer_in_thread():
    """Запускает Kafka consumer в отдельном потоке"""
    consumer_thread = threading.Thread(target=consume_wallet_requests)
    consumer_thread.daemon = True
    consumer_thread.start()
    logger.info("Started Kafka consumer for wallet requests in background thread")

def consume_wallet_requests():
    """
    Прослушивает запросы приватных ключей от Treasury Service
    """
    try:
        logger.info(f"Starting Kafka consumer for wallet requests on topic {settings.KAFKA_TOPIC_WALLET_REQUESTS}")
        
        consumer = KafkaConsumer(
            settings.KAFKA_TOPIC_WALLET_REQUESTS,
            bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
            group_id=settings.KAFKA_GROUP_ID,
            auto_offset_reset='latest',
            value_deserializer=lambda m: json.loads(m.decode('utf-8')),
            enable_auto_commit=True
        )
        
        for message in consumer:
            try:
                # Обработка запроса в отдельном потоке
                handle_request(message.value)
            except Exception as e:
                logger.error(f"Error processing wallet request: {e}")
                
    except Exception as e:
        logger.error(f"Error in wallet request consumer: {e}")

def handle_request(request_data: Dict[str, Any]):
    """
    Обрабатывает запрос на приватный ключ
    
    Args:
        request_data: Данные запроса
    """
    try:
        logger.info(f"Processing wallet request: {request_data}")
        
        # Проверка типа запроса
        if request_data.get('action') != 'get_private_key':
            logger.warning(f"Unknown action in wallet request: {request_data.get('action')}")
            return
        
        # Получение данных из запроса
        request_id = request_data.get('request_id')
        wallet_address = request_data.get('wallet_address')
        currency = request_data.get('currency')
        
        if not all([request_id, wallet_address]):
            logger.error(f"Invalid request data: {request_data}")
            return
        
        # Поиск кошелька в БД
        db = SessionLocal()
        try:
            # Различная обработка для ETH и других адресов
            wallet = None
            
            if currency == "ETH":
                # Для ETH адресов делаем регистронезависимое сравнение
                logger.info(f"Поиск ETH адреса без учета регистра: {wallet_address}")
                
                # Попробуем найти адрес без учета регистра
                wallet_query = select(Wallet).where(func.lower(Wallet.address) == wallet_address.lower())
                wallet = db.scalar(wallet_query)
                
                # Если не нашли, попробуем еще вариант с prefixed и non-prefixed (0x)
                if not wallet:
                    if wallet_address.startswith('0x'):
                        # Попробуем без префикса 0x
                        alt_address = wallet_address[2:]
                        logger.info(f"Поиск ETH адреса без префикса 0x: {alt_address}")
                        wallet_query = select(Wallet).where(func.lower(Wallet.address) == alt_address.lower())
                        wallet = db.scalar(wallet_query)
                    else:
                        # Попробуем с префиксом 0x
                        alt_address = '0x' + wallet_address
                        logger.info(f"Поиск ETH адреса с префиксом 0x: {alt_address}")
                        wallet_query = select(Wallet).where(func.lower(Wallet.address) == alt_address.lower())
                        wallet = db.scalar(wallet_query)
            else:
                # Для TRON и других адресов используем точное сравнение
                wallet_query = select(Wallet).where(Wallet.address == wallet_address)
                wallet = db.scalar(wallet_query)
            
            if not wallet:
                logger.error(f"Wallet not found: {wallet_address}, currency: {currency}")
                send_error_response(request_id, f"Wallet not found: {wallet_address}")
                return
            
            # Расшифровываем приватный ключ
            try:
                private_key = decrypt_private_key(wallet.private_key)
                
                # Логируем информацию о запросе ключа (без самого ключа!)
                logger.info(f"Расшифрован приватный ключ для кошелька {wallet_address}, валюты {currency}")
                
                # КРИТИЧЕСКИ ВАЖНО: Проверяем соответствие приватного ключа и адреса
                if validate_key_address_match(private_key, wallet_address, currency):
                    # Ключ соответствует адресу - все хорошо
                    logger.info(f"Private key matches address {wallet_address} for currency {currency}")
                    
                    # Отправляем ответ с приватным ключом
                    send_private_key_response(request_id, private_key, wallet_address)
                    logger.info(f"Sent private key response for wallet {wallet_address}")
                else:
                    # Попробуем проверить ключ к фактическому адресу из БД
                    if validate_key_address_match(private_key, wallet.address, currency):
                        logger.warning(f"Private key matches DB address {wallet.address} but not requested address {wallet_address}")
                        send_private_key_response(request_id, private_key, wallet_address)
                        logger.info(f"Sent private key response for wallet {wallet_address} (validated against DB address)")
                    else:
                        # Ключ НЕ соответствует адресу - критическая ошибка!
                        error_msg = f"CRITICAL ERROR: Private key does not match address {wallet_address} or {wallet.address} for currency {currency}"
                        logger.error(error_msg)
                        send_error_response(request_id, error_msg)
                
            except Exception as e:
                logger.error(f"Error decrypting private key for wallet {wallet_address}: {e}")
                send_error_response(request_id, f"Error decrypting private key: {str(e)}")
        finally:
            db.close()
                
    except Exception as e:
        logger.error(f"Error handling wallet request: {e}")
        if request_data and request_data.get('request_id'):
            send_error_response(request_data.get('request_id'), f"Internal error: {str(e)}")

def send_private_key_response(request_id: str, private_key: str, wallet_address: str):
    """
    Отправляет ответ с приватным ключом
    
    Args:
        request_id: ID запроса
        private_key: Приватный ключ
        wallet_address: Адрес кошелька
    """
    try:
        producer = get_kafka_producer()
        
        response = {
            "request_id": request_id,
            "status": "success",
            "private_key": private_key,
            "wallet_address": wallet_address
        }
        
        producer.send(settings.KAFKA_TOPIC_WALLET_RESPONSES, response)
        producer.flush()
        
    except Exception as e:
        logger.error(f"Error sending private key response: {e}")

def send_error_response(request_id: str, error_message: str):
    """
    Отправляет ответ с ошибкой
    
    Args:
        request_id: ID запроса
        error_message: Сообщение об ошибке
    """
    try:
        producer = get_kafka_producer()
        
        response = {
            "request_id": request_id,
            "status": "error",
            "error": error_message
        }
        
        producer.send(settings.KAFKA_TOPIC_WALLET_RESPONSES, response)
        producer.flush()
        
    except Exception as e:
        logger.error(f"Error sending error response: {e}")