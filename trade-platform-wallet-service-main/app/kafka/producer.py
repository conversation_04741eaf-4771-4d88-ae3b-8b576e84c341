import json
import logging
from datetime import datetime
from typing import Dict, Any
from kafka import KafkaProducer
from kafka.errors import KafkaError

from app.config import settings

logger = logging.getLogger(__name__)

_producer = None

def get_kafka_producer() -> KafkaProducer:
    """
    Получение экземпляра Kafka Producer в режиме Singleton
    
    Returns:
        KafkaProducer: Экземпляр Kafka Producer
    """
    global _producer
    if _producer is None:
        _producer = KafkaProducer(
            bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
            value_serializer=lambda v: json.dumps(v).encode('utf-8')
        )
    return _producer

def send_wallet_created_event(user_id: int, currency: str, address: str) -> bool:
    """
    Отправка события о создании нового кошелька в Kafka
    
    Args:
        user_id: ID пользователя
        currency: Валюта кошелька
        address: Адрес кошелька
        
    Returns:
        bool: True если сообщение успешно отправлено, иначе False
    """
    try:
        producer = get_kafka_producer()
        
        message = {
            "event_type": "wallet_created",
            "user_id": user_id,
            "currency": currency,
            "address": address,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        future = producer.send(settings.KAFKA_TOPIC_WALLET_EVENTS, message)
        # Блокирующий вызов для получения результата (можно сделать асинхронным с callback)
        future.get(timeout=10)
        
        logger.info(f"Wallet created event sent: user_id={user_id}, currency={currency}, address={address}")
        return True
        
    except KafkaError as e:
        logger.error(f"Failed to send wallet created event: {str(e)}")
        return False