import uuid
from sqlalchemy import Column, String, Integer, DateTime, Float, Foreign<PERSON>ey, Boolean
from sqlalchemy.dialects.postgresql import UUID
from datetime import datetime
from app.database import Base

class Wallet(Base):
    """Модель для хранения информации о криптовалютных кошельках пользователей"""
    __tablename__ = "wallets"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, index=True, nullable=False)
    currency = Column(String, nullable=False)  # BTC, ETH, USDT, USDC
    address = Column(String, unique=True, nullable=False)
    private_key = Column(String, nullable=False)  # Зашифрованный ключ
    network = Column(String, nullable=False)  # BTC, ERC20, TRC20
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    def __repr__(self):
        return f"<Wallet(user_id={self.user_id}, currency={self.currency}, address={self.address})>"