import base64
import logging
from typing import Tuple

from app.config import settings

logger = logging.getLogger("app.crypto.crypto_utils")

# Простой xor шифр с ключом
def _xor_encrypt_decrypt(data: bytes, key: str) -> bytes:
    """
    Простой XOR шифр для кодирования/декодирования данных
    """
    key_bytes = key.encode('utf-8')
    key_len = len(key_bytes)
    
    result = bytearray()
    for i, byte in enumerate(data):
        result.append(byte ^ key_bytes[i % key_len])
        
    return bytes(result)

def encrypt_private_key(private_key: str) -> str:
    """
    Шифрует приватный ключ
    
    Args:
        private_key: Приватный ключ в формате строки (hex)
        
    Returns:
        str: Зашифрованный приватный ключ в формате base64
    """
    try:
        # Проверяем, что приватный ключ - валидная hex-строка
        if not all(c in '0123456789abcdefABCDEF' for c in private_key):
            logger.error(f"Приватный ключ не является hex-строкой: {private_key[:4]}...")
            raise ValueError("Приватный ключ должен быть в hex-формате")
            
        # Шифруем ключ нашим секретным ключом (простое XOR шифрование)
        encrypted_bytes = _xor_encrypt_decrypt(bytes.fromhex(private_key), settings.SECRET_KEY)
        
        # Кодируем результат в base64 для хранения в БД
        encrypted_key = base64.b64encode(encrypted_bytes).decode('utf-8')
        
        return encrypted_key
        
    except Exception as e:
        logger.error(f"Ошибка при шифровании приватного ключа: {str(e)}")
        raise
    
def decrypt_private_key(encrypted_key: str) -> str:
    """
    Дешифрует приватный ключ
    
    Args:
        encrypted_key: Зашифрованный приватный ключ в формате base64
        
    Returns:
        str: Расшифрованный приватный ключ
    """
    try:
        # Декодируем base64 в бинарные данные
        encrypted_bytes = base64.b64decode(encrypted_key)
        
        # Дешифруем с тем же ключом (XOR с тем же ключом дает исходные данные)
        decrypted_bytes = _xor_encrypt_decrypt(encrypted_bytes, settings.SECRET_KEY)
        
        # Возвращаем hex-строку
        return decrypted_bytes.hex()
        
    except Exception as e:
        logger.error(f"Ошибка при дешифровании приватного ключа: {str(e)}")
        raise ValueError(f"Не удалось дешифровать приватный ключ: {str(e)}")

def validate_key_address_match(private_key: str, address: str, currency: str) -> bool:
    """
    Проверяет, соответствует ли приватный ключ адресу для указанной валюты
    
    Args:
        private_key: Приватный ключ (hex)
        address: Адрес кошелька
        currency: Валюта (BTC, ETH, TRON, и т.д.)
        
    Returns:
        bool: True если приватный ключ соответствует адресу, иначе False
    """
    try:
        if currency in ["TRON", "TRX", "USDT-TRC20"]:
            from tronpy.keys import PrivateKey
            key_obj = PrivateKey(bytes.fromhex(private_key))
            derived_address = key_obj.public_key.to_base58check_address()
            
            # Добавляем логирование для сравнения адресов
            logger.info(f"Проверка TRON адреса: ожидаемый={address}, полученный={derived_address}")
            
            # Результат проверки
            matches = derived_address == address
            if matches:
                logger.info(f"✅ Адрес {address} соответствует приватному ключу")
            else:
                logger.warning(f"❌ Адрес {address} НЕ соответствует приватному ключу! " 
                              f"Ключ принадлежит адресу {derived_address}")
            
            return matches
            
        elif currency in ["ETH", "USDC-ERC20", "USDT-ERC20"]:
            from eth_account import Account
            account = Account.from_key(private_key)
            derived_address = account.address
            
            # Добавляем логирование для сравнения адресов
            logger.info(f"Проверка ETH адреса: ожидаемый={address}, полученный={derived_address}")
            
            # Результат проверки (для ETH сравниваем без учета регистра)
            matches = derived_address.lower() == address.lower()
            if matches:
                logger.info(f"✅ Адрес {address} соответствует приватному ключу")
            else:
                logger.warning(f"❌ Адрес {address} НЕ соответствует приватному ключу! " 
                              f"Ключ принадлежит адресу {derived_address}")
            
            return matches
            
        elif currency == "BTC":
            # Импортируем bitcoinlib для проверки BTC ключей
            from bitcoinlib.keys import Key
            
            try:
                # Определяем формат ключа (WIF или hex)
                if private_key.startswith(('5', 'K', 'L')):
                    # WIF формат (который возвращает BlockCypher API)
                    key_obj = Key(private_key)  # Прямая загрузка WIF
                else:
                    # Hex формат
                    key_obj = Key(bytes.fromhex(private_key))
                    
                # Получаем Bitcoin адрес из ключа
                derived_address = key_obj.address()
                
                # Логирование для сравнения адресов
                logger.info(f"Проверка BTC адреса: ожидаемый={address}, полученный={derived_address}")
                
                # Результат проверки
                matches = derived_address == address
                if matches:
                    logger.info(f"✅ Адрес {address} соответствует приватному ключу")
                else:
                    logger.warning(f"❌ Адрес {address} НЕ соответствует приватному ключу! " 
                                  f"Ключ принадлежит адресу {derived_address}")
                
                return matches
                
            except Exception as btc_err:
                logger.error(f"Ошибка при проверке соответствия BTC ключа: {str(btc_err)}")
                return False
            
        else:
            logger.warning(f"Неизвестная валюта для проверки соответствия: {currency}")
            return True
            
    except Exception as e:
        logger.error(f"Ошибка при проверке соответствия ключа и адреса: {str(e)}")
        return False