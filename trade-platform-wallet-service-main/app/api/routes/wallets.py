import logging
from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.database import get_db
from app.schemas.wallet import WalletCreate, WalletResponse
from app.models.wallet import Wallet
from app.services.btc_service import BTCService
from app.services.eth_service import ETHService
from app.services.tron_service import TronService
from app.crypto.crypto_utils import encrypt_private_key
from app.kafka.producer import send_wallet_created_event
from app.api.deps import validate_currencies

# Настройка логгера
logger = logging.getLogger(__name__)

router = APIRouter()

@router.post("/", response_model=WalletResponse, status_code=status.HTTP_201_CREATED)
async def create_wallet(
    wallet_data: WalletCreate,
    db: Session = Depends(get_db)
):
    """
    Создает новый криптовалютный кошелек для пользователя.
    
    - **currency**: Код валюты (BTC, ETH, USDT, USDC)
    - **user_id**: ID пользователя
    """
    logger.info(f"Начало обработки запроса на создание кошелька: {wallet_data.currency} для пользователя {wallet_data.user_id}")
    
    # Проверяем, поддерживается ли валюта
    logger.info(f"Проверка поддерживаемости валюты: {wallet_data.currency}")
    try:
        validate_currencies(wallet_data.currency)
        logger.info(f"Валюта {wallet_data.currency} поддерживается")
    except Exception as e:
        logger.error(f"Ошибка при проверке валюты: {str(e)}")
        raise
    
    # Проверяем, существует ли уже кошелек для этого пользователя и валюты
    logger.info(f"Проверка существующего кошелька для пользователя {wallet_data.user_id} и валюты {wallet_data.currency}")
    try:
        existing_wallet = db.query(Wallet).filter(
            Wallet.user_id == wallet_data.user_id,
            Wallet.currency == wallet_data.currency,
            Wallet.is_active == True
        ).first()
        
        if existing_wallet:
            logger.info(f"Найден существующий кошелек: {existing_wallet.address}")
            return WalletResponse(
                address=existing_wallet.address,
                currency=existing_wallet.currency,
                network=existing_wallet.network
            )
        logger.info("Существующий кошелек не найден, приступаем к генерации нового")
    except Exception as e:
        logger.error(f"Ошибка при проверке существующего кошелька: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка базы данных при проверке существующего кошелька: {str(e)}"
        )
    
    # Генерируем новый кошелек в зависимости от выбранной валюты
    try:
        logger.info(f"Начало генерации нового кошелька для {wallet_data.currency}")
        
        if wallet_data.currency == "BTC":
            logger.info("Генерация BTC адреса")
            address, private_key = BTCService.generate_address(wallet_data.user_id)
            network = "BTC"
        elif wallet_data.currency in ["ETH", "USDC"]:
            logger.info(f"Генерация {wallet_data.currency} адреса на сети ERC20")
            address, private_key = ETHService.generate_address(wallet_data.user_id)
            network = "ERC20"
        elif wallet_data.currency == "USDT":
            logger.info("Генерация USDT адреса на сети TRC20")
            address, private_key = TronService.generate_address(wallet_data.user_id)
            network = "TRC20"
        else:
            # Этот блок не должен выполняться, так как мы уже проверили валюту
            logger.error(f"Непредвиденная ошибка: неподдерживаемая валюта {wallet_data.currency} прошла валидацию")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Неподдерживаемая валюта: {wallet_data.currency}"
            )
        
        logger.info(f"Адрес успешно сгенерирован: {address}, сеть: {network}")
        
        # Шифруем приватный ключ
        logger.info("Шифрование приватного ключа")
        try:
            encrypted_private_key = encrypt_private_key(private_key)
            logger.info("Приватный ключ успешно зашифрован")
        except Exception as e:
            logger.error(f"Ошибка при шифровании приватного ключа: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Ошибка шифрования: {str(e)}"
            )
        
        # Создаем новый кошелек в базе данных
        logger.info("Сохранение кошелька в базе данных")
        try:
            new_wallet = Wallet(
                user_id=wallet_data.user_id,
                currency=wallet_data.currency,
                address=address,
                private_key=encrypted_private_key,
                network=network,
                is_active=True
            )
            
            db.add(new_wallet)
            db.commit()
            db.refresh(new_wallet)
            logger.info(f"Кошелек успешно сохранен в базе данных с ID: {new_wallet.id}")
        except Exception as e:
            logger.error(f"Ошибка при сохранении кошелька в базе данных: {str(e)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Ошибка базы данных при сохранении кошелька: {str(e)}"
            )
        
        # Отправляем событие в Kafka
        logger.info(f"Отправка события в Kafka о создании кошелька для пользователя {wallet_data.user_id}")
        try:
            send_wallet_created_event(wallet_data.user_id, wallet_data.currency, address)
            logger.info("Событие успешно отправлено в Kafka")
        except Exception as e:
            logger.warning(f"Ошибка при отправке события в Kafka: {str(e)}")
            # Мы не хотим, чтобы ошибка Kafka блокировала создание кошелька,
            # поэтому просто логируем ошибку и продолжаем
        
        logger.info(f"Возврат информации о новом кошельке: {address}")
        return WalletResponse(
            address=new_wallet.address,
            currency=new_wallet.currency,
            network=new_wallet.network
        )
        
    except HTTPException:
        # Пробрасываем HTTPException дальше
        raise
    except Exception as e:
        logger.error(f"Неожиданная ошибка при создании кошелька: {str(e)}")
        if 'db' in locals():
            db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Не удалось создать кошелек: {str(e)}"
        )

@router.get("/btc/all", response_model=List[dict])
async def get_all_btc_wallets(db: Session = Depends(get_db)):
    """
    Получение всех Bitcoin кошельков для мониторинга
    
    Returns:
        List[dict]: Список словарей с информацией о кошельках Bitcoin
    """
    logger.info("Запрос на получение всех Bitcoin кошельков")
    try:
        wallets = db.query(Wallet).filter(
            Wallet.currency == "BTC",
            Wallet.is_active == True
        ).all()
        
        result = [{"user_id": wallet.user_id, "address": wallet.address} for wallet in wallets]
        logger.info(f"Найдено {len(result)} Bitcoin кошельков")
        return result
    except Exception as e:
        logger.error(f"Ошибка при получении Bitcoin кошельков: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при получении Bitcoin кошельков: {str(e)}"
        )

@router.get("/eth/all", response_model=List[dict])
async def get_all_eth_wallets(db: Session = Depends(get_db)):
    """
    Получение всех Ethereum кошельков для мониторинга (включая ETH и USDC)
    
    Returns:
        List[dict]: Список словарей с информацией о кошельках Ethereum
    """
    logger.info("Запрос на получение всех Ethereum кошельков")
    try:
        wallets = db.query(Wallet).filter(
            Wallet.currency.in_(["ETH", "USDC"]),
            Wallet.is_active == True
        ).all()
        
        result = [{"user_id": wallet.user_id, "address": wallet.address} for wallet in wallets]
        logger.info(f"Найдено {len(result)} Ethereum кошельков")
        return result
    except Exception as e:
        logger.error(f"Ошибка при получении Ethereum кошельков: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при получении Ethereum кошельков: {str(e)}"
        )

@router.get("/tron/all", response_model=List[dict])
async def get_all_tron_wallets(db: Session = Depends(get_db)):
    """
    Получение всех TRON кошельков для мониторинга (USDT TRC20)
    
    Returns:
        List[dict]: Список словарей с информацией о кошельках TRON
    """
    logger.info("Запрос на получение всех TRON кошельков")
    try:
        wallets = db.query(Wallet).filter(
            Wallet.currency == "USDT",
            Wallet.is_active == True
        ).all()
        
        result = [{"user_id": wallet.user_id, "address": wallet.address} for wallet in wallets]
        logger.info(f"Найдено {len(result)} TRON кошельков")
        return result
    except Exception as e:
        logger.error(f"Ошибка при получении TRON кошельков: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при получении TRON кошельков: {str(e)}"
        )