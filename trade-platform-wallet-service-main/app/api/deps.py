from fastapi import HTTPException, status

def validate_currencies(currency: str):
    """
    Проверяет, поддерживается ли указанная валюта
    
    Args:
        currency: Код валюты
        
    Returns:
        str: Проверенный код валюты
        
    Raises:
        HTTPException: Если валюта не поддерживается
    """
    supported_currencies = ["BTC", "ETH", "USDT", "USDC"]
    
    if currency not in supported_currencies:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Валюта {currency} не поддерживается. Поддерживаемые валюты: {', '.join(supported_currencies)}"
        )
    
    return currency