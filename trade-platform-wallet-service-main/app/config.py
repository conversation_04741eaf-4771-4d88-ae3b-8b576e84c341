import os
from typing import List

from pydantic_settings import BaseSettings
from pydantic import field_validator

class Settings(BaseSettings):
    # Основные настройки приложения
    APP_NAME: str = "wallet-service"
    DEBUG: bool = False
    PORT: int = 8003
    
    # CORS настройки
    CORS_ORIGINS: List[str] = [
        "http://localhost:3000",      # ← УБРАЛ ЛИШНИЙ http:// и слеш
        "https://app.bitmei.com",     # ← УБРАЛ ЛИШНИЙ слеш
        "https://wallet-api.eu.ngrok.io",  # ← ДОБАВЬ СВОЙ ngrok URL
        "*"  # ← ИЛИ РАЗРЕШИТЬ ВСЕ (только для разработки!)
    ]
    
    # Настройки базы данных
    DATABASE_URL: str
    
    # Настройки Kafka
    KAFKA_BOOTSTRAP_SERVERS: str
    KAFKA_TOPIC_WALLET_EVENTS: str = "wallet-events"
    
    # Новые настройки для взаимодействия с Treasury Service
    KAFKA_TOPIC_WALLET_REQUESTS: str = "wallet-service-requests"
    KAFKA_TOPIC_WALLET_RESPONSES: str = "wallet-service-responses"
    KAFKA_GROUP_ID: str = "wallet-service"
    
    # Настройки AWS
    AWS_REGION: str
    AWS_ACCESS_KEY: str
    AWS_SECRET_KEY: str
    KMS_KEY_ID: str
    
    # Настройки криптовалют
    BTC_API_KEY: str = ""
    ETH_INFURA_KEY: str = ""
    TRON_API_KEY: str = ""
    
    # Минимальная сумма пополнения для каждой валюты
    # Эти значения можно менять в зависимости от требований
    MIN_DEPOSIT_BTC: float = 0.0001
    MIN_DEPOSIT_ETH: float = 0.001
    MIN_DEPOSIT_USDT: float = 1.0
    MIN_DEPOSIT_USDC: float = 1.0



    SECRET_KEY: str = os.getenv("SECRET_KEY", "")
    
    @field_validator("DATABASE_URL")
    @classmethod
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL must be provided")
        return v
    
    model_config = {
    "env_file": ".env",
    "case_sensitive": True
    }

settings = Settings()