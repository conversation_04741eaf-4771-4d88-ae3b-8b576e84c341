from typing import Optional
from datetime import datetime
from uuid import UUID
from pydantic import BaseModel, Field

class WalletBase(BaseModel):
    """Базовая модель для кошелька"""
    currency: str = Field(..., description="Валюта кошелька (BTC, ETH, USDT, USDC)")
    user_id: int = Field(..., description="ID пользователя")

class WalletCreate(WalletBase):
    """Модель для создания кошелька"""
    pass

class WalletDB(WalletBase):
    """Модель для представления кошелька в базе данных"""
    id: UUID
    address: str
    network: str
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True  # Обновлено для Pydantic v2

class WalletResponse(BaseModel):
    """Модель для ответа API с информацией о кошельке"""
    address: str
    currency: str
    network: str
    
    class Config:
        from_attributes = True  # Обновлено для Pydantic v2