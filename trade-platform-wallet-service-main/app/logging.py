# app/core/logging.py или другое место в вашем проекте

import logging
import sys
from logging.handlers import RotatingFileHandler
import os

def setup_logging():
    """
    Настройка логирования для приложения.
    Логи будут выводиться в консоль и в файл.
    """
    # Создаем логгер для всего приложения
    logger = logging.getLogger("app")
    logger.setLevel(logging.INFO)
    
    # Формат логов
    formatter = logging.Formatter(
        "%(asctime)s [%(levelname)s] - %(name)s - %(message)s (%(filename)s:%(lineno)d)"
    )
    
    # Обработчик для консоли
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.INFO)
    console_handler.setFormatter(formatter)
    
    # Добавляем обработчик в логгер
    logger.addHandler(console_handler)
    
    # Опционально: добавляем обработчик для записи в файл
    try:
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)
            
        file_handler = RotatingFileHandler(
            f"{log_dir}/wallet_service.log", 
            maxBytes=10*1024*1024,  # 10 MB
            backupCount=5
        )
        file_handler.setLevel(logging.INFO)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    except Exception as e:
        print(f"Не удалось настроить запись логов в файл: {e}")
    
    # Устанавливаем этот логгер как корневой, чтобы все модули его использовали
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    root_logger.addHandler(console_handler)
    if 'file_handler' in locals():
        root_logger.addHandler(file_handler)
    
    # Отключаем стандартные логи библиотек
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.error").setLevel(logging.ERROR)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    
    return logger