import uvicorn
import threading  # Добавлен импорт
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api.routes import wallets
from app.config import settings
from app.database import Base, engine
from sqlalchemy import text
from app.kafka.consumer import start_consumer_in_thread
from app.logging import setup_logging  # Импорт функции для запуска consumer


# Создание всех таблиц при запуске приложения
Base.metadata.create_all(bind=engine)

logger = setup_logging()
logger.info("Wallet Service запускается...")

app = FastAPI(title="Wallet Service", description="Генерация и управление криптовалютными кошельками")

# Настройка CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",      # Локальная разработка
        "http://localhost:3012",      # Локальная разработка
        "https://app.bitmei.com",     # Продакшен фронт
        "https://*.ngrok.io",         # Ngrok домены (если нужно)
    ],
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS"],  # Конкретные методы
    allow_headers=["*"],
)
# Регистрация роутов
app.include_router(wallets.router, prefix="/api/wallets", tags=["wallets"])

@app.get("/health", tags=["health"])
async def health_check():
    """Проверка здоровья сервиса"""
    return {"status": "ok"}


@app.on_event("startup")
async def startup_db_client():
    try:
        with engine.connect() as conn:
            result = conn.execute(text("SELECT 1"))
            print("Соединение с базой данных установлено успешно")
            
        # Запуск Kafka consumer'а для обработки запросов от Treasury Service
        start_consumer_in_thread()
        print("Kafka consumer для запросов от Treasury Service запущен")
        
    except Exception as e:
        print(f"Ошибка при инициализации: {str(e)}")


if __name__ == "__main__":
    uvicorn.run("app.main:app", host="0.0.0.0", port=settings.PORT, reload=settings.DEBUG)

    # uvicorn app.main:app --reload --port 8005