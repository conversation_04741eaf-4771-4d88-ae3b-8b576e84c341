import logging
import secrets
from typing import Tu<PERSON>, Optional, Literal
from app.config import settings
from app.crypto.crypto_utils import validate_key_address_match

logger = logging.getLogger("app.services.tron_service")

class TronService:
    """Сервис для работы с TRON и токенами TRC20 (USDT)"""
    
    # Константы для USDT-TRC20
    USDT_CONTRACT = "TR7NHqjeKQxGTCi8q8ZY4pL8otSzgjLj6t"  # USDT-TRC20 контракт на TRON
    
    @staticmethod
    def generate_address(user_id: int, token_type: Literal['TRX', 'USDT'] = 'TRX') -> Tuple[str, str]:
        """
        Генерирует новый TRON адрес и приватный ключ с использованием tronpy
        
        Args:
            user_id: ID пользователя
            token_type: Тип токена ('TRX' или 'USDT')
            
        Returns:
            Tuple[str, str]: (TRON адрес, приватный ключ)
        """
        try:
            logger.info(f"Генерация {token_type} адреса для пользователя {user_id}")
            
            # Импортируем здесь, чтобы не требовало при импорте модуля
            from tronpy.keys import PrivateKey
            
            # Генерируем случайный приватный ключ (32 байта)
            private_key_bytes = secrets.token_bytes(32)
            private_key_hex = private_key_bytes.hex()
            
            # Создаем экземпляр PrivateKey напрямую из библиотеки tronpy
            private_key = PrivateKey(private_key_bytes)
            
            # Получаем адрес с использованием tronpy (метод гарантированно даст правильный адрес)
            tron_address = private_key.public_key.to_base58check_address()
            
            # Проверяем соответствие ключа и адреса
            currency = "TRON" if token_type == "TRX" else "USDT-TRC20"
            is_valid = validate_key_address_match(private_key_hex, tron_address, currency)
            
            if not is_valid:
                raise ValueError(f"Generated {token_type} address does not match private key")
            
            logger.info(f"{token_type} адрес успешно сгенерирован: {tron_address}")
            return tron_address, private_key_hex
            
        except Exception as e:
            logger.error(f"Ошибка при генерации {token_type} адреса: {str(e)}")
            raise ValueError(f"Failed to generate {token_type} address: {str(e)}")
    
    @staticmethod
    def validate_address(address: str, token_type: Literal['TRX', 'USDT'] = 'TRX') -> bool:
        """
        Проверяет валидность TRON адреса с использованием tronpy
        
        Args:
            address: TRON адрес для проверки
            token_type: Тип токена ('TRX' или 'USDT')
            
        Returns:
            bool: True если адрес валидный, иначе False
        """
        try:
            logger.info(f"Валидация {token_type} адреса: {address}")
            
            # Импортируем здесь, чтобы не требовало при импорте модуля
            from tronpy.keys import is_address
            
            # Используем встроенную функцию проверки адресов tronpy
            valid = is_address(address)
            
            if valid:
                logger.info(f"{token_type} адрес успешно валидирован: {address}")
            else:
                logger.warning(f"Невалидный формат {token_type} адреса: {address}")
                
            return valid
            
        except Exception as e:
            logger.error(f"Ошибка при валидации {token_type} адреса {address}: {str(e)}")
            return False
    
    @staticmethod
    def get_balance(address: str, token_type: Literal['TRX', 'USDT'] = 'TRX') -> Optional[float]:
        """
        Получает баланс TRON адреса или токена USDT-TRC20
        
        Args:
            address: TRON адрес
            token_type: Тип токена ('TRX' или 'USDT')
            
        Returns:
            Optional[float]: Баланс в TRX/USDT или None при ошибке
        """
        try:
            logger.info(f"Запрос баланса {token_type} для адреса: {address}")
            
            # Импортируем tronpy для работы с сетью TRON
            from tronpy import Tron
            from tronpy.providers import HTTPProvider
            
            # Инициализируем клиент TRON
            client = Tron(HTTPProvider(settings.TRON_API_URL))
            
            if token_type == 'TRX':
                # Получаем баланс TRX
                balance_sun = client.get_account_balance(address)
                # TRX в SUN, нужно перевести в TRX (делим на 10^6)
                balance = balance_sun / 1e6
            else:
                # Получаем баланс USDT-TRC20
                contract = client.get_contract(TronService.USDT_CONTRACT)
                # Вызываем метод balanceOf смарт-контракта
                balance_sun = contract.functions.balanceOf(address)
                # USDT в микроединицах, нужно перевести в USDT (делим на 10^6)
                balance = balance_sun / 1e6
            
            logger.info(f"Баланс {token_type} для адреса {address}: {balance}")
            return balance
            
        except Exception as e:
            logger.error(f"Ошибка при получении баланса {token_type} для адреса {address}: {str(e)}")
            return None