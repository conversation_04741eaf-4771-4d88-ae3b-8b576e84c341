import logging
import requests
import secrets
from typing import Tu<PERSON>, Optional, Literal
from eth_account import Account
from eth_utils import is_address, to_checksum_address

from app.config import settings
from app.crypto.crypto_utils import validate_key_address_match, encrypt_private_key, decrypt_private_key

logger = logging.getLogger("app.services.eth_service")

class ETHService:
    """Сервис для работы с Ethereum и токенами ERC20 (ETH, USDC)"""
    
    # Константы для адресов контрактов
    USDC_CONTRACT = "******************************************"  # USDC на Ethereum mainnet
    
    @staticmethod
    def generate_address(user_id: int, token_type: Literal['ETH', 'USDC'] = 'ETH') -> Tuple[str, str]:
        """
        Генерирует новый Ethereum адрес и приватный ключ
        
        Args:
            user_id: ID пользователя
            token_type: Тип токена ('ETH' или 'USDC')
            
        Returns:
            Tuple[str, str]: (Ethereum адрес, приватный ключ в формате hex без префикса 0x)
        """
        try:
            logger.info(f"Генерация {token_type} адреса для пользователя {user_id}")
            
            # Генерация случайного приватного ключа (32 байта), как в TronService
            private_key_bytes = secrets.token_bytes(32)
            private_key_hex = private_key_bytes.hex()  # без префикса 0x
            
            # Создаем аккаунт из приватного ключа
            account = Account.from_key("0x" + private_key_hex)
            ethereum_address = account.address
            
            # Определяем тип валюты для проверки
            currency = "ETH" if token_type == "ETH" else "USDC-ERC20"
            
            # Проверяем и логируем соответствие приватного ключа и адреса
            is_valid = validate_key_address_match(private_key_hex, ethereum_address, currency)
            
            if not is_valid:
                raise ValueError(f"Generated {token_type} address does not match private key")
            
            logger.info(f"{token_type} адрес успешно сгенерирован: {ethereum_address}")
            return ethereum_address, private_key_hex
            
        except Exception as e:
            logger.error(f"Ошибка при генерации {token_type} адреса: {str(e)}")
            raise ValueError(f"Failed to generate {token_type} address: {str(e)}")
    
    @staticmethod
    def validate_address(address: str, token_type: Literal['ETH', 'USDC'] = 'ETH') -> bool:
        """
        Проверяет валидность Ethereum адреса
        
        Args:
            address: Ethereum адрес для проверки
            token_type: Тип токена ('ETH' или 'USDC')
            
        Returns:
            bool: True если адрес валидный, иначе False
        """
        try:
            logger.info(f"Валидация {token_type} адреса: {address}")
            
            # Используем eth_utils для базовой валидации формата
            if not is_address(address):
                logger.warning(f"Невалидный формат {token_type} адреса: {address}")
                return False
            
            # Нормализуем адрес в checksum формат
            checksum_address = to_checksum_address(address)
            
            # Опционально: проверка через Etherscan API
            try:
                # Базовые параметры запроса
                params = {
                    "module": "account",
                    "action": "balance",
                    "address": checksum_address,
                    "tag": "latest",
                    "apikey": settings.ETH_INFURA_KEY
                }
                
                # Если это USDC, меняем параметры запроса
                if token_type == 'USDC':
                    params["action"] = "tokenbalance"
                    params["contractaddress"] = ETHService.USDC_CONTRACT
                
                response = requests.get("https://api.etherscan.io/api", params=params)
                data = response.json()
                
                if data["status"] != "1":
                    logger.warning(f"API валидация не прошла для {token_type} адреса: {address}")
                    return False
                    
            except Exception as api_error:
                logger.error(f"Ошибка при API валидации {token_type} адреса {address}: {str(api_error)}")
                # API ошибки не должны останавливать валидацию, если формат адреса верный
            
            logger.info(f"{token_type} адрес успешно валидирован: {address}")
            return True
            
        except Exception as e:
            logger.error(f"Ошибка при валидации {token_type} адреса {address}: {str(e)}")
            return False
    
    @staticmethod
    def get_balance(address: str, token_type: Literal['ETH', 'USDC'] = 'ETH') -> Optional[float]:
        """
        Получает баланс Ethereum адреса или токена USDC
        
        Args:
            address: Ethereum адрес
            token_type: Тип токена ('ETH' или 'USDC')
            
        Returns:
            Optional[float]: Баланс в ETH/USDC или None при ошибке
        """
        try:
            logger.info(f"Запрос баланса {token_type} для адреса: {address}")
            
            # Для ETH запрашиваем баланс напрямую
            if token_type == 'ETH':
                params = {
                    "module": "account",
                    "action": "balance",
                    "address": address,
                    "tag": "latest",
                    "apikey": settings.ETH_INFURA_KEY
                }
                # ETH в wei, нужно перевести в ETH (делим на 10^18)
                decimals = 18
            # Для USDC используем токен контракт
            else:
                params = {
                    "module": "account",
                    "action": "tokenbalance",
                    "contractaddress": ETHService.USDC_CONTRACT,
                    "address": address,
                    "tag": "latest",
                    "apikey": settings.ETH_INFURA_KEY
                }
                # USDC в микроединицах, нужно перевести в USDC (делим на 10^6)
                decimals = 6
            
            response = requests.get("https://api.etherscan.io/api", params=params)
            data = response.json()
            
            if data["status"] == "1":
                # Конвертируем с учетом десятичных разрядов
                balance = float(data["result"]) / (10 ** decimals)
                logger.info(f"Баланс {token_type} для адреса {address}: {balance}")
                return balance
            else:
                logger.warning(f"Не удалось получить баланс {token_type} для адреса {address}: {data.get('message', 'Unknown error')}")
                return None
                
        except Exception as e:
            logger.error(f"Ошибка при получении баланса {token_type} для адреса {address}: {str(e)}")
            return None