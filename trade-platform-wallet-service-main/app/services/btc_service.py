import logging
import requests
import secrets
from typing import <PERSON>ple, Optional

from app.config import settings
from app.crypto.crypto_utils import validate_key_address_match, encrypt_private_key, decrypt_private_key

logger = logging.getLogger("app.services.btc_service")

class BTCService:
    """Сервис для работы с Bitcoin"""
    
    @staticmethod
    def generate_address(user_id: int) -> Tuple[str, str]:
        """
        Генерирует новый Bitcoin адрес и приватный ключ
        
        Args:
            user_id: ID пользователя
            
        Returns:
            Tuple[str, str]: (Bitcoin адрес, приватный ключ в формате WIF)
        """
        try:
            logger.info(f"Генерация Bitcoin адреса для пользователя {user_id}")
            
            # Использование BlockCypher API для генерации адреса
            response = requests.post(
                "https://api.blockcypher.com/v1/btc/main/addrs",
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code != 201:
                logger.error(f"Не удалось сгенерировать BTC адрес через API: {response.text}")
                raise ValueError(f"Failed to generate BTC address: HTTP {response.status_code}")
            
            data = response.json()
            bitcoin_address = data["address"]
            private_key = data["private"]  # WIF формат
            
            logger.info(f"Bitcoin адрес успешно сгенерирован: {bitcoin_address}")
            
            # Проверяем соответствие ключа и адреса
            is_valid = validate_key_address_match(private_key, bitcoin_address, "BTC")
            
            if not is_valid:
                logger.error(f"⚠️ Генерация BTC адреса завершилась с ошибкой валидации: приватный ключ не соответствует адресу")
                raise ValueError("Generated BTC address does not match private key")
                
            logger.info(f"✅ Валидация BTC адреса успешна: приватный ключ соответствует адресу {bitcoin_address}")
            
            return bitcoin_address, private_key
            
        except Exception as e:
            logger.error(f"Ошибка при генерации Bitcoin адреса: {str(e)}")
            raise ValueError(f"Failed to generate Bitcoin address: {str(e)}")
    
    @staticmethod
    def validate_address(address: str) -> bool:
        """
        Проверяет валидность Bitcoin адреса
        
        Args:
            address: Bitcoin адрес для проверки
            
        Returns:
            bool: True если адрес валидный, иначе False
        """
        try:
            logger.info(f"Валидация Bitcoin адреса: {address}")
            
            # Базовая проверка формата (можно расширить)
            if not (address.startswith('1') or address.startswith('3') or address.startswith('bc1')):
                logger.warning(f"Неправильный формат Bitcoin адреса: {address}")
                return False
            
            # Проверка через BlockCypher API
            response = requests.get(
                f"https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance"
            )
            
            if response.status_code == 200:
                logger.info(f"Bitcoin адрес успешно валидирован: {address}")
                return True
            else:
                logger.warning(f"Bitcoin адрес не прошел валидацию: {address}, ответ API: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"Ошибка при валидации Bitcoin адреса {address}: {str(e)}")
            return False
    
    @staticmethod
    def get_balance(address: str) -> Optional[float]:
        """
        Получает баланс Bitcoin адреса
        
        Args:
            address: Bitcoin адрес
            
        Returns:
            Optional[float]: Баланс в BTC или None при ошибке
        """
        try:
            logger.info(f"Запрос баланса для Bitcoin адреса: {address}")
            
            response = requests.get(
                f"https://api.blockcypher.com/v1/btc/main/addrs/{address}/balance"
            )
            
            if response.status_code == 200:
                data = response.json()
                # Баланс в satoshis, нужно перевести в BTC (делим на 10^8)
                balance = float(data["final_balance"]) / 1e8
                logger.info(f"Баланс Bitcoin для адреса {address}: {balance} BTC")
                return balance
            else:
                logger.warning(f"Не удалось получить баланс Bitcoin для адреса {address}: ответ API {response.status_code}")
                return None
                
        except Exception as e:
            logger.error(f"Ошибка при получении баланса Bitcoin для адреса {address}: {str(e)}")
            return None
    
    @staticmethod
    def send_transaction(from_private_key: str, to_address: str, amount: float) -> Optional[str]:
        """
        Отправляет Bitcoin транзакцию
        
        Args:
            from_private_key: Приватный ключ отправителя
            to_address: Bitcoin адрес получателя
            amount: Сумма в BTC
            
        Returns:
            Optional[str]: Хеш транзакции или None при ошибке
        """
        try:
            logger.info(f"Подготовка к отправке {amount} BTC на адрес {to_address}")
            
            # В реальной реализации здесь должен быть код для создания и подписи транзакции
            # Пример использования библиотеки bitcoinlib:
            # from bitcoinlib.wallets import Wallet
            # wallet = Wallet.from_key(from_private_key)
            # tx_hash = wallet.send_to(to_address, amount, fee='normal')
            
            # Заглушка для примера
            logger.warning("⚠️ Метод отправки транзакций не реализован полностью!")
            return "sample_transaction_hash"
            
        except Exception as e:
            logger.error(f"Ошибка при отправке Bitcoin транзакции: {str(e)}")
            return None