#!/bin/bash

# BitMei WebSocket Gateway Deploy Script
# Автоматическое обновление и перезапуск сервера

set -e  # Выход при ошибке

echo "🚀 Starting deployment..."

# Функция для логирования
log() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1"
}

# Проверка что мы в правильной директории
if [ ! -f "coinapi_gateway.py" ]; then
    log "❌ Error: coinapi_gateway.py not found. Please run from project directory"
    exit 1
fi

# Остановка текущего процесса
log "🛑 Stopping current server..."
pkill -f coinapi_gateway.py || true
sleep 2

# Обновление кода
log "📥 Pulling latest changes..."
git fetch origin
git reset --hard origin/main

# Проверка Python окружения
if [ ! -d "venv" ]; then
    log "🐍 Creating virtual environment..."
    python3 -m venv venv
fi

# Активация venv и обновление зависимостей
log "📦 Installing dependencies..."
source venv/bin/activate
pip install --upgrade pip
pip install -r requirements.txt

# Проверка синтаксиса
log "🔍 Checking syntax..."
python -m py_compile coinapi_gateway.py

# Запуск сервера
log "🚀 Starting server..."
source venv/bin/activate
nohup python coinapi_gateway.py > ../server.log 2>&1 &
SERVER_PID=$!
echo $SERVER_PID > ../server.pid

# Проверка что сервер запустился
sleep 3
if ps -p $SERVER_PID > /dev/null; then
    log "✅ Server started successfully! PID: $SERVER_PID"
    
    # Простой тест подключения
    if command -v python3 &> /dev/null; then
        echo "🧪 Testing connection..."
        python3 -c "
import asyncio
import websockets
import json

async def test():
    try:
        async with websockets.connect('ws://localhost:8765') as ws:
            greeting = await ws.recv()
            data = json.loads(greeting)
            if data.get('type') == 'connected':
                print('✅ Connection test passed')
            else:
                print('⚠️ Unexpected greeting:', greeting)
    except Exception as e:
        print('❌ Connection test failed:', e)

asyncio.run(test())
        " 2>/dev/null || echo "⚠️ Connection test skipped (websockets not available)"
    fi
else
    log "❌ Server failed to start"
    cat ../server.log | tail -20
    exit 1
fi

log "🎉 Deployment completed successfully!"
log "📊 Server status: http://your-domain:8765"
log "📝 Logs: tail -f ../server.log"