# Python
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
*.so
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Virtual environments
venv/
env/
ENV/

# Server files
server.log
server.pid
*.log

# Test files (except official ones)
test_remote.py
test_simple.py
test_websocket.py
test_price_sync.py
test_*.py
test_*.html
wsout.log
*.fish
test-all.sh
setup_and_test.sh
run_local.sh

# Documentation (except official README)
docs/
*.md
!README.md

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db