# Порт для вебсокет-сервера
WS_SERVER_PORT=8765
# Хост для вебсокет-сервера, 0.0.0.0 позволяет принимать соединения со всех интерфейсов
WS_SERVER_HOST=0.0.0.0
# Базовый URL для WebSocket Binance
BINANCE_WS_URL=wss://stream.binance.com:9443/ws
# Базовый URL для REST API Binance
BINANCE_API_URL=https://api.binance.com/api/v3
# API ключ Binance (опционально)
BINANCE_API_KEY=
# API секрет Binance (опционально)
BINANCE_API_SECRET=
# Время таймаута для запросов (в миллисекундах)
REQUEST_TIMEOUT=10000
# Уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL=INFO