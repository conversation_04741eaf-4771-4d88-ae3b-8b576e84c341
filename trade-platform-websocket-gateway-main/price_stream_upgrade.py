"""
Улучшенный Price Stream для единого источника цен
- Приоритетная отправка price updates
- Unified price management
- Лучшая синхронизация
"""

import asyncio
import json
import time
from typing import Dict, Set, Optional
import logging

logger = logging.getLogger("unified_price_stream")

class UnifiedPriceManager:
    """Унифицированный менеджер цен для стабильной синхронизации"""
    
    def __init__(self):
        # Текущие цены с timestamp
        self.current_prices: Dict[str, Dict] = {}
        # Подписчики на приоритетные обновления цен
        self.priority_price_subscribers: Dict[any, Set[str]] = {}
        # Буфер для batch отправки
        self.price_update_buffer: Dict[str, Dict] = {}
        # Таймеры для debounce
        self.debounce_timers: Dict[str, asyncio.Task] = {}
        
        # Настройки
        self.PRICE_DEBOUNCE_MS = 50  # Максимум 20 обновлений в секунду
        self.PRIORITY_UPDATE_MS = 100  # Критичные обновления каждые 100мс
        
    async def update_price(self, symbol: str, price: float, source: str = "binance"):
        """Обновление цены с приоритетной обработкой"""
        now = time.time() * 1000
        
        # Сохраняем цену
        price_data = {
            "price": price,
            "timestamp": now,
            "source": source,
            "symbol": symbol
        }
        
        self.current_prices[symbol] = price_data
        self.price_update_buffer[symbol] = price_data
        
        # Отменяем предыдущий debounce таймер
        if symbol in self.debounce_timers:
            self.debounce_timers[symbol].cancel()
        
        # Создаем новый debounce таймер
        self.debounce_timers[symbol] = asyncio.create_task(
            self._debounced_price_send(symbol)
        )
        
        # Немедленная отправка для критичных случаев
        await self._send_priority_updates(symbol, price_data)
    
    async def _debounced_price_send(self, symbol: str):
        """Debounced отправка цен для избежания спама"""
        await asyncio.sleep(self.PRICE_DEBOUNCE_MS / 1000)
        
        if symbol in self.price_update_buffer:
            price_data = self.price_update_buffer.pop(symbol)
            await self._broadcast_price_update(symbol, price_data)
    
    async def _send_priority_updates(self, symbol: str, price_data: Dict):
        """Отправка приоритетных обновлений цен"""
        # Отправляем тем, кто подписан на приоритетные обновления
        for websocket, symbols in self.priority_price_subscribers.items():
            if symbol in symbols:
                try:
                    await websocket.send(json.dumps({
                        "type": "priceUpdate",
                        "symbol": symbol,
                        "price": price_data["price"],
                        "timestamp": price_data["timestamp"],
                        "priority": True
                    }))
                except Exception as e:
                    logger.error(f"Error sending priority price update: {e}")
                    # Удаляем неработающее соединение
                    self._cleanup_subscriber(websocket)
    
    async def _broadcast_price_update(self, symbol: str, price_data: Dict):
        """Обычная широковещательная отправка цен"""
        message = {
            "type": "ticker",
            "symbol": symbol,
            "price": price_data["price"],
            "timestamp": price_data["timestamp"]
        }
        
        # Отправляем всем обычным подписчикам  
        # Импортируем из основного модуля
        from coinapi_gateway import price_ticker_subscriptions
        
        disconnected = []
        for websocket, symbols in price_ticker_subscriptions.items():
            if symbol in symbols and websocket not in self.priority_price_subscribers:
                try:
                    await websocket.send(json.dumps(message))
                except:
                    disconnected.append(websocket)
        
        # Убираем отключенных
        for ws in disconnected:
            self._cleanup_subscriber(ws)
    
    def subscribe_priority_price(self, websocket, symbol: str):
        """Подписка на приоритетные обновления цен"""
        if websocket not in self.priority_price_subscribers:
            self.priority_price_subscribers[websocket] = set()
        
        self.priority_price_subscribers[websocket].add(symbol)
        
        # Отправляем текущую цену сразу
        if symbol in self.current_prices:
            asyncio.create_task(self._send_current_price(websocket, symbol))
    
    async def _send_current_price(self, websocket, symbol: str):
        """Отправка текущей цены новому подписчику"""
        if symbol in self.current_prices:
            price_data = self.current_prices[symbol]
            try:
                await websocket.send(json.dumps({
                    "type": "currentPrice",
                    "symbol": symbol,
                    "price": price_data["price"],
                    "timestamp": price_data["timestamp"]
                }))
            except Exception as e:
                logger.error(f"Error sending current price: {e}")
    
    def unsubscribe_priority_price(self, websocket, symbol: str):
        """Отписка от приоритетных обновлений"""
        if websocket in self.priority_price_subscribers:
            self.priority_price_subscribers[websocket].discard(symbol)
            if not self.priority_price_subscribers[websocket]:
                del self.priority_price_subscribers[websocket]
    
    def _cleanup_subscriber(self, websocket):
        """Очистка отключенного подписчика"""
        self.priority_price_subscribers.pop(websocket, None)
    
    def get_current_price(self, symbol: str) -> Optional[float]:
        """Получение текущей цены"""
        if symbol in self.current_prices:
            return self.current_prices[symbol]["price"]
        return None
    
    def get_price_with_metadata(self, symbol: str) -> Optional[Dict]:
        """Получение цены с метаданными"""
        return self.current_prices.get(symbol)


# Глобальный экземпляр
unified_price_manager = UnifiedPriceManager()


# Новые handler функции для интеграции
async def handle_subscribe_priority_price(websocket, data):
    """Подписка на приоритетные обновления цен"""
    symbol = data.get("symbol", "")
    
    if not symbol:
        await websocket.send(json.dumps({
            "type": "error",
            "code": "MISSING_SYMBOL",
            "message": "Symbol is required for priority price subscription"
        }))
        return
    
    unified_price_manager.subscribe_priority_price(websocket, symbol)
    
    await websocket.send(json.dumps({
        "type": "priorityPriceSubscribed",
        "symbol": symbol
    }))
    
    logger.info(f"Client subscribed to priority price updates for {symbol}")


async def handle_unsubscribe_priority_price(websocket, data):
    """Отписка от приоритетных обновлений цен"""
    symbol = data.get("symbol", "")
    
    unified_price_manager.unsubscribe_priority_price(websocket, symbol)
    
    await websocket.send(json.dumps({
        "type": "priorityPriceUnsubscribed", 
        "symbol": symbol
    }))


# Интеграция с существующим кодом
def integrate_unified_price_manager():
    """Интеграция с существующим Binance WebSocket handler"""
    
    async def enhanced_ticker_handler(message, original_symbol):
        """Улучшенный handler для ticker обновлений"""
        try:
            data = json.loads(message)
            
            if "c" in data:  # current price from Binance
                price = float(data["c"])
                
                # Обновляем через unified manager
                await unified_price_manager.update_price(original_symbol, price)
                
                # Сохраняем для обратной совместимости
                current_prices[original_symbol] = price
                
        except Exception as e:
            logger.error(f"Error in enhanced ticker handler: {e}")
    
    return enhanced_ticker_handler