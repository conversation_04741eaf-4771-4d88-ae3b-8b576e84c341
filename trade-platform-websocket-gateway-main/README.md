# WebSocket Gateway для торговой платформы

Этот сервис предоставляет WebSocket подключение к Binance API для получения данных о ценах и торговых парах в реальном времени.

## 🚀 Новые возможности (v2.0)

- **UnifiedPriceManager** - единый источник правды для цен с приоритетными обновлениями
- **PerformanceMonitor** - real-time мониторинг производительности и метрики
- **EnhancedConnectionManager** - улучшенное управление соединениями с auto-recovery
- **Health Endpoints** - HTTP API для мониторинга состояния сервиса

## Функциональность

- Real-time данные с Binance WebSocket API
- Поддержка множественных таймфреймов (1MIN, 3MIN, 5MIN, 15MIN, 30MIN, 1HRS, 4HRS, 1DAY, 1WKS, 1MTH)
- Автоматическое переподключение при обрывах связи
- Буферизация сообщений во время переподключения  
- Heartbeat механизм для проверки соединения
- Ограничение частоты обновлений для экономии ресурсов
- HTTP REST API для получения исторических данных
- **НОВОЕ**: Приоритетные обновления цен с debouncing
- **НОВОЕ**: Performance monitoring и алерты
- **НОВОЕ**: Health check endpoints

## Запуск

```bash
# Установка зависимостей
pip install -r requirements.txt

# Запуск сервиса
python coinapi_gateway.py
```

По умолчанию:
- WebSocket сервер: порт 8765
- Health endpoints: порт 8766

Можно изменить порт через переменную окружения:

```bash
WS_SERVER_PORT=9000 python coinapi_gateway.py
```

Или через аргумент командной строки:

```bash
python coinapi_gateway.py --port 9000
```

## 📊 Health Endpoints

### GET /health
Проверка состояния сервиса
```bash
curl http://localhost:8766/health
```

Ответ:
```json
{
  "status": "healthy",
  "timestamp": **********.123,
  "uptime_seconds": 3600.5,
  "checks": {
    "performance_monitor": "ok",
    "connection_manager": "ok", 
    "websocket_server": "ok"
  }
}
```

### GET /metrics
Детальные метрики производительности
```bash
curl http://localhost:8766/metrics
```

### GET /connections
Состояние соединений с Binance
```bash
curl http://localhost:8766/connections
```

### POST /reset-metrics
Сброс метрик (для тестирования)
```bash
curl -X POST http://localhost:8766/reset-metrics
```

## API

### WebSocket команды

#### Подписка на данные
```json
{
  "type": "subscribe",
  "symbol": "BTC_USDT",
  "timeframe": "1MIN",
  "fetchHistory": true
}
```

#### Подписка на ticker (обновления цен)
```json
{
  "type": "subscribeTicker",
  "symbol": "BTC_USDT"
}
```

#### 🆕 Подписка на приоритетные обновления цен
```json
{
  "type": "subscribePriorityPrice",
  "symbol": "BTC_USDT"
}
```

#### Отписка от данных
```json
{
  "type": "unsubscribe", 
  "symbol": "BTC_USDT"
}
```

#### Отписка от ticker
```json
{
  "type": "unsubscribeTicker",
  "symbol": "BTC_USDT"
}
```

#### 🆕 Отписка от приоритетных цен
```json
{
  "type": "unsubscribePriorityPrice",
  "symbol": "BTC_USDT"
}
```

#### Получение статистики по паре
```json
{
  "type": "fetch_pair_stats",
  "symbol": "BTC_USDT",
  "timeframe": "1MIN"
}
```

#### Получение исторических данных
```json
{
  "type": "fetchHistory",
  "symbol": "BTC_USDT",
  "timeframe": "1MIN",
  "limit": 300,
  "to": **********000
}
```

#### Проверка соединения
```json
{
  "type": "ping"
}
```

### Ответы WebSocket

#### Подтверждение подписки на ticker
```json
{
  "type": "tickerSubscribed",
  "symbol": "BTC_USDT"
}
```

#### 🆕 Обновления ticker (каждые 1-3 сек)
```json
{
  "type": "ticker",
  "symbol": "BTC_USDT",
  "price": 47050.25,
  "timestamp": **********000
}
```

#### 🆕 Приоритетные обновления цен
```json
{
  "type": "priceUpdate",
  "symbol": "BTC_USDT",
  "price": 47050.25,
  "timestamp": **********000,
  "priority": true
}
```

#### Текущая цена при смене timeframe
```json
{
  "type": "currentPrice",
  "symbol": "BTC_USDT",
  "price": 47050.25,
  "timestamp": **********000
}
```

#### Подтверждение подписки
```json
{
  "type": "subscribed",
  "symbol": "BTC_USDT",
  "timeframe": "1MIN"
}
```

#### Исторические данные
```json
{
  "type": "history",
  "symbol": "BTC_USDT", 
  "timeframe": "1MIN",
  "data": [...],
  "hasMore": true,
  "to": 1640992600000
}
```

#### Новые данные
```json
{
  "type": "kline",
  "symbol": "BTC_USDT",
  "timeframe": "1MIN", 
  "data": {
    "timestamp": **********000,
    "open": 47000.00,
    "high": 47100.00,
    "low": 46900.00,
    "close": 47050.00,
    "volume": 1.23456789,
    "isFinal": true
  }
}
```

#### Статистика по паре
```json
{
  "type": "stats",
  "symbol": "BTC_USDT",
  "data": {
    "indexPrice": "47050.00",
    "oraclePrice": "47238.20", 
    "change24h": "+1250.00",
    "changePercent": "+2.73",
    "volume24h": "1234567890.12",
    "high24h": "48000.00",
    "low24h": "45500.00",
    "volBase24h": "26234.56",
    "volUsd24h": "1.23B",
    "isPositiveChange": true
  }
}
```

#### Pong ответ
```json
{
  "type": "pong",
  "timestamp": **********000
}
```

## 🧪 Тестирование

Запуск интеграционных тестов:

```bash
# В одном терминале запустите сервер
python coinapi_gateway.py

# В другом терминале запустите тесты
python test_integration.py
```

## Примеры использования

### JavaScript клиент с новыми возможностями
```javascript
const ws = new WebSocket('ws://localhost:8765');

ws.onopen = function() {
    // Подписываемся на приоритетные обновления цен
    ws.send(JSON.stringify({
        type: 'subscribePriorityPrice',
        symbol: 'BTC_USDT'
    }));
    
    // Подписываемся на обычный ticker
    ws.send(JSON.stringify({
        type: 'subscribeTicker',
        symbol: 'BTC_USDT'
    }));
    
    // Подписываемся на график
    ws.send(JSON.stringify({
        type: 'subscribe',
        symbol: 'BTC_USDT',
        timeframe: '1MIN',
        fetchHistory: true
    }));
};

ws.onmessage = function(event) {
    const data = JSON.parse(event.data);
    
    if (data.type === 'priceUpdate' && data.priority) {
        // Приоритетное обновление цены (быстрее обычного ticker)
        updatePriceInUI(data.price);
    } else if (data.type === 'ticker') {
        // Обычное обновление ticker
        updateTickerInUI(data.price);
    } else if (data.type === 'currentPrice') {
        // Синхронизация цены при смене timeframe
        synchronizePriceInUI(data.price);
    } else if (data.type === 'kline') {
        // Обновление графика
        updateChartInUI(data.data);
    }
};

function updatePriceInUI(price) {
    document.getElementById('current-price').textContent = price;
}
```

### Python клиент
```python
import asyncio
import websockets
import json

async def client():
    uri = "ws://localhost:8765"
    async with websockets.connect(uri) as websocket:
        # Подписываемся на приоритетные цены
        await websocket.send(json.dumps({
            "type": "subscribePriorityPrice",
            "symbol": "BTC_USDT"
        }))
        
        # Подписываемся на данные
        await websocket.send(json.dumps({
            "type": "subscribe",
            "symbol": "BTC_USDT", 
            "timeframe": "1MIN",
            "fetchHistory": True
        }))
        
        # Слушаем сообщения
        async for message in websocket:
            data = json.loads(message)
            print(f"Получено: {data['type']}")
            
            if data['type'] == 'priceUpdate' and data.get('priority'):
                print(f"🚀 Приоритетная цена: {data['price']}")

asyncio.run(client())
```

## 📈 Мониторинг производительности

```python
import aiohttp

async def check_performance():
    async with aiohttp.ClientSession() as session:
        async with session.get('http://localhost:8766/metrics') as resp:
            metrics = await resp.json()
            
            print(f"Сообщений/сек: {metrics['metrics']['messages']['per_second']}")
            print(f"Средняя задержка: {metrics['metrics']['latency']['average_ms']}мс")
            print(f"Обновлений цен/сек: {metrics['metrics']['performance']['price_updates_per_second']}")
```

## 🔧 Архитектура

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Frontend      │◄──►│  WebSocket       │◄──►│   Binance       │
│   Clients       │    │  Gateway         │    │   API           │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                              │
                              ▼
                       ┌──────────────────┐
                       │  Health          │
                       │  Endpoints       │
                       │  (HTTP)          │
                       └──────────────────┘
                              │
            ┌─────────────────┼─────────────────┐
            ▼                 ▼                 ▼
    ┌───────────────┐ ┌───────────────┐ ┌───────────────┐
    │ UnifiedPrice  │ │ Performance   │ │ Enhanced      │
    │ Manager       │ │ Monitor       │ │ Connection    │
    │               │ │               │ │ Manager       │
    └───────────────┘ └───────────────┘ └───────────────┘
```

## 🎯 Рекомендации по использованию

1. **Для единого источника цен**: используйте `subscribePriorityPrice` для критичных UI компонентов
2. **Для мониторинга**: регулярно проверяйте `/health` и `/metrics` endpoints
3. **Для производительности**: следите за метриками latency и throughput
4. **Для надежности**: система автоматически восстанавливает соединения при сбоях

## Настройки через переменные окружения

- `WS_SERVER_HOST` - хост сервера (по умолчанию 0.0.0.0)
- `WS_SERVER_PORT` - порт сервера (по умолчанию 8765)
- `LOG_LEVEL` - уровень логирования (DEBUG, INFO, WARNING, ERROR, CRITICAL)
- `BINANCE_WS_URL` - URL Binance WebSocket API
- `BINANCE_API_URL` - URL Binance REST API
- `REQUEST_TIMEOUT` - таймаут HTTP запросов в миллисекундах