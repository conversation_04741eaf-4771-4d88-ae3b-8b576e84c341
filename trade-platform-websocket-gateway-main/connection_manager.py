"""
Улучшенный менеджер соединений для WebSocket Gateway
- Connection pooling
- Health monitoring
- Automatic recovery
"""

import asyncio
import json
import time
import logging
import websockets
from typing import Dict, Set, Optional, List
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger("connection_manager")

class ConnectionState(Enum):
    CONNECTING = "connecting"
    CONNECTED = "connected"
    DISCONNECTED = "disconnected"
    FAILED = "failed"
    RECOVERING = "recovering"

@dataclass
class ConnectionHealth:
    """Метрики здоровья соединения"""
    last_message_time: float
    message_count: int
    error_count: int
    reconnect_count: int
    latency_ms: float
    state: ConnectionState

class EnhancedConnectionManager:
    """Улучшенный менеджер соединений с мониторингом"""
    
    def __init__(self):
        # Пул соединений с Binance
        self.binance_connections: Dict[str, any] = {}
        # Здоровье соединений
        self.connection_health: Dict[str, ConnectionHealth] = {}
        # Активные символы для каждого соединения
        self.connection_symbols: Dict[str, Set[str]] = {}
        # Клиенты фронтенда
        self.frontend_clients: Set[any] = set()
        
        # Настройки
        self.HEALTH_CHECK_INTERVAL = 30  # секунд
        self.MAX_RECONNECT_ATTEMPTS = 5
        self.CONNECTION_TIMEOUT = 10  # секунд
        self.MESSAGE_TIMEOUT = 60  # секунд без сообщений = проблема
        
        # Задачи мониторинга
        self.health_monitor_task = None
        
    async def start(self):
        """Запуск менеджера соединений"""
        self.health_monitor_task = asyncio.create_task(self._health_monitor_loop())
        logger.info("Connection manager started")
    
    async def stop(self):
        """Остановка менеджера"""
        if self.health_monitor_task:
            self.health_monitor_task.cancel()
            try:
                await self.health_monitor_task
            except asyncio.CancelledError:
                pass
        
        # Закрываем все соединения
        for connection_key, ws in self.binance_connections.items():
            if ws:
                try:
                    await ws.close()
                    logger.info(f"Closed connection: {connection_key}")
                except Exception as e:
                    logger.error(f"Error closing connection {connection_key}: {e}")
        
        logger.info("Connection manager stopped")
    
    async def get_or_create_connection(self, symbol: str, stream_type: str = "ticker") -> Optional[any]:
        """Получить существующее соединение или создать новое"""
        connection_key = f"{symbol}@{stream_type}"
        
        # Проверяем существующее соединение
        if connection_key in self.binance_connections:
            ws = self.binance_connections[connection_key]
            health = self.connection_health.get(connection_key)
            
            # Если соединение здоровое, возвращаем его
            if ws and health and health.state == ConnectionState.CONNECTED:
                return ws
        
        # Создаем новое соединение
        return await self._create_connection(symbol, stream_type)
    
    async def _create_connection(self, symbol: str, stream_type: str) -> Optional[any]:
        """Создание нового соединения с Binance"""
        connection_key = f"{symbol}@{stream_type}"
        
        # Помечаем как connecting
        self.connection_health[connection_key] = ConnectionHealth(
            last_message_time=time.time(),
            message_count=0,
            error_count=0,
            reconnect_count=0,
            latency_ms=0,
            state=ConnectionState.CONNECTING
        )
        
        try:
            ws_url = f"wss://stream.binance.com:9443/ws/{connection_key}"
            
            start_time = time.time()
            ws = await asyncio.wait_for(
                websockets.connect(ws_url, ping_interval=30, ping_timeout=10),
                timeout=self.CONNECTION_TIMEOUT
            )
            
            # Измеряем latency
            connect_latency = (time.time() - start_time) * 1000
            
            # Обновляем здоровье
            health = self.connection_health[connection_key]
            health.state = ConnectionState.CONNECTED
            health.latency_ms = connect_latency
            
            # Сохраняем соединение
            self.binance_connections[connection_key] = ws
            self.connection_symbols[connection_key] = {symbol}
            
            # Запускаем обработчик сообщений
            asyncio.create_task(self._message_handler(connection_key, ws))
            
            logger.info(f"Created connection {connection_key} with latency {connect_latency:.2f}ms")
            return ws
            
        except Exception as e:
            logger.error(f"Failed to create connection {connection_key}: {e}")
            
            # Помечаем как failed
            if connection_key in self.connection_health:
                self.connection_health[connection_key].state = ConnectionState.FAILED
                self.connection_health[connection_key].error_count += 1
            
            return None
    
    async def _message_handler(self, connection_key: str, websocket):
        """Обработчик сообщений от Binance"""
        try:
            async for message in websocket:
                # Обновляем метрики
                if connection_key in self.connection_health:
                    health = self.connection_health[connection_key]
                    health.last_message_time = time.time()
                    health.message_count += 1
                
                # Обрабатываем сообщение
                await self._process_binance_message(connection_key, message)
                
        except Exception as e:
            logger.error(f"Message handler error for {connection_key}: {e}")
            
            # Обновляем здоровье
            if connection_key in self.connection_health:
                self.connection_health[connection_key].error_count += 1
                self.connection_health[connection_key].state = ConnectionState.DISCONNECTED
            
            # Запускаем восстановление
            await self._schedule_recovery(connection_key)
    
    async def _process_binance_message(self, connection_key: str, message: str):
        """Обработка сообщения от Binance"""
        try:
            data = json.loads(message)
            
            # Извлекаем символ
            symbol = self._extract_symbol_from_key(connection_key)
            
            # Определяем тип сообщения и обрабатываем
            if "c" in data:  # ticker price
                price = float(data["c"])
                await self._broadcast_price_update(symbol, price)
            elif data.get("e") == "kline":  # kline data
                await self._broadcast_kline_update(symbol, data)
                
        except Exception as e:
            logger.error(f"Error processing message from {connection_key}: {e}")
    
    def _extract_symbol_from_key(self, connection_key: str) -> str:
        """Извлечение символа из ключа соединения"""
        return connection_key.split("@")[0]
    
    async def _broadcast_price_update(self, symbol: str, price: float):
        """Рассылка обновления цены клиентам"""
        message = {
            "type": "ticker",
            "symbol": symbol,
            "price": price,
            "timestamp": int(time.time() * 1000)
        }
        
        await self._broadcast_to_clients(message)
    
    async def _broadcast_kline_update(self, symbol: str, kline_data: dict):
        """Рассылка обновления kline клиентам"""
        # Преобразуем данные Binance в наш формат
        k = kline_data.get("k", {})
        
        message = {
            "type": "kline",
            "symbol": symbol,
            "data": {
                "timestamp": k.get("t", int(time.time() * 1000)),
                "open": float(k.get("o", 0)),
                "high": float(k.get("h", 0)),
                "low": float(k.get("l", 0)),
                "close": float(k.get("c", 0)),
                "volume": float(k.get("v", 0)),
                "isFinal": k.get("x", False)
            }
        }
        
        await self._broadcast_to_clients(message)
    
    async def _broadcast_to_clients(self, message: dict):
        """Рассылка сообщения всем клиентам фронтенда"""
        disconnected_clients = []
        
        for client in self.frontend_clients:
            try:
                await client.send(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending to client: {e}")
                disconnected_clients.append(client)
        
        # Убираем отключенных клиентов
        for client in disconnected_clients:
            self.frontend_clients.discard(client)
    
    async def _health_monitor_loop(self):
        """Цикл мониторинга здоровья соединений"""
        while True:
            try:
                await asyncio.sleep(self.HEALTH_CHECK_INTERVAL)
                await self._check_connections_health()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Health monitor error: {e}")
    
    async def _check_connections_health(self):
        """Проверка здоровья всех соединений"""
        current_time = time.time()
        
        for connection_key, health in list(self.connection_health.items()):
            # Проверяем, давно ли были сообщения
            time_since_message = current_time - health.last_message_time
            
            if time_since_message > self.MESSAGE_TIMEOUT:
                logger.warning(f"Connection {connection_key} is stale (no messages for {time_since_message:.1f}s)")
                health.state = ConnectionState.DISCONNECTED
                await self._schedule_recovery(connection_key)
    
    async def _schedule_recovery(self, connection_key: str):
        """Планирование восстановления соединения"""
        if connection_key not in self.connection_health:
            return
        
        health = self.connection_health[connection_key]
        
        if health.reconnect_count >= self.MAX_RECONNECT_ATTEMPTS:
            logger.error(f"Max reconnect attempts reached for {connection_key}")
            health.state = ConnectionState.FAILED
            return
        
        health.state = ConnectionState.RECOVERING
        health.reconnect_count += 1
        
        # Ждем перед переподключением (exponential backoff)
        delay = min(2 ** health.reconnect_count, 60)
        await asyncio.sleep(delay)
        
        # Пытаемся переподключиться
        symbol = self._extract_symbol_from_key(connection_key)
        stream_type = connection_key.split("@")[1]
        
        new_ws = await self._create_connection(symbol, stream_type)
        if new_ws:
            logger.info(f"Successfully recovered connection {connection_key}")
        else:
            logger.error(f"Failed to recover connection {connection_key}")
    
    def add_frontend_client(self, websocket):
        """Добавление клиента фронтенда"""
        self.frontend_clients.add(websocket)
        logger.info(f"Added frontend client, total: {len(self.frontend_clients)}")
    
    def remove_frontend_client(self, websocket):
        """Удаление клиента фронтенда"""
        self.frontend_clients.discard(websocket)
        logger.info(f"Removed frontend client, total: {len(self.frontend_clients)}")
    
    def get_connection_stats(self) -> Dict:
        """Получение статистики соединений"""
        stats = {
            "total_connections": len(self.binance_connections),
            "healthy_connections": 0,
            "failed_connections": 0,
            "frontend_clients": len(self.frontend_clients),
            "connections": {}
        }
        
        for key, health in self.connection_health.items():
            stats["connections"][key] = {
                "state": health.state.value,
                "message_count": health.message_count,
                "error_count": health.error_count,
                "reconnect_count": health.reconnect_count,
                "latency_ms": health.latency_ms,
                "last_message_ago": time.time() - health.last_message_time
            }
            
            if health.state == ConnectionState.CONNECTED:
                stats["healthy_connections"] += 1
            elif health.state == ConnectionState.FAILED:
                stats["failed_connections"] += 1
        
        return stats


# Глобальный экземпляр
connection_manager = EnhancedConnectionManager()