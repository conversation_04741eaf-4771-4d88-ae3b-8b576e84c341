"""
HTTP endpoints для мониторинга здоровья и метрик WebSocket Gateway
"""

import json
import time
from aiohttp import web, web_request
from performance_monitor import performance_monitor
from connection_manager import connection_manager
import asyncio
import logging

logger = logging.getLogger("health_endpoints")

async def health_check(request: web_request.Request):
    """Endpoint для проверки здоровья сервиса"""
    try:
        # Базовые проверки
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "uptime_seconds": time.time() - performance_monitor.start_time,
            "checks": {
                "performance_monitor": "ok",
                "connection_manager": "ok",
                "websocket_server": "ok"
            }
        }
        
        # Проверяем состояние компонентов
        conn_stats = connection_manager.get_connection_stats()
        if conn_stats["failed_connections"] > conn_stats["healthy_connections"]:
            health_status["checks"]["connection_manager"] = "warning"
            health_status["status"] = "degraded"
        
        # Проверяем последние алерты
        if performance_monitor.performance_alerts:
            recent_alerts = [alert for alert in performance_monitor.performance_alerts 
                           if alert.get("severity") == "critical"]
            if recent_alerts:
                health_status["status"] = "unhealthy"
                health_status["critical_alerts"] = len(recent_alerts)
        
        status_code = 200 if health_status["status"] == "healthy" else 503
        return web.json_response(health_status, status=status_code)
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return web.json_response({
            "status": "error",
            "message": str(e),
            "timestamp": time.time()
        }, status=500)


async def metrics_endpoint(request: web_request.Request):
    """Endpoint для получения метрик производительности"""
    try:
        # Получаем полный отчет о производительности
        performance_report = performance_monitor.get_performance_report()
        
        # Добавляем статистику соединений
        connection_stats = connection_manager.get_connection_stats()
        performance_report["connection_stats"] = connection_stats
        
        # Добавляем информацию о системе
        performance_report["system_info"] = {
            "active_tasks": len([task for task in asyncio.all_tasks() if not task.done()]),
            "total_tasks": len(list(asyncio.all_tasks()))
        }
        
        return web.json_response(performance_report)
        
    except Exception as e:
        logger.error(f"Metrics endpoint error: {e}")
        return web.json_response({
            "error": str(e),
            "timestamp": time.time()
        }, status=500)


async def connection_details(request: web_request.Request):
    """Детальная информация о соединениях"""
    try:
        conn_stats = connection_manager.get_connection_stats()
        
        # Добавляем дополнительную информацию
        detailed_info = {
            "summary": {
                "total_connections": conn_stats["total_connections"],
                "healthy_connections": conn_stats["healthy_connections"], 
                "failed_connections": conn_stats["failed_connections"],
                "frontend_clients": conn_stats["frontend_clients"]
            },
            "connections": conn_stats["connections"],
            "alerts": [],
            "recommendations": []
        }
        
        # Анализируем проблемы и даем рекомендации
        for conn_key, conn_info in conn_stats["connections"].items():
            if conn_info["error_count"] > 5:
                detailed_info["alerts"].append(f"High error count for {conn_key}")
            
            if conn_info["reconnect_count"] > 3:
                detailed_info["alerts"].append(f"Frequent reconnects for {conn_key}")
                
            if conn_info["last_message_ago"] > 120:
                detailed_info["alerts"].append(f"Stale connection {conn_key}")
        
        if detailed_info["alerts"]:
            detailed_info["recommendations"].append("Consider connection pool optimization")
            detailed_info["recommendations"].append("Review network stability")
        
        return web.json_response(detailed_info)
        
    except Exception as e:
        logger.error(f"Connection details error: {e}")
        return web.json_response({
            "error": str(e),
            "timestamp": time.time()
        }, status=500)


async def reset_metrics(request: web_request.Request):
    """Сброс метрик (для тестирования)"""
    try:
        performance_monitor.reset_metrics()
        return web.json_response({
            "status": "success",
            "message": "Metrics reset successfully",
            "timestamp": time.time()
        })
    except Exception as e:
        return web.json_response({
            "error": str(e),
            "timestamp": time.time()
        }, status=500)


def setup_health_routes(app: web.Application):
    """Настройка маршрутов для health endpoints"""
    app.router.add_get('/health', health_check)
    app.router.add_get('/metrics', metrics_endpoint)
    app.router.add_get('/connections', connection_details)
    app.router.add_post('/reset-metrics', reset_metrics)
    
    # Добавляем CORS middleware
    @web.middleware
    async def cors_handler(request, handler):
        if request.method == 'OPTIONS':
            response = web.Response()
        else:
            response = await handler(request)
        response.headers['Access-Control-Allow-Origin'] = '*'
        response.headers['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response.headers['Access-Control-Allow-Headers'] = 'Content-Type'
        return response
    
    app.middlewares.append(cors_handler)
    
    logger.info("Health endpoints configured: /health, /metrics, /connections, /reset-metrics")


async def start_health_server(port: int = 8766):
    """Запуск HTTP сервера для health endpoints"""
    app = web.Application()
    setup_health_routes(app)
    
    runner = web.AppRunner(app)
    await runner.setup()
    
    site = web.TCPSite(runner, '0.0.0.0', port)
    await site.start()
    
    logger.info(f"Health server started on port {port}")
    return runner


async def stop_health_server(runner):
    """Остановка health сервера"""
    if runner:
        await runner.cleanup()
        logger.info("Health server stopped")