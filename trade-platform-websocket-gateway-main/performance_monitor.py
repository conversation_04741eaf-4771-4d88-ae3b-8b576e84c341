"""
Система мониторинга производительности WebSocket Gateway
- Real-time метрики
- Performance tracking
- Bottleneck detection
"""

import asyncio
import time
import json
import logging
from typing import Dict, List, Optional
from dataclasses import dataclass, field
from collections import deque
import statistics

logger = logging.getLogger("performance_monitor")

@dataclass
class PerformanceMetrics:
    """Метрики производительности"""
    # Сообщения
    messages_sent: int = 0
    messages_received: int = 0
    message_errors: int = 0
    
    # Latency
    avg_latency_ms: float = 0.0
    max_latency_ms: float = 0.0
    min_latency_ms: float = float('inf')
    latency_history: deque = field(default_factory=lambda: deque(maxlen=100))
    
    # Throughput
    messages_per_second: float = 0.0
    bytes_per_second: float = 0.0
    
    # Connections
    active_connections: int = 0
    total_connections: int = 0
    failed_connections: int = 0
    
    # Memory
    memory_usage_mb: float = 0.0
    cache_size: int = 0
    
    # Custom metrics
    price_updates_per_second: float = 0.0
    subscription_changes_per_minute: float = 0.0

class PerformanceMonitor:
    """Монитор производительности WebSocket Gateway"""
    
    def __init__(self):
        self.metrics = PerformanceMetrics()
        self.start_time = time.time()
        
        # Счетчики для расчета rate
        self.message_count_history = deque(maxlen=60)  # 1 минута истории
        self.price_update_history = deque(maxlen=60)
        self.subscription_history = deque(maxlen=60)
        
        # Временные метки
        self.last_metrics_update = time.time()
        self.monitoring_task = None
        
        # Производительность по символам
        self.symbol_metrics: Dict[str, Dict] = {}
        
        # Bottleneck detection
        self.performance_alerts = []
        
    async def start_monitoring(self):
        """Запуск мониторинга"""
        self.monitoring_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Performance monitoring started")
    
    async def stop_monitoring(self):
        """Остановка мониторинга"""
        if self.monitoring_task:
            self.monitoring_task.cancel()
            try:
                await self.monitoring_task
            except asyncio.CancelledError:
                pass
        logger.info("Performance monitoring stopped")
    
    async def _monitoring_loop(self):
        """Основной цикл мониторинга"""
        while True:
            try:
                await asyncio.sleep(1)  # Обновляем метрики каждую секунду
                await self._update_metrics()
                await self._check_performance_alerts()
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Monitoring loop error: {e}")
    
    async def _update_metrics(self):
        """Обновление метрик"""
        current_time = time.time()
        
        # Обновляем rate metrics
        self._update_rate_metrics()
        
        # Обновляем latency statistics
        if self.metrics.latency_history:
            self.metrics.avg_latency_ms = statistics.mean(self.metrics.latency_history)
            self.metrics.max_latency_ms = max(self.metrics.latency_history)
            self.metrics.min_latency_ms = min(self.metrics.latency_history)
        
        # Memory usage (приблизительная оценка)
        self._estimate_memory_usage()
        
        self.last_metrics_update = current_time
    
    def _update_rate_metrics(self):
        """Обновление метрик скорости"""
        current_time = time.time()
        
        # Messages per second
        self.message_count_history.append((current_time, self.metrics.messages_sent))
        if len(self.message_count_history) >= 2:
            time_diff = current_time - self.message_count_history[0][0]
            msg_diff = self.metrics.messages_sent - self.message_count_history[0][1]
            if time_diff > 0:
                self.metrics.messages_per_second = msg_diff / time_diff
        
        # Price updates per second
        # (обновляется через record_price_update)
        
    def _estimate_memory_usage(self):
        """Приблизительная оценка использования памяти"""
        # Простая оценка на основе количества активных структур данных
        estimated_mb = (
            len(self.symbol_metrics) * 0.1 +  # Метрики по символам
            self.metrics.active_connections * 0.05 +  # Соединения
            len(self.metrics.latency_history) * 0.001  # История latency
        )
        self.metrics.memory_usage_mb = estimated_mb
    
    async def _check_performance_alerts(self):
        """Проверка производительности и генерация алертов"""
        alerts = []
        
        # High latency alert
        if self.metrics.avg_latency_ms > 1000:  # > 1 секунды
            alerts.append({
                "type": "HIGH_LATENCY",
                "message": f"High average latency: {self.metrics.avg_latency_ms:.2f}ms",
                "severity": "warning"
            })
        
        # Low throughput alert
        if self.metrics.messages_per_second < 1 and self.metrics.active_connections > 0:
            alerts.append({
                "type": "LOW_THROUGHPUT", 
                "message": f"Low message throughput: {self.metrics.messages_per_second:.2f} msg/s",
                "severity": "warning"
            })
        
        # High error rate alert
        if self.metrics.messages_sent > 0:
            error_rate = self.metrics.message_errors / self.metrics.messages_sent
            if error_rate > 0.05:  # > 5% error rate
                alerts.append({
                    "type": "HIGH_ERROR_RATE",
                    "message": f"High error rate: {error_rate:.2%}",
                    "severity": "critical"
                })
        
        # Connection issues
        if self.metrics.failed_connections > 0:
            failure_rate = self.metrics.failed_connections / max(self.metrics.total_connections, 1)
            if failure_rate > 0.1:  # > 10% failure rate
                alerts.append({
                    "type": "HIGH_CONNECTION_FAILURES",
                    "message": f"High connection failure rate: {failure_rate:.2%}",
                    "severity": "critical"
                })
        
        # Memory usage
        if self.metrics.memory_usage_mb > 500:  # > 500MB
            alerts.append({
                "type": "HIGH_MEMORY_USAGE",
                "message": f"High memory usage: {self.metrics.memory_usage_mb:.2f}MB",
                "severity": "warning"
            })
        
        # Добавляем новые алерты
        for alert in alerts:
            if alert not in self.performance_alerts:
                self.performance_alerts.append(alert)
                logger.warning(f"Performance alert: {alert}")
        
        # Убираем устаревшие алерты (простая логика)
        if len(self.performance_alerts) > 10:
            self.performance_alerts = self.performance_alerts[-5:]
    
    # Методы для записи метрик
    
    def record_message_sent(self, message_size_bytes: int = 0):
        """Запись отправленного сообщения"""
        self.metrics.messages_sent += 1
        self.metrics.bytes_per_second += message_size_bytes
    
    def record_message_received(self, message_size_bytes: int = 0):
        """Запись полученного сообщения"""
        self.metrics.messages_received += 1
    
    def record_message_error(self):
        """Запись ошибки сообщения"""
        self.metrics.message_errors += 1
    
    def record_latency(self, latency_ms: float):
        """Запись latency"""
        self.metrics.latency_history.append(latency_ms)
    
    def record_connection_established(self):
        """Запись нового соединения"""
        self.metrics.active_connections += 1
        self.metrics.total_connections += 1
    
    def record_connection_closed(self):
        """Запись закрытого соединения"""
        self.metrics.active_connections -= 1
    
    def record_connection_failed(self):
        """Запись неудачного соединения"""
        self.metrics.failed_connections += 1
    
    def record_price_update(self, symbol: str):
        """Запись обновления цены"""
        current_time = time.time()
        
        # Обновляем глобальную метрику
        self.price_update_history.append(current_time)
        
        # Считаем price updates per second
        recent_updates = [t for t in self.price_update_history if current_time - t <= 1.0]
        self.metrics.price_updates_per_second = len(recent_updates)
        
        # Обновляем метрики по символу
        if symbol not in self.symbol_metrics:
            self.symbol_metrics[symbol] = {
                "price_updates": 0,
                "last_update": current_time,
                "subscribers": 0
            }
        
        self.symbol_metrics[symbol]["price_updates"] += 1
        self.symbol_metrics[symbol]["last_update"] = current_time
    
    def record_subscription_change(self, symbol: str, change_type: str):
        """Запись изменения подписки"""
        current_time = time.time()
        self.subscription_history.append(current_time)
        
        # Считаем subscription changes per minute
        recent_changes = [t for t in self.subscription_history if current_time - t <= 60.0]
        self.metrics.subscription_changes_per_minute = len(recent_changes)
        
        # Обновляем счетчик подписчиков для символа
        if symbol in self.symbol_metrics:
            if change_type == "subscribe":
                self.symbol_metrics[symbol]["subscribers"] += 1
            elif change_type == "unsubscribe":
                self.symbol_metrics[symbol]["subscribers"] = max(0, 
                    self.symbol_metrics[symbol]["subscribers"] - 1)
    
    def get_performance_report(self) -> Dict:
        """Получение полного отчета о производительности"""
        uptime_seconds = time.time() - self.start_time
        
        return {
            "timestamp": time.time(),
            "uptime_seconds": uptime_seconds,
            "uptime_formatted": self._format_uptime(uptime_seconds),
            "metrics": {
                "messages": {
                    "sent": self.metrics.messages_sent,
                    "received": self.metrics.messages_received,
                    "errors": self.metrics.message_errors,
                    "per_second": self.metrics.messages_per_second
                },
                "latency": {
                    "average_ms": self.metrics.avg_latency_ms,
                    "max_ms": self.metrics.max_latency_ms,
                    "min_ms": self.metrics.min_latency_ms if self.metrics.min_latency_ms != float('inf') else 0
                },
                "connections": {
                    "active": self.metrics.active_connections,
                    "total": self.metrics.total_connections,
                    "failed": self.metrics.failed_connections
                },
                "performance": {
                    "price_updates_per_second": self.metrics.price_updates_per_second,
                    "subscription_changes_per_minute": self.metrics.subscription_changes_per_minute,
                    "memory_usage_mb": self.metrics.memory_usage_mb
                }
            },
            "symbols": self.symbol_metrics,
            "alerts": self.performance_alerts[-5:],  # Последние 5 алертов
            "recommendations": self._get_performance_recommendations()
        }
    
    def _format_uptime(self, seconds: float) -> str:
        """Форматирование uptime"""
        hours = int(seconds // 3600)
        minutes = int((seconds % 3600) // 60)
        secs = int(seconds % 60)
        return f"{hours:02d}:{minutes:02d}:{secs:02d}"
    
    def _get_performance_recommendations(self) -> List[str]:
        """Рекомендации по улучшению производительности"""
        recommendations = []
        
        # Анализируем метрики и даем рекомендации
        if self.metrics.avg_latency_ms > 500:
            recommendations.append("Consider optimizing message processing to reduce latency")
        
        if self.metrics.messages_per_second > 1000:
            recommendations.append("High message throughput - consider connection pooling")
        
        if len(self.symbol_metrics) > 50:
            recommendations.append("Many symbols tracked - consider implementing symbol grouping")
        
        if self.metrics.memory_usage_mb > 200:
            recommendations.append("High memory usage - consider implementing data cleanup")
        
        error_rate = self.metrics.message_errors / max(self.metrics.messages_sent, 1)
        if error_rate > 0.01:
            recommendations.append("Consider improving error handling and retry logic")
        
        return recommendations
    
    def reset_metrics(self):
        """Сброс метрик (для тестирования)"""
        self.metrics = PerformanceMetrics()
        self.symbol_metrics.clear()
        self.performance_alerts.clear()
        self.start_time = time.time()
        logger.info("Performance metrics reset")

# Глобальный экземпляр
performance_monitor = PerformanceMonitor()