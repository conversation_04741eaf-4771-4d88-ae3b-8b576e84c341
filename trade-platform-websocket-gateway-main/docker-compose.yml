version: '3.8'

services:
  websocket-gateway:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8009:8009"
    environment:
      - COINAPI_KEY=${COINAPI_KEY}
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://redis:6379
    depends_on:
      - kafka
      - redis
    networks:
      - bitmei-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6386:6379"
    volumes:
      - redis_gateway_data:/data
    networks:
      - bitmei-network

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9100:9092"
    depends_on:
      - zookeeper
    networks:
      - bitmei-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2189:2181"
    networks:
      - bitmei-network

volumes:
  redis_gateway_data:

networks:
  bitmei-network:
    driver: bridge