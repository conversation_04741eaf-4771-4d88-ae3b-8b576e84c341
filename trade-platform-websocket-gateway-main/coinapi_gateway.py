import asyncio
import websockets
import json
import logging
import time
import aiohttp
import sys
import os
from dotenv import load_dotenv
from typing import Optional, Dict, List, Any

# Импорт новых компонентов
from price_stream_upgrade import unified_price_manager, handle_subscribe_priority_price, handle_unsubscribe_priority_price
from performance_monitor import performance_monitor
from connection_manager import connection_manager
from health_endpoints import start_health_server, stop_health_server

# Загрузка переменных окружения из .env файла
load_dotenv()

# Настройка логирования
log_level = os.getenv("LOG_LEVEL", "INFO")
log_level_map = {
    "DEBUG": logging.DEBUG,
    "INFO": logging.INFO,
    "WARNING": logging.WARNING,
    "ERROR": logging.ERROR,
    "CRITICAL": logging.CRITICAL,
}

logging.basicConfig(
    level=log_level_map.get(log_level, logging.INFO),
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler(sys.stdout)],
)
logger = logging.getLogger("binance_gateway")

# Глобальные переменные
connected_clients = set()
client_subscriptions: Dict[Any, List[Dict[str, str]]] = (
    {}
)  # {websocket: [{"symbol": "BINANCE_SPOT_ETH_USDT", "timeframe": "1MIN"}, ...]}
latest_data: Dict[str, Dict[str, Any]] = {}  # Кэш последних данных по символам
binance_ws_connections: Dict[str, Optional[Any]] = {}  # Хранение WebSocket соединений с Binance
message_buffer: Dict[str, List[Dict[str, Any]]] = {}  # Буфер сообщений во время переподключения
connection_states: Dict[str, Dict[str, Any]] = {}  # Состояние соединений
reconnect_tasks: Dict[str, Any] = {}  # Задачи переподключения
last_update_times: Dict[str, float] = {}  # Время последнего обновления для каждого символа

# Новые структуры для синхронизации цен
price_ticker_subscriptions: Dict[Any, List[str]] = {}  # {websocket: ["BTC_USDT", "ETH_USDT"]}
current_prices: Dict[str, float] = {}  # Текущие цены по символам
binance_ticker_connections: Dict[str, Optional[Any]] = {}  # Ticker соединения с Binance

# HTTP сессия для асинхронных запросов
http_session: Optional[aiohttp.ClientSession] = None

# Health server runner
health_server_runner = None

# Загрузка настроек из переменных окружения
WS_SERVER_HOST = os.getenv("WS_SERVER_HOST", "0.0.0.0")
WS_SERVER_PORT = int(os.getenv("WS_SERVER_PORT", "8765"))

# Поддержка аргументов командной строки
import argparse
parser = argparse.ArgumentParser(description='Binance WebSocket Gateway')
parser.add_argument('--port', type=int, help='WebSocket server port')
args, unknown = parser.parse_known_args()

if args.port:
    WS_SERVER_PORT = args.port
BINANCE_WS_URL = os.getenv("BINANCE_WS_URL", "wss://stream.binance.com:9443/ws")
BINANCE_API_URL = os.getenv("BINANCE_API_URL", "https://api.binance.com/api/v3")
BINANCE_API_KEY = os.getenv("BINANCE_API_KEY", "")
BINANCE_API_SECRET = os.getenv("BINANCE_API_SECRET", "")
REQUEST_TIMEOUT = int(os.getenv("REQUEST_TIMEOUT", "30000"))  # Увеличиваем до 30 секунд

# Настройки стабильности соединения
HEARTBEAT_INTERVAL = 30  # Интервал heartbeat в секундах
HEARTBEAT_TIMEOUT = 60  # Таймаут ожидания pong
MAX_RECONNECT_DELAY = 60  # Максимальная задержка переподключения
INITIAL_RECONNECT_DELAY = 1  # Начальная задержка переподключения
RECONNECT_FACTOR = 2  # Фактор увеличения задержки
MAX_MESSAGE_BUFFER_SIZE = 1000  # Максимальный размер буфера сообщений
CONNECTION_TIMEOUT = 30  # Таймаут подключения в секундах

# Настройки частоты обновлений (в секундах)
UPDATE_FREQUENCIES = {
    "1MIN": 10,    # максимум раз в 10 секунд
    "3MIN": 15,    # максимум раз в 15 секунд
    "5MIN": 30,    # максимум раз в 30 секунд
    "15MIN": 120,  # максимум раз в 2 минуты
    "30MIN": 240,  # максимум раз в 4 минуты
    "1HRS": 600,   # максимум раз в 10 минут
    "4HRS": 1200,  # максимум раз в 20 минут
    "1DAY": 3600,  # максимум раз в час
    "1WKS": 7200,  # максимум раз в 2 часа
    "1MTH": 14400  # максимум раз в 4 часа
}


# Преобразует таймфрейм в интервал Binance
def map_timeframe_to_binance_interval(timeframe):
    """Преобразует таймфрейм в формат Binance"""
    mapping = {
        "1MIN": "1m", "3MIN": "3m", "5MIN": "5m", "15MIN": "15m", "30MIN": "30m",
        "1HRS": "1h", "4HRS": "4h", "1DAY": "1d", "1WKS": "1w", "1MTH": "1M"
    }
    return mapping.get(timeframe, "1m")


# Извлекает символ для Binance из символа фронтенда
def get_binance_symbol(symbol):
    """Извлекает символ Binance из формата фронтенда"""
    # Поддерживаем различные форматы: BINANCE_SPOT_BTC_USDT, BTC_USDT, BTCUSDT, BTCUSD
    if "_SPOT_" in symbol:
        parts = symbol.split("_SPOT_")
        return parts[1].replace("_", "")
    elif "_" in symbol and len(symbol.split("_")) == 2:
        # Формат BTC_USDT -> BTCUSDT
        return symbol.replace("_", "")
    elif symbol.endswith("USD") and not symbol.endswith("USDT"):
        # Формат BTCUSD -> BTCUSDT (добавляем T к USD)
        return symbol + "T"
    # Уже в правильном формате BTCUSDT
    return symbol


async def get_http_session():
    """Получение или создание HTTP сессии"""
    global http_session
    if http_session is None or http_session.closed:
        timeout = aiohttp.ClientTimeout(total=REQUEST_TIMEOUT / 1000)
        http_session = aiohttp.ClientSession(timeout=timeout)
    return http_session


def get_binance_headers():
    """Получение заголовков для REST API Binance"""
    headers = {}
    if BINANCE_API_KEY:
        headers["X-MBX-APIKEY"] = BINANCE_API_KEY
    return headers


async def close_http_session():
    """Закрытие HTTP сессии"""
    global http_session
    if http_session and not http_session.closed:
        await http_session.close()
        http_session = None


async def fetch_ohlcv_data(symbol, timeframe, limit=500, start_time=None, end_time=None):
    """Получение исторических данных OHLCV через REST API"""
    binance_symbol = get_binance_symbol(symbol).upper()
    interval = map_timeframe_to_binance_interval(timeframe)

    # Ограничиваем количество свечей (максимум 1000 для Binance API)
    limit = min(limit, 1000)

    url = f"{BINANCE_API_URL}/klines"
    params = {"symbol": binance_symbol, "interval": interval, "limit": limit}

    # Добавляем временные рамки если указаны
    if start_time is not None:
        params["startTime"] = int(start_time)
    if end_time is not None:
        params["endTime"] = int(end_time)

    logger.debug(f"Запрос OHLCV: {url} с параметрами {params}")

    try:
        session = await get_http_session()
        async with session.get(url, params=params, headers=get_binance_headers()) as response:
            logger.debug(f"Получен ответ от Binance API: статус {response.status}")

            if response.status == 200:
                data = await response.json()
                logger.debug(f"Получено {len(data)} свечей от Binance API")

                # Преобразуем данные в формат OHLCV, ожидаемый фронтендом
                ohlcv = []
                for item in data:
                    # item[0] - открытие времени (timestamp)
                    # item[6] - закрытие времени (timestamp)
                    open_time = int(item[0])

                    ohlcv_item = {
                        "timestamp": open_time,
                        "open": float(item[1]),
                        "high": float(item[2]),
                        "low": float(item[3]),
                        "close": float(item[4]),
                        "volume": float(item[5]),
                        "isFinal": True
                    }
                    ohlcv.append(ohlcv_item)

                logger.info(f"Успешно обработано {len(ohlcv)} свечей для {symbol}")
                return ohlcv
            else:
                text = await response.text()
                logger.error(f"Ошибка при получении OHLCV: {response.status}, {text}")
                return []
    except asyncio.TimeoutError:
        logger.error(f"Таймаут при получении OHLCV для {symbol} (возможно медленное соединение)")
        return []
    except aiohttp.ClientError as e:
        logger.error(f"Ошибка HTTP клиента при получении OHLCV для {symbol}: {str(e)}")
        return []
    except Exception as e:
        logger.error(f"Неожиданное исключение при получении OHLCV для {symbol}: {str(e)}")
        return []


async def fetch_current_price(symbol):
    """Получение текущей цены через REST API"""
    binance_symbol = get_binance_symbol(symbol).upper()

    url = f"{BINANCE_API_URL}/ticker/price"
    params = {"symbol": binance_symbol}

    try:
        session = await get_http_session()
        async with session.get(url, params=params, headers=get_binance_headers()) as response:
            if response.status == 200:
                data = await response.json()
                return float(data["price"])
            else:
                text = await response.text()
                logger.error(f"Ошибка при получении текущей цены: {response.status}, {text}")
                return 0
    except asyncio.TimeoutError:
        logger.error(f"Таймаут при получении цены для {symbol}")
        return 0
    except Exception as e:
        logger.error(f"Исключение при получении текущей цены: {str(e)}")
        return 0


async def fetch_24h_change(symbol):
    """Получение данных 24ч изменения"""
    binance_symbol = get_binance_symbol(symbol).upper()

    url = f"{BINANCE_API_URL}/ticker/24hr"
    params = {"symbol": binance_symbol}

    try:
        session = await get_http_session()
        async with session.get(url, params=params, headers=get_binance_headers()) as response:
            if response.status == 200:
                data = await response.json()

                return {
                    "priceChange": float(data["priceChange"]),
                    "priceChangePercent": float(data["priceChangePercent"]),
                    "highPrice": float(data["highPrice"]),
                    "lowPrice": float(data["lowPrice"]),
                    "volume": float(data["volume"]),
                    "quoteVolume": float(data["quoteVolume"]),
                }
            else:
                text = await response.text()
                logger.error(f"Ошибка при получении 24ч изменения: {response.status}, {text}")
                return create_default_24h_data()
    except asyncio.TimeoutError:
        logger.error(f"Таймаут при получении 24ч данных для {symbol}")
        return create_default_24h_data()
    except Exception as e:
        logger.error(f"Исключение при получении 24ч изменения: {str(e)}")
        return create_default_24h_data()


def create_default_24h_data():
    """Создает данные 24ч по умолчанию"""
    return {"priceChange": 0, "priceChangePercent": 0, "highPrice": 0, "lowPrice": 0, "volume": 0, "quoteVolume": 0}


async def fetch_pair_stats(symbol, timeframe):
    """Получение статистики по паре"""
    try:
        # Получаем текущую цену
        current_price = await fetch_current_price(symbol)

        # Получаем данные о 24-часовом изменении
        change_24h_data = await fetch_24h_change(symbol)

        # Форматируем данные для возврата
        index_price = f"{current_price:.2f}"
        oracle_price = f"{current_price * 1.004:.2f}"

        # Используем данные о 24-часовом изменении
        change_24h = f"{change_24h_data['priceChange']:.2f}"
        change_percent = f"{change_24h_data['priceChangePercent']:.2f}"

        # Данные о высоком/низком и объеме за 24 часа
        high_24h = f"{change_24h_data['highPrice']:.2f}"
        low_24h = f"{change_24h_data['lowPrice']:.2f}"

        # Объем
        vol_base_24h = f"{change_24h_data['volume']:.2f}"
        vol_usd_24h = f"{change_24h_data['quoteVolume']:.2f}"

        # Форматируем объем для UI
        vol_usd_num = change_24h_data["quoteVolume"]
        if vol_usd_num >= 1000000000:
            formatted_vol = f"{vol_usd_num / 1000000000:.2f}B"
        elif vol_usd_num >= 1000000:
            formatted_vol = f"{vol_usd_num / 1000000:.2f}M"
        elif vol_usd_num >= 1000:
            formatted_vol = f"{vol_usd_num / 1000:.2f}K"
        else:
            formatted_vol = f"{vol_usd_num:.2f}"

        return {
            "indexPrice": index_price,
            "oraclePrice": oracle_price,
            "change24h": change_24h,
            "changePercent": change_percent,
            "volume24h": vol_usd_24h,
            "high24h": high_24h,
            "low24h": low_24h,
            "volBase24h": vol_base_24h,
            "volUsd24h": formatted_vol,
            "isPositiveChange": change_24h_data["priceChange"] >= 0,
        }
    except Exception as e:
        logger.error(f"Ошибка при получении статистики: {str(e)}")
        return create_default_stats()


def create_default_stats():
    """Создает статистику по умолчанию"""
    return {
        "indexPrice": "0.00",
        "oraclePrice": "0.00",
        "change24h": "0.00",
        "changePercent": "0.00",
        "volume24h": "0.00",
        "high24h": "0.00",
        "low24h": "0.00",
        "volBase24h": "0.00",
        "volUsd24h": "0.00",
        "isPositiveChange": True,
    }


async def client_heartbeat(websocket):
    """Отправка heartbeat сообщений клиенту"""
    try:
        while websocket in connected_clients:
            await asyncio.sleep(HEARTBEAT_INTERVAL)
            if websocket.closed:
                break
            try:
                pong_waiter = await websocket.ping()
                await asyncio.wait_for(pong_waiter, timeout=HEARTBEAT_TIMEOUT)
                logger.debug("Heartbeat успешен для клиента")
            except asyncio.TimeoutError:
                logger.warning("Heartbeat таймаут для клиента")
                await websocket.close()
                break
            except Exception as e:
                logger.error(f"Ошибка heartbeat: {str(e)}")
                break
    except Exception as e:
        logger.error(f"Ошибка в цикле heartbeat: {str(e)}")


async def handle_websocket_client(websocket, path):
    """Обработка подключения клиента фронтенда"""
    # Добавляем клиента в список подключенных
    connected_clients.add(websocket)
    client_subscriptions[websocket] = []
    
    # Регистрируем в менеджерах
    connection_manager.add_frontend_client(websocket)
    performance_monitor.record_connection_established()

    # Запускаем heartbeat для этого клиента
    heartbeat_task = asyncio.create_task(client_heartbeat(websocket))

    try:
        # Отправляем приветственное сообщение
        await websocket.send(json.dumps({"type": "connected", "message": "Успешно подключено к Binance Gateway"}))

        # Обрабатываем сообщения от клиента
        async for message in websocket:
            try:
                data = json.loads(message)
                command = data.get("type", "")

                if command == "subscribe":
                    await handle_subscribe(websocket, data)
                elif command == "unsubscribe":
                    await handle_unsubscribe(websocket, data)
                elif command == "subscribeTicker":
                    await handle_subscribe_ticker(websocket, data)
                elif command == "unsubscribeTicker":
                    await handle_unsubscribe_ticker(websocket, data)
                elif command == "subscribePriorityPrice":
                    await handle_subscribe_priority_price(websocket, data)
                elif command == "unsubscribePriorityPrice":
                    await handle_unsubscribe_priority_price(websocket, data)
                elif command == "fetch_pair_stats":
                    await handle_fetch_stats(websocket, data)
                elif command == "fetchHistory":
                    await handle_fetch_history(websocket, data)
                elif command == "ping":
                    # Отвечаем на ping от клиента
                    await websocket.send(json.dumps({"type": "pong", "timestamp": int(time.time() * 1000)}))
                else:
                    await websocket.send(json.dumps({
                        "type": "error",
                        "code": "UNKNOWN_COMMAND",
                        "message": f"Unknown command: {command}"
                    }))

            except json.JSONDecodeError:
                logger.warning(f"Получено некорректное сообщение от клиента: {message}")
            except Exception as e:
                logger.error(f"Ошибка при обработке сообщения от клиента: {str(e)}")

    except websockets.exceptions.ConnectionClosed as e:
        if e.code == 1000:
            logger.info("Клиент корректно закрыл соединение")
        else:
            logger.warning(f"Клиент отключился с кодом: {e.code} - {e.reason}")
    except Exception as e:
        logger.error(f"Ошибка при обработке соединения с клиентом: {str(e)}")
    finally:
        # Отменяем heartbeat задачу
        heartbeat_task.cancel()
        try:
            await heartbeat_task
        except asyncio.CancelledError:
            pass

        # Удаляем клиента при отключении
        connected_clients.discard(websocket)
        if websocket in client_subscriptions:
            del client_subscriptions[websocket]
        if websocket in price_ticker_subscriptions:
            del price_ticker_subscriptions[websocket]
        
        # Очищаем в менеджерах
        connection_manager.remove_frontend_client(websocket)
        unified_price_manager._cleanup_subscriber(websocket)
        performance_monitor.record_connection_closed()

        # Очищаем буфер сообщений для этого клиента
        if websocket in message_buffer:
            del message_buffer[websocket]

        # Отключаем неиспользуемые WebSocket соединения с Binance
        await cleanup_unused_binance_connections()
        await cleanup_unused_ticker_connections()


async def handle_subscribe(websocket, data):
    """Обработка подписки на символ"""
    symbol = data.get("symbol", "")
    timeframe = data.get("timeframe", "1MIN")
    fetch_history_flag = data.get("fetchHistory", True)

    # Валидация символа
    if not symbol:
        await websocket.send(json.dumps({
            "type": "error",
            "code": "MISSING_SYMBOL",
            "message": "Symbol is required"
        }))
        return

    # Проверка поддерживаемых таймфреймов
    supported_timeframes = ["1MIN", "3MIN", "5MIN", "15MIN", "30MIN", "1HRS", "4HRS", "1DAY", "1WKS", "1MTH"]
    if timeframe not in supported_timeframes:
        await websocket.send(json.dumps({
            "type": "error",
            "code": "INVALID_TIMEFRAME",
            "message": f"Timeframe {timeframe} not supported",
            "supportedTimeframes": supported_timeframes
        }))
        return

    # Проверка лимита подписок
    if len(client_subscriptions.get(websocket, [])) >= 10:
        await websocket.send(json.dumps({
            "type": "error",
            "code": "SUBSCRIPTION_LIMIT",
            "message": "Maximum 10 subscriptions per connection"
        }))
        return

    # Логирование для отладки
    logger.info(
        f"Получен запрос на подписку: symbol={symbol}, timeframe={timeframe}, fetchHistory={fetch_history_flag}"
    )

    # Добавляем подписку
    subscription = {"symbol": symbol, "timeframe": timeframe}
    client_subscriptions[websocket].append(subscription)
    
    # Записываем метрику
    performance_monitor.record_subscription_change(symbol, "subscribe")

    logger.info(f"Клиент подписался на {symbol} с таймфреймом {timeframe}")

    # Подтверждаем подписку
    await websocket.send(json.dumps({"type": "subscribed", "symbol": symbol, "timeframe": timeframe}))

    # Подключаемся к Binance через улучшенный менеджер
    binance_symbol = get_binance_symbol(symbol).lower()
    binance_interval = map_timeframe_to_binance_interval(timeframe)
    
    # Используем новый connection manager
    stream_type = f"kline_{binance_interval}"
    ws = await connection_manager.get_or_create_connection(binance_symbol, stream_type)
    
    if ws:
        logger.info(f"Успешно подключено к {binance_symbol}@{stream_type}")
    else:
        logger.error(f"Не удалось подключиться к {binance_symbol}@{stream_type}")
        # Отправляем ошибку в performance monitor
        performance_monitor.record_connection_failed()

    # Отправляем исторические данные, если запрошены
    if fetch_history_flag:
        try:
            logger.info(f"Начинаем получение истории для {symbol} {timeframe}")
            ohlcv_data = await fetch_ohlcv_data(symbol, timeframe)

            if ohlcv_data:  # Проверяем что данные не пустые
                logger.info(f"История получена для {symbol}: {len(ohlcv_data)} свечей")
                await websocket.send(
                    json.dumps({"type": "history", "symbol": symbol, "timeframe": timeframe, "data": ohlcv_data})
                )
            else:
                logger.warning(f"Пустые исторические данные для {symbol}")
                await websocket.send(json.dumps({
                    "type": "error",
                    "code": "NO_HISTORY_DATA",
                    "message": f"No historical data available for {symbol}"
                }))
        except websockets.exceptions.ConnectionClosed:
            logger.warning(f"WebSocket соединение закрыто во время отправки истории для {symbol}")
            return  # Выходим из функции, не пытаемся отправлять больше данных
        except Exception as e:
            logger.error(f"Error fetching history: {str(e)}")
            try:
                await websocket.send(json.dumps({
                    "type": "error",
                    "code": "HISTORY_FETCH_ERROR",
                    "message": f"Failed to fetch history for {symbol}"
                }))
            except websockets.exceptions.ConnectionClosed:
                logger.warning(f"Не удалось отправить ошибку - соединение закрыто для {symbol}")
                return

    # Отправляем статистику по паре
    try:
        stats = await fetch_pair_stats(symbol, timeframe)
        await websocket.send(json.dumps({"type": "stats", "symbol": symbol, "data": stats}))
    except Exception as e:
        logger.error(f"Error fetching stats: {str(e)}")

    # Если есть последние данные, сразу отправляем их
    data_key = f"{symbol}_{timeframe}"
    if data_key in latest_data:
        await websocket.send(
            json.dumps({"type": "kline", "symbol": symbol, "timeframe": timeframe, "data": latest_data[data_key]})
        )
    
    # Отправляем текущую цену для синхронизации
    if symbol in current_prices:
        await websocket.send(json.dumps({
            "type": "currentPrice",
            "symbol": symbol,
            "price": current_prices[symbol],
            "timestamp": int(time.time() * 1000)
        }))


async def handle_unsubscribe(websocket, data):
    """Обработка отписки от символа"""
    symbol = data.get("symbol", "")

    if not symbol:
        await websocket.send(json.dumps({"type": "error", "message": "Не указан символ для отписки"}))
        return

    # Удаляем подписки на этот символ
    if websocket in client_subscriptions:
        client_subscriptions[websocket] = [sub for sub in client_subscriptions[websocket] if sub["symbol"] != symbol]
    
    # Записываем метрику
    performance_monitor.record_subscription_change(symbol, "unsubscribe")

    logger.info(f"Клиент отписался от {symbol}")

    # Подтверждаем отписку
    await websocket.send(json.dumps({"type": "unsubscribed", "symbol": symbol}))

    # Очищаем неиспользуемые соединения
    await cleanup_unused_binance_connections()


async def handle_fetch_stats(websocket, data):
    """Обработка запроса статистики по паре"""
    symbol = data.get("symbol", "")
    timeframe = data.get("timeframe", "1MIN")

    if not symbol:
        await websocket.send(json.dumps({"type": "error", "message": "Не указан символ для получения статистики"}))
        return

    # Получаем и отправляем статистику
    stats = await fetch_pair_stats(symbol, timeframe)

    await websocket.send(json.dumps({"type": "stats", "symbol": symbol, "data": stats}))


async def get_timeframe_milliseconds(timeframe):
    """Возвращает длительность таймфрейма в миллисекундах"""
    timeframe_map = {
        "1MIN": 60 * 1000,
        "3MIN": 3 * 60 * 1000,
        "5MIN": 5 * 60 * 1000,
        "15MIN": 15 * 60 * 1000,
        "30MIN": 30 * 60 * 1000,
        "1HRS": 60 * 60 * 1000,
        "4HRS": 4 * 60 * 60 * 1000,
        "1DAY": 24 * 60 * 60 * 1000,
        "1WKS": 7 * 24 * 60 * 60 * 1000,
        "1MTH": 30 * 24 * 60 * 60 * 1000  # Приблизительно
    }
    return timeframe_map.get(timeframe, 60 * 1000)


async def fetch_history_range(symbol, timeframe, to_timestamp=None, limit=300):
    """Получение исторических данных с поддержкой "скролла назад" """
    MAX_CHUNK_SIZE = 1000  # Максимальный размер чанка
    
    # Если to_timestamp не указан, получаем последние данные
    if to_timestamp is None:
        data = await fetch_ohlcv_data(symbol, timeframe, limit=min(limit, MAX_CHUNK_SIZE))
        # Сортируем по убыванию времени (новые данные сверху)
        data.sort(key=lambda x: x["timestamp"], reverse=True)
        
        # Проверяем есть ли более ранние данные
        has_more = len(data) == min(limit, MAX_CHUNK_SIZE)
        return data, has_more
    
    # Запрашиваем данные ДО указанного timestamp (для скролла назад)
    # Используем endTime = to_timestamp чтобы получить данные до этой точки
    data = await fetch_ohlcv_data(
        symbol, timeframe,
        limit=min(limit, MAX_CHUNK_SIZE),
        end_time=to_timestamp
    )
    
    # Фильтруем данные, чтобы исключить саму точку to_timestamp
    if data:
        data = [candle for candle in data if candle["timestamp"] < to_timestamp]
    
    # Сортируем по убыванию времени (новые данные сверху)
    data.sort(key=lambda x: x["timestamp"], reverse=True)
    
    # Ограничиваем по limit
    if len(data) > limit:
        data = data[:limit]
    
    # Проверяем есть ли еще более ранние данные
    # Если получили полный лимит, вероятно есть еще данные
    has_more = len(data) >= min(limit, MAX_CHUNK_SIZE) and len(data) > 0
    
    return data, has_more


async def handle_fetch_history(websocket, data):
    """Обработка запроса исторических данных согласно ТЗ фронтенда"""
    symbol = data.get("symbol", "")
    timeframe = data.get("timeframe", "1MIN")
    to_timestamp = data.get("to")  # Конец диапазона (опционально)
    limit = data.get("limit", 300)  # По умолчанию 300

    # Валидация символа
    if not symbol:
        await websocket.send(json.dumps({
            "type": "error",
            "message": "Symbol is required"
        }))
        return

    # Проверка поддерживаемых таймфреймов
    supported_timeframes = ["1MIN", "3MIN", "5MIN", "15MIN", "30MIN", "1HRS", "4HRS", "1DAY", "1WKS", "1MTH"]
    if timeframe not in supported_timeframes:
        await websocket.send(json.dumps({
            "type": "error",
            "message": f"Timeframe {timeframe} not supported. Supported: {', '.join(supported_timeframes)}"
        }))
        return

    # Валидация limit
    if not isinstance(limit, int) or limit <= 0:
        await websocket.send(json.dumps({
            "type": "error",
            "message": "Limit must be a positive integer"
        }))
        return

    # Ограничиваем максимальный limit
    if limit > 1000:
        await websocket.send(json.dumps({
            "type": "error",
            "message": "Maximum limit is 1000 candles per request"
        }))
        return

    # Валидация to_timestamp
    if to_timestamp is not None and not isinstance(to_timestamp, (int, float)):
        await websocket.send(json.dumps({
            "type": "error",
            "message": "'to' must be a valid timestamp"
        }))
        return

    try:
        logger.info(f"fetchHistory запрос: symbol={symbol}, timeframe={timeframe}, to={to_timestamp}, limit={limit}")

        # Получаем исторические данные
        ohlcv_data, has_more = await fetch_history_range(
            symbol, timeframe, to_timestamp, limit
        )

        # Определяем timestamp самой старой свечи для поля "to"
        oldest_timestamp = None
        if ohlcv_data:
            # Данные уже отсортированы по убыванию, берем последний элемент (самый старый)
            oldest_timestamp = ohlcv_data[-1]["timestamp"]

        # Формируем ответ согласно ТЗ фронтенда
        response = {
            "type": "history",
            "symbol": symbol,
            "timeframe": timeframe,
            "data": ohlcv_data,
            "hasMore": has_more,
            "to": oldest_timestamp  # timestamp самой старой возвращённой свечи
        }

        await websocket.send(json.dumps(response))
        logger.info(f"Отправлено {len(ohlcv_data)} свечей для {symbol}, hasMore={has_more}, oldest={oldest_timestamp}")

    except Exception as e:
        logger.error(f"Error fetching history for {symbol}: {str(e)}")
        await websocket.send(json.dumps({
            "type": "error",
            "message": f"Failed to fetch history for {symbol}: {str(e)}"
        }))


async def connect_to_binance_symbol(binance_symbol, interval, original_symbol, timeframe):
    """Подключение к вебсокету Binance для конкретного символа и интервала"""
    stream_name = f"{binance_symbol}@kline_{interval}"
    ws_url = f"{BINANCE_WS_URL}/{stream_name}"

    # Создаем ключ для сохранения соединения
    ws_key = stream_name

    # Инициализируем состояние соединения
    connection_states[ws_key] = {"reconnect_count": 0, "last_connect_time": None, "is_connecting": False}

    async def ws_handler():
        reconnect_delay = INITIAL_RECONNECT_DELAY

        while True:
            try:
                # Обновляем состояние
                connection_states[ws_key]["is_connecting"] = True
                connection_states[ws_key]["last_connect_time"] = time.time()

                logger.info(f"Подключение к Binance WebSocket: {ws_url}")

                # Используем таймаут для подключения
                async with websockets.connect(
                    ws_url, ping_interval=HEARTBEAT_INTERVAL, ping_timeout=HEARTBEAT_TIMEOUT, close_timeout=10
                ) as websocket:
                    binance_ws_connections[ws_key] = websocket
                    connection_states[ws_key]["is_connecting"] = False
                    connection_states[ws_key]["reconnect_count"] = 0
                    reconnect_delay = INITIAL_RECONNECT_DELAY

                    logger.info(f"Подключено к Binance WebSocket для {stream_name}")

                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            # Обрабатываем данные от Binance
                            if data.get("e") == "kline":
                                k = data.get("k", {})

                                # Создаем данные тика
                                tick_data = {
                                    "timestamp": k.get("t", int(time.time() * 1000)),
                                    "open": float(k.get("o", 0)),
                                    "high": float(k.get("h", 0)),
                                    "low": float(k.get("l", 0)),
                                    "close": float(k.get("c", 0)),
                                    "volume": float(k.get("v", 0)),
                                    "isFinal": k.get("x", False),
                                }
                                
                                # Обновляем текущую цену для синхронизации
                                current_prices[original_symbol] = float(k.get("c", 0))
                                
                                # Обновляем через unified price manager
                                await unified_price_manager.update_price(original_symbol, float(k.get("c", 0)), "binance_kline")

                                # Проверяем частоту обновлений
                                update_key = f"{original_symbol}_{timeframe}"
                                current_time = time.time()
                                min_interval = UPDATE_FREQUENCIES.get(timeframe, 10)

                                should_update = True
                                if update_key in last_update_times:
                                    time_since_last = current_time - last_update_times[update_key]
                                    should_update = time_since_last >= min_interval

                                if should_update:
                                    # Сохраняем последние данные
                                    data_key = f"{original_symbol}_{timeframe}"
                                    latest_data[data_key] = tick_data
                                    last_update_times[update_key] = current_time

                                    # Отправляем данные всем подписанным клиентам
                                    await send_data_to_subscribers(original_symbol, timeframe, tick_data)
                        except json.JSONDecodeError:
                            logger.warning(f"Получено некорректное сообщение от Binance: {message}")
                        except Exception as e:
                            logger.error(f"Ошибка при обработке сообщения от Binance: {str(e)}")

            except Exception as e:
                logger.error(f"Ошибка подключения к Binance: {str(e)}")
                # Сбрасываем соединение
                binance_ws_connections[ws_key] = None
                connection_states[ws_key]["is_connecting"] = False

                # Проверяем, есть ли еще подписчики
                if not has_subscribers_for_stream(original_symbol, timeframe):
                    logger.info(
                        f"Нет подписчиков для {original_symbol}, останавливаем соединение"
                    )
                    # Удаляем состояние соединения
                    if ws_key in connection_states:
                        del connection_states[ws_key]
                    return

                # Увеличиваем счетчик переподключений
                connection_states[ws_key]["reconnect_count"] += 1

                # Exponential backoff с ограничением
                logger.info(
                    f"Переподключение через {reconnect_delay} секунд (попытка {connection_states[ws_key]['reconnect_count']})"
                )
                await asyncio.sleep(reconnect_delay)

                # Увеличиваем задержку для следующей попытки
                reconnect_delay = min(reconnect_delay * RECONNECT_FACTOR, MAX_RECONNECT_DELAY)

    # Сохраняем задачу для возможной отмены
    if ws_key not in reconnect_tasks:
        reconnect_tasks[ws_key] = asyncio.create_task(ws_handler())


def buffer_message(symbol, timeframe, data):
    """Добавление сообщения в буфер"""
    buffer_key = f"{symbol}_{timeframe}"
    if buffer_key not in message_buffer:
        message_buffer[buffer_key] = []

    # Ограничиваем размер буфера
    if len(message_buffer[buffer_key]) >= MAX_MESSAGE_BUFFER_SIZE:
        message_buffer[buffer_key].pop(0)

    message_buffer[buffer_key].append(data)


async def send_buffered_messages(websocket, symbol, timeframe):
    """Отправка буферизованных сообщений клиенту - убрано для устранения дублей"""
    pass


async def send_data_to_subscribers(symbol, timeframe, data):
    """Отправка данных всем подписанным клиентам"""
    disconnected_clients = []

    for client, subscriptions in list(client_subscriptions.items()):
        for subscription in subscriptions:
            if subscription["symbol"] == symbol and subscription["timeframe"] == timeframe:
                try:
                    message = json.dumps({"type": "kline", "symbol": symbol, "timeframe": timeframe, "data": data})
                    await client.send(message)
                    performance_monitor.record_message_sent(len(message.encode('utf-8')))
                except websockets.exceptions.ConnectionClosed:
                    disconnected_clients.append(client)
                    performance_monitor.record_message_error()
                except Exception as e:
                    logger.error(f"Ошибка при отправке данных клиенту: {str(e)}")
                    performance_monitor.record_message_error()

    # Удаляем отключенных клиентов
    for client in disconnected_clients:
        connected_clients.discard(client)
        if client in client_subscriptions:
            del client_subscriptions[client]


def has_subscribers_for_stream(symbol, timeframe):
    """Проверяет, есть ли подписчики на данный символ и таймфрейм"""
    for client, subscriptions in client_subscriptions.items():
        for subscription in subscriptions:
            if subscription["symbol"] == symbol and subscription["timeframe"] == timeframe:
                return True
    return False


async def cleanup_unused_binance_connections():
    """Очистка неиспользуемых соединений с Binance"""
    # Собираем все активные подписки
    active_streams = set()
    for client, subscriptions in client_subscriptions.items():
        for subscription in subscriptions:
            symbol = subscription["symbol"]
            timeframe = subscription["timeframe"]
            binance_symbol = get_binance_symbol(symbol).lower()
            binance_interval = map_timeframe_to_binance_interval(timeframe)
            stream_name = f"{binance_symbol}@kline_{binance_interval}"
            active_streams.add(stream_name)

    # Закрываем неиспользуемые соединения
    for stream_name, ws in list(binance_ws_connections.items()):
        if stream_name not in active_streams:
            # Отменяем задачу переподключения
            if stream_name in reconnect_tasks:
                reconnect_tasks[stream_name].cancel()
                try:
                    await reconnect_tasks[stream_name]
                except asyncio.CancelledError:
                    pass
                del reconnect_tasks[stream_name]

            # Закрываем WebSocket
            if ws is not None:
                try:
                    await ws.close()
                    logger.info(f"Закрыто неиспользуемое соединение: {stream_name}")
                except Exception as e:
                    logger.error(f"Ошибка при закрытии соединения: {str(e)}")
                finally:
                    binance_ws_connections[stream_name] = None

            # Удаляем состояние
            if stream_name in connection_states:
                del connection_states[stream_name]


async def handle_subscribe_ticker(websocket, data):
    """Обработка подписки на price ticker"""
    symbol = data.get("symbol", "")
    
    if not symbol:
        await websocket.send(json.dumps({
            "type": "error",
            "code": "MISSING_SYMBOL",
            "message": "Symbol is required for ticker subscription"
        }))
        return
    
    # Инициализируем список подписок для клиента
    if websocket not in price_ticker_subscriptions:
        price_ticker_subscriptions[websocket] = []
    
    # Проверяем, не подписан ли уже
    if symbol in price_ticker_subscriptions[websocket]:
        await websocket.send(json.dumps({
            "type": "error",
            "code": "ALREADY_SUBSCRIBED",
            "message": f"Already subscribed to ticker for {symbol}"
        }))
        return
    
    # Добавляем подписку
    price_ticker_subscriptions[websocket].append(symbol)
    logger.info(f"Клиент подписался на price ticker для {symbol}")
    
    # Подтверждаем подписку
    await websocket.send(json.dumps({
        "type": "tickerSubscribed",
        "symbol": symbol
    }))
    
    # Подключаемся к Binance ticker через connection manager
    binance_symbol = get_binance_symbol(symbol).lower()
    
    ws = await connection_manager.get_or_create_connection(binance_symbol, "ticker")
    if ws:
        logger.info(f"Успешно подключено к ticker {binance_symbol}")
        performance_monitor.record_subscription_change(symbol, "subscribe")
    else:
        logger.error(f"Не удалось подключиться к ticker {binance_symbol}")
        performance_monitor.record_connection_failed()
    
    # Если есть текущая цена, сразу отправляем
    if symbol in current_prices:
        await websocket.send(json.dumps({
            "type": "ticker",
            "symbol": symbol,
            "price": current_prices[symbol],
            "timestamp": int(time.time() * 1000)
        }))


async def handle_unsubscribe_ticker(websocket, data):
    """Обработка отписки от price ticker"""
    symbol = data.get("symbol", "")
    
    if not symbol:
        await websocket.send(json.dumps({
            "type": "error",
            "message": "Symbol is required for ticker unsubscription"
        }))
        return
    
    # Удаляем подписку
    if websocket in price_ticker_subscriptions:
        if symbol in price_ticker_subscriptions[websocket]:
            price_ticker_subscriptions[websocket].remove(symbol)
            performance_monitor.record_subscription_change(symbol, "unsubscribe")
            logger.info(f"Клиент отписался от price ticker для {symbol}")
    
    # Подтверждаем отписку
    await websocket.send(json.dumps({
        "type": "tickerUnsubscribed",
        "symbol": symbol
    }))
    
    # Очищаем неиспользуемые ticker соединения
    await cleanup_unused_ticker_connections()


async def connect_to_binance_ticker(binance_symbol, original_symbol):
    """Подключение к Binance ticker stream для получения real-time цен"""
    ticker_key = f"{binance_symbol}@ticker"
    ws_url = f"{BINANCE_WS_URL}/{ticker_key}"
    
    async def ticker_handler():
        reconnect_delay = INITIAL_RECONNECT_DELAY
        
        while True:
            try:
                logger.info(f"Подключение к Binance Ticker WebSocket: {ws_url}")
                
                async with websockets.connect(
                    ws_url,
                    ping_interval=HEARTBEAT_INTERVAL,
                    ping_timeout=HEARTBEAT_TIMEOUT
                ) as websocket:
                    binance_ticker_connections[ticker_key] = websocket
                    reconnect_delay = INITIAL_RECONNECT_DELAY
                    
                    logger.info(f"Подключено к Binance Ticker для {binance_symbol}")
                    
                    async for message in websocket:
                        try:
                            data = json.loads(message)
                            
                            # Обновляем текущую цену
                            if "c" in data:  # current price
                                price = float(data["c"])
                                current_prices[original_symbol] = price
                                
                                # Обновляем через unified price manager
                                await unified_price_manager.update_price(original_symbol, price, "binance_ticker")
                                
                                # Отправляем всем подписанным клиентам (для обратной совместимости)
                                await send_ticker_to_subscribers(original_symbol, price)
                                
                                # Записываем метрику
                                performance_monitor.record_price_update(original_symbol)
                                
                        except json.JSONDecodeError:
                            logger.warning(f"Некорректное ticker сообщение: {message}")
                        except Exception as e:
                            logger.error(f"Ошибка обработки ticker: {str(e)}")
                            
            except Exception as e:
                logger.error(f"Ошибка ticker соединения: {str(e)}")
                binance_ticker_connections[ticker_key] = None
                
                # Проверяем есть ли подписчики
                if not has_ticker_subscribers(original_symbol):
                    logger.info(f"Нет подписчиков для ticker {original_symbol}")
                    return
                
                await asyncio.sleep(reconnect_delay)
                reconnect_delay = min(reconnect_delay * RECONNECT_FACTOR, MAX_RECONNECT_DELAY)
    
    # Запускаем handler в фоне
    if ticker_key not in reconnect_tasks:
        reconnect_tasks[ticker_key] = asyncio.create_task(ticker_handler())


async def send_ticker_to_subscribers(symbol, price):
    """Отправка ticker данных подписчикам"""
    disconnected_clients = []
    
    for client, symbols in list(price_ticker_subscriptions.items()):
        if symbol in symbols:
            try:
                message = json.dumps({
                    "type": "ticker",
                    "symbol": symbol,
                    "price": price,
                    "timestamp": int(time.time() * 1000)
                })
                await client.send(message)
                performance_monitor.record_message_sent(len(message.encode('utf-8')))
            except websockets.exceptions.ConnectionClosed:
                disconnected_clients.append(client)
                performance_monitor.record_message_error()
            except Exception as e:
                logger.error(f"Ошибка отправки ticker: {str(e)}")
                performance_monitor.record_message_error()
    
    # Удаляем отключенных клиентов
    for client in disconnected_clients:
        if client in price_ticker_subscriptions:
            del price_ticker_subscriptions[client]


def has_ticker_subscribers(symbol):
    """Проверяет наличие подписчиков на ticker"""
    for client, symbols in price_ticker_subscriptions.items():
        if symbol in symbols:
            return True
    return False


async def cleanup_unused_ticker_connections():
    """Очистка неиспользуемых ticker соединений"""
    # Собираем активные символы
    active_symbols = set()
    for symbols in price_ticker_subscriptions.values():
        active_symbols.update(symbols)
    
    # Преобразуем в ticker keys
    active_ticker_keys = set()
    for symbol in active_symbols:
        binance_symbol = get_binance_symbol(symbol).lower()
        active_ticker_keys.add(f"{binance_symbol}@ticker")
    
    # Закрываем неиспользуемые
    for ticker_key, ws in list(binance_ticker_connections.items()):
        if ticker_key not in active_ticker_keys:
            if ticker_key in reconnect_tasks:
                reconnect_tasks[ticker_key].cancel()
                try:
                    await reconnect_tasks[ticker_key]
                except asyncio.CancelledError:
                    pass
                del reconnect_tasks[ticker_key]
            
            if ws is not None:
                try:
                    await ws.close()
                    logger.info(f"Закрыто ticker соединение: {ticker_key}")
                except Exception as e:
                    logger.error(f"Ошибка закрытия ticker: {str(e)}")
                finally:
                    binance_ticker_connections[ticker_key] = None


async def shutdown():
    """Корректное завершение работы"""
    logger.info("Начинаем завершение работы...")

    # Останавливаем health server
    global health_server_runner
    if health_server_runner:
        await stop_health_server(health_server_runner)
    
    # Останавливаем новые компоненты
    await connection_manager.stop()
    await performance_monitor.stop_monitoring()
    logger.info("Менеджеры остановлены")

    # Закрываем все клиентские соединения
    for client in list(connected_clients):
        try:
            await client.close()
        except Exception as e:
            logger.error(f"Ошибка при закрытии клиента: {str(e)}")

    # Отменяем все задачи переподключения
    for task in list(reconnect_tasks.values()):
        task.cancel()
        try:
            await task
        except asyncio.CancelledError:
            pass

    # Закрываем все Binance WebSocket соединения
    for ws in list(binance_ws_connections.values()):
        if ws is not None:
            try:
                await ws.close()
            except Exception as e:
                logger.error(f"Ошибка при закрытии Binance WebSocket: {str(e)}")
    
    # Закрываем все Binance Ticker соединения
    for ws in list(binance_ticker_connections.values()):
        if ws is not None:
            try:
                await ws.close()
            except Exception as e:
                logger.error(f"Ошибка при закрытии Binance Ticker: {str(e)}")

    # Закрываем HTTP сессию
    await close_http_session()

    logger.info("Завершение работы выполнено")


async def main():
    """Основная функция запуска сервера"""
    logger.info(f"Запуск Binance Gateway на {WS_SERVER_HOST}:{WS_SERVER_PORT}")

    # Инициализируем новые компоненты
    await connection_manager.start()
    await performance_monitor.start_monitoring()
    logger.info("Менеджеры соединений и производительности запущены")
    
    # Запускаем health server
    global health_server_runner
    health_server_runner = await start_health_server(WS_SERVER_PORT + 1)
    logger.info(f"Health server запущен на порту {WS_SERVER_PORT + 1}")

    # Запускаем сервер для клиентов
    server = await websockets.serve(
        handle_websocket_client,
        WS_SERVER_HOST,
        WS_SERVER_PORT,
        ping_interval=HEARTBEAT_INTERVAL,
        ping_timeout=HEARTBEAT_TIMEOUT,
    )

    logger.info("Сервер успешно запущен")

    try:
        # Держим сервер запущенным
        await server.wait_closed()
    except asyncio.CancelledError:
        pass
    finally:
        await shutdown()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Сервер остановлен пользователем")
    except Exception as e:
        logger.exception(f"Неожиданная ошибка: {e}")
    finally:
        # Убеждаемся, что все ресурсы освобождены
        loop = asyncio.new_event_loop()
        loop.run_until_complete(close_http_session())
        loop.close()
