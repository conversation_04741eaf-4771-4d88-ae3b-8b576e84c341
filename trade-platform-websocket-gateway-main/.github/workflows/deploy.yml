name: Deploy WebSocket Gateway

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:  # Позволяет запускать вручную

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: '3.10'
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flake8 pytest websockets
    
    - name: Lint with flake8
      run: |
        # Проверка синтаксиса и основных ошибок
        flake8 coinapi_gateway.py --count --select=E9,F63,F7,F82 --show-source --statistics
        # Предупреждения (не блокируют деплой)
        flake8 coinapi_gateway.py --count --max-line-length=120 --statistics || true
    
    - name: Test syntax
      run: |
        python -m py_compile coinapi_gateway.py
        python -m py_compile test_fetch_history.py
        python -m py_compile test_frontend_spec.py
    
    - name: Run basic tests
      run: |
        # Базовые импорты и инициализация
        python -c "
        import coinapi_gateway
        print('✅ Main module imports successfully')
        "

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.7
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        script: |
          cd ~/trade-platform-websocket-gateway
          ./deploy.sh
          
    - name: Verify deployment
      uses: appleboy/ssh-action@v0.1.7
      with:
        host: ${{ secrets.SERVER_HOST }}
        username: ${{ secrets.SERVER_USER }}
        key: ${{ secrets.SERVER_SSH_KEY }}
        script: |
          # Проверяем что сервер работает
          sleep 5
          if pgrep -f coinapi_gateway.py > /dev/null; then
            echo "✅ Server is running"
            # Простой тест подключения
            cd ~/trade-platform-websocket-gateway
            echo '{"type":"fetchHistory","symbol":"BTC_USDT","timeframe":"1MIN","limit":1}' | timeout 10 websocat ws://localhost:8765 | head -2
          else
            echo "❌ Server is not running"
            tail -20 ../server.log
            exit 1
          fi

  notify:
    needs: [test, deploy]
    runs-on: ubuntu-latest
    if: always()
    
    steps:
    - name: Notify deployment status
      run: |
        if [ "${{ needs.deploy.result }}" == "success" ]; then
          echo "🎉 Deployment successful!"
        else
          echo "❌ Deployment failed!"
        fi