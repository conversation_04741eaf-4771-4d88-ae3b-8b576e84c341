# Multi-stage build для оптимизации размера образа
FROM node:18-alpine AS builder

# Установка рабочей директории
WORKDIR /app

# Копирование package.json и package-lock.json
COPY package*.json ./

# Установка зависимостей
RUN npm ci --only=production && npm cache clean --force

# Копирование исходного кода
COPY . .

# Сборка приложения
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Создание пользователя для безопасности
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nestjs -u 1001

# Установка рабочей директории
WORKDIR /app

# Копирование package.json для установки production зависимостей
COPY package*.json ./

# Установка только production зависимостей
RUN npm ci --only=production && npm cache clean --force

# Копирование собранного приложения из builder stage
COPY --from=builder --chown=nestjs:nodejs /app/dist ./dist

# Переключение на пользователя без root привилегий
USER nestjs

# Открытие порта
EXPOSE 8001

# Команда запуска
CMD ["node", "dist/main.js"]