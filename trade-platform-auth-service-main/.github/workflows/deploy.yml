name: Build, Test and Deploy Auth Service

on:
  push:
    branches:
      - main
  pull_request:
    branches:
      - main

jobs:
  test:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm test

      - name: Build application
        run: npm run build

  deploy:
    needs: test
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    # Define environment variables
    env:
      # Use PUBLIC_IP as the hostname for deployment
      # Note: ************ is a private IP that cannot be reached from GitHub Actions
      # You need to use either the public IP, Elastic IP, or domain name
      AWS_HOST: "${{ secrets.AWS_PUBLIC_IP }}"
      # If you don't have a public IP, set up a GitHub self-hosted runner in your AWS VPC

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup SSH key for deploy
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.AWS_SSH_KEY }}" > ~/.ssh/aws-deploy-key.pem
          chmod 600 ~/.ssh/aws-deploy-key.pem
          ssh-keyscan -H $AWS_HOST >> ~/.ssh/known_hosts

      - name: Deploy to AWS
        run: |
          ssh -i ~/.ssh/aws-deploy-key.pem -o StrictHostKeyChecking=no ubuntu@$AWS_HOST '
            cd ~/trade-platform-auth-service &&
            git pull origin main &&
            npm ci &&
            npm run build &&
            pm2 restart auth-service || pm2 start dist/main.js --name auth-service
          '
