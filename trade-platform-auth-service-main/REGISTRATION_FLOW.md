# Процесс регистрации в BitMei

## Обзор
Регистрация в BitMei происходит в два этапа:
1. Регистрация с отправкой кода подтверждения
2. Подтверждение email

## API Endpoints

### 1. Регистрация пользователя
**POST** `/auth/register`

**Тело запроса:**
```json
{
  "email": "<EMAIL>",
  "password": "password123"
}
```

**Требования:**
- `email`: Валидный email адрес
- `password`: Минимум 6 символов

**Ответ при успехе (200):**
```json
{
  "message": "Код подтверждения отправлен на email",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Ошибки:**
- `409 Conflict`: Email уже используется

**Что происходит:**
1. Проверяется, не занят ли email
2. Генерируется 6-значный код подтверждения
3. Пароль хешируется с помощью bcrypt
4. Данные временно сохраняются в памяти
5. Код отправляется на email
6. Возвращается временный JWT токен (без userId)

### 2. Подтверждение email
**POST** `/auth/confirm`

**Тело запроса:**
```json
{
  "email": "<EMAIL>",
  "code": "123456"
}
```

**Ответ при успехе (200):**
```json
{
  "message": "Email подтверждён",
  "accessToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

**Ошибки:**
- `401 Unauthorized`: Неверный код подтверждения

**Что происходит:**
1. Проверяется код из временного хранилища
2. Создается пользователь в базе данных
3. Возвращается JWT токен (пока без userId, т.к. профиль еще не создан)
4. Данные удаляются из временного хранилища

## Структура JWT токена

### После регистрации (до подтверждения):
```json
{
  "email": "<EMAIL>",
  "isVerified": false,
  "id": undefined
}
```

### После подтверждения email:
```json
{
  "email": "<EMAIL>",
  "id": undefined
}
```

### После создания профиля и входа:
```json
{
  "id": 123,
  "email": "<EMAIL>"
}
```

## Важные моменты

1. **Двухэтапная регистрация**: Пользователь не создается в БД до подтверждения email
2. **Временное хранилище**: Данные хранятся в Map до подтверждения
3. **JWT без userId**: До создания профиля токен не содержит userId
4. **Профиль создается отдельно**: После подтверждения email нужно создать профиль в profile-service

## Swagger документация

После запуска сервиса документация доступна по адресу:
```
http://localhost:8001/api
```

## Пример полного процесса регистрации

```bash
# 1. Регистрация
curl -X POST http://localhost:8001/auth/register \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'

# 2. Подтверждение email (код придет на почту)
curl -X POST http://localhost:8001/auth/confirm \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","code":"123456"}'
```

## Настройки SMTP

Для отправки email используются следующие переменные окружения:
- `SMTP_HOST`: Хост SMTP сервера
- `SMTP_PORT`: Порт SMTP сервера (по умолчанию 587)
- `SMTP_USER`: Email отправителя
- `SMTP_PASSWORD`: Пароль для SMTP