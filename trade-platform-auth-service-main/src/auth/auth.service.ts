import {
  Injectable,
  Logger,
  UnauthorizedException,
  ConflictException,
  InternalServerErrorException,
  NotFoundException,
  Inject,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { DataSource, QueryRunner, Repository } from 'typeorm';
import { User } from './user.entity';
import * as bcrypt from 'bcryptjs';
import * as nodemailer from 'nodemailer';
import { JwtService } from '@nestjs/jwt';
import { ClientKafka } from '@nestjs/microservices';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);
  private temporaryStorage = new Map<
    string,
    { email: string; password: string; code: string }
  >();
  private resetPasswordStorage = new Map<
    string,
    { email: string; code: string }
  >();
  private passwordChangeStorage = new Map<string, string>();

  constructor(
    @InjectRepository(User) private readonly userRepository: Repository<User>,
    private readonly jwtService: JwtService,
    private readonly dataSource: DataSource,

    @Inject('KAFKA_SERVICE')
    private readonly kafkaClient: ClientKafka, // ✅ Добавляем Kafka
  ) {}

  async onModuleInit() {
    this.kafkaClient.subscribeToResponseOf('profile.getUserIdByEmail');
    this.kafkaClient.subscribeToResponseOf('auth.deleteUserByEmail');
    this.kafkaClient.subscribeToResponseOf('auth.updateToken');
    this.kafkaClient.subscribeToResponseOf('user.email.updated');
    this.kafkaClient.subscribeToResponseOf('auth.getUserByEmail');
    this.kafkaClient.subscribeToResponseOf('auth.getUserByEmail.reply');

    await this.kafkaClient.connect(); // ✅ Подключаем клиента после подписки
  }

  private transporter = nodemailer.createTransport({
    host: process.env.SMTP_HOST ?? 'smtp.example.com',
    port: parseInt(process.env.SMTP_PORT ?? '587', 10),
    secure: false,
    auth: {
      user: process.env.SMTP_USER ?? '<EMAIL>',
      pass: process.env.SMTP_PASSWORD ?? 'password123',
    },
  });

  private generateCode(): string {
    return Math.floor(100000 + Math.random() * 900000).toString();
  }

  private async sendEmail(email: string, code: string) {
    await this.transporter.sendMail({
      from: `"No-Reply" <${process.env.SMTP_USER ?? '<EMAIL>'}>`,
      to: email,
      subject: 'Код подтверждения',
      text: `Ваш код подтверждения: ${code}`,
    });
  }

  private async sendPasswordChangeCode(email: string, code: string) {
    await this.transporter.sendMail({
      from: `"No-Reply" <${process.env.SMTP_USER ?? '<EMAIL>'}>`,
      to: email,
      subject: 'Смена пароля',
      text: `Ваш код для смены пароля: ${code}`,
    });
  }

  // 📌 Регистрация пользователя
  async register(email: string, password: string) {
    this.logger.log(`📝 Регистрация пользователя: ${email}`);
    const existingUser = await this.userRepository.findOne({
      where: { email },
    });

    if (existingUser) {
      this.logger.warn(`⚠️ Email уже используется: ${email}`);
      throw new ConflictException('Email уже используется');
    }

    const verificationCode = this.generateCode();
    const hashedPassword = await bcrypt.hash(password, 10);

    // 🔹 Временное хранение данных до подтверждения email
    this.temporaryStorage.set(email, {
      email,
      password: hashedPassword,
      code: verificationCode,
    });

    await this.sendEmail(email, verificationCode);

    // ⚠️ НЕ создаём `id` в токене, потому что юзер пока не подтверждён!
    const payload = {
      email,
      isVerified: false,
      id: undefined as unknown as number,
    };
    const accessToken = this.jwtService.sign(payload);

    return { message: 'Код подтверждения отправлен на email', accessToken };
  }

  // 📌 Подтверждение email (отправляем токен только с email)
  async confirmEmail(email: string, code: string) {
    this.logger.log(`📩 Подтверждение email: ${email} с кодом ${code}`);

    const storedData = this.temporaryStorage.get(email);

    if (!storedData || storedData.code !== code) {
      this.logger.warn(`🚫 Неверный код подтверждения для ${email}`);
      throw new UnauthorizedException('Неверный код подтверждения');
    }

    // ✅ Создаём пользователя в базе
    const newUser = this.userRepository.create({
      email,
      password: storedData.password,
    });

    const savedUser = await this.userRepository.save(newUser);
    if (!savedUser.id) {
      this.logger.error(
        `❌ Ошибка! ID не создался при сохранении пользователя.`,
      );
      throw new InternalServerErrorException('Не удалось создать пользователя');
    }

    this.logger.log(`✅ Пользователь создан с ID: ${savedUser.id}`);

    // 🔥 Возвращаем токен с email и id:undefined (профиль не создан)
    const payload = {
      email: savedUser.email,
      id: undefined as unknown as number,
    };
    const accessToken = this.jwtService.sign(payload);

    this.temporaryStorage.delete(email);
    return { message: 'Email подтверждён', accessToken };
  }

  // 📌 Получение userId через Kafka
  private async getProfileIdByEmail(email: string): Promise<number> {
    this.logger.log(`📡 Запрашиваем userId для email: ${email} через Kafka`);
    return new Promise<number>((resolve, reject) => {
      this.kafkaClient.send('profile.getUserIdByEmail', { email }).subscribe({
        next: (response: unknown) => {
          // Детальное логирование ответа
          this.logger.log(
            `📡 Получен ответ от Kafka: ${JSON.stringify(response)}`,
          );
          this.logger.log(`📡 Тип ответа: ${typeof response}`);

          if (response === null || response === undefined) {
            this.logger.error(
              `❌ Ошибка: Получен пустой ответ от Kafka для ${email}`,
            );
            return reject(
              new NotFoundException(
                'Профиль не найден. Необходимо создать профиль.',
              ),
            );
          }

          let userId: number | undefined = undefined;

          // Если ответ - это объект с полем userId
          if (typeof response === 'object' && response !== null) {
            const typedResponse = response as Record<string, unknown>;
            if ('userId' in typedResponse) {
              userId = Number(typedResponse.userId);
              this.logger.log(`📡 Извлечен userId из объекта: ${userId}`);
            } else if ('id' in typedResponse) {
              userId = Number(typedResponse.id);
              this.logger.log(`📡 Извлечен id из объекта: ${userId}`);
            } else {
              // Если это сложный вложенный объект
              this.logger.log(
                `📡 Сложный объект без прямого userId/id: ${JSON.stringify(response)}`,
              );
              // Пробуем найти id рекурсивно (до 2 уровней вложенности)
              for (const key in typedResponse) {
                const value = typedResponse[key];
                if (typeof value === 'object' && value !== null) {
                  const nestedObj = value as Record<string, unknown>;
                  if ('userId' in nestedObj) {
                    userId = Number(nestedObj.userId);
                    this.logger.log(`📡 Найден вложенный userId: ${userId}`);
                    break;
                  } else if ('id' in nestedObj) {
                    userId = Number(nestedObj.id);
                    this.logger.log(`📡 Найден вложенный id: ${userId}`);
                    break;
                  }
                }
              }
            }
          }
          // Если ответ - это число напрямую
          else if (typeof response === 'number') {
            userId = response;
            this.logger.log(`📡 Ответ является числом напрямую: ${userId}`);
          }
          // Если ответ - это строка, которую можно преобразовать в число
          else if (typeof response === 'string' && !isNaN(Number(response))) {
            userId = Number(response);
            this.logger.log(`📡 Строка преобразована в число: ${userId}`);
          }

          if (userId === undefined || (userId !== 0 && !userId)) {
            this.logger.error(
              `❌ Не удалось извлечь userId из ответа: ${JSON.stringify(response)}`,
            );
            return reject(
              new NotFoundException(
                'Не удалось получить ID профиля. Необходимо создать профиль.',
              ),
            );
          }

          this.logger.log(`✅ Итоговый userId: ${userId} для email: ${email}`);
          resolve(userId);
        },
        error: (err: Error) => {
          this.logger.error(`⛔ Ошибка Kafka запроса: ${err.message}`);
          reject(new InternalServerErrorException('Ошибка запроса профиля'));
        },
      });
    });
  }

  // ✅ Обновление email пользователя с транзакцией
  async updateUserEmail(
    oldEmail: string,
    newEmail: string,
  ): Promise<User | null> {
    this.logger.log(`🔄 Запрос на обновление email: ${oldEmail} → ${newEmail}`);

    if (oldEmail === newEmail) {
      this.logger.warn(
        '⚠️ Новый email совпадает со старым, обновление не требуется',
      );
      return null;
    }

    const queryRunner: QueryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // 1️⃣ Ищем пользователя по старому email
      const user = await queryRunner.manager.findOne(User, {
        where: { email: oldEmail },
      });
      if (!user) {
        this.logger.warn(`❌ Пользователь с email ${oldEmail} не найден`);
        await queryRunner.rollbackTransaction(); // ✅ Откат при ошибке
        return null;
      }

      // 2️⃣ Проверяем, что новый email ещё не занят
      const existingUser = await queryRunner.manager.findOne(User, {
        where: { email: newEmail },
      });
      if (existingUser) {
        this.logger.warn(`⚠️ Новый email ${newEmail} уже используется`);
        await queryRunner.rollbackTransaction(); // ✅ Откат при ошибке
        throw new ConflictException(`Email ${newEmail} уже занят`);
      }

      // 3️⃣ Обновляем email пользователя
      user.email = newEmail;
      await queryRunner.manager.save(user);

      // ✅ Фиксируем изменения в БД
      await queryRunner.commitTransaction();
      this.logger.log(`✅ Email успешно обновлён: ${oldEmail} → ${newEmail}`);

      return user; // ✅ Возвращаем обновленного пользователя
    } catch (error) {
      await queryRunner.rollbackTransaction(); // ❌ Откат изменений в случае ошибки
      this.logger.error(`⛔ Ошибка обновления email: ${error.message}`);
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  async setPassword(email: string, password: string) {
    this.logger.log(`🔑 Установка пароля для ${email}`);

    const user = await this.userRepository.findOne({
      where: { email, isVerified: true },
    });
    if (!user) {
      throw new NotFoundException('Пользователь не найден или не подтверждён');
    }

    user.password = await bcrypt.hash(password, 10);
    await this.userRepository.save(user);
    return { message: 'Пароль установлен' };
  }

  async login(email: string, password: string) {
    this.logger.log(`🔐 Вход для ${email}`);

    const user = await this.userRepository.findOne({ where: { email } });
    if (!user || !user.password) {
      // ✅ Проверка, что пароль не null
      this.logger.error(`❌ Неверный email или пароль для ${email}`);
      throw new UnauthorizedException('Неверный email или пароль');
    }

    const isMatch = await bcrypt.compare(password, user.password);
    if (!isMatch) {
      this.logger.error(`❌ Неверный пароль для ${email}`);
      throw new UnauthorizedException('Неверный email или пароль');
    }

    const code = this.generateCode();
    user.confirmationCode = code;
    await this.userRepository.save(user);

    this.logger.log(`📩 Отправка кода подтверждения на ${email}: ${code}`);
    await this.sendEmail(email, code);

    return { requiresCode: true, message: 'Введите код подтверждения' };
  }

  // 📌 Подтверждение логин-кода + получаем `id`
  async verifyLoginCode(email: string, code: string) {
    this.logger.log(`🔑 Проверка кода логина для ${email}`);

    const user = await this.userRepository.findOne({
      where: { email, confirmationCode: code },
    });
    if (!user) {
      this.logger.error(`❌ Неверный код для ${email}`);
      throw new UnauthorizedException('Неверный код');
    }

    // Очищаем код сразу после проверки, чтобы избежать повторных запросов
    user.confirmationCode = null;
    await this.userRepository.save(user);

    try {
      // 🔥 Запрашиваем `id` через Kafka (теперь профиль уже должен быть создан)
      const id = await this.getProfileIdByEmail(email);

      this.logger.log(`📌 Получен id=${id} для ${email}`);

      // ✅ Генерируем токен с `userId` как простое свойство (не вложенный объект)
      const payload = {
        id: id, // Используем id вместо userId для совместимости
        email: user.email,
      };

      // Логируем структуру payload перед созданием токена
      this.logger.log(`🔍 Payload для JWT: ${JSON.stringify(payload)}`);
      const accessToken = this.jwtService.sign(payload);

      return { accessToken, message: 'Вход выполнен', userId: id };
    } catch (error) {
      if (error instanceof NotFoundException) {
        // Если профиль не найден, генерируем токен только с email
        this.logger.warn(
          `⚠️ Профиль не найден, генерируем токен только с email`,
        );
        const payload = {
          id: undefined as unknown as number, // Используем id вместо userId для совместимости с типами
          email: user.email,
        };
        const accessToken = this.jwtService.sign(payload);

        return {
          accessToken,
          message: 'Вход выполнен. Необходимо создать профиль.',
          needProfile: true,
          userId: null,
        };
      }

      // Если другая ошибка, пробрасываем её дальше
      throw error;
    }
  }

  // ✅ Обновление JWT-токена с `id`
  async updateUserToken(payload: { id: number; email: string }) {
    this.logger.log(
      `🔄 Обновление токена для ${payload.email} с ID ${payload.id}`,
    );

    // 🔍 Находим пользователя по email
    const user = await this.userRepository.findOne({
      where: { email: payload.email },
    });

    if (!user) {
      this.logger.error(`❌ Пользователь с email ${payload.email} не найден`);
      return { error: 'User not found' };
    }

    // 🔹 Создаем новый JWT с id и email (используем id вместо userId)
    const jwtPayload = {
      id: payload.id, // Используем id как простое свойство для совместимости
      email: payload.email,
    };

    // Логируем структуру payload перед созданием токена
    this.logger.log(
      `🔍 Payload для JWT updateUserToken: ${JSON.stringify(jwtPayload)}`,
    );

    const newToken = this.jwtService.sign(jwtPayload);

    this.logger.log(`✅ Новый токен с id и email создан: ${newToken}`);

    // ❗ Токен отправляется обратно в `ProfileService` для обновления на фронте
    return { accessToken: newToken, userId: payload.id };
  }

  async forgotPassword(email: string) {
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new NotFoundException('Пользователь с таким email не найден');
    }

    const resetCode = this.generateCode();
    this.resetPasswordStorage.set(email, { email, code: resetCode });

    try {
      await this.sendEmail(email, resetCode);
      this.logger.log(
        `📩 Код восстановления ${resetCode} отправлен на ${email}`,
      );
    } catch (error) {
      throw new InternalServerErrorException('Ошибка отправки email');
    }

    return { message: 'Код восстановления отправлен на email.' };
  }

  async verifyResetCode(email: string, code: string) {
    const storedData = this.resetPasswordStorage.get(email);
    if (!storedData || storedData.code !== code) {
      throw new UnauthorizedException('Неверный или истёкший код.');
    }

    this.logger.log(`✅ Код подтвержден для ${email}`);
    return { message: 'Код подтвержден.' };
  }

  async resetForgotPassword(email: string, code: string, newPassword: string) {
    this.logger.log(`🔄 Попытка сброса пароля для ${email}`);

    const storedData = this.resetPasswordStorage.get(email);
    if (!storedData || storedData.code !== code) {
      throw new UnauthorizedException('Неверный или истёкший код.');
    }

    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      throw new NotFoundException('Пользователь не найден');
    }

    user.password = await bcrypt.hash(newPassword, 10);
    await this.userRepository.save(user);
    this.resetPasswordStorage.delete(email);

    this.logger.log(`✅ Пароль успешно сброшен для ${email}`);
    return { message: 'Пароль успешно сброшен.' };
  }

  // ✅ Удаление пользователя по email
  async deleteUserByEmail(email: string): Promise<boolean> {
    this.logger.log(`🗑 Удаляем пользователя с Email: ${email}`);

    // 🔍 Ищем пользователя
    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      this.logger.warn(`⚠️ Пользователь с Email ${email} не найден`);
      return false;
    }

    // 🗑 Удаляем пользователя
    await this.userRepository.delete({ email });
    this.logger.log(`✅ Пользователь с Email ${email} успешно удалён`);
    return true;
  }

  // ✅ Метод для поиска пользователя по email
  async findUserByEmail(email: string): Promise<User | null> {
    this.logger.log(`📡 Запрос пользователя с email: ${email}`);
    const user = await this.userRepository.findOne({ where: { email } });

    if (!user) {
      this.logger.warn(`⚠️ Пользователь с email ${email} не найден`);
      return null;
    }

    return user;
  }

  // ✅ Запрос кода для смены пароля (по email)
  async requestPasswordChange(email: string) {
    this.logger.log(`📩 Генерация кода для смены пароля для email: ${email}`);

    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      this.logger.warn(`⚠️ Пользователь с email ${email} не найден`);
      throw new NotFoundException('Пользователь не найден');
    }

    const changeCode = this.generateCode();
    this.passwordChangeStorage.set(email, changeCode); // ✅ Отдельное хранилище кода

    try {
      await this.sendPasswordChangeCode(user.email, changeCode);
      this.logger.log(
        `📩 Код смены пароля ${changeCode} отправлен на ${email}`,
      );
    } catch (error) {
      throw new InternalServerErrorException('Ошибка отправки email');
    }

    return { message: 'Код смены пароля отправлен' };
  }

  // ✅ Подтверждение смены пароля (по email)
  async confirmPasswordChange(
    email: string,
    code: string,
    newPassword: string,
  ) {
    this.logger.log(`🔑 Подтверждение смены пароля для email: ${email}`);

    const storedCode = this.passwordChangeStorage.get(email);
    if (!storedCode || storedCode !== code) {
      this.logger.warn(`🚫 Неверный код подтверждения для email: ${email}`);
      throw new UnauthorizedException('Неверный код');
    }

    const user = await this.userRepository.findOne({ where: { email } });
    if (!user) {
      this.logger.warn(`⚠️ Пользователь с email ${email} не найден`);
      throw new NotFoundException('Пользователь не найден');
    }

    user.password = await bcrypt.hash(newPassword, 10);
    await this.userRepository.save(user);

    // Удаляем временные данные из `Map`
    this.passwordChangeStorage.delete(email);

    this.logger.log(`✅ Пароль успешно изменён для email: ${email}`);
    return { message: 'Пароль успешно изменен' };
  }
}
