import { <PERSON>, Post, Body, Logger } from '@nestjs/common';
import { AuthService } from './auth.service';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { InjectRepository } from '@nestjs/typeorm';
import { User } from './user.entity';
import { Repository } from 'typeorm';
import { ApiTags, ApiOperation, ApiResponse, ApiBody } from '@nestjs/swagger';

@ApiTags('auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthService.name);
  constructor(
    private readonly authService: AuthService,

    @InjectRepository(User) private readonly userRepository: Repository<User>,
  ) {}

  // 📡 Слушаем Kafka-топик "auth.deleteUserByEmail"
  @MessagePattern('auth.deleteUserByEmail')
  async deleteUserByEmail(@Payload() data: { email: string }) {
    this.logger.log(
      `📩 Получен запрос на удаление пользователя: ${data.email}`,
    );

    const result = await this.authService.deleteUserByEmail(data.email);

    if (result) {
      this.logger.log(`✅ Пользователь с Email ${data.email} успешно удалён`);
      return { success: true, message: 'Пользователь успешно удалён' };
    } else {
      this.logger.warn(`⚠️ Пользователь с Email ${data.email} не найден`);
      return { success: false, message: 'Пользователь не найден' };
    }
  }

  // ✅ Обработчик сообщений от Kafka
  @MessagePattern('user.email.updated')
  async updateUserEmail(
    @Payload() message: { oldEmail: string; newEmail: string },
  ) {
    try {
      this.logger.log(
        `📩 Пришло сообщение от Kafka: ${JSON.stringify(message)}`,
      );

      if (!message || !message.oldEmail || !message.newEmail) {
        this.logger.error(
          '❌ Ошибка: отсутствует oldEmail или newEmail в payload',
        );
        return;
      }

      // ✅ Обновляем email в `AuthService`
      const updatedUser = await this.authService.updateUserEmail(
        message.oldEmail,
        message.newEmail,
      );

      if (!updatedUser) {
        this.logger.warn(
          `⚠️ Пользователь с email ${message.oldEmail} не найден`,
        );
      } else {
        this.logger.log(
          `✅ Email успешно обновлён: ${message.oldEmail} → ${message.newEmail}`,
        );
      }
    } catch (error) {
      this.logger.error(
        `⛔ Ошибка при обработке сообщения Kafka: ${error.message}`,
        error.stack,
      );
    }
  }

  // ✅ Kafka Listener для обновления токена
  @MessagePattern('auth.updateToken')
  async handleTokenUpdate(@Payload() payload: { id: number; email: string }) {
    this.logger.log(
      `🔄 Получен запрос на обновление токена: ${JSON.stringify(payload)}`,
    );
    return this.authService.updateUserToken(payload);
  }

  // ✅ Kafka Listener для поиска email
  @MessagePattern('auth.getUserByEmail')
  async getUserByEmail(@Payload() payload: { email: string }) {
    this.logger.log(`🔍 Поиск пользователя по email: ${payload.email}`);
    const user = await this.authService.findUserByEmail(payload.email);

    if (!user) {
      this.logger.error(`❌ Пользователь с email ${payload.email} не найден`);
      return null;
    }

    return { id: user.id, email: user.email };
  }

  @Post('register')
  @ApiOperation({ summary: 'Регистрация нового пользователя' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['email', 'password'],
      properties: {
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        password: { type: 'string', minLength: 6, example: 'password123' }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Код подтверждения отправлен на email' })
  @ApiResponse({ status: 409, description: 'Email уже используется' })
  async register(@Body() body: { email: string; password: string }) {
    this.logger.log(`📩 Контроллер получил: ${JSON.stringify(body)}`);
    return await this.authService.register(body.email, body.password);
  }

  @Post('confirm')
  @ApiOperation({ summary: 'Подтверждение email' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['email', 'code'],
      properties: {
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        code: { type: 'string', example: '123456' }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Email подтверждён' })
  @ApiResponse({ status: 401, description: 'Неверный код подтверждения' })
  async confirm(@Body() body: { email: string; code: string }) {
    return await this.authService.confirmEmail(body.email, body.code);
  }

  @Post('set-password')
  async setPassword(@Body() body: { email: string; password: string }) {
    return await this.authService.setPassword(body.email, body.password);
  }

  @Post('login')
  @ApiOperation({ summary: 'Вход в систему' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['email', 'password'],
      properties: {
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        password: { type: 'string', example: 'password123' }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Код подтверждения отправлен' })
  @ApiResponse({ status: 401, description: 'Неверный email или пароль' })
  async login(@Body() body: { email: string; password: string }) {
    return await this.authService.login(body.email, body.password);
  }

  @Post('confirm-login')
  @ApiOperation({ summary: 'Подтверждение входа' })
  @ApiBody({
    schema: {
      type: 'object',
      required: ['email', 'code'],
      properties: {
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        code: { type: 'string', example: '123456' }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Вход выполнен успешно' })
  @ApiResponse({ status: 401, description: 'Неверный код' })
  async confirmLogin(@Body() body: { email: string; code: string }) {
    return await this.authService.verifyLoginCode(body.email, body.code);
  }

  // 🔹 **Восстановление пароля**
  @Post('forgot-password')
  async forgotPassword(@Body() body: { email: string }) {
    return await this.authService.forgotPassword(body.email);
  }

  @Post('verify-reset-code')
  async verifyResetCode(@Body() body: { email: string; code: string }) {
    return await this.authService.verifyResetCode(body.email, body.code);
  }

  @Post('reset-forgot-password')
  async resetForgotPassword(
    @Body() body: { email: string; code: string; newPassword: string },
  ) {
    return await this.authService.resetForgotPassword(
      body.email,
      body.code,
      body.newPassword,
    );
  }

  // 📌 1️⃣ Запрос кода для смены пароля (по email)
  @Post('request-password-reset')
  async requestPasswordChange(@Body() body: { email: string }) {
    this.logger.log(`📩 Запрос кода для смены пароля для email: ${body.email}`);
    return await this.authService.requestPasswordChange(body.email);
  }

  // 📌 2️⃣ Подтверждение смены пароля
  @Post('confirm-password-reset')
  async confirmPasswordChange(
    @Body() body: { email: string; code: string; newPassword: string },
  ) {
    this.logger.log(`🔑 Подтверждение смены пароля для email: ${body.email}`);
    return await this.authService.confirmPasswordChange(
      body.email,
      body.code,
      body.newPassword,
    );
  }
}
