import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as cors from 'cors';
import * as dotenv from 'dotenv';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // Разрешаем CORS для всех методов
  const allowedOrigins = [
    'https://app.bitmei.com',
    'https://dev.bitmei.com',
    'https://bitmei.com',
    'http://localhost:3000',
    'http://localhost:3001',
    'http://localhost:3012',
    'https://auth-api.eu.ngrok.io',
    'https://profile-api.eu.ngrok.io',
    'https://wallet-api.eu.ngrok.io',
    'https://balance-api.eu.ngrok.io',
    'https://balance-ws.eu.ngrok.io',
    'https://analysis-api.eu.ngrok.io',
    'https://analysis-ws.eu.ngrok.io',
    'https://trading-api.eu.ngrok.io',
    'https://trading-ws.eu.ngrok.io',
    'https://withdraw-api.eu.ngrok.io',
    'https://referal-api.eu.ngrok.io',
    'https://support-api.eu.ngrok.io',
    'https://media-api.eu.ngrok.io',
    'https://trading-chart-ws.eu.ngrok.io'
  ];

  app.use(cors({
    origin: (origin, callback) => {
      // Разрешаем запросы без origin (например, от Postman)
      if (!origin) return callback(null, true);
      
      if (allowedOrigins.includes(origin)) {
        callback(null, true);
      } else {
        callback(new Error('Not allowed by CORS'));
      }
    },
    credentials: true,
    methods: '*',
    allowedHeaders: '*'
  }));

  // Настройка Swagger
  const config = new DocumentBuilder()
    .setTitle('Auth Service API')
    .setDescription('API для аутентификации и авторизации пользователей BitMei')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  // Подключаем Kafka Consumer
  const kafkaApp = await NestFactory.createMicroservice<MicroserviceOptions>(
    AppModule,
    {
      transport: Transport.KAFKA,
      options: {
        client: {
          brokers: ['*************:9093'],
        },
        consumer: {
          groupId: 'auth-service-consumer',
          allowAutoTopicCreation: true,
          sessionTimeout: 30000,
        },
      },
    },
  );

  kafkaApp.listen();
  await app.listen(8001);
  console.log('✅ Auth Service запущен на порту 3001');
}

bootstrap();
