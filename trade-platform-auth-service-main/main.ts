import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';
import * as cors from 'cors';
import * as dotenv from 'dotenv';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Разрешаем CORS для всех методов
  app.use(cors({
    origin:  ['http://*************:3000',  'http://localhost:3000'],// В продакшене замените на конкретные домены
    methods: ['GET', 'HEAD', 'PUT', 'PATCH', 'POST', 'DELETE', 'OPTIONS'], // Явно указаны все методы
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'Accept'],
    credentials: true,
    preflightContinue: false,
    optionsSuccessStatus: 204
  }));

  // Подключаем Kafka Consumer
  const kafkaApp = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: Transport.KAFKA,
    options: {
      client: {
        brokers: ['*************:9093'],
        // brokers: ['localhost:9093'],
      },
      consumer: {
        groupId: 'auth-service-consumer',
        allowAutoTopicCreation: true,
        sessionTimeout: 30000,
      },
    },
  });

  kafkaApp.listen();
  await app.listen(8001);
  console.log('✅ Auth Service запущен на порту 3001');
}

bootstrap();
