import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import { AppModule } from './app.module';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import * as cors from 'cors';
import * as dotenv from 'dotenv';

dotenv.config();

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  
  // Разрешаем CORS для всех методов
  app.use(cors({ origin: '*', methods: '*' }));

  // Настройка Swagger
  const config = new DocumentBuilder()
    .setTitle('Profile Service API')
    .setDescription('API для управления профилями пользователей BitMei')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api', app, document);

  // Подключаем Kafka Consumer
  const kafkaApp = await NestFactory.createMicroservice<MicroserviceOptions>(AppModule, {
    transport: Transport.KAFKA,
    options: {
      client: {
        brokers: ['*************:9093'],
      },
      consumer: {
        groupId: 'profile-service-consumer',
      },
    },
  });

  kafkaApp.listen();
  await app.listen(8002);
  console.log('✅ Profile Service запущен на порту 3002');
}

bootstrap();
