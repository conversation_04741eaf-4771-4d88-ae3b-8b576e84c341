import { Controller, Post, Body, Logger, Get, Param, NotFoundException, Put, Inject, Delete, BadRequestException } from '@nestjs/common';
import { ProfileService } from './profile.service';
import { Profile } from './profile.entity'; // ✅ Импортируем Profile
import { ClientKafka, Ctx, MessagePattern, Payload, RmqContext } from '@nestjs/microservices';
import { Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody } from '@nestjs/swagger';

@ApiTags('profile')
@Controller('profile')
export class ProfileController {
  private readonly logger = new Logger(ProfileController.name);
 

  constructor(
    private readonly profileService: ProfileService,
    @Inject('KAFKA_SERVICE') private readonly kafkaClient: ClientKafka
  ) {}

  // ✅ Обработка запроса на обновление аватарки от медиа-сервиса
@MessagePattern('profile.update_avatar')
async updateUserAvatar(@Payload() data: { userId: string, avatarUrl: string }) {
  this.logger.log(`📩 Получен запрос на обновление аватарки от медиа-сервиса: userId=${data.userId}, avatarUrl=${data.avatarUrl}`);

  if (!data.userId || !data.avatarUrl) {
    this.logger.warn('⚠️ Неполные данные для обновления аватарки');
    return { success: false, error: 'Отсутствуют обязательные параметры' };
  }

  try {
    const updated = await this.profileService.updateUserAvatar(Number(data.userId), data.avatarUrl);
    
    if (!updated) {
      this.logger.warn(`⚠️ Пользователь с ID ${data.userId} не найден для обновления аватарки`);
      return { success: false, error: 'Пользователь не найден' };
    }

    this.logger.log(`✅ Аватарка обновлена для пользователя ${data.userId}: ${data.avatarUrl}`);
    return { success: true };
  } catch (error) {
    this.logger.error(`❌ Ошибка при обновлении аватарки: ${error.message}`);
    return { success: false, error: error.message };
  }
}

// ✅ Message pattern для проверки существования пользователя (для медиа-сервиса)
@MessagePattern('profile.check_user')
async checkUserExists(@Payload() data: { userId: string }) {
  this.logger.log(`📩 Проверка существования пользователя: userId=${data.userId}`);

  if (!data.userId) {
    this.logger.warn('⚠️ Отсутствует ID пользователя для проверки');
    return { exists: false, error: 'Отсутствует ID пользователя' };
  }

  try {
    const profile = await this.profileService.getProfileById(Number(data.userId));
    const exists = !!profile;
    
    this.logger.log(`✅ Результат проверки пользователя ${data.userId}: ${exists ? 'существует' : 'не существует'}`);
    return { exists };
  } catch (error) {
    this.logger.error(`❌ Ошибка при проверке пользователя: ${error.message}`);
    return { exists: false, error: error.message };
  }
}

  // ✅ Получаем данные профиля через Kafka
  @MessagePattern('profile.request')
  async getProfile(@Payload() data: { id: number }) {
    this.logger.log(`📩 Запрос профиля через Kafka по ID: ${data.id}`);

    const profile = await this.profileService.getProfileDataById(data.id);
    if (!profile) {
      this.logger.warn(`⚠️ Профиль не найден: ${data.id}`);
      return { error: 'Профиль не найден' };
    }

    this.logger.log(`✅ Отправляем профиль через Kafka: ${JSON.stringify(profile, null, 2)}`);
    return JSON.parse(JSON.stringify(profile)); // 💡 Гарантируем чистый JSON
  }



  // Отдельный топик специально для запроса email пользователя
  @MessagePattern('profile.email.request')
  async getUserEmail(@Payload() data: { userId: number }) {
    this.logger.log(`📩 Запрос email пользователя через Kafka по ID: ${data.userId}`);
    
    try {
      const result = await this.profileService.handleEmailRequest(data.userId);
      
      // Если нужно эмитить, делаем это здесь с проверкой
      if (this.kafkaClient && result.success) {
        try {
          this.kafkaClient.emit('profile.email.response', result);
          this.logger.log(`✅ Отправлен ответ в топик profile.email.response`);
        } catch (error) {
          this.logger.error(`❌ Ошибка при отправке в Kafka: ${error.message}`);
        }
      }
      
      return result;
    } catch (error) {
      this.logger.error(`❌ Ошибка при обработке запроса email: ${error.message}`);
      return { 
        userId: data.userId,
        success: false,
        error: error.message 
      };
    }
  }

  // ✅ Обрабатываем запрос профиля для комментариев через Kafka
  @MessagePattern('profile.comment.request')
  async handleProfileRequest(@Payload() data: { id: number }) {
    this.logger.log(`📩 Получен запрос профиля для комментариев по ID: ${JSON.stringify(data)}`);

    if (!data.id) {
      this.logger.warn('⚠️ Запрос профиля без ID');
      return { error: 'ID обязателен' };
    }

    const profile = await this.profileService.getProfileDataById(data.id); // ✅ Используем `getProfileDataById`

    if (!profile) {
      this.logger.warn(`⚠️ Профиль не найден для ID: ${data.id}`);
      return { error: 'Профиль не найден' };
    }

    this.logger.log(`✅ Отправляем профиль: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Получение userId по email через Kafka
  @MessagePattern('profile.getUserIdByEmail')
  async getUserIdByEmail(@Payload() data: { email: string }) {
    this.logger.log(`📩 Запрос userId по email: ${data.email}`);

    const profile = await this.profileService.getProfileByEmail(data.email);

    if (!profile) {
      this.logger.warn(`⚠️ Профиль с email ${data.email} не найден!`);
      return { userId: null };
    }

    this.logger.log(`✅ Найден userId=${profile.id} для email ${data.email}`);
    return profile.id;
  }
  
  // ✅ Создание профиля с поддержкой реферальных кодов
  @Post()
  @ApiOperation({ summary: 'Создание нового профиля пользователя' })
  @ApiBody({
    description: 'Данные для создания профиля',
    schema: {
      type: 'object',
      required: ['email', 'firstName', 'lastName'],
      properties: {
        email: { type: 'string', format: 'email', example: '<EMAIL>' },
        firstName: { type: 'string', example: 'Иван' },
        lastName: { type: 'string', example: 'Иванов' },
        phone: { type: 'string', example: '+7900123456' },
        referralCode: { type: 'string', example: 'REF123' },
        referrerCode: { type: 'string', example: 'FRIEND456' }
      }
    }
  })
  @ApiResponse({ status: 201, description: 'Профиль успешно создан' })
  @ApiResponse({ status: 400, description: 'Неверные данные' })
  async createProfile(@Body() profileData: Pick<Profile, 'email' | 'firstName' | 'lastName' | 'phone' | 'referralCode' | 'referrerCode'>) {
    this.logger.log(`🆕 Запрос на создание профиля: ${JSON.stringify({
      ...profileData,
      referralCode: profileData.referralCode || 'не задан',
      referrerCode: profileData.referrerCode || 'не задан'
    })}`);

    const profile = await this.profileService.createProfile(profileData);
    this.logger.log(`✅ Профиль успешно создан: ${JSON.stringify({
      ...profile,
      profile: {
        ...profile.profile,
        referralCode: profile.profile.referralCode || 'не задан',
        referrerCode: profile.profile.referrerCode || 'не задан'
      }
    })}`);

    return profile;
  }

  // ✅ Получение профиля по email
  @Get('email/:email')
  @ApiOperation({ summary: 'Получение профиля по email' })
  @ApiParam({ name: 'email', description: 'Email пользователя', example: '<EMAIL>' })
  @ApiResponse({ status: 200, description: 'Профиль найден' })
  @ApiResponse({ status: 404, description: 'Профиль не найден' })
  async getProfileByEmail(@Param('email') email: string) {
    this.logger.log(`🔍 Запрос профиля по email: ${email}`);

    const profile = await this.profileService.getProfileByEmail(email);
    if (!profile) {
      this.logger.warn(`⚠️ Профиль с email ${email} не найден`);
      throw new NotFoundException(`Профиль с email ${email} не найден`);
    }

    this.logger.log(`✅ Профиль найден: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Получение профиля по реферальному коду
  @Get('referral/:code')
  async getProfileByReferralCode(@Param('code') code: string) {
    this.logger.log(`🔍 Запрос профиля по реферальному коду: ${code}`);

    const profile = await this.profileService.getProfileByReferralCode(code);
    if (!profile) {
      this.logger.warn(`⚠️ Профиль с реферальным кодом ${code} не найден`);
      throw new NotFoundException(`Профиль с реферальным кодом ${code} не найден`);
    }

    this.logger.log(`✅ Профиль найден: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Получение профиля по id
  @Get('id/:id')
  @ApiOperation({ summary: 'Получение профиля по ID' })
  @ApiParam({ name: 'id', description: 'ID пользователя', example: 1 })
  @ApiResponse({ status: 200, description: 'Профиль найден' })
  @ApiResponse({ status: 404, description: 'Профиль не найден' })
  async getProfileById(@Param('id') id: number) {
    this.logger.log(`🔍 Запрос профиля по ID: ${id}`);

    const profile = await this.profileService.getProfileById(id);
    if (!profile) {
      this.logger.warn(`⚠️ Профиль с ID ${id} не найден`);
      throw new NotFoundException(`Профиль с ID ${id} не найден`);
    }

    this.logger.log(`✅ Профиль найден: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Обновление профиля
  @Put('id/:id')
  @ApiOperation({ summary: 'Обновление профиля пользователя' })
  @ApiParam({ name: 'id', description: 'ID пользователя', example: 1 })
  @ApiBody({
    description: 'Данные для обновления профиля',
    schema: {
      type: 'object',
      properties: {
        firstName: { type: 'string', example: 'Иван' },
        lastName: { type: 'string', example: 'Петров' },
        phone: { type: 'string', example: '+7900123456' },
        country: { type: 'string', example: 'Russia' }
      }
    }
  })
  @ApiResponse({ status: 200, description: 'Профиль обновлен' })
  @ApiResponse({ status: 404, description: 'Профиль не найден' })
  async updateProfileById(@Param('id') id: number, @Body() profileData: Partial<Profile>) {
    this.logger.log(`✏️ Запрос на обновление профиля ID: ${id}, данные: ${JSON.stringify({
      ...profileData,
      referralCode: profileData.referralCode || 'не задан',
      referrerCode: profileData.referrerCode || 'не задан'
    })}`);
    
    const updatedProfile = await this.profileService.updateProfileById(id, profileData);
    if (!updatedProfile) {
      this.logger.warn(`⚠️ Профиль с ID ${id} не найден для обновления`);
      throw new NotFoundException(`Профиль с ID ${id} не найден`);
    }
  
    this.logger.log(`✅ Профиль обновлён: ${JSON.stringify(updatedProfile)}`);
    return updatedProfile;
  }
  
  // Удалить профиль
  @Delete("delete/:id") // 🔥 Передаем ID в URL
  async deleteProfile(@Param("id") id: number) { // ✅ Берем ID из URL
    this.logger.log(`🗑 Запрос на удаление профиля с ID: ${id}`);

    if (!id) {
      throw new BadRequestException("ID профиля обязателен");
    }

    const result = await this.profileService.deleteProfile(Number(id));

    if (!result) {
      throw new NotFoundException(`Профиль с ID ${id} не найден`);
    }

    this.logger.log(`✅ Профиль удалён: ID ${id}`);
    return { message: "Профиль успешно удалён" };
  }


  // ✅ Получение всех пользователей для админки с пагинацией и поиском
@Get('admin/all')
async getAllUsersForAdmin(
  @Query('page') page: number = 1,
  @Query('limit') limit: number = 20,
  @Query('search') search?: string
) {
  this.logger.log(`📋 Запрос всех пользователей для админки: страница ${page}, лимит ${limit}, поиск: ${search || 'нет'}`);

  const result = await this.profileService.getAllUsersForAdmin(Number(page), Number(limit), search);
  
  this.logger.log(`✅ Найдено пользователей: ${result.total}`);
  return result;
}

// ✅ Получение статистики пользователя для админки
@Get('admin/statistics/:id')
async getUserStatistics(@Param('id') id: number) {
  this.logger.log(`📊 Запрос статистики пользователя ID: ${id}`);

  const statistics = await this.profileService.getUserStatistics(Number(id));
  
  this.logger.log(`✅ Статистика получена для пользователя ${id}`);
  return statistics;
}

// ✅ Расширенный поиск пользователей
@Get('admin/search')
async searchUsers(
  @Query('query') query?: string,
  @Query('isActive') isActive?: boolean,
  @Query('isVerified') isVerified?: boolean,
  @Query('country') country?: string,
  @Query('dateFrom') dateFrom?: string,
  @Query('dateTo') dateTo?: string,
  @Query('page') page: number = 1,
  @Query('limit') limit: number = 20
) {
  this.logger.log(`🔍 Расширенный поиск пользователей с фильтрами`);

  const searchParams = {
    query,
    isActive: isActive !== undefined ? Boolean(isActive) : undefined,
    isVerified: isVerified !== undefined ? Boolean(isVerified) : undefined,
    country,
    dateFrom,
    dateTo,
    page: Number(page),
    limit: Number(limit)
  };

  const result = await this.profileService.searchUsers(searchParams);
  
  this.logger.log(`✅ Найдено пользователей: ${result.total}`);
  return result;
}

// ✅ Блокировка/разблокировка пользователя
@Put('admin/toggle-status/:id')
async toggleUserStatus(@Param('id') id: number, @Body() body: { isActive: boolean }) {
  this.logger.log(`🔄 Изменение статуса пользователя ID: ${id} на ${body.isActive ? 'активный' : 'заблокированный'}`);

  const updatedUser = await this.profileService.toggleUserStatus(Number(id), body.isActive);
  
  if (!updatedUser) {
    this.logger.warn(`⚠️ Пользователь с ID ${id} не найден для изменения статуса`);
    throw new NotFoundException(`Пользователь с ID ${id} не найден`);
  }

  this.logger.log(`✅ Статус пользователя ${id} изменен на ${body.isActive ? 'активный' : 'заблокированный'}`);
  return updatedUser;
}

// ✅ Удалены методы сброса пароля и обновления баланса - они не нужны в профильном сервисе

// ✅ Получение общей статистики по всем пользователям
@Get('admin/general-statistics')
async getGeneralStatistics() {
  this.logger.log(`📈 Запрос общей статистики пользователей`);

  const statistics = await this.profileService.getGeneralStatistics();
  
  this.logger.log(`✅ Общая статистика получена`);
  return statistics;
}
}