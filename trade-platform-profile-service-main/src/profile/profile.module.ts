import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Profile } from './profile.entity';
import { ProfileService } from './profile.service';
import { ProfileController } from './profile.controller';
import { ClientsModule, Transport } from '@nestjs/microservices';

@Module({
  imports: [
    TypeOrmModule.forFeature([Profile]),

    // ✅ Добавляем Kafka-клиент для отправки сообщений
    ClientsModule.register([
      {
        name: 'KAFKA_SERVICE',
        transport: Transport.KAFKA,
        options: {
          client: {
            brokers: ['172.31.10.207:9093'],
          },
          consumer: {
            groupId: 'profile-service-consumer',
          },
        },
      },
    ]),
  ],
  controllers: [ProfileController],
  providers: [ProfileService],
  exports: [ProfileService], // ✅ Экспортируем, если надо использовать в других модулях
})
export class ProfileModule {}
