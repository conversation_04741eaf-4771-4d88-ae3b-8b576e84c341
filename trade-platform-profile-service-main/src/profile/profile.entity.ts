import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

@Entity()
export class Profile {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ unique: true, nullable: false }) // ❗ Email обязателен и уникальный
  email: string;

  @Column({ nullable: false }) // ❗ Имя обязательно
  firstName: string;

  @Column({ nullable: true }) // ❗ Имя обязательно
  username: string;

  @Column({ nullable: false }) // ❗ Фамилия обязательно
  lastName: string;

  @Column({ nullable: true }) // ✅ Телефон опционален
  phone?: string;

  @Column({ nullable: true }) // ✅ Реферальный код пользователя
  referralCode?: string;

  @Column({ nullable: true }) // ✅ Реферальный код пригласившего пользователя
  referrerCode?: string;

  @Column({ nullable: true }) // ✅ URL аватарки пользователя
  avatarUrl?: string;

  // ✅ Новые поля для админки
  @Column({ nullable: true }) // ✅ Страна пользователя
  country?: string;

  @Column({ nullable: true }) // ✅ IP адрес пользователя
  ipAddress?: string;

  @Column({ default: true }) // ✅ Активность пользователя (по умолчанию активен)
  isActive: boolean;

  @Column({ default: false }) // ✅ Верификация пользователя (по умолчанию не верифицирован)
  isVerified: boolean;

  @Column({ default: false }) // ✅ Двухфакторная аутентификация (по умолчанию отключена)
  twoFactorEnabled: boolean;

  @Column({ type: 'date', nullable: true }) // ✅ Дата рождения
  birthDate?: string;

  @Column({ type: 'timestamp', nullable: true }) // ✅ Последний вход в систему
  lastLoginAt?: Date;

  @CreateDateColumn() // ✅ Дата создания профиля (автоматически)
  createdAt: Date;

  @UpdateDateColumn() // ✅ Дата последнего обновления профиля (автоматически)
  updatedAt: Date;
}