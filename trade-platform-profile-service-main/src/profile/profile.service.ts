import { Injectable, Inject, InternalServerErrorException, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Between, ILike, MoreThan, Repository } from 'typeorm';
import { Profile } from './profile.entity';
import { Logger } from '@nestjs/common';
import { ClientKafka } from '@nestjs/microservices';
import { lastValueFrom } from 'rxjs';
import { firstValueFrom } from 'rxjs';


@Injectable()
export class ProfileService {
  private readonly logger = new Logger(ProfileService.name);

  constructor(
    @InjectRepository(Profile)
    private readonly profileRepository: Repository<Profile>,

    @Inject('KAFKA_SERVICE')
    private readonly kafkaClient: ClientKafka, // ✅ Добавляем Kafka

    
  ) {}

  async onModuleInit() {
    this.kafkaClient.subscribeToResponseOf('profile.getUserIdByEmail'); 
    this.kafkaClient.subscribeToResponseOf('auth.deleteUserByEmail'); 
    this.kafkaClient.subscribeToResponseOf('auth.updateToken'); 
    this.kafkaClient.subscribeToResponseOf('user.email.updated'); 
    this.kafkaClient.subscribeToResponseOf('auth.getUserByEmail'); 
    this.kafkaClient.subscribeToResponseOf('profile.check_user'); // ✅ Добавляем проверку пользователя
    this.kafkaClient.subscribeToResponseOf('profile.update_avatar'); // ✅ Добавляем обновление аватарки
    this.kafkaClient.subscribeToResponseOf('profile.email.response');
    await this.kafkaClient.connect(); // ✅ Подключаем клиента после подписки
  }

  // ✅ Получаем профиль по id (возвращает только нужные данные)
  async getProfileDataById(id: number) {
    this.logger.log(`🔍 Ищем профиль по ID: ${id}`);

    const profile = await this.profileRepository.findOne({
      where: { id },
      select: ['id', 'firstName', 'lastName', 'email', 'referralCode'], // ✅ Добавили реферальный код
    });

    if (!profile) {
      this.logger.warn(`⚠️ Профиль не найден для ID: ${id}`);
      return null;
    }

    // 🔥 Преобразуем в чистый JSON перед отправкой через Kafka
    const cleanProfile = JSON.parse(JSON.stringify(profile));

    this.logger.log(`✅ Найден профиль: ${cleanProfile.firstName} ${cleanProfile.lastName}`);
    return cleanProfile;
  }


 // Метод для получения только email пользователя по ID - сделаем по аналогии с getProfileDataById
async getUserEmailById(userId: number) {
  this.logger.log(`🔍 Поиск email пользователя по ID: ${userId}`);

  const profile = await this.profileRepository.findOne({
    where: { id: userId },
    select: ['id', 'email'],
  });

  if (!profile) {
    this.logger.warn(`⚠️ Профиль не найден для ID: ${userId}`);
    return null;
  }

  // Преобразуем в чистый JSON перед отправкой через Kafka (как в getProfileDataById)
  const cleanProfile = JSON.parse(JSON.stringify(profile));

  this.logger.log(`✅ Найден email для пользователя ${userId}: ${cleanProfile.email}`);
  return cleanProfile;
}

// Метод обработки для MessagePattern 'profile.email.request'
// В profile.service.ts
async handleEmailRequest(userId: number) {
  this.logger.log(`📩 Запрос email пользователя через Kafka по ID: ${userId}`);

  const profile = await this.getUserEmailById(userId);
  if (!profile) {
    this.logger.warn(`⚠️ Email не найден для пользователя ID: ${userId}`);
    return { 
      userId,
      success: false,
      error: 'Email не найден' 
    };
  }

  this.logger.log(`✅ Найден email: ${profile.email}`);
  
  return { 
    userId,
    success: true,
    email: profile.email
  };
}

  // ✅ Создание профиля + отправка `id` в Auth для обновления токена
  // ✅ Добавлена отправка данных в реферальный сервис
  async createProfile(profileData: { 
    email: string;
    username?: string;
    firstName: string; 
    lastName: string; 
    phone?: string;
    referralCode?: string;  // ✅ Добавляем реферальный код пользователя
    referrerCode?: string;  // ✅ Добавляем реферальный код пригласившего 
  }) {
    this.logger.log(`📩 Попытка создания профиля: ${JSON.stringify({
      ...profileData,
      referralCode: profileData.referralCode || 'не задан',
      referrerCode: profileData.referrerCode || 'не задан'
    })}`);

    try {
        // ✅ Запрос в `AuthService` для получения `id`
        const authUser = await firstValueFrom(
            this.kafkaClient.send<{ id: number; email: string }>(
                'auth.getUserByEmail',
                { email: profileData.email }
            )
        );

        if (!authUser || !authUser.id) {
            this.logger.error(`❌ Ошибка: Email ${profileData.email} не найден в AuthService!`);
            throw new Error("Ошибка: Email не найден в AuthService");
        }

        // ✅ Создаём профиль с реферальными кодами
        const newProfile = this.profileRepository.create({ 
          ...profileData, 
          id: authUser.id,
          referralCode: profileData.referralCode,
          referrerCode: profileData.referrerCode
        });
        
        const savedProfile = await this.profileRepository.save(newProfile);

        this.logger.log(`✅ Профиль создан: ${JSON.stringify({
          ...savedProfile,
          referralCode: savedProfile.referralCode || 'не задан',
          referrerCode: savedProfile.referrerCode || 'не задан'
        })}`);

        // 🔥 **Отправляем `id` в `AuthService`, чтобы обновить токен**
        const updatedToken = await firstValueFrom(
            this.kafkaClient.send<{ accessToken: string }>(
                'auth.updateToken',
                { id: savedProfile.id, email: savedProfile.email }
            )
        );

        if (!updatedToken || !updatedToken.accessToken) {
            this.logger.error(`❌ Ошибка: не удалось обновить токен для ${savedProfile.email}`);
            throw new Error("Ошибка обновления токена");
        }

        // 🆕 Отправляем данные в реферальный сервис
        try {
            this.logger.log(`📤 Отправка данных в реферальный сервис для userId: ${savedProfile.id}`);
            
            // Формируем объект для отправки в реферальный сервис
            const referalData = {
                userId: savedProfile.id,
                email: savedProfile.email,
                username: savedProfile.username,
                firstName: savedProfile.firstName,
                lastName: savedProfile.lastName,
                referralCode: savedProfile.referralCode,
                referrerCode: savedProfile.referrerCode
            };
            
            // Отправляем данные в реферальный сервис через Kafka
            await this.kafkaClient.emit('profile.created', referalData);
            
            this.logger.log(`✅ Данные успешно отправлены в реферальный сервис`);
        } catch (error) {
            // Логируем ошибку, но не прерываем процесс создания профиля
            this.logger.error(`⚠️ Ошибка при отправке данных в реферальный сервис: ${error.message}`);
        }

        this.logger.log(`✅ Новый токен получен: ${updatedToken.accessToken}`);
        return { profile: savedProfile, accessToken: updatedToken.accessToken };
    } catch (err) {
        this.logger.error(`❌ Ошибка Kafka: ${err.message}`);
        throw new Error("Ошибка запроса к AuthService");
    }
  }

  // ✅ Получение профиля по email
  async getProfileByEmail(email: string): Promise<Profile | null> {
    this.logger.log(`🔎 Поиск профиля по email: ${email}`);
    
    const profile = await this.profileRepository.findOne({ where: { email } });

    if (!profile) {
      this.logger.warn(`⚠️ Профиль с email ${email} не найден`);
      return null;
    }

    this.logger.log(`✅ Найден профиль: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Получение профиля по ID
  async getProfileById(id: number): Promise<Profile | null> {
    this.logger.log(`🔎 Поиск профиля по ID: ${id}`);

    const profile = await this.profileRepository.findOne({ where: { id } });

    if (!profile) {
      this.logger.warn(`⚠️ Профиль с ID ${id} не найден`);
      return null;
    }

    this.logger.log(`✅ Найден профиль: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Получение профиля по реферальному коду
  async getProfileByReferralCode(referralCode: string): Promise<Profile | null> {
    this.logger.log(`🔎 Поиск профиля по реферальному коду: ${referralCode}`);
    
    const profile = await this.profileRepository.findOne({ where: { referralCode } });

    if (!profile) {
      this.logger.warn(`⚠️ Профиль с реферальным кодом ${referralCode} не найден`);
      return null;
    }

    this.logger.log(`✅ Найден профиль: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Обновление профиля по ID + НЕ ТРОГАЕМ КАФКУ!
  async updateProfileById(id: number, profileData: Partial<Pick<Profile, 'firstName' | 'lastName' | 'phone' | 'email' | 'referralCode' | 'referrerCode'>>): Promise<Profile | null> {
    this.logger.log(`🔄 Обновление профиля ID: ${id}`);

    const profile = await this.profileRepository.findOne({ where: { id } });
    if (!profile) {
      this.logger.warn(`⚠️ Профиль с ID ${id} не найден`);
      return null;
    }

    // **Проверяем, изменился ли email**
    if (profileData.email && profileData.email !== profile.email) {
      this.logger.log(`📢 Email изменён: ${profile.email} → ${profileData.email}`);

      try {
        // ✅ Проверяем, подключен ли Kafka-клиент
        await this.kafkaClient.connect();

        // ✅ Отправляем событие в Kafka
        await lastValueFrom(this.kafkaClient.emit('user.email.updated', {
          oldEmail: profile.email,
          newEmail: profileData.email,
        }));

        this.logger.log(`📡 Kafka: успешно отправлено событие о смене email`);
      } catch (error) {
        this.logger.error(`⛔ Ошибка при отправке события в Kafka: ${error.message}`);
        throw new InternalServerErrorException('Ошибка при обновлении email, попробуйте позже');
      }
    }

    // ✅ Обновляем профиль (без лишних проверок)
    Object.assign(profile, profileData);
    await this.profileRepository.save(profile);

    this.logger.log(`✅ Профиль обновлён: ${JSON.stringify(profile)}`);
    return profile;
  }

  // ✅ Удаление профиля по ID с отправкой события в Auth-сервис
  async deleteProfile(id: number): Promise<boolean> {
    this.logger.log(`🗑 Удаляем профиль и связанные данные: ID ${id}`);

    // 🔍 Ищем профиль
    const profile = await this.profileRepository.findOne({ where: { id } });
    if (!profile) {
      this.logger.warn(`⚠️ Профиль с ID ${id} не найден`);
      throw new NotFoundException(`Профиль с ID ${id} не найден`);
    }

    const email = profile.email; // ✅ Берем email пользователя для удаления из Auth-сервиса

    // 🗑 Удаляем профиль
    await this.profileRepository.delete(id);
    this.logger.log(`✅ Профиль успешно удалён: ID ${id}, Email: ${email}`);

    try {
      // 📡 Отправляем запрос в Auth-сервис через Kafka
      this.logger.log(`📡 Отправка запроса в Auth-сервис на удаление пользователя с Email: ${email}`);

      const authResponse = await lastValueFrom(
        this.kafkaClient.send('auth.deleteUserByEmail', { email })
      );

      if (authResponse.success) {
        this.logger.log(`✅ Пользователь в Auth-сервисе успешно удалён: Email: ${email}`);
        return true;
      } else {
        this.logger.error(`⛔ Ошибка удаления пользователя в Auth-сервисе: ${authResponse.message}`);
        throw new InternalServerErrorException('Ошибка при удалении пользователя в Auth-сервисе');
      }
    } catch (error) {
      this.logger.error(`⛔ Ошибка при отправке запроса в Kafka: ${error.message}`);
      throw new InternalServerErrorException('Ошибка удаления пользователя, попробуйте позже');
    }
  }



  // ✅ Метод для обновления аватарки пользователя
async updateUserAvatar(userId: number, avatarUrl: string): Promise<boolean> {
  this.logger.log(`🔄 Обновление аватарки для пользователя ID: ${userId}`);

  try {
    const profile = await this.profileRepository.findOne({ where: { id: userId } });
    
    if (!profile) {
      this.logger.warn(`⚠️ Пользователь с ID ${userId} не найден для обновления аватарки`);
      return false;
    }

    // Обновляем URL аватарки
    profile.avatarUrl = avatarUrl;
    await this.profileRepository.save(profile);
    
    this.logger.log(`✅ Аватарка пользователя ${userId} обновлена: ${avatarUrl}`);
    return true;
  } catch (error) {
    this.logger.error(`❌ Ошибка при обновлении аватарки: ${error.message}`);
    throw new Error(`Ошибка при обновлении аватарки: ${error.message}`);
  }
}


// ✅ Получение всех пользователей для админки с пагинацией и поиском
async getAllUsersForAdmin(page: number = 1, limit: number = 20, search?: string) {
  this.logger.log(`📋 Получение пользователей для админки: страница ${page}, лимит ${limit}`);

  const skip = (page - 1) * limit;
  let whereCondition = {};

  // Если есть поисковый запрос, ищем по email или ID
  if (search) {
    if (isNaN(Number(search))) {
      // Поиск по email
      whereCondition = { email: ILike(`%${search}%`) };
    } else {
      // Поиск по ID
      whereCondition = { id: Number(search) };
    }
  }

  const [users, total] = await this.profileRepository.findAndCount({
    where: whereCondition,
    skip,
    take: limit,
    order: { id: 'DESC' }
  });

  const pages = Math.ceil(total / limit);

  return {
    items: users,
    total,
    page,
    limit,
    pages
  };
}

// ✅ Получение статистики пользователя
async getUserStatistics(userId: number) {
  this.logger.log(`📊 Получение статистики для пользователя ${userId}`);

  try {
    // Получаем данные из разных сервисов через Kafka
    // Здесь будут запросы к торговому сервису, балансовому сервису и т.д.
    
    // Пока возвращаем базовую статистику
    return {
      totalTrades: 0,
      totalVolume: 0,
      pnl: 0,
      totalDeposits: 0,
      totalWithdrawals: 0,
      referralsCount: 0
    };
  } catch (error) {
    this.logger.error(`❌ Ошибка при получении статистики пользователя ${userId}: ${error.message}`);
    throw error;
  }
}

// ✅ Расширенный поиск пользователей
async searchUsers(searchParams: {
  query?: string;
  isActive?: boolean;
  isVerified?: boolean;
  country?: string;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
}) {
  this.logger.log(`🔍 Расширенный поиск пользователей`);

  const { query, isActive, isVerified, country, dateFrom, dateTo, page = 1, limit = 20 } = searchParams;
  const skip = (page - 1) * limit;

  let whereConditions: any = {};

  // Поиск по тексту (email или имя)
  if (query) {
    whereConditions = [
      { email: ILike(`%${query}%`) },
      { firstName: ILike(`%${query}%`) },
      { lastName: ILike(`%${query}%`) }
    ];
  }

  // Фильтр по стране
  if (country) {
    whereConditions.country = country;
  }

  // Фильтр по дате регистрации
  if (dateFrom && dateTo) {
    whereConditions.createdAt = Between(new Date(dateFrom), new Date(dateTo));
  } else if (dateFrom) {
    whereConditions.createdAt = MoreThan(new Date(dateFrom));
  }

  const [users, total] = await this.profileRepository.findAndCount({
    where: whereConditions,
    skip,
    take: limit,
    order: { id: 'DESC' }
  });

  const pages = Math.ceil(total / limit);

  return {
    items: users,
    total,
    page,
    limit,
    pages
  };
}

// ✅ Блокировка/разблокировка пользователя
async toggleUserStatus(userId: number, isActive: boolean): Promise<Profile | null> {
  this.logger.log(`🔄 Изменение статуса пользователя ${userId} на ${isActive ? 'активный' : 'заблокированный'}`);

  const user = await this.profileRepository.findOne({ where: { id: userId } });
  
  if (!user) {
    this.logger.warn(`⚠️ Пользователь с ID ${userId} не найден`);
    return null;
  }

  // Обновляем статус (добавьте поле isActive в entity если его нет)
  await this.profileRepository.update(userId, { isActive } as any);
  
  const updatedUser = await this.profileRepository.findOne({ where: { id: userId } });
  
  this.logger.log(`✅ Статус пользователя ${userId} изменен`);
  return updatedUser;
}

// ✅ Удалены методы сброса пароля и обновления баланса - они не относятся к профильному сервису

// ✅ Получение общей статистики по всем пользователям
async getGeneralStatistics() {
  this.logger.log(`📈 Получение общей статистики пользователей`);

  try {
    const totalUsers = await this.profileRepository.count();
    
    // Активные пользователи
    const activeUsers = await this.profileRepository.count({ where: { isActive: true } });
    
    // Верифицированные пользователи  
    const verifiedUsers = await this.profileRepository.count({ where: { isVerified: true } });

    // Заблокированные пользователи
    const blockedUsers = await this.profileRepository.count({ where: { isActive: false } });

    // Новые пользователи за сегодня
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const newUsersToday = await this.profileRepository.count({
      where: { createdAt: MoreThan(today) }
    });

    // Новые пользователи за неделю
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    const newUsersThisWeek = await this.profileRepository.count({
      where: { createdAt: MoreThan(weekAgo) }
    });

    // Новые пользователи за месяц
    const monthAgo = new Date();
    monthAgo.setMonth(monthAgo.getMonth() - 1);
    const newUsersThisMonth = await this.profileRepository.count({
      where: { createdAt: MoreThan(monthAgo) }
    });

    return {
      totalUsers,
      activeUsers,
      verifiedUsers,
      blockedUsers,
      totalBalance: 0, // Баланс получается из Balance-сервиса отдельно
      newUsersToday,
      newUsersThisWeek,
      newUsersThisMonth
    };
  } catch (error) {
    this.logger.error(`❌ Ошибка при получении общей статистики: ${error.message}`);
    throw error;
  }
}
}