version: '3.8'

services:
  profile-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8002:8002"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=********************************************************/profile_service
    depends_on:
      - postgres
      - kafka
    networks:
      - bitmei-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: profile_service
      POSTGRES_USER: profile_user
      POSTGRES_PASSWORD: profile_password
    ports:
      - "5433:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bitmei-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9093:9092"
    depends_on:
      - zookeeper
    networks:
      - bitmei-network
    restart: unless-stopped

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2182:2181"
    networks:
      - bitmei-network
    restart: unless-stopped

volumes:
  postgres_data:

networks:
  bitmei-network:
    driver: bridge