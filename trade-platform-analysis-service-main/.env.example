# Server settings
HOST=0.0.0.0
PORT=8007
DEBUG=True

# Настройки CORS
CORS_ORIGINS=["https://app.bitmei.com","https://dev.bitmei.com","https://bitmei.com","http://localhost:3000","http://localhost:3001","http://localhost:3012","http://localhost:8000","http://localhost:8001","http://localhost:8002","http://localhost:8003","http://localhost:8004","http://localhost:8005","http://localhost:8006","http://localhost:8007","http://localhost:8008","http://localhost:8009","http://localhost:8010","http://localhost:8080","https://analysis-api.eu.ngrok.io","https://auth-api.eu.ngrok.io","https://profile-api.eu.ngrok.io","https://wallet-api.eu.ngrok.io","https://balance-api.eu.ngrok.io","https://balance-ws.eu.ngrok.io","https://trading-api.eu.ngrok.io","https://withdraw-api.eu.ngrok.io","https://referal-api.eu.ngrok.io","https://support-api.eu.ngrok.io","https://media-api.eu.ngrok.io"]
CORS_ALLOW_CREDENTIALS=True

# Настройки базы данных
DATABASE_URL=postgresql://postgres:<EMAIL>/trade_platform_db
DATABASE_POOL_SIZE=20
DATABASE_MAX_OVERFLOW=10

# Настройки Kafka
KAFKA_BOOTSTRAP_SERVERS=*************:9093
KAFKA_TRADE_TOPIC=trade-orders
KAFKA_TRANSACTION_TOPIC=transaction-events
KAFKA_GROUP_ID=analysis-service-group

# Logging
LOG_LEVEL=INFO
