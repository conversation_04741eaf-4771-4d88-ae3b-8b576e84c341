name: Deploy to AWS

on:
  push:
    branches: [ main ]

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Set up SSH key
      run: |
        mkdir -p ~/.ssh
        echo "${{ secrets.AWS_SSH_PRIVATE_KEY }}" > ~/.ssh/aws-deploy-key.pem
        chmod 600 ~/.ssh/aws-deploy-key.pem
        echo "${{ secrets.AWS_HOST }} ssh-rsa ${{ secrets.AWS_HOST_KEY }}" >> ~/.ssh/known_hosts
    
    - name: Deploy to AWS
      env:
        AWS_HOST: ${{ secrets.AWS_HOST }}
      run: |
        ssh -i ~/.ssh/aws-deploy-key.pem -o StrictHostKeyChecking=no ubuntu@$AWS_HOST '
          cd ~/trade-platform-analysis-service &&
          git fetch origin main &&
          git reset --hard origin/main &&
          # Update .env from .env.example if it exists
          if [ -f .env.example ] && [ ! -f .env ]; then
            cp .env.example .env
          fi
          source venv/bin/activate &&
          pip install -r requirements.txt &&
          pkill -f "uvicorn app.main:app" || true &&
          sleep 2 &&
          nohup python -m uvicorn app.main:app --host 0.0.0.0 --port 8007 > uvicorn.log 2>&1 &
          echo "Analysis service deployed and restarted"
        '