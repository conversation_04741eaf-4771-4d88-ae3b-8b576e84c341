# CI/CD Setup for Analysis Service

## GitHub Secrets Required

Add the following secrets to your GitHub repository settings:

1. **AWS_SSH_PRIVATE_KEY**: The SSH private key for accessing the AWS instance
   - This should be the content of your `vova-ngrok-proxy-ssh.pem` file

2. **AWS_HOST**: `**************`
   - The IP address of the AWS instance

3. **AWS_HOST_KEY**: The SSH host key for the AWS instance
   - Get this by running: `ssh-keyscan -t rsa ************** | cut -d' ' -f3`

## How to Add Secrets

1. Go to your repository on GitHub
2. Click on Settings → Secrets and variables → Actions
3. Click "New repository secret"
4. Add each secret with the names and values specified above

## Deployment Process

When you push to the `main` branch:

1. GitHub Actions will automatically trigger the deployment
2. The workflow will SSH into the AWS instance
3. It will pull the latest code from the repository
4. Install any new dependencies
5. Restart the service with the updated code

## Service Details

- **Port**: 8007
- **Process Manager**: Manual with nohup (consider setting up supervisor)
- **Logs**: Available in `~/trade-platform-analysis-service/uvicorn.log`

## Testing the Deployment

After deployment, verify the service is running:

```bash
curl https://analysis-api.eu.ngrok.io/docs
```

The CORS configuration has been updated to allow:
- https://app.bitmei.com
- https://dev.bitmei.com
- https://bitmei.com
- All localhost ports
- All ngrok tunnel domains