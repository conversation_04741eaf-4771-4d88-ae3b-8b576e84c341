import json
import time
from fastapi import WebSocket, WebSocketDisconnect
from loguru import logger
from typing import Dict, List, Set
import asyncio

from app.core.models import (
    WebSocketMessage, 
    WebSocketResponse, 
    TradeCalculationRequest, 
    TradeExecutionRequest
)
from app.core.calculator import LeverageCalculator
from app.services.kafka_producer import kafka_service

class ConnectionManager:
    """Менеджер WebSocket соединений"""
    
    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.user_ids: Set[str] = set()
    
    async def connect(self, websocket: WebSocket, client_id: str):
        """Установить соединение"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client connected: {client_id}")
    
    def disconnect(self, client_id: str):
        """Отключить соединение"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client disconnected: {client_id}")
    
    async def send_response(self, client_id: str, response: WebSocketResponse):
        """Отправить ответ клиенту"""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_json(response.model_dump())
            except Exception as e:
                logger.error(f"Error sending message to client {client_id}: {str(e)}")
    
    async def broadcast(self, message: dict):
        """Широковещательная рассылка всем подключенным клиентам"""
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_json(message)
            except Exception:
                disconnected_clients.append(client_id)
        
        # Удаляем отключившихся клиентов
        for client_id in disconnected_clients:
            self.disconnect(client_id)

# Создаем менеджер соединений
connection_manager = ConnectionManager()

async def handle_websocket(websocket: WebSocket, client_id: str):
    """
    Обработчик WebSocket соединений.
    
    Args:
        websocket: WebSocket соединение
        client_id: Идентификатор клиента
    """
    await connection_manager.connect(websocket, client_id)
    
    try:
        while True:
            # Ждем сообщение от клиента
            data = await websocket.receive_text()
            
            try:
                # Парсим сообщение
                message_dict = json.loads(data)
                message = WebSocketMessage(**message_dict)
                
                # Обрабатываем различные типы действий
                if message.action == "calculate":
                    await handle_calculate(client_id, message)
                elif message.action == "execute":
                    await handle_execute(client_id, message)
                else:
                    await connection_manager.send_response(
                        client_id,
                        WebSocketResponse(
                            status="error",
                            action=message.action,
                            error=f"Unknown action: {message.action}"
                        )
                    )
                    
            except Exception as e:
                logger.error(f"Error processing message from client {client_id}: {str(e)}")
                await connection_manager.send_response(
                    client_id,
                    WebSocketResponse(
                        status="error",
                        action="unknown",
                        error=f"Error processing message: {str(e)}"
                    )
                )
                
    except WebSocketDisconnect:
        connection_manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"Unexpected error for client {client_id}: {str(e)}")
        connection_manager.disconnect(client_id)

async def handle_calculate(client_id: str, message: WebSocketMessage):
    """
    Обработчик запроса на расчет параметров сделки.
    
    Args:
        client_id: Идентификатор клиента
        message: Сообщение с запросом на расчет
    """
    try:
        # Создаем запрос на расчет из данных сообщения
        calculation_request = TradeCalculationRequest(**message.data)
        
        # Выполняем расчет
        calculation_result = LeverageCalculator.calculate_leverage_trading(calculation_request)
        
        # Отправляем результат клиенту
        await connection_manager.send_response(
            client_id,
            WebSocketResponse(
                status="success",
                action="calculate",
                data=calculation_result.model_dump()
            )
        )
        
    except Exception as e:
        logger.error(f"Error calculating trade for client {client_id}: {str(e)}")
        await connection_manager.send_response(
            client_id,
            WebSocketResponse(
                status="error",
                action="calculate",
                error=f"Error calculating trade: {str(e)}"
            )
        )

async def handle_execute(client_id: str, message: WebSocketMessage):
    """
    Обработчик запроса на исполнение сделки.
    
    Args:
        client_id: Идентификатор клиента
        message: Сообщение с запросом на исполнение
    """
    try:
        # Получаем данные для исполнения сделки
        execution_data = message.data
        user_id = execution_data.get("user_id")
        
        if not user_id:
            raise ValueError("Missing user_id in execution request")
            
        # Создаем запрос на исполнение сделки
        calculation_request = TradeCalculationRequest(**execution_data.get("calculation", {}))
        calculation_result = LeverageCalculator.calculate_leverage_trading(calculation_request)
        
        # Перепроверяем результаты для безопасности
        execution_request = TradeExecutionRequest(
            user_id=user_id,
            calculation=calculation_request,
            results=calculation_result,
            execution_timestamp=int(time.time())
        )
        
        # Отправляем запрос в Kafka
        success = kafka_service.send_trade_execution(execution_request)
        
        if success:
            # Отправляем успешный ответ клиенту
            await connection_manager.send_response(
                client_id,
                WebSocketResponse(
                    status="success",
                    action="execute",
                    data={"message": "Trade execution request sent to Trade Service"}
                )
            )
        else:
            # Отправляем сообщение об ошибке
            await connection_manager.send_response(
                client_id,
                WebSocketResponse(
                    status="error",
                    action="execute",
                    error="Failed to send trade execution request to Trade Service"
                )
            )
            
    except Exception as e:
        logger.error(f"Error executing trade for client {client_id}: {str(e)}")
        await connection_manager.send_response(
            client_id,
            WebSocketResponse(
                status="error",
                action="execute",
                error=f"Error executing trade: {str(e)}"
            )
        )