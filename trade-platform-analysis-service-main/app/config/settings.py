import os
import json
from typing import List
from pydantic import Field, field_validator
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

# Загрузка переменных окружения из .env файла
load_dotenv()

class Settings(BaseSettings):
    # Server settings
    HOST: str = Field(default=os.getenv("HOST", "0.0.0.0"))
    PORT: int = Field(default=int(os.getenv("PORT", "8007")))
    DEBUG: bool = Field(default=(os.getenv("DEBUG", "False").lower() in ('true', '1', 't')))
    
    # CORS settings
    CORS_ORIGINS: List[str] = Field(default=[
      "https://app.bitmei.com",
      "https://dev.bitmei.com",
      "https://bitmei.com",
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:3012",
      "https://auth-api.eu.ngrok.io",
      "https://profile-api.eu.ngrok.io",
      "https://wallet-api.eu.ngrok.io",
      "https://balance-api.eu.ngrok.io",
      "https://balance-ws.eu.ngrok.io",
      "https://analysis-api.eu.ngrok.io",
      "https://analysis-ws.eu.ngrok.io",
      "https://trading-api.eu.ngrok.io",
      "https://trading-ws.eu.ngrok.io",
      "https://withdraw-api.eu.ngrok.io",
      "https://referal-api.eu.ngrok.io",
      "https://support-api.eu.ngrok.io",
      "https://media-api.eu.ngrok.io",
      "https://trading-chart-ws.eu.ngrok.io"
  ])
    CORS_ALLOW_CREDENTIALS: bool = Field(default=True)

    
    # Database settings
    DATABASE_URL: str = Field(
        default=os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost/trade_platform_db")
    )
    DATABASE_POOL_SIZE: int = Field(default=int(os.getenv("DATABASE_POOL_SIZE", "20")))
    DATABASE_MAX_OVERFLOW: int = Field(default=int(os.getenv("DATABASE_MAX_OVERFLOW", "10")))
    
    # Kafka settings
    KAFKA_BOOTSTRAP_SERVERS: str = Field(default=os.getenv("KAFKA_BOOTSTRAP_SERVERS", "*************:9093"))
    KAFKA_TRADE_TOPIC: str = Field(default=os.getenv("KAFKA_TRADE_TOPIC", "trade-orders"))
    KAFKA_TRANSACTION_TOPIC: str = Field(default=os.getenv("KAFKA_TRANSACTION_TOPIC", "transaction-events"))
    KAFKA_GROUP_ID: str = Field(default=os.getenv("KAFKA_GROUP_ID", "analysis-service-group"))
    
    
    # Logging
    LOG_LEVEL: str = Field(default=os.getenv("LOG_LEVEL", "INFO").upper())
    
    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def assemble_cors_origins(cls, v):
        if isinstance(v, str):
            try:
                # Пытаемся распарсить как JSON
                return json.loads(v)
            except json.JSONDecodeError:
                # Если не получается, то просто разбиваем строку по запятой
                return [origin.strip() for origin in v.split(",")]
        return v

    class Config:
        env_file = ".env"

# Создаем экземпляр настроек
settings = Settings()
