from app.core.models import TradeCalculationRequest, TradeCalculationResponse
from loguru import logger

class LeverageCalculator:
    """
    Калькулятор для расчета параметров маржинальной торговли.
    Обновлен с правильной формулой комиссий согласно таблице.
    """
    
    @staticmethod
    def get_fee_percentage_by_leverage(leverage: float) -> float:
        """
        Возвращает процент комиссии в зависимости от плеча согласно таблице.
        
        Формула: Fee = Trading Fee% * Leverage * 2
        Где Trading Fee% = 0.06% (базовая торговая комиссия)
        """
        # Таблица комиссий согласно предоставленным данным
        fee_table = {
            1: 0.12,
            3: 0.36,
            5: 0.60,
            10: 1.20,
            12: 1.80,
            20: 2.40,
            25: 3.00,
            30: 3.60,
            40: 4.80,
            50: 6.00,
            75: 9.00,
            100: 12.00
        }
        
        # Если точное значение есть в таблице
        if leverage in fee_table:
            return fee_table[leverage]
        
        # Если нет точного значения, используем формулу: 0.06% * leverage * 2
        base_trading_fee = 0.06  # 0.06% базовая торговая комиссия
        calculated_fee = base_trading_fee * leverage * 2
        
        # Ограничиваем максимум до 12% (как в таблице для x100)
        return min(calculated_fee, 12.0)
    
    @staticmethod
    def calculate_leverage_trading(request: TradeCalculationRequest) -> TradeCalculationResponse:
        """
        Рассчитывает метрики маржинальной торговли для заданных параметров.
        
        Args:
            request: TradeCalculationRequest с параметрами для расчета
            
        Returns:
            TradeCalculationResponse с результатами расчетов
        """
        try:
            # Извлекаем параметры из запроса
            entry_price = request.entry_price
            current_price = request.current_price
            order_type = request.order_type
            leverage = request.leverage
            margin = request.margin
            stop_loss = request.stop_loss
            take_profit = request.take_profit
            
            # Получаем процент комиссии согласно таблице
            adjusted_fee_percentage = LeverageCalculator.get_fee_percentage_by_leverage(leverage)
            
            # Рассчитываем размер позиции (notional value)
            position_size = margin * leverage
            
            # Рассчитываем требуемую маржу
            required_margin = position_size / leverage
            
            # Рассчитываем заемные средства
            borrowed_funds = position_size - margin
            
            # Рассчитываем комиссию (уже включает открытие и закрытие позиции)
            fee = margin * (adjusted_fee_percentage / 100)
            
            # Рассчитываем цену ликвидации
            if order_type.lower() == 'long':
                liquidation_price = entry_price * (1 - 1 / leverage)
            else:  # Short position
                liquidation_price = entry_price * (1 + 1 / leverage)
            
            # Рассчитываем Profit & Loss (PnL)
            if order_type.lower() == 'long':
                pnl = position_size * (current_price - entry_price) / entry_price
            else:  # Short position
                pnl = position_size * (entry_price - current_price) / entry_price
            
            # Рассчитываем эффективную маржу после PnL
            effective_margin = margin + pnl
            
            # Проверяем, будет ли позиция ликвидирована
            is_liquidated = False
            if order_type.lower() == 'long' and current_price <= liquidation_price:
                is_liquidated = True
            elif order_type.lower() == 'short' and current_price >= liquidation_price:
                is_liquidated = True
            
            # Рассчитываем расстояние до ликвидации
            if order_type.lower() == 'long':
                distance_to_liquidation_price = current_price - liquidation_price
                distance_to_liquidation_percent = (distance_to_liquidation_price / current_price) * 100
            else:  # Short position
                distance_to_liquidation_price = liquidation_price - current_price
                distance_to_liquidation_percent = (distance_to_liquidation_price / current_price) * 100
            
            # Рассчитываем результаты для Stop Loss, если указан
            sl_results = None
            if stop_loss is not None:
                if order_type.lower() == 'long':
                    sl_pnl = position_size * (stop_loss - entry_price) / entry_price
                else:  # Short position
                    sl_pnl = position_size * (entry_price - stop_loss) / entry_price
                
                sl_return_percentage = (sl_pnl / margin) * 100
                sl_results = {
                    'pnl': sl_pnl,
                    'return_percentage': sl_return_percentage
                }
            
            # Рассчитываем результаты для Take Profit, если указан
            tp_results = None
            if take_profit is not None:
                if order_type.lower() == 'long':
                    tp_pnl = position_size * (take_profit - entry_price) / entry_price
                else:  # Short position
                    tp_pnl = position_size * (entry_price - take_profit) / entry_price
                
                tp_return_percentage = (tp_pnl / margin) * 100
                tp_results = {
                    'pnl': tp_pnl,
                    'return_percentage': tp_return_percentage
                }
            
            # Создаем объект ответа
            result = TradeCalculationResponse(
                position_size=position_size,
                required_margin=required_margin,
                borrowed_funds=borrowed_funds,
                fee=fee,
                fee_percentage=adjusted_fee_percentage,
                liquidation_price=liquidation_price,
                pnl=pnl,
                effective_margin=effective_margin,
                is_liquidated=is_liquidated,
                return_percentage=(pnl / margin) * 100,
                distance_to_liquidation_price=distance_to_liquidation_price,
                distance_to_liquidation_percent=distance_to_liquidation_percent,
                stop_loss_results=sl_results,
                take_profit_results=tp_results
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in leverage calculation: {str(e)}")
            raise ValueError(f"Ошибка в расчетах: {str(e)}")