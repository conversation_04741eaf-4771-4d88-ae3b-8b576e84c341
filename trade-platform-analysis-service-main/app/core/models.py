from pydantic import BaseModel, Field
from typing import Optional, Literal, Dict, Union, List

class TradeCalculationRequest(BaseModel):
    """Модель запроса для расчета параметров сделки"""
    entry_price: float = Field(..., description="Цена входа в сделку")
    current_price: float = Field(..., description="Текущая цена актива")
    order_type: Literal["Long", "Short"] = Field(..., description="Тип ордера (Long или Short)")
    fee_percentage: float = Field(..., description="Процент комиссии")
    leverage: float = Field(..., description="Кредитное плечо")
    margin: float = Field(..., description="Начальная маржа")
    stop_loss: Optional[float] = Field(None, description="Цена Stop Loss (опционально)")
    take_profit: Optional[float] = Field(None, description="Цена Take Profit (опционально)")
    
class StopLossResult(BaseModel):
    """Результаты для цены Stop Loss"""
    pnl: float = Field(..., description="Прибыль/убыток в абсолютных значениях")
    return_percentage: float = Field(..., description="Процент возврата инвестиций")

class TakeProfitResult(BaseModel):
    """Результаты для цены Take Profit"""
    pnl: float = Field(..., description="Прибыль/убыток в абсолютных значениях")
    return_percentage: float = Field(..., description="Процент возврата инвестиций")
    
class TradeCalculationResponse(BaseModel):
    """Модель ответа с результатами расчета сделки"""
    position_size: float = Field(..., description="Размер позиции (нотациональная стоимость)")
    required_margin: float = Field(..., description="Требуемая маржа")
    borrowed_funds: float = Field(..., description="Заемные средства")
    fee: float = Field(..., description="Комиссия за сделку")
    fee_percentage: Optional[float] = None  # Добавлено это поле
    liquidation_price: float = Field(..., description="Цена ликвидации")
    pnl: float = Field(..., description="Текущая прибыль/убыток")
    effective_margin: float = Field(..., description="Эффективная маржа")
    is_liquidated: bool = Field(..., description="Позиция ликвидирована?")
    return_percentage: float = Field(..., description="Процент возврата инвестиций")
    distance_to_liquidation_price: float = Field(..., description="Расстояние до цены ликвидации в абсолютных значениях")
    distance_to_liquidation_percent: float = Field(..., description="Расстояние до цены ликвидации в процентах")
    stop_loss_results: Optional[StopLossResult] = Field(None, description="Результаты для Stop Loss (если указан)")
    take_profit_results: Optional[TakeProfitResult] = Field(None, description="Результаты для Take Profit (если указан)")

class TradeExecutionRequest(BaseModel):
    """Модель запроса на исполнение сделки (для отправки в Trade Service)"""
    user_id: str = Field(..., description="ID пользователя")
    calculation: TradeCalculationRequest = Field(..., description="Параметры расчета")
    results: TradeCalculationResponse = Field(..., description="Результаты расчета")
    execution_timestamp: int = Field(..., description="Timestamp выполнения")
    
class WebSocketMessage(BaseModel):
    """Базовая модель для WebSocket сообщений"""
    action: str = Field(..., description="Тип действия (calculate, execute, etc.)")
    data: Union[Dict, List, str] = Field(..., description="Данные запроса")
    
class WebSocketResponse(BaseModel):
    """Базовая модель для ответов через WebSocket"""
    status: str = Field(..., description="Статус ответа (success, error)")
    action: str = Field(..., description="Исходное действие")
    data: Union[Dict, List, str, None] = Field(None, description="Данные ответа") 
    error: Optional[str] = Field(None, description="Сообщение об ошибке (если status=error)")