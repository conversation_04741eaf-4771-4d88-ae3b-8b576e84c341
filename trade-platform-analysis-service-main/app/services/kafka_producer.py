import json
from kafka import KafkaProducer
from loguru import logger
from app.config.settings import settings
from app.core.models import TradeExecutionRequest

class KafkaService:
    """
    Сервис для работы с Kafka.
    Отвечает за отправку сообщений в Kafka для Trade Service.
    """
    
    def __init__(self):
        """Инициализация Kafka Producer."""
        try:
            self.producer = KafkaProducer(
                bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                value_serializer=lambda v: json.dumps(v).encode('utf-8'),
                key_serializer=lambda k: k.encode('utf-8') if isinstance(k, str) else str(k).encode('utf-8')
            )
            logger.info(f"Kafka producer initialized with bootstrap servers: {settings.KAFKA_BOOTSTRAP_SERVERS}")
        except Exception as e:
            logger.error(f"Failed to initialize Kafka producer: {str(e)}")
            self.producer = None
    
    def send_trade_execution(self, trade_request: TradeExecutionRequest) -> bool:
        """
        Отправляет запрос на исполнение сделки в Kafka.
        
        Args:
            trade_request: Запрос на исполнение сделки
            
        Returns:
            bool: True если сообщение успешно отправлено, иначе False
        """
        if self.producer is None:
            logger.error("Kafka producer is not initialized")
            return False
        
        try:
            # Используем user_id как ключ для обеспечения порядка сообщений для одного пользователя
            key = trade_request.user_id
            
            # Конвертируем запрос в dict для сериализации
            message = trade_request.model_dump()
            
            # Отправляем сообщение в топик
            future = self.producer.send(
                topic=settings.KAFKA_TRADE_TOPIC,
                key=key,
                value=message
            )
            
            # Ждем подтверждения отправки
            record_metadata = future.get(timeout=10)
            
            logger.info(f"Trade execution sent to Kafka: topic={record_metadata.topic}, "
                       f"partition={record_metadata.partition}, offset={record_metadata.offset}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send trade execution to Kafka: {str(e)}")
            return False
    
    def close(self):
        """Закрывает соединение с Kafka."""
        if self.producer:
            self.producer.close()
            logger.info("Kafka producer closed")

# Создаем глобальный экземпляр сервиса
kafka_service = KafkaService()