import sys
import uuid
import time
from fastapi import FastAPI, WebSocket, Request, HTTPException
from fastapi.middleware.cors import CORSMiddleware
import uvicorn
from loguru import logger
import asyncio

from app.config.settings import settings
from app.api.websocket import handle_websocket
from app.services.kafka_producer import kafka_service
from app.core.models import (
    TradeCalculationRequest, 
    TradeExecutionRequest,
    WebSocketResponse
)
from app.core.calculator import LeverageCalculator

# Настройка логирования
logger.remove()  # Удаляем стандартный обработчик
logger.add(
    sys.stdout,
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level=settings.LOG_LEVEL,
    colorize=True
)
logger.add(
    "logs/analysis_service.log",
    rotation="10 MB",
    retention="7 days",
    format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
    level=settings.LOG_LEVEL
)

# Создаем приложение FastAPI
app = FastAPI(
    title="Analysis Service",
    description="Сервис для расчета параметров торговых сделок",
    version="1.0.0"
)

# Настройка CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.on_event("startup")
async def startup_event():
    """Выполняется при запуске приложения"""
    logger.info("Starting Analysis Service")

@app.on_event("shutdown")
async def shutdown_event():
    """Выполняется при остановке приложения"""
    logger.info("Shutting down Analysis Service")
    # Закрываем подключение к Kafka
    kafka_service.close()

@app.get("/health")
async def health_check():
    """Эндпоинт для проверки работоспособности сервиса"""
    return {"status": "ok"}

# HTTP эндпоинт для расчета параметров сделки
@app.post("/api/calculate")
async def calculate_trade(request: TradeCalculationRequest):
    """HTTP эндпоинт для расчета параметров сделки"""
    try:
        # Выполняем расчет
        calculation_result = LeverageCalculator.calculate_leverage_trading(request)
        
        return calculation_result
    except Exception as e:
        logger.error(f"Error calculating trade: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Error calculating trade: {str(e)}")

# HTTP эндпоинт для исполнения сделки
@app.post("/api/execute")
async def execute_trade(data: dict):
    """HTTP эндпоинт для исполнения сделки"""
    try:
        user_id = data.get("user_id")
        calculation = data.get("calculation")
        
        if not user_id or not calculation:
            raise HTTPException(status_code=400, detail="Missing user_id or calculation data")
        
        # Создаем запрос на расчет из данных
        calculation_request = TradeCalculationRequest(**calculation)
        
        # Выполняем расчет перед отправкой в Trade Service для проверки
        calculation_result = LeverageCalculator.calculate_leverage_trading(calculation_request)
        
        # Проверяем, не будет ли ордер ликвидирован сразу
        if calculation_result.is_liquidated:
            raise HTTPException(
                status_code=400, 
                detail="Ордер будет ликвидирован при текущих параметрах"
            )
        
        # Создаем запрос на исполнение с актуальными расчетами
        execution_request = TradeExecutionRequest(
            user_id=user_id,
            calculation=calculation_request,
            results=calculation_result,
            execution_timestamp=int(time.time())
        )
        
        # Отправляем запрос в Kafka
        success = kafka_service.send_trade_execution(execution_request)
        
        if success:
            return {
                "message": "Ордер успешно отправлен",
                "order_details": {
                    "type": calculation_request.order_type,
                    "size": calculation_result.position_size,
                    "entry_price": calculation_request.entry_price,
                    "leverage": calculation_request.leverage,
                    "liquidation_price": calculation_result.liquidation_price
                }
            }
        else:
            raise HTTPException(
                status_code=500, 
                detail="Не удалось отправить ордер в торговый сервис"
            )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing trade: {str(e)}")
        raise HTTPException(status_code=400, detail=f"Error executing trade: {str(e)}")

@app.websocket("/ws/analise")
async def websocket_endpoint(websocket: WebSocket):
    """Основной WebSocket эндпоинт"""
    client_id = str(uuid.uuid4())
    await handle_websocket(websocket, client_id)

if __name__ == "__main__":
    uvicorn.run(
        "app.main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG
    )