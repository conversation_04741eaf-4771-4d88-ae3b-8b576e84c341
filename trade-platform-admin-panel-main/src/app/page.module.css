.container {
  min-height: calc(100vh - 80px);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2rem 1rem;
  background-color: #f5f5f7;
}

.formWrapper {
  width: 100%;
  max-width: 440px;
  margin: 0 auto;
}

.formContainer {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.1);
  padding: 2rem;
}

.heading {
  font-size: 1.8rem;
  font-weight: 700;
  margin-bottom: 1.5rem;
  color: #1a1a2e;
  text-align: center;
}

.formGroup {
  margin-bottom: 1.5rem;
}

.label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
  color: #333;
}

.input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.input:focus {
  border-color: #4f46e5;
  outline: none;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.formFooter {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.rememberMeContainer {
  display: flex;
  align-items: center;
}

.checkbox {
  margin-right: 0.5rem;
}

.checkboxLabel {
  font-size: 0.875rem;
  color: #4b5563;
}

.buttonContainer {
  margin-top: 1.5rem;
}

.button {
  width: 100%;
  padding: 0.75rem 1.25rem;
  background-color: #4f46e5;
  color: white;
  font-weight: 600;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.button:hover {
  background-color: #4338ca;
}

.error {
  margin-top: 1rem;
  color: #ef4444;
  font-size: 0.875rem;
  text-align: center;
}

.success {
  margin-top: 1rem;
  color: #10b981;
  font-size: 0.875rem;
  text-align: center;
}

/* Стили для ввода кода подтверждения */
.codeDescription {
  text-align: center;
  margin-bottom: 1.5rem;
  color: #4b5563;
}

.codeInputContainer {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.codeInput {
  width: 3rem;
  height: 3.5rem;
  font-size: 1.5rem;
  text-align: center;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  margin: 0 4px;
}

.codeInput:focus {
  border-color: #4f46e5;
  outline: none;
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

.timerContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.timer {
  font-size: 1rem;
  font-weight: 500;
  color: #4b5563;
}

.resendButton {
  background: none;
  border: none;
  color: #4f46e5;
  font-size: 0.875rem;
  cursor: pointer;
  padding: 0;
  text-decoration: underline;
}

.resendButton:disabled {
  color: #9ca3af;
  cursor: not-allowed;
  text-decoration: none;
}