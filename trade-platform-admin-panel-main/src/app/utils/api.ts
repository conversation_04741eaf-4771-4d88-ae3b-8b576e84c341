// Определяем, находимся ли мы в продакшене (проверяем по домену)
const isProduction = typeof window !== 'undefined' && window.location.hostname === 'admin.bitmei.com';

// ВСЕГДА используем HTTPS URL-ы для продакшена
const AUTH_API_URL = process.env.NEXT_PUBLIC_AUTH_SERVICE_URL || "https://auth-api.eu.ngrok.io";
const BALANCE_API_URL = process.env.NEXT_PUBLIC_BALANCE_SERVICE_URL || "https://balance-api.eu.ngrok.io";
const WITHDRAW_API_URL = process.env.NEXT_PUBLIC_WITHDRAW_API_URL || "https://withdraw-api.eu.ngrok.io";
const REFERAL_API_URL = process.env.NEXT_PUBLIC_REFERAL_SERVICE_URL || "https://referal-api.eu.ngrok.io";
const SUPPORT_API_URL = process.env.NEXT_PUBLIC_SUPPORT_SERVICE_URL || "https://support-api.eu.ngrok.io";
const PROFILE_API_URL = process.env.NEXT_PUBLIC_PROFILE_SERVICE_URL || "https://profile-api.eu.ngrok.io";
const TRADING_API_URL = process.env.NEXT_PUBLIC_TRADING_SERVICE_URL || "https://trading-api.eu.ngrok.io";




import { v4 as uuidv4 } from 'uuid';

// Добавляем определение типа WithdrawalRequest, основываясь на бэкенде
export enum WithdrawalStatus {
  PENDING = 'PENDING',
  IN_PROGRESS = 'IN_PROGRESS',
  APPROVED_DONE = 'APPROVED_DONE',
  BLOCKCHAIN_CONFIRMING = 'BLOCKCHAIN_CONFIRMING',
  REJECTED = 'REJECTED'
}

export interface WithdrawalRequest {
  id: string;
  userId: number;
  email?: string;
  internalTxReference: string;
  amount: number;
  currency: string;
  network: string;
  destinationWallets: string[];
  fee: number;
  status: WithdrawalStatus;
  createdAt: string;
  updatedAt: string;
  completedAt?: string;
  blockchainTxHash?: string;
  rejectionReason?: string;
  requiresAdditionalKyc: boolean;
  additionalKycSubmitted: boolean;
}

export function getUUID() {
  // Если код выполняется на сервере и функция доступна, используем её
  if (typeof window === 'undefined' && typeof crypto !== 'undefined' && typeof crypto.randomUUID === 'function') {
    return crypto.randomUUID();
  }
  // В противном случае (например, в браузере) используем uuid из пакета
  return uuidv4();
}

// Функция для извлечения user_id из JWT токена
function getUserIdFromToken(): number | null {
    if (typeof window === 'undefined') {
      return null; // Если код выполняется на сервере
    }
    
    const token = localStorage.getItem("token");
    if (!token) {
      return null;
    }
    
    try {
      // JWT состоит из трех частей, разделенных точками
      // Нам нужна вторая часть, которая содержит payload
      const payload = token.split('.')[1];
      // Декодируем Base64Url и преобразуем в объект
      const decodedPayload = JSON.parse(atob(payload.replace(/-/g, '+').replace(/_/g, '/')));
      
      // В зависимости от структуры вашего токена, id пользователя может быть в разных полях
      // Обычно это поля: "sub", "id", "userId", "user_id"
      const userId = decodedPayload.sub || decodedPayload.id || decodedPayload.userId || decodedPayload.user_id;
      
      if (!userId) {
        console.error("Не удалось извлечь user_id из токена");
        return null;
      }
      
      return Number(userId);
    } catch (error) {
      console.error("Ошибка при декодировании токена:", error);
      return null;
    }
  }

// Интерфейсы для работы с транзакциями
interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

interface HistoryItem {
  id: string;
  userId: number;
  userEmail?: string;
  currency: string;
  amount_change: string;
  transaction_hash?: string;
  event_type: string;
  timestamp: string;
  is_processed: boolean;
}

interface GetTransactionsParams {
  page?: number;
  limit?: number;
  status?: string | null;
  from_date?: string;
  to_date?: string;
  search?: string;
}

// 📌 Универсальная функция отправки запроса с автоматическим добавлением токена
async function sendRequest(apiUrl: string, endpoint: string, data: any, method = "POST", auth = false) {
  const headers: HeadersInit = {
    "Content-Type": "application/json",
  };

  // ⬅ Если требуется авторизация, добавляем токен
  if (auth) {
    if (typeof window !== 'undefined') {
      const token = localStorage.getItem("token");
      if (token) {
        headers["Authorization"] = `Bearer ${token}`;
      }
    }
  }

  const options: RequestInit = {
    method,
    headers,
  };

  if (method !== "GET" && data !== null) {
    options.body = JSON.stringify(data);
  }

  const url = `${apiUrl}/${endpoint}${method === "GET" && data ? formatQueryParams(data) : ""}`;

  const response = await fetch(url, options);

  if (!response.ok) {
    // Логируем ошибку для отладки
    console.error(`API Error: ${method} ${url}`, {
      status: response.status,
      statusText: response.statusText
    });
    
    try {
      const errorData = await response.json();
      throw new Error(errorData.message || `Ошибка ${response.status}: ${response.statusText}`);
    } catch (e) {
      // Если не удалось распарсить JSON, возвращаем общую ошибку
      throw new Error(`Ошибка ${response.status}: ${response.statusText}`);
    }
  }

  try {
    return await response.json();
  } catch (e) {
    return { success: true };
  }
}

// Вспомогательная функция для формирования query параметров в URL
function formatQueryParams(params: any): string {
  if (!params) return '';
  
  const queryParams = new URLSearchParams();
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      queryParams.append(key, value.toString());
    }
  });
  
  const queryString = queryParams.toString();
  return queryString ? `?${queryString}` : '';
}

// 📌 **Функция логина (с сохранением токена)**
export async function login(email: string, password: string) {
  try {
    const data = await sendRequest(AUTH_API_URL, "auth/login", { email, password });

    if (data.accessToken) {
      localStorage.setItem("token", data.accessToken); // ✅ Сохраняем токен
      console.log("🔑 Токен сохранён в localStorage:", data.accessToken);
    }

    return data;
  } catch (error) {
    console.error("Ошибка при логине:", error);
    throw error;
  }
}

// 📌 **Функция подтверждения кода логина (сохранение токена)**
export async function verifyLoginCode(email: string, code: string) {
  try {
    const response = await sendRequest(AUTH_API_URL, "auth/confirm-login", { email, code });

    if (response.accessToken) {
      localStorage.setItem("token", response.accessToken); // ✅ Сохраняем токен
      console.log("🔑 Токен подтверждён и сохранён в localStorage:", response.accessToken);
    }

    return response;
  } catch (error) {
    console.error("Ошибка при проверке кода логина:", error);
    throw error;
  }
}

// 📌 **Функция для получения истории пополнений**
export async function getDeposits(params: GetTransactionsParams = {}): Promise<PaginatedResponse<HistoryItem>> {
  try {
    const userId = getUserIdFromToken();
    
    if (!userId) {
      throw new Error("Не удалось получить ID пользователя из токена");
    }
    
    const queryParams = {
      ...params,
      type: 'deposit',
      user_id: userId
    };
    
    const response = await sendRequest(BALANCE_API_URL, "api/balance/history/by-type", queryParams, "GET", true);
    
    // 🔍 ДОБАВЬТЕ ЭТО ДЛЯ ОТЛАДКИ
    console.log("API Response:", response);
    if (response && response.items) {
      console.log("First item:", response.items[0]);
      console.log("is_processed type:", typeof response.items[0]?.is_processed);
      console.log("is_processed value:", response.items[0]?.is_processed);
    }
    
    if (response && response.items) {
      response.items = response.items.map((item: HistoryItem) => {
        if (!item.id) {
          item.id = getUUID();
        }
        return item;
      });
    }
    
    return response;
  } catch (error) {
    console.error("Ошибка при получении пополнений:", error);
    throw error;
  }
}

// Функция для получения детальной истории выводов из сервиса вывода
export async function getWithdrawalsHistory(): Promise<WithdrawalRequest[]> {
  try {
    // Получаем user_id из токена
    const userId = getUserIdFromToken();
    
    if (!userId) {
      throw new Error("Не удалось получить ID пользователя из токена");
    }
    
    // GET запрос к сервису вывода
    const response = await sendRequest(WITHDRAW_API_URL, `withdraw/history/${userId}`, null, "GET", true);
    
    return response;
  } catch (error) {
    console.error("Ошибка при получении истории выводов:", error);
    throw error;
  }
}

// Расширенный интерфейс для админских заявок на вывод
export interface AdminWithdrawalRequest extends WithdrawalRequest {
  destinationWallet?: string; // Адрес кошелька
  originalAmount?: number; // Исходная сумма до комиссии
  netAmount?: number; // Сумма к выводу (без комиссии)
  feeAmount?: number; // Размер комиссии
  userEmail?: string; // Email пользователя
}

// Интерфейс для пагинированного ответа админских выводов
export interface AdminWithdrawalsResponse {
  status: string;
  message: string;
  count?: number;
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  data: AdminWithdrawalRequest[];
}

// Параметры для получения админских выводов
export interface GetAdminWithdrawalsParams {
  page?: number;
  limit?: number;
  status?: WithdrawalStatus | null;
  userId?: string;
}

/**
 * ПОЛУЧЕНИЕ ВСЕХ ЗАЯВОК НА ВЫВОД (ДЛЯ АДМИНКИ)
 */
export async function getAllWithdrawalsForAdmin(): Promise<AdminWithdrawalRequest[]> {
  try {
    console.log('🔍 Запрос всех заявок на вывод для админки');
    
    const response = await sendRequest(
      WITHDRAW_API_URL,
      "withdraw/admin/all",
      null,
      "GET",
      true
    );
    
    console.log(`✅ Получено ${response.data?.length || 0} заявок на вывод`);
    return response.data || [];
  } catch (error) {
    console.error("❌ Ошибка при получении всех заявок на вывод:", error);
    throw error;
  }
}

/**
 * ПОЛУЧЕНИЕ ЗАЯВОК НА ВЫВОД С ПАГИНАЦИЕЙ (ДЛЯ АДМИНКИ)
 */
export async function getAllWithdrawalsPaginated(params: GetAdminWithdrawalsParams = {}): Promise<AdminWithdrawalsResponse> {
  try {
    console.log('🔍 Запрос заявок на вывод с пагинацией:', params);
    
    const queryParams = new URLSearchParams();
    
    if (params.page) queryParams.append('page', params.page.toString());
    if (params.limit) queryParams.append('limit', params.limit.toString());
    if (params.status) queryParams.append('status', params.status);
    if (params.userId) queryParams.append('userId', params.userId);
    
    const url = `withdraw/admin/all-paginated${queryParams.toString() ? '?' + queryParams.toString() : ''}`;
    
    const response = await sendRequest(
      WITHDRAW_API_URL,
      url,
      null,
      "GET",
      true
    );
    
    console.log(`✅ Получено ${response.data?.length || 0} заявок из ${response.pagination?.total || 0}`);
    return response;
  } catch (error) {
    console.error("❌ Ошибка при получении заявок с пагинацией:", error);
    throw error;
  }
}


// Функция для обновления статуса заявки на вывод (для админки)
export async function updateWithdrawalStatus(withdrawalId: string, status: string): Promise<any> {
  try {
    // PUT запрос для обновления статуса
    const response = await sendRequest(
      WITHDRAW_API_URL, 
      `withdraw/admin/withdrawal/${withdrawalId}/status`, // Предполагаемый эндпоинт для админского обновления
      { status }, 
      "PUT", 
      true
    );
    
    return response;
  } catch (error) {
    console.error("Ошибка при обновлении статуса вывода:", error);
    throw error;
  }
}




// Интерфейсы для работы с рефералами
export interface Referal {
    id: number;
    userId: number;
    referralCode: string;
    referrerId?: number | null;
    totalReferrals: number;
    activeReferrals: number;
    totalTradingVolume: number;
    totalOrdersCount: number;
    totalCommission: number;
    rewardPercentage: number;
    rewardAmount: number;
    tierLevel: number;
    year: number;
    month: number;
    projectedTotalVolume: number;
    projectedTotalCommission: number;
    projectedTierLevel: number;
    projectedRewardPercentage: number;
    projectedRewardAmount: number;
    createdAt: string;
    updatedAt: string;
  }
  
  // Используем интерфейс ReferalPaginatedResponse вместо PaginatedResponse
  export interface ReferalPaginatedResponse<T> {
    items: T[];
    total: number;
    page: number;
    limit: number;
    pages: number;
  }
  
  export interface ReferalStatistics {
    totalReferals: number;
    activeReferrers: number;
    totalVolume: number;
    totalCommission: number;
    totalReward: number;
    tierStatistics: {
      tier1: number;
      tier2: number;
      tier3: number;
    }
  }
  
  export interface ReferalDetail {
    referal: Referal;
    referrals: Referal[];
  }
  
  // Получение всех рефералов для админки
  export async function getAllReferalsForAdmin(page: number = 1, limit: number = 20, year?: number, month?: number): Promise<ReferalPaginatedResponse<Referal>> {
    try {
      // Формируем URL с параметрами
      let queryParams = `page=${page}&limit=${limit}`;
      if (year) queryParams += `&year=${year}`;
      if (month) queryParams += `&month=${month}`;
      
      const response = await sendRequest(
        REFERAL_API_URL,
        `referals/admin/all?${queryParams}`,
        null,
        "GET",
        true
      );
      
      return response;
    } catch (error) {
      console.error("Ошибка при получении списка рефералов:", error);
      throw error;
    }
  }
  
  // Получение статистики по реферальной программе
  export async function getReferalStatistics(year?: number, month?: number): Promise<ReferalStatistics> {
    try {
      // Формируем URL с параметрами
      let queryParams = '';
      if (year) queryParams += `year=${year}&`;
      if (month) queryParams += `month=${month}`;
      
      const url = `referals/admin/statistics${queryParams ? '?' + queryParams : ''}`;
      
      const response = await sendRequest(
        REFERAL_API_URL,
        url,
        null,
        "GET",
        true
      );
      
      return response;
    } catch (error) {
      console.error("Ошибка при получении статистики рефералов:", error);
      throw error;
    }
  }
  
  // Получение детальной информации о реферале
  export async function getReferalDetail(userId: number, year?: number, month?: number): Promise<ReferalDetail> {
    try {
      // Формируем URL с параметрами
      let queryParams = '';
      if (year) queryParams += `year=${year}&`;
      if (month) queryParams += `month=${month}`;
      
      const url = `referals/admin/detail/${userId}${queryParams ? '?' + queryParams : ''}`;
      
      const response = await sendRequest(
        REFERAL_API_URL,
        url,
        null,
        "GET",
        true
      );
      
      return response;
    } catch (error) {
      console.error("Ошибка при получении детальной информации о реферале:", error);
      throw error;
    }
  }
  
  // Получение активных рефереров
  export async function getActiveReferrers(page: number = 1, limit: number = 20, year?: number, month?: number): Promise<ReferalPaginatedResponse<Referal>> {
    try {
      // Формируем URL с параметрами
      let queryParams = `page=${page}&limit=${limit}`;
      if (year) queryParams += `&year=${year}`;
      if (month) queryParams += `&month=${month}`;
      
      const response = await sendRequest(
        REFERAL_API_URL,
        `referals/admin/referrers?${queryParams}`,
        null,
        "GET",
        true
      );
      
      return response;
    } catch (error) {
      console.error("Ошибка при получении активных рефереров:", error);
      throw error;
    }
  }

// Типы данных для системы поддержки
export interface SupportRequest {
    id: string;
    userId: string;
    userName: string;
    email: string;
    message: string;
    topic: 'account' | 'deposit' | 'withdrawal' | 'trading' | 'referral' | 'other';
    response: string | null;
    status: 'incoming' | 'processing' | 'completed';
    createdAt: string;
    updatedAt: string;
    completedAt: string | null;
  }
  
  export interface SupportPaginatedResponse {
    items: SupportRequest[];
    total: number;
    pages: number;
    page: number;
  }
  
  export interface SupportStatistics {
    totalIncoming: number;
    totalProcessing: number;
    totalCompleted: number;
    averageResponseTime: number; // в часах
    responseRateLastWeek: number; // процент обработанных запросов за последнюю неделю
    mostCommonTopics: { topic: string; count: number }[];
  }
  
  export interface CreateSupportResponseDto {
    response: string;
  }
  
  export interface UpdateStatusDto {
    status: 'incoming' | 'processing' | 'completed';
  }
  
  // Получение статистики по запросам в поддержку
  export const getSupportStatistics = async (): Promise<SupportStatistics> => {
    try {
      const response = await fetch(`${SUPPORT_API_URL}/support/dashboard/stats`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
  
      if (!response.ok) {
        throw new Error('Не удалось получить статистику поддержки');
      }
  
      return await response.json();
    } catch (error) {
      console.error('Ошибка при получении статистики поддержки:', error);
      throw error;
    }
  };
  
  // Получение всех запросов в поддержку с пагинацией и фильтрацией
  export const getAllSupportRequests = async (
    page: number = 1,
    limit: number = 10,
    status?: 'incoming' | 'processing' | 'completed',
    search?: string
  ): Promise<SupportPaginatedResponse> => {
    try {
      const queryParams = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      
      if (status) {
        queryParams.append('status', status);
      }
      
      if (search) {
        queryParams.append('search', search);
      }
      
      const response = await fetch(`${SUPPORT_API_URL}/support?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
  
      if (!response.ok) {
        throw new Error('Не удалось получить запросы в поддержку');
      }
  
      return await response.json();
    } catch (error) {
      console.error('Ошибка при получении запросов в поддержку:', error);
      throw error;
    }
  };
  
  // Получение детальной информации о запросе
  export const getSupportRequestById = async (requestId: string): Promise<SupportRequest> => {
    try {
      const response = await fetch(`${SUPPORT_API_URL}/support/${requestId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
  
      if (!response.ok) {
        throw new Error(`Не удалось получить информацию о запросе #${requestId}`);
      }
  
      return await response.json();
    } catch (error) {
      console.error(`Ошибка при получении информации о запросе #${requestId}:`, error);
      throw error;
    }
  };
  
  // Обновление статуса запроса
  export const updateSupportRequestStatus = async (
    requestId: string,
    status: 'incoming' | 'processing' | 'completed'
  ): Promise<SupportRequest> => {
    try {
      const response = await fetch(`${SUPPORT_API_URL}/support/${requestId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });
  
      if (!response.ok) {
        throw new Error(`Не удалось обновить статус запроса #${requestId}`);
      }
  
      return await response.json();
    } catch (error) {
      console.error(`Ошибка при обновлении статуса запроса #${requestId}:`, error);
      throw error;
    }
  };
  
  // Ответ на запрос в поддержку
  export const respondToSupportRequest = async (
    requestId: string,
    response: string
  ): Promise<SupportRequest> => {
    try {
      const responseObj = await fetch(`${SUPPORT_API_URL}/support/${requestId}/respond`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ response }),
      });
  
      if (!responseObj.ok) {
        throw new Error(`Не удалось отправить ответ на запрос #${requestId}`);
      }
  
      return await responseObj.json();
    } catch (error) {
      console.error(`Ошибка при отправке ответа на запрос #${requestId}:`, error);
      throw error;
    }
  };
  
  // Создание нового запроса в поддержку (для пользовательского интерфейса)
  export const createSupportRequest = async (
    userId: string, 
    userName: string, 
    message: string,
    topic: 'account' | 'deposit' | 'withdrawal' | 'trading' | 'referral' | 'other' = 'other'
  ): Promise<SupportRequest> => {
    try {
      const response = await fetch(`${SUPPORT_API_URL}/support`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId,
          userName,
          message,
          topic
        }),
      });
  
      if (!response.ok) {
        throw new Error('Не удалось создать запрос в поддержку');
      }
  
      return await response.json();
    } catch (error) {
      console.error('Ошибка при создании запроса в поддержку:', error);
      throw error;
    }
  };
  
  // Получение запросов конкретного пользователя
  export const getUserSupportRequests = async (userId: string): Promise<SupportRequest[]> => {
    try {
      const response = await fetch(`${SUPPORT_API_URL}/support/user/${userId}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
  
      if (!response.ok) {
        throw new Error('Не удалось получить запросы пользователя');
      }
  
      return await response.json();
    } catch (error) {
      console.error('Ошибка при получении запросов пользователя:', error);
      throw error;
    }
  };






  // Интерфейсы для работы с пользователями
export interface User {
  id: number;
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  balance: number;
  country?: string;
  ipAddress?: string;
  isActive: boolean;
  isVerified: boolean;
  twoFactorEnabled: boolean;
  birthDate?: string;
  referralCode?: string;
  referrerCode?: string;
  avatarUrl?: string;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export interface UserStatistics {
  totalTrades: number;
  totalVolume: number;
  pnl: number;
  totalDeposits: number;
  totalWithdrawals: number;
  referralsCount: number;
}

export interface UserPaginatedResponse {
  items: User[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface UserDetail {
  user: User;
  statistics: UserStatistics;
}

export interface UpdateUserData {
  email?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  isActive?: boolean;
  isVerified?: boolean;
  country?: string;
  referralCode?: string;
  referrerCode?: string;
}

// Получение всех пользователей для админки с пагинацией и поиском
export async function getAllUsers(page: number = 1, limit: number = 20, search?: string): Promise<UserPaginatedResponse> {
  try {
    let queryParams = `page=${page}&limit=${limit}`;
    if (search) queryParams += `&search=${encodeURIComponent(search)}`;
    
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/admin/all?${queryParams}`,
      null,
      "GET",
      true
    );
    
    return response;
  } catch (error) {
    console.error("Ошибка при получении списка пользователей:", error);
    throw error;
  }
}

// Получение пользователя по ID
export async function getUserById(userId: number): Promise<User> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/id/${userId}`,
      null,
      "GET",
      true
    );
    
    return response;
  } catch (error) {
    console.error(`Ошибка при получении пользователя ${userId}:`, error);
    throw error;
  }
}

// Получение пользователя по email
export async function getUserByEmail(email: string): Promise<User> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/email/${encodeURIComponent(email)}`,
      null,
      "GET",
      true
    );
    
    return response;
  } catch (error) {
    console.error(`Ошибка при получении пользователя по email ${email}:`, error);
    throw error;
  }
}

// Получение детальной информации о пользователе (профиль + статистика)
export async function getUserDetail(userId: number): Promise<UserDetail> {
  try {
    // Получаем базовую информацию о пользователе
    const user = await getUserById(userId);
    
    // Получаем статистику пользователя (этот эндпоинт нужно будет добавить на бэкенд)
    const statistics = await sendRequest(
      PROFILE_API_URL,
      `profile/admin/statistics/${userId}`,
      null,
      "GET",
      true
    );
    
    return {
      user,
      statistics: statistics || {
        totalTrades: 0,
        totalVolume: 0,
        pnl: 0,
        totalDeposits: 0,
        totalWithdrawals: 0,
        referralsCount: 0
      }
    };
  } catch (error) {
    console.error(`Ошибка при получении детальной информации о пользователе ${userId}:`, error);
    throw error;
  }
}

// Обновление данных пользователя
export async function updateUserData(userId: number, userData: UpdateUserData): Promise<User> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/id/${userId}`,
      userData,
      "PUT",
      true
    );
    
    return response;
  } catch (error) {
    console.error(`Ошибка при обновлении пользователя ${userId}:`, error);
    throw error;
  }
}

// Удаление пользователя
export async function deleteUser(userId: number): Promise<{ message: string }> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/delete/${userId}`,
      null,
      "DELETE",
      true
    );
    
    return response;
  } catch (error) {
    console.error(`Ошибка при удалении пользователя ${userId}:`, error);
    throw error;
  }
}

// Создание нового пользователя (для админки)
export async function createUser(userData: {
  email: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  referralCode?: string;
  referrerCode?: string;
}): Promise<{ profile: User }> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      "profile",
      userData,
      "POST",
      true
    );
    
    return response;
  } catch (error) {
    console.error("Ошибка при создании пользователя:", error);
    throw error;
  }
}

// Получение пользователя по реферальному коду
export async function getUserByReferralCode(referralCode: string): Promise<User> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/referral/${encodeURIComponent(referralCode)}`,
      null,
      "GET",
      true
    );
    
    return response;
  } catch (error) {
    console.error(`Ошибка при получении пользователя по реферальному коду ${referralCode}:`, error);
    throw error;
  }
}

// Поиск пользователей (расширенный поиск)
export async function searchUsers(searchParams: {
  query?: string;
  isActive?: boolean;
  isVerified?: boolean;
  country?: string;
  dateFrom?: string;
  dateTo?: string;
  page?: number;
  limit?: number;
}): Promise<UserPaginatedResponse> {
  try {
    const queryParams = new URLSearchParams();
    
    Object.entries(searchParams).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value.toString());
      }
    });
    
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/admin/search?${queryParams.toString()}`,
      null,
      "GET",
      true
    );
    
    return response;
  } catch (error) {
    console.error("Ошибка при поиске пользователей:", error);
    throw error;
  }
}

// Блокировка/разблокировка пользователя
export async function toggleUserStatus(userId: number, isActive: boolean): Promise<User> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      `profile/admin/toggle-status/${userId}`,
      { isActive },
      "PUT",
      true
    );
    
    return response;
  } catch (error) {
    console.error(`Ошибка при изменении статуса пользователя ${userId}:`, error);
    throw error;
  }
}

// ✅ Убрали функции сброса пароля и обновления баланса - они не относятся к профильному сервису

// Получение статистики по всем пользователям
export async function getUsersStatistics(): Promise<{
  totalUsers: number;
  activeUsers: number;
  verifiedUsers: number;
  blockedUsers: number;
  totalBalance: number;
  newUsersToday: number;
  newUsersThisWeek: number;
  newUsersThisMonth: number;
}> {
  try {
    const response = await sendRequest(
      PROFILE_API_URL,
      "profile/admin/general-statistics",
      null,
      "GET",
      true
    );
    
    return response;
  } catch (error) {
    console.error("Ошибка при получении общей статистики пользователей:", error);
    throw error;
  }
}

// Интерфейсы
export interface UpdateBalanceRequest {
  fixed_usd_balance: number;
  reason?: string;
}

export interface UserWithBalance extends User {
  availableBalance: number;
}

// ==============================================
// ОСНОВНЫЕ ФУНКЦИИ
// ==============================================

/**
 * ПОЛУЧЕНИЕ ДОСТУПНОГО БАЛАНСА ОДНОГО ПОЛЬЗОВАТЕЛЯ
 */
export async function getUserAvailableBalance(userId: number): Promise<number> {
  console.log(`🎯 Запрос баланса пользователя ${userId}`);
  
  try {
    const response = await sendRequest(
      BALANCE_API_URL,
      `balance/available/${userId}`,
      null,
      "GET",
      true
    );
    
    console.log(`✅ Получен баланс:`, response);
    return response.available_usd_balance || 0;
  } catch (error) {
    console.error(`❌ Ошибка получения баланса пользователя ${userId}:`, error);
    return 0;
  }
}

/**
 * МАССОВОЕ ПОЛУЧЕНИЕ БАЛАНСОВ ДЛЯ ТАБЛИЦЫ
 */
export async function getUsersBalances(userIds: number[]): Promise<{ [userId: number]: number }> {
  console.log(`🎯 Массовый запрос балансов для ${userIds.length} пользователей:`, userIds);
  
  try {
    if (!userIds.length || userIds.length > 100) {
      console.log(`⚠️ Неверное количество пользователей: ${userIds.length}`);
      return {};
    }
    
    const response = await sendRequest(
      BALANCE_API_URL,
      "api/balance/bulk/available",
      { user_ids: userIds },
      "POST",
      true
    );
    
    console.log(`✅ Получены балансы:`, response);
    return response;
  } catch (error) {
    console.error("❌ Ошибка при получении массовых балансов:", error);
    return {};
  }
}

// Обновленный интерфейс БЕЗ поля reason
export interface UpdateBalanceRequest {
  fixed_usd_balance: number;
}

/**
 * ОБНОВЛЕНИЕ БАЛАНСА ПОЛЬЗОВАТЕЛЯ
 */
export async function updateUserBalance(
  userId: number, 
  newBalance: number
): Promise<any> {
  console.log(`🎯 Обновление баланса пользователя ${userId} на ${newBalance}`);
  
  try {
    const updateData: UpdateBalanceRequest = {
      fixed_usd_balance: newBalance
    };
    
    const response = await sendRequest(
      BALANCE_API_URL,
      `api/balance/admin/update/${userId}`,
      updateData,
      "PUT",
      true
    );
    
    console.log(`✅ Баланс обновлен:`, response);
    return response;
  } catch (error) {
    console.error(`❌ Ошибка при обновлении баланса пользователя ${userId}:`, error);
    throw error;
  }
}

/**
 * ПОЛУЧЕНИЕ ПОЛЬЗОВАТЕЛЕЙ С БАЛАНСАМИ ДЛЯ ТАБЛИЦЫ
 */
export async function getUsersWithBalances(page: number = 1, limit: number = 20, search?: string): Promise<{
  items: UserWithBalance[];
  total: number;
  page: number;
  limit: number;
  pages: number;
}> {
  console.log(`🎯 Получение пользователей с балансами, страница ${page}`);
  
  try {
    // 1. Получаем список пользователей
    const usersResponse = await getAllUsers(page, limit, search);
    console.log(`📋 Получено пользователей: ${usersResponse.items.length}`);
    
    // 2. Извлекаем ID пользователей
    const userIds = usersResponse.items.map(user => user.id);
    console.log(`🆔 ID пользователей:`, userIds);
    
    // 3. Получаем балансы пользователей
    const balances = await getUsersBalances(userIds);
    console.log(`💰 Получены балансы:`, balances);
    
    // 4. Обогащаем пользователей данными о балансе
    const usersWithBalances: UserWithBalance[] = usersResponse.items.map(user => ({
      ...user,
      availableBalance: balances[user.id] || 0,
      balance: balances[user.id] || 0 // для обратной совместимости
    }));
    
    console.log(`✅ Пользователи обогащены балансами`);
    
    return {
      ...usersResponse,
      items: usersWithBalances
    };
  } catch (error) {
    console.error("❌ Ошибка при получении пользователей с балансами:", error);
    throw error;
  }
}

/**
 * ПОЛУЧЕНИЕ ДЕТАЛЬНОЙ ИНФОРМАЦИИ О ПОЛЬЗОВАТЕЛЕ
 */
export async function getUserDetailWithBalance(userId: number): Promise<{
  user: UserWithBalance;
  statistics: UserStatistics;
}> {
  console.log(`🎯 Получение деталей пользователя ${userId}`);
  
  try {
    // Получаем базовую информацию
    const userDetail = await getUserDetail(userId);
    console.log(`📋 Базовая информация получена`);
    
    // Получаем баланс
    const balance = await getUserAvailableBalance(userId);
    console.log(`💰 Баланс получен: ${balance}`);
    
    // Обогащаем пользователя
    const userWithBalance: UserWithBalance = {
      ...userDetail.user,
      availableBalance: balance,
      balance: balance
    };
    
    console.log(`✅ Детали пользователя готовы`);
    
    return {
      user: userWithBalance,
      statistics: userDetail.statistics
    };
  } catch (error) {
    console.error(`❌ Ошибка при получении деталей пользователя ${userId}:`, error);
    throw error;
  }
}



/**
 * АДМИНСКИЙ СБРОС ПАРОЛЯ ПОЛЬЗОВАТЕЛЯ
 */
export async function adminResetUserPassword(email: string, newPassword: string): Promise<{ message: string }> {
  console.log(`🔐 Админский сброс пароля для ${email}`);
  
  try {
    const response = await sendRequest(
      AUTH_API_URL,
      "auth/admin-reset-password",
      { email, newPassword },
      "POST",
      true
    );
    
    console.log(`✅ Пароль успешно сброшен через админку`);
    return response;
  } catch (error) {
    console.error(`❌ Ошибка при сбросе пароля через админку:`, error);
    throw error;
  }
}



// ДОБАВИТЬ В api.ts:
export async function getAllDepositsForAdmin(params: GetTransactionsParams = {}): Promise<PaginatedResponse<HistoryItem>> {
  try {
    console.log('🎯 Запрос всех депозитов для админки:', params);
    
    const response = await sendRequest(
      BALANCE_API_URL, 
      "api/balance/admin/deposits/all", // ⭐ НОВЫЙ ЭНДПОИНТ
      params, 
      "GET", 
      true
    );
    
    if (response && response.items) {
      response.items = response.items.map((item: HistoryItem) => {
        if (!item.id) {
          item.id = getUUID();
        }
        return item;
      });
    }
    
    console.log('✅ Получены все депозиты для админки:', response);
    return response;
  } catch (error) {
    console.error("❌ Ошибка при получении всех депозитов:", error);
    throw error;
  }
}




// Интерфейсы для позиций (добавить в api.ts)
// Обновленный интерфейс Position в api.ts
export interface Position {
  id: string;
  userId: string;
  tradingPair: string;  // trading_pair в БД
  direction: "LONG" | "SHORT";
  entryPrice: number;   // entry_price в БД
  currentPrice?: number; // current_price в БД
  positionSize: number; // position_size в БД
  margin: number;
  leverage: number;
  liquidationPrice?: number; // liquidation_price в БД
  pnl: number;
  returnPercentage: number; // return_percentage в БД
  status: "OPEN" | "CLOSED" | "LIQUIDATED" | "PENDING";
  openedAt: string;     // opened_at в БД
  closedAt?: string;    // closed_at в БД
  stopLoss?: number;    // stop_loss в БД
  takeProfit?: number;  // take_profit в БД
  fee?: number;
  closeReason?: string; // close_reason в БД
  orderExecutionType?: string; // order_execution_type в БД
}

export interface UserPositionsStats {
  openPositions: Position[];
  closedPositions: Position[];
  totalVolume: number;
  totalPnl: number;
  totalTrades: number;
  winRate: number;
}

// ==============================================
// ФУНКЦИИ ДЛЯ РАБОТЫ С ПОЗИЦИЯМИ
// ==============================================

/**
 * ПОЛУЧЕНИЕ ОТКРЫТЫХ ПОЗИЦИЙ ПОЛЬЗОВАТЕЛЯ
 */
export async function getUserOpenPositions(userId: string): Promise<Position[]> {
  console.log(`🎯 Запрос открытых позиций пользователя ${userId}`);
  
  try {
    const response = await sendRequest(
      TRADING_API_URL,
      `api/positions?user_id=${userId}&status=OPEN`,
      null,
      "GET",
      true
    );
    
    console.log(`✅ Получено открытых позиций: ${response.length}`);
    return response;
  } catch (error) {
    console.error(`❌ Ошибка получения открытых позиций пользователя ${userId}:`, error);
    return [];
  }
}

/**
 * ПОЛУЧЕНИЕ ИСТОРИИ ЗАКРЫТЫХ ПОЗИЦИЙ ПОЛЬЗОВАТЕЛЯ
 */
export async function getUserPositionHistory(userId: string): Promise<Position[]> {
  console.log(`🎯 Запрос истории позиций пользователя ${userId}`);
  
  try {
    const response = await sendRequest(
      TRADING_API_URL,
      `api/positions/history?user_id=${userId}`,
      null,
      "GET",
      true
    );
    
    console.log(`✅ Получено закрытых позиций: ${response.length}`);
    return response;
  } catch (error) {
    console.error(`❌ Ошибка получения истории позиций пользователя ${userId}:`, error);
    return [];
  }
}

/**
 * ПОЛУЧЕНИЕ ВСЕХ ПОЗИЦИЙ ПОЛЬЗОВАТЕЛЯ (ОТКРЫТЫЕ + ЗАКРЫТЫЕ)
 */
export async function getAllUserPositions(userId: string): Promise<Position[]> {
  console.log(`🎯 Запрос всех позиций пользователя ${userId}`);
  
  try {
    const response = await sendRequest(
      TRADING_API_URL,
      `api/positions?user_id=${userId}`,
      null,
      "GET",
      true
    );
    
    console.log(`✅ Получено всех позиций: ${response.length}`);
    return response;
  } catch (error) {
    console.error(`❌ Ошибка получения всех позиций пользователя ${userId}:`, error);
    return [];
  }
}

/**
 * ПОЛУЧЕНИЕ СТАТИСТИКИ ПО ПОЗИЦИЯМ ПОЛЬЗОВАТЕЛЯ (ИСПРАВЛЕННАЯ)
 */
export async function getUserPositionsStats(userId: string): Promise<UserPositionsStats> {
  console.log(`🎯 Расчет статистики позиций пользователя ${userId}`);
  
  try {
    // Получаем открытые позиции
    const openPositions = await getUserOpenPositions(userId);
    
    // Получаем закрытые позиции
    const closedPositions = await getUserPositionHistory(userId);
    
    // ОТЛАДКА: Логируем RAW данные
    console.log(`🔍 RAW открытые позиции:`, openPositions);
    console.log(`🔍 RAW закрытые позиции:`, closedPositions);
    
    // Проверяем что данные корректные
    const validOpenPositions = Array.isArray(openPositions) ? openPositions : [];
    const validClosedPositions = Array.isArray(closedPositions) ? closedPositions : [];
    
    console.log(`📊 Получено открытых позиций: ${validOpenPositions.length}`);
    console.log(`📊 Получено закрытых позиций: ${validClosedPositions.length}`);
    
    // ОТЛАДКА: Логируем первую позицию для проверки структуры
    if (validOpenPositions.length > 0) {
      console.log(`🔍 Структура первой открытой позиции:`, validOpenPositions[0]);
      console.log(`🔍 Поля позиции:`, Object.keys(validOpenPositions[0]));
    }
    
    if (validClosedPositions.length > 0) {
      console.log(`🔍 Структура первой закрытой позиции:`, validClosedPositions[0]);
      console.log(`🔍 Поля позиции:`, Object.keys(validClosedPositions[0]));
    }
    
    // Рассчитываем статистику
    const allPositions = [...validOpenPositions, ...validClosedPositions];
    
    // ИСПРАВЛЕННЫЙ расчет объема - используем правильное поле position_size
    const totalVolume = allPositions.reduce((sum, position) => {
      // Используем snake_case название поля как в бэкенде
      const positionAny = position as any;
      const positionSize = Number(positionAny.position_size) || 0;
      
      console.log(`💰 Позиция ${positionAny.id}: position_size = ${positionSize}`, {
        position_size: positionAny.position_size,
        rawValue: positionAny.position_size
      });
      
      if (isNaN(positionSize)) {
        console.warn(`⚠️ Некорректный размер позиции:`, position);
        return sum;
      }
      return sum + positionSize;
    }, 0);
    
    // ИСПРАВЛЕННЫЙ расчет PnL - используем правильное поле pnl  
    const totalPnl = allPositions.reduce((sum, position) => {
      const positionAny = position as any;
      const pnl = Number(positionAny.pnl) || 0;
      
      console.log(`📈 Позиция ${positionAny.id}: PnL = ${pnl}`, {
        pnl: positionAny.pnl,
        rawValue: positionAny.pnl
      });
      
      if (isNaN(pnl)) {
        console.warn(`⚠️ Некорректный PnL:`, position);
        return sum;
      }
      return sum + pnl;
    }, 0);
    
    console.log(`💡 ИТОГОВЫЕ РАСЧЕТЫ:`);
    console.log(`   - Всего позиций: ${allPositions.length}`);
    console.log(`   - Общий объем: ${totalVolume}`);
    console.log(`   - Общий PnL: ${totalPnl}`);
    
    // Общее количество сделок
    const totalTrades = allPositions.length;
    
    // Винрейт (только для закрытых позиций)
    let winRate = 0;
    if (validClosedPositions.length > 0) {
      const profitableTrades = validClosedPositions.filter(position => {
        const positionAny = position as any;
        const pnl = Number(positionAny.pnl) || 0;
        return !isNaN(pnl) && pnl > 0;
      }).length;
      
      winRate = (profitableTrades / validClosedPositions.length) * 100;
      
      if (isNaN(winRate)) {
        console.warn(`⚠️ Некорректный винрейт, устанавливаем 0`);
        winRate = 0;
      }
    }
    
    const stats: UserPositionsStats = {
      openPositions: validOpenPositions,
      closedPositions: validClosedPositions,
      totalVolume: isNaN(totalVolume) ? 0 : totalVolume,
      totalPnl: isNaN(totalPnl) ? 0 : totalPnl,
      totalTrades,
      winRate: isNaN(winRate) ? 0 : winRate
    };
    
    console.log(`✅ Финальная статистика:`, stats);
    
    return stats;
  } catch (error) {
    console.error(`❌ Ошибка получения статистики позиций пользователя ${userId}:`, error);
    
    return {
      openPositions: [],
      closedPositions: [],
      totalVolume: 0,
      totalPnl: 0,
      totalTrades: 0,
      winRate: 0
    };
  }
}

// Также добавим функцию для форматирования чисел с проверкой на NaN
export function safeFormatNumber(num: number | string | undefined | null): string {
  // Преобразуем в число
  const numValue = Number(num);
  
  // Проверяем на NaN, null, undefined
  if (isNaN(numValue) || num === null || num === undefined) {
    return '0.00';
  }
  
  // Проверяем на бесконечность
  if (!isFinite(numValue)) {
    return '0.00';
  }
  
  return new Intl.NumberFormat('en-US', { 
    maximumFractionDigits: 2,
    minimumFractionDigits: 0
  }).format(numValue);
}


/**
 * ПОЛУЧЕНИЕ ПОЗИЦИЙ С АКТУАЛЬНЫМ PNL
 */
export async function getUserPositionsWithCurrentPnL(userId: string): Promise<Position[]> {
  console.log(`🎯 Запрос позиций с актуальным PnL пользователя ${userId}`);
  
  try {
    const response = await sendRequest(
      TRADING_API_URL,
      `positions/pnl/${userId}`,
      null,
      "GET",
      true
    );
    
    console.log(`✅ Получено позиций с актуальным PnL: ${response.length}`);
    return response;
  } catch (error) {
    console.error(`❌ Ошибка получения позиций с PnL пользователя ${userId}:`, error);
    return [];
  }
}

/**
 * ОБНОВЛЕНИЕ СТАТИСТИКИ ПОЛЬЗОВАТЕЛЯ НА ОСНОВЕ ПОЗИЦИЙ
 */
export async function updateUserStatisticsFromPositions(userId: string): Promise<UserStatistics> {
  console.log(`🎯 Обновление статистики пользователя ${userId} на основе позиций`);
  
  try {
    // Получаем статистику по позициям
    const positionsStats = await getUserPositionsStats(userId);
    
    // Обновляем существующую статистику пользователя
    const updatedStatistics: UserStatistics = {
      totalTrades: positionsStats.totalTrades,
      totalVolume: positionsStats.totalVolume,
      pnl: positionsStats.totalPnl,
      totalDeposits: 0, // Эти поля получаем из других сервисов
      totalWithdrawals: 0,
      referralsCount: 0
    };
    
    console.log(`✅ Статистика обновлена:`, updatedStatistics);
    return updatedStatistics;
  } catch (error) {
    console.error(`❌ Ошибка обновления статистики пользователя ${userId}:`, error);
    
    return {
      totalTrades: 0,
      totalVolume: 0,
      pnl: 0,
      totalDeposits: 0,
      totalWithdrawals: 0,
      referralsCount: 0
    };
  }
}

/**
 * ОБНОВЛЕННАЯ ФУНКЦИЯ getUserDetailWithBalance С ПОЗИЦИЯМИ
 */
export async function getUserDetailWithBalanceAndPositions(userId: number): Promise<{
  user: UserWithBalance;
  statistics: UserStatistics;
  positionsStats: UserPositionsStats;
}> {
  console.log(`🎯 Получение полной информации о пользователе ${userId}`);
  
  try {
    // Получаем базовую информацию
    const userDetail = await getUserDetailWithBalance(userId);
    console.log(`📋 Базовая информация получена`);
    
    // Получаем статистику по позициям
    const positionsStats = await getUserPositionsStats(userId.toString());
    console.log(`📊 Статистика позиций получена`);
    
    // Обновляем статистику пользователя данными из позиций
    const updatedStatistics: UserStatistics = {
      ...userDetail.statistics,
      totalTrades: positionsStats.totalTrades,
      totalVolume: positionsStats.totalVolume,
      pnl: positionsStats.totalPnl
    };
    
    console.log(`✅ Полная информация о пользователе готова`);
    
    return {
      user: userDetail.user,
      statistics: updatedStatistics,
      positionsStats
    };
  } catch (error) {
    console.error(`❌ Ошибка получения полной информации о пользователе ${userId}:`, error);
    throw error;
  }
}

// ==============================================
// ВСПОМОГАТЕЛЬНЫЕ ФУНКЦИИ
// ==============================================

/**
 * РАСЧЕТ ТОРГОВОГО ОБЪЕМА ИЗ ПОЗИЦИЙ
 */
export function calculateTotalVolumeFromPositions(positions: Position[]): number {
  return positions.reduce((total, position) => {
    return total + position.positionSize;
  }, 0);
}

/**
 * РАСЧЕТ ОБЩЕГО PNL ИЗ ПОЗИЦИЙ
 */
export function calculateTotalPnLFromPositions(positions: Position[]): number {
  return positions.reduce((total, position) => {
    return total + (position.pnl || 0);
  }, 0);
}

/**
 * РАСЧЕТ ВИНРЕЙТА
 */
export function calculateWinRate(closedPositions: Position[]): number {
  if (closedPositions.length === 0) return 0;
  
  const profitablePositions = closedPositions.filter(position => (position.pnl || 0) > 0);
  return (profitablePositions.length / closedPositions.length) * 100;
}