"use client";

import React from "react";
import Image from "next/image";
import styles from "./AdminNavbar.module.css";

const AdminNavbar = () => {
  return (
    <header className={styles.header}>
      <div className={styles.container}>
        <div className={styles.logo}>
          <Image 
            src="/logo.svg"
            alt="TradePlatform Admin"
            width={120}
            height={40}
            priority
          />
        </div>
        <h1 className={styles.title}>Админ-панель трейдинговой платформы</h1>
      </div>
    </header>
  );
};

export default AdminNavbar;