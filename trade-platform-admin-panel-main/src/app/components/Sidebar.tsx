"use client";

import React, { useState } from "react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import styles from "./Sidebar.module.css";
import Image from "next/image";

// Menu icons
import { 
  RiDashboardLine, 
  RiMoneyDollarCircleLine,
  RiUserSettingsLine,
  RiSettings4Line,
  RiLogoutBoxRLine,
  RiMenuLine,
  RiCloseLine,
  RiCustomerService2Line,
  RiTeamLine // Adding icon for users
} from "react-icons/ri";

// Adding interface for props
interface SidebarProps {
  currentPath: string;
}

const Sidebar: React.FC<SidebarProps> = ({ currentPath }) => {
  const router = useRouter();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  const menuItems = [
    {
      name: "Dashboard",
      path: "/admin/dashboard",
      icon: <RiDashboardLine className={styles.menuIcon} />
    },
    {
      name: "Users",
      path: "/admin/users",
      icon: <RiTeamLine className={styles.menuIcon} />
    },
    {
      name: "Deposits & Withdrawals",
      path: "/admin/transactions",
      icon: <RiMoneyDollarCircleLine className={styles.menuIcon} />
    },
    {
      name: "Referral System",
      path: "/admin/referals",
      icon: <RiUserSettingsLine className={styles.menuIcon} />
    },
    {
      name: "Support",
      path: "/admin/support",
      icon: <RiCustomerService2Line className={styles.menuIcon} />
    },
    {
      name: "Settings",
      path: "/admin/settings",
      icon: <RiSettings4Line className={styles.menuIcon} />
    }
  ];
  
  const handleLogout = () => {
    // Remove authorization data
    localStorage.removeItem("token");
    localStorage.removeItem("userRole");
    
    // Redirect to login page
    router.push("/");
  };
  
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };
  
  return (
    <>
      {/* Mobile menu button */}
      <button 
        className={styles.mobileMenuButton} 
        onClick={toggleMobileMenu}
        aria-label="Toggle menu"
      >
        {isMobileMenuOpen ? (
          <RiCloseLine className={styles.menuToggleIcon} />
        ) : (
          <RiMenuLine className={styles.menuToggleIcon} />
        )}
      </button>
      
      {/* Sidebar */}
      <aside className={`${styles.sidebar} ${isMobileMenuOpen ? styles.sidebarMobileOpen : ''}`}>
        <div className={styles.sidebarHeader}>
          <div className={styles.logoContainer}>
            <Image 
              src="/logo.svg"
              alt="TradePlatform Admin"
              width={150}
              height={50}
              priority
            />
          </div>
        </div>
        
        <nav className={styles.navigation}>
          <ul className={styles.menuList}>
            {menuItems.map((item) => (
              <li key={item.path} className={styles.menuItem}>
                <Link
                  href={item.path}
                  className={`${styles.menuLink} ${
                    currentPath === item.path ? styles.activeLink : ""
                  }`}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.icon}
                  <span className={styles.menuText}>{item.name}</span>
                </Link>
              </li>
            ))}
          </ul>
        </nav>
        
        <div className={styles.sidebarFooter}>
          <button className={styles.logoutButton} onClick={handleLogout}>
            <RiLogoutBoxRLine className={styles.menuIcon} />
            <span className={styles.menuText}>Logout</span>
          </button>
        </div>
      </aside>
      
      {/* Mobile menu overlay */}
      {isMobileMenuOpen && (
        <div 
          className={styles.mobileOverlay} 
          onClick={toggleMobileMenu}
        />
      )}
    </>
  );
};

export default Sidebar;