.sidebar {
    width: 260px;
    background-color: #1a1a2e;
    color: #e2e8f0;
    display: flex;
    flex-direction: column;
    height: 100vh;
    position: sticky;
    top: 0;
    transition: all 0.3s ease;
    z-index: 100;
  }
  
  .sidebarHeader {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .logoContainer {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .navigation {
    flex: 1;
    padding: 1.5rem 0;
    overflow-y: auto;
  }
  
  .menuList {
    list-style: none;
    padding: 0;
    margin: 0;
  }
  
  .menuItem {
    margin-bottom: 0.5rem;
  }
  
  .menuLink {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #e2e8f0;
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
  }
  
  .menuLink:hover {
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  .activeLink {
    background-color: rgba(79, 70, 229, 0.2);
    color: #ffffff;
    border-left: 3px solid #4f46e5;
  }
  
  .menuIcon {
    font-size: 1.25rem;
    margin-right: 0.75rem;
  }
  
  .menuText {
    font-size: 0.95rem;
  }
  
  .sidebarFooter {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .logoutButton {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0.75rem 1rem;
    background-color: rgba(239, 68, 68, 0.1);
    color: #e2e8f0;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }
  
  .logoutButton:hover {
    background-color: rgba(239, 68, 68, 0.2);
  }
  
  /* Мобильная версия */
  .mobileMenuButton {
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 101;
    background-color: #1a1a2e;
    color: white;
    border: none;
    border-radius: 0.375rem;
    padding: 0.5rem;
    cursor: pointer;
    display: none;
  }
  
  .menuToggleIcon {
    font-size: 1.5rem;
  }
  
  .mobileOverlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 99;
    display: none;
  }
  
  @media (max-width: 768px) {
    .sidebar {
      position: fixed;
      left: -100%;
      width: 240px;
    }
    
    .sidebarMobileOpen {
      left: 0;
    }
    
    .mobileMenuButton {
      display: block;
    }
    
    .mobileOverlay {
      display: block;
    }
  }