"use client";

import React, { useState, useRef, useEffect } from "react";
import { useRouter } from "next/navigation";
import styles from "./page.module.css";
import { login, verifyLoginCode } from "./utils/api";
import Cookies from "js-cookie";

// Импортируем навбар для страницы авторизации
import AdminNavbar from "./components/AdminNavbar";

export default function AdminLogin() {
  const router = useRouter();
  const [isCodeStep, setIsCodeStep] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const [codeDigits, setCodeDigits] = useState<string[]>(['', '', '', '', '', '']);
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [code, setCode] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    // Проверяем только на клиентской стороне
    if (typeof window !== 'undefined') {
      // Загружаем сохраненные данные при монтировании компонента
      const storedEmail = Cookies.get("rememberEmail");
      const storedPassword = Cookies.get("rememberPassword");
      
      if (storedEmail && storedPassword) {
        setEmail(storedEmail);
        setPassword(storedPassword);
        setRememberMe(true); // Устанавливаем флажок "Remember me"
      }
      
      // Проверяем, есть ли уже токен авторизации
      const token = localStorage.getItem("token");
      const userRole = localStorage.getItem("userRole");
      
      // Перенаправляем только если есть токен И роль администратора
      if (token && userRole === "admin") {
        router.push("/admin/dashboard");
      }
    }
  }, [router]);

  // Эффект для управления таймером обратного отсчета
  useEffect(() => {
    if (countdown > 0) {
      const timer = setTimeout(() => {
        setCountdown(countdown - 1);
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [countdown]);

  const startCountdown = () => {
    setCountdown(45); // Запуск 45-секундного таймера
  };

  // Обработка вставки (Ctrl+V) в первое поле
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    e.preventDefault();
    
    // Получаем вставляемый текст
    const pastedText = e.clipboardData.getData('text').trim();
    
    // Проверяем, что текст состоит только из цифр
    if (!/^\d+$/.test(pastedText)) return;
    
    // Берем первые 6 символов, если их больше
    const codeToFill = pastedText.substring(0, 6).padEnd(6, '');
    
    // Заполняем массив codeDigits
    const newCodeDigits = codeToFill.split('');
    while (newCodeDigits.length < 6) newCodeDigits.push('');
    
    setCodeDigits(newCodeDigits);
    setCode(codeToFill);
    
    // Фокусируемся на последней заполненной ячейке
    const lastFilledIndex = Math.min(codeToFill.length - 1, 5);
    if (lastFilledIndex >= 0 && inputRefs.current[lastFilledIndex]) {
      setTimeout(() => {
        inputRefs.current[lastFilledIndex]?.focus();
      }, 0);
    }
  };

  const handleDigitChange = (index: number, value: string) => {
    if (value.length > 1) value = value.substring(0, 1);
    
    // Обновляем конкретную цифру в массиве
    const newCodeDigits = [...codeDigits];
    newCodeDigits[index] = value;
    setCodeDigits(newCodeDigits);
    
    // Объединяем все цифры в одну строку кода
    setCode(newCodeDigits.join(''));
    
    // Автофокус на следующее поле после ввода
    if (value && index < 5 && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleDigitKeyDown = (index: number, e: React.KeyboardEvent<HTMLInputElement>) => {
    // При нажатии backspace и пустом поле - переход на предыдущее поле
    if (e.key === 'Backspace' && !codeDigits[index] && index > 0 && inputRefs.current[index - 1]) {
      inputRefs.current[index - 1]?.focus();
    }
  };

  // Функция для отправки кода на email снова
  const handleResendCode = async () => {
    try {
      if (countdown > 0) return; // Если таймер активен, не отправляем код
      
      // Отправка кода входа
      await login(email, password);
      
      startCountdown(); // Запускаем таймер после отправки
      setSuccessMessage("Код отправлен на ваш email");
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Произошла ошибка');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError("");
  
    try {
      if (!isCodeStep) {
        // Логин
        const response = await login(email, password);

        if (response.requiresCode) {
          setIsCodeStep(true);
          startCountdown(); // Запускаем таймер после отправки кода
        } else {
          // Сохраняем данные в cookies, если выбран флажок "Remember me"
          if (rememberMe) {
            Cookies.set("rememberEmail", email, { expires: 30, path: "/" });
            Cookies.set("rememberPassword", password, { expires: 30, path: "/" });
          } else {
            // Если флажок не выбран, удаляем cookies
            Cookies.remove("rememberEmail", { path: "/" });
            Cookies.remove("rememberPassword", { path: "/" });
          }
          
          localStorage.setItem("token", response.accessToken);
          localStorage.setItem("userRole", "admin"); // Устанавливаем роль администратора
          
          // Перенаправляем на панель администратора
          router.push("/admin/dashboard");
        }
      } else {
        // Подтверждение кода
        const loginResponse = await verifyLoginCode(email, code);
        localStorage.setItem("token", loginResponse.accessToken);
        localStorage.setItem("userRole", "admin"); // Устанавливаем роль администратора
        
        // Сохраняем данные, если флажок "Remember me" выбран
        if (rememberMe) {
          Cookies.set("rememberEmail", email, { expires: 30, path: "/" });
          Cookies.set("rememberPassword", password, { expires: 30, path: "/" });
        }
        
        // Перенаправляем на панель администратора
        router.push("/admin/dashboard");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Произошла ошибка');
    }
  };

  // Функция для отображения маскированного email
  const getMaskedEmail = () => {
    if (!email) return "";
    const parts = email.split('@');
    if (parts.length !== 2) return email;
    
    const username = parts[0];
    const domain = parts[1];
    
    const firstChar = username.charAt(0);
    const lastChar = username.length > 1 ? username.charAt(username.length - 1) : '';
    const maskedUsername = username.length > 2 
      ? `${firstChar}${'*'.repeat(username.length - 2)}${lastChar}`
      : `${firstChar}*`;
      
    return `${maskedUsername}@${domain}`;
  };

  return (
    <>
      <AdminNavbar />
      <div className={styles.container}>
        <div className={styles.formWrapper}>
          <div className={styles.formContainer}>
            <h1 className={styles.heading}>
              {isCodeStep ? "Подтверждение Email" : "Вход в админ-панель"}
            </h1>
            
            {isCodeStep ? (
              <>
                <p className={styles.codeDescription}>
                  Код был отправлен на {getMaskedEmail()}
                </p>
                
                <div className={styles.timerContainer}>
                  <div className={styles.timer}>
                    {String(Math.floor(countdown / 60)).padStart(2, '0')}:{String(countdown % 60).padStart(2, '0')}
                  </div>
                  <button 
                    type="button" 
                    className={styles.resendButton}
                    onClick={handleResendCode}
                    disabled={countdown > 0}
                  >
                    Отправить код повторно
                  </button>
                </div>
                
                <form onSubmit={handleSubmit}>
                  <div className={styles.codeInputContainer}>
                    {codeDigits.map((digit, index) => (
                      <input
                        key={index}
                        ref={(el) => {
                          inputRefs.current[index] = el;
                        }}
                        type="text"
                        inputMode="numeric"
                        pattern="[0-9]*"
                        maxLength={1}
                        className={styles.codeInput}
                        value={digit}
                        onChange={(e) => handleDigitChange(index, e.target.value)}
                        onKeyDown={(e) => handleDigitKeyDown(index, e)}
                        onPaste={index === 0 ? handlePaste : undefined}
                        required
                      />
                    ))}
                  </div>
                  
                  <div className={styles.buttonContainer}>
                    <button type="submit" className={styles.button}>
                      Подтвердить
                    </button>
                  </div>
                </form>
              </>
            ) : (
              <>
                <form onSubmit={handleSubmit}>
                  <div className={styles.formGroup}>
                    <label className={styles.label}>Email</label>
                    <input
                      className={styles.input}
                      type="email"
                      placeholder="<EMAIL>"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className={styles.formGroup}>
                    <label className={styles.label}>Пароль</label>
                    <input
                      className={styles.input}
                      type="password"
                      placeholder="••••••••••"
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                      required
                    />
                  </div>
                  
                  <div className={styles.formFooter}>
                    <div className={styles.rememberMeContainer}>
                      <input
                        id="rememberMe"
                        type="checkbox"
                        checked={rememberMe}
                        onChange={(e) => setRememberMe(e.target.checked)}
                        className={styles.checkbox}
                      />
                      <label htmlFor="rememberMe" className={styles.checkboxLabel}>
                        Запомнить меня
                      </label>
                    </div>
                  </div>
                  
                  <div className={styles.buttonContainer}>
                    <button type="submit" className={styles.button}>
                      Войти
                    </button>
                  </div>
                </form>
              </>
            )}
            
            {error && <p className={styles.error}>{error}</p>}
            {successMessage && <p className={styles.success}>{successMessage}</p>}
          </div>
        </div>
      </div>
    </>
  );
}