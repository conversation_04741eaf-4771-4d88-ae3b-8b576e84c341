"use client";

import React, { useState, useEffect } from "react";
import { 
  getUsersWithBalances, 
  getUserDetailWithBalance, 
  updateUserData, 
  deleteUser, 
  updateUserBalance,
  getUsersStatistics,
  adminResetUserPassword,
  getUserPositionsStats,
  type UserWithBalance, 
  type UserStatistics,
  type UserPositionsStats,
  type Position
} from "@/app/utils/api";
import styles from "./page.module.css";
import { format } from "date-fns";
import { enUS } from "date-fns/locale";
import { 
  RiSearchLine, RiRefreshLine, RiUserLine, RiLoader4Line, RiTeamLine,
  RiMoneyDollarCircleLine, RiGlobalLine, RiMapPinLine, RiEditLine,
  RiDeleteBinLine, RiEyeLine, RiSaveLine, RiLockLine, RiMailLine,
  RiWalletLine, RiCalendarLine, RiS<PERSON>eldCheckLine, RiErrorWarningLine,
  RiCheckboxCircleLine, RiPhoneLine, RiArrowUpLine, RiArrowDownLine,
  RiBarChartLine, RiStockLine
} from "react-icons/ri";

export default function UsersPage() {
  console.log("🎯 UsersPage component loaded");
  
  const [users, setUsers] = useState<UserWithBalance[]>([]);
  const [selectedUser, setSelectedUser] = useState<UserWithBalance | null>(null);
  const [userDetail, setUserDetail] = useState<{ 
    user: UserWithBalance, 
    statistics: UserStatistics
  } | null>(null);
  
  // Новое состояние для позиций пользователя
  const [userPositions, setUserPositions] = useState<UserPositionsStats | null>(null);
  const [positionsLoading, setPositionsLoading] = useState(false);
  
  const [userStats, setUserStats] = useState({
    totalUsers: 0, activeUsers: 0, verifiedUsers: 0, blockedUsers: 0, newUsersToday: 0
  });
  
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showBalanceEditModal, setShowBalanceEditModal] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  
  const [editForm, setEditForm] = useState({
    email: "", firstName: "", lastName: "", phone: "",
    password: "", isActive: true, isVerified: false, country: ""
  });

  const [balanceEditForm, setBalanceEditForm] = useState({
    newBalance: 0
  });

  useEffect(() => {
    console.log("📱 useEffect called, page:", page, "searchTerm:", searchTerm);
    fetchUsers();
    fetchUserStats();
  }, [page, searchTerm]);

  // ИСПРАВЛЕННАЯ функция для получения позиций пользователя через API
  const fetchUserPositions = async (userId: string) => {
    console.log("📊 Загрузка позиций пользователя:", userId);
    setPositionsLoading(true);
    
    try {
      console.log("🔄 Вызов getUserPositionsStats...");
      const positionsStats = await getUserPositionsStats(userId);
      
      console.log("✅ Статистика позиций получена:", positionsStats);
      setUserPositions(positionsStats);
      
    } catch (err) {
      console.error("❌ Ошибка при загрузке позиций:", err);
      // Устанавливаем пустую статистику в случае ошибки
      setUserPositions({
        openPositions: [],
        closedPositions: [],
        totalVolume: 0,
        totalPnl: 0,
        totalTrades: 0,
        winRate: 0
      });
    } finally {
      setPositionsLoading(false);
    }
  };

  const fetchUserStats = async () => {
    console.log("📊 Loading user statistics...");
    try {
      const stats = await getUsersStatistics();
      console.log("✅ Statistics received:", stats);
      setUserStats(stats);
    } catch (err) {
      console.error('❌ Error loading statistics:', err);
    }
  };

  const fetchUsers = async () => {
    console.log("👥 Loading users with balances...");
    setIsLoading(true);
    setError("");
    
    try {
      console.log(`🔄 Calling getUsersWithBalances(${page}, 20, "${searchTerm}")`);
      const response = await getUsersWithBalances(page, 20, searchTerm);
      
      console.log("✅ Users with balances received:", response);
      console.log("👤 Number of users:", response.items?.length || 0);
      
      setUsers(response.items || []);
      setTotalPages(response.pages || 1);
    } catch (err) {
      console.error("❌ Error loading users:", err);
      setError(err instanceof Error ? err.message : 'An error occurred while loading users');
    } finally {
      setIsLoading(false);
    }
  };

  // ✅ ИСПРАВЛЕННАЯ функция - используем данные из таблицы вместо API
  const handleViewDetails = async (user: UserWithBalance) => {
    console.log("🔍 Opening user details:", user.id);
    console.log("🔍 User balance from table:", user.availableBalance);
    
    setSelectedUser(user);
    setIsLoading(true);
    
    try {
      // ✅ НЕ ВЫЗЫВАЕМ getUserDetailWithBalance! ИСПОЛЬЗУЕМ ДАННЫЕ ИЗ ТАБЛИЦЫ!
      console.log("✅ Using data from table instead of API call");
      
      // Создаем userDetail из данных таблицы
      const tableBasedDetail = {
        user: user, // ← ДАННЫЕ ИЗ ТАБЛИЦЫ С ПРАВИЛЬНЫМ БАЛАНСОМ!
        statistics: {
          totalTrades: 0,
          totalVolume: 0,
          pnl: 0,
          totalDeposits: 0,
          totalWithdrawals: 0,
          referralsCount: 0
        }
      };
      
      console.log("✅ Table-based detail created:", tableBasedDetail);
      console.log("✅ Balance from table:", user.availableBalance);
      
      setUserDetail(tableBasedDetail);
      
      // Загружаем позиции пользователя
      await fetchUserPositions(user.id.toString());
      
      setShowDetailModal(true);
    } catch (err) {
      console.error("❌ Error:", err);
      setError(err instanceof Error ? err.message : 'Error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEditUser = (user: UserWithBalance) => {
    console.log("✏️ Opening user edit form:", user.id);
    setSelectedUser(user);
    setEditForm({
      email: user.email,
      firstName: user.firstName || "",
      lastName: user.lastName || "",
      phone: user.phone || "",
      password: "",
      isActive: user.isActive,
      isVerified: user.isVerified,
      country: user.country || ""
    });
    setShowEditModal(true);
  };

  const handleEditBalance = (user: UserWithBalance) => {
    console.log("💰 Opening balance edit form:", user.id);
    setSelectedUser(user);
    setBalanceEditForm({
      newBalance: user.availableBalance || 0
    });
    setShowBalanceEditModal(true);
  };

  const handleSaveUser = async () => {
    if (!selectedUser) return;
    
    console.log("💾 Saving user changes:", selectedUser.id);
    setIsLoading(true);
    
    try {
      const profileChanged = 
        editForm.email !== selectedUser.email ||
        editForm.firstName !== (selectedUser.firstName || "") ||
        editForm.lastName !== (selectedUser.lastName || "") ||
        editForm.phone !== (selectedUser.phone || "") ||
        editForm.isActive !== selectedUser.isActive ||
        editForm.isVerified !== selectedUser.isVerified ||
        editForm.country !== (selectedUser.country || "");
  
      // Update profile if there are changes
      if (profileChanged) {
        console.log("🔄 Updating user profile...");
        await updateUserData(selectedUser.id, {
          email: editForm.email,
          firstName: editForm.firstName,
          lastName: editForm.lastName,
          phone: editForm.phone,
          isActive: editForm.isActive,
          isVerified: editForm.isVerified,
          country: editForm.country
        });
        console.log("✅ Profile updated");
      }
  
      // Reset password if new one is provided
      if (editForm.password && editForm.password.length >= 6) {
        console.log("🔐 Admin password reset...");
        await adminResetUserPassword(editForm.email, editForm.password);
        console.log("✅ Password reset via admin");
      }
  
      setShowEditModal(false);
      fetchUsers();
      
    } catch (err) {
      console.error("❌ Error saving user:", err);
      setError(err instanceof Error ? err.message : 'Error updating user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveBalance = async () => {
    if (!selectedUser) return;
    
    console.log(`💰 Saving balance: ${balanceEditForm.newBalance} for user ${selectedUser.id}`);
    setIsLoading(true);
    
    try {
      console.log(`🔄 Calling updateUserBalance(${selectedUser.id}, ${balanceEditForm.newBalance})`);
      
      await updateUserBalance(
        selectedUser.id, 
        balanceEditForm.newBalance,
      );
      
      console.log("✅ Balance updated");
      setShowBalanceEditModal(false);
      fetchUsers(); // Refresh list
      
    } catch (err) {
      console.error("❌ Error updating balance:", err);
      setError(err instanceof Error ? err.message : 'Error updating balance');
    } finally {
      setIsLoading(false);
    }
  };

  const handleDeleteUser = async () => {
    if (!selectedUser) return;
    
    console.log("🗑️ Deleting user:", selectedUser.id);
    setIsLoading(true);
    try {
      await deleteUser(selectedUser.id);
      console.log("✅ User deleted");
      setShowDeleteConfirm(false);
      fetchUsers();
    } catch (err) {
      console.error("❌ Error deleting user:", err);
      setError(err instanceof Error ? err.message : 'Error deleting user');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseModals = () => {
    console.log("❌ Closing modal windows");
    setShowDetailModal(false);
    setShowEditModal(false);
    setShowBalanceEditModal(false);
    setShowDeleteConfirm(false);
    setUserDetail(null);
    setSelectedUser(null);
    setUserPositions(null);
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'dd MMMM yyyy HH:mm', { locale: enUS });
    } catch (e) {
      return dateString;
    }
  };

  // УЛУЧШЕННАЯ функция форматирования чисел
  const formatNumber = (num: number | string | undefined | null) => {
    // Преобразуем в число
    const numValue = Number(num);
    
    // Проверяем на NaN, null, undefined
    if (isNaN(numValue) || num === null || num === undefined) {
      return '0.00';
    }
    
    // Проверяем на бесконечность
    if (!isFinite(numValue)) {
      return '0.00';
    }
    
    return new Intl.NumberFormat('en-US', { 
      maximumFractionDigits: 2,
      minimumFractionDigits: 0
    }).format(numValue);
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log("🔍 Search:", searchTerm);
    setPage(1);
    fetchUsers();
  };

  const getUserStatus = (user: UserWithBalance) => {
    if (!user.isActive) return { text: 'Blocked', class: styles.statusBlocked };
    if (user.isVerified) return { text: 'Verified', class: styles.statusVerified };
    return { text: 'Active', class: styles.statusActive };
  };

  // ПОЛНОСТЬЮ ПЕРЕПИСАННАЯ функция для рендеринга позиций
  const renderPositionsSection = () => {
    console.log("🏗️ renderPositionsSection вызвана");
  
    if (positionsLoading) {
      return (
        <div className={styles.loadingContainer}>
          <RiLoader4Line className={styles.loadingSpinner} />
          <p>Загрузка позиций...</p>
        </div>
      );
    }
  
    if (!userPositions) {
      console.log("⚠️ userPositions = null, показываем заглушку");
      return <p>Позиции не загружены</p>;
    }
  
    // БЕЗОПАСНАЯ обработка данных с детальным логированием
    const safeUserPositions = {
      openPositions: Array.isArray(userPositions.openPositions) ? userPositions.openPositions : [],
      closedPositions: Array.isArray(userPositions.closedPositions) ? userPositions.closedPositions : [],
      totalVolume: Number(userPositions.totalVolume) || 0,
      totalPnl: Number(userPositions.totalPnl) || 0,
      totalTrades: Number(userPositions.totalTrades) || 0,
      winRate: Number(userPositions.winRate) || 0
    };
  
    return (
      <div className={styles.positionsSection}>
        <div className={styles.positionsStats}>
          <div className={styles.statItem}>
            <RiBarChartLine className={styles.statIcon} />
            <div>
              <div className={styles.statValue}>
                ${formatNumber(safeUserPositions.totalVolume)}
              </div>
              <div className={styles.statLabel}>Общий объем</div>
            </div>
          </div>
          
          <div className={styles.statItem}>
            <RiArrowUpLine className={`${styles.statIcon} ${safeUserPositions.totalPnl >= 0 ? styles.positive : styles.negative}`} />
            <div>
              <div className={`${styles.statValue} ${safeUserPositions.totalPnl >= 0 ? styles.positiveValue : styles.negativeValue}`}>
                ${formatNumber(safeUserPositions.totalPnl)}
              </div>
              <div className={styles.statLabel}>Общий PnL</div>
            </div>
          </div>
          
          <div className={styles.statItem}>
            <RiStockLine className={styles.statIcon} />
            <div>
              <div className={styles.statValue}>{safeUserPositions.totalTrades}</div>
              <div className={styles.statLabel}>Всего сделок</div>
            </div>
          </div>
          
          <div className={styles.statItem}>
            <RiArrowUpLine className={styles.statIcon} />
            <div>
              <div className={styles.statValue}>{formatNumber(safeUserPositions.winRate)}%</div>
              <div className={styles.statLabel}>Винрейт</div>
            </div>
          </div>
        </div>
  
        <div className={styles.positionsTabs}>
          <div className={styles.positionsTab}>
            <h4>Открытые позиции ({safeUserPositions.openPositions.length})</h4>
            {safeUserPositions.openPositions.length > 0 ? (
              <div className={styles.positionsTable}>
                {safeUserPositions.openPositions.map((position: Position) => {
                  const positionAny = position as any;
                  const positionSize = positionAny.position_size || 0;
                  const tradingPair = positionAny.trading_pair || 'N/A';
                  const pnl = positionAny.pnl || 0;
                  const leverage = positionAny.leverage || 1;
                  
                  return (
                    <div key={positionAny.id} className={styles.positionRow}>
                      <div className={styles.positionInfo}>
                        <span className={styles.tradingPair}>{tradingPair}</span>
                        <span className={`${styles.direction} ${positionAny.direction === 'LONG' ? styles.long : styles.short}`}>
                          {positionAny.direction || 'N/A'}
                        </span>
                      </div>
                      <div className={styles.positionData}>
                        <span>Размер: ${formatNumber(positionSize)}</span>
                        <span>Плечо: {leverage}x</span>
                        <span className={pnl >= 0 ? styles.positiveValue : styles.negativeValue}>
                          PnL: ${formatNumber(pnl)}
                        </span>
                      </div>
                    </div>
                  );
                })}
              </div>
            ) : (
              <p>Нет открытых позиций</p>
            )}
          </div>
  
          <div className={styles.positionsTab}>
            <h4>Закрытые позиции ({safeUserPositions.closedPositions.length})</h4>
            {safeUserPositions.closedPositions.length > 0 ? (
              <div className={styles.positionsTable}>
                {safeUserPositions.closedPositions.slice(0, 10).map((position: Position) => {
                  const positionAny = position as any;
                  const positionSize = positionAny.position_size || 0;
                  const tradingPair = positionAny.trading_pair || 'N/A';
                  const pnl = positionAny.pnl || 0;
                  const closedAt = positionAny.closed_at;
                  const status = positionAny.status || 'CLOSED';
                  
                  return (
                    <div key={positionAny.id} className={styles.positionRow}>
                      <div className={styles.positionInfo}>
                        <span className={styles.tradingPair}>{tradingPair}</span>
                        <span className={`${styles.direction} ${positionAny.direction === 'LONG' ? styles.long : styles.short}`}>
                          {positionAny.direction || 'N/A'}
                        </span>
                        <span className={styles.status}>{status}</span>
                      </div>
                      <div className={styles.positionData}>
                        <span>Размер: ${formatNumber(positionSize)}</span>
                        <span className={pnl >= 0 ? styles.positiveValue : styles.negativeValue}>
                          PnL: ${formatNumber(pnl)}
                        </span>
                        <span>{closedAt ? formatDate(closedAt) : 'N/A'}</span>
                      </div>
                    </div>
                  );
                })}
                {safeUserPositions.closedPositions.length > 10 && (
                  <p className={styles.morePositions}>
                    И еще {safeUserPositions.closedPositions.length - 10} позиций...
                  </p>
                )}
              </div>
            ) : (
              <p>Нет закрытых позиций</p>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderUsersTable = () => {
    console.log("🏗️ Rendering table, users:", users.length);
    
    if (users.length === 0) {
      return (
        <div className={styles.emptyState}>
          <RiTeamLine className={styles.emptyStateIcon} />
          <p>No users found</p>
        </div>
      );
    }

    return (
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>User</th>
              <th>Balance</th>
              <th>Country</th>
              <th>Status</th>
              <th>Registration</th>
              <th>Last Login</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {users.map(user => {
              const status = getUserStatus(user);
              return (
                <tr key={user.id}>
                  <td>
                    <div className={styles.userInfoCell}>
                      <div className={styles.userAvatar}>
                        <RiUserLine />
                        {user.isVerified && (
                          <div className={styles.verifiedBadge}>
                            <RiCheckboxCircleLine />
                          </div>
                        )}
                      </div>
                      <div className={styles.userDetails}>
                        <div className={styles.userEmail}>{user.email}</div>
                        <div className={styles.userMeta}>
                          <span className={styles.userId}>ID: {user.id}</span>
                          {user.firstName && user.lastName && (
                            <span className={styles.userName}>{user.firstName} {user.lastName}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className={styles.balanceCell}>
                      <RiMoneyDollarCircleLine className={styles.balanceIcon} />
                      <span className={styles.balanceAmount}>
                        ${formatNumber(user.availableBalance)}
                      </span>
                      <span className={styles.balanceCurrency}>USD</span>
                    </div>
                  </td>
                  <td>
                    <div className={styles.countryCell}>
                      <RiGlobalLine className={styles.countryIcon} />
                      <span>{user.country || 'Not specified'}</span>
                      {user.ipAddress && (
                        <span className={styles.ipAddress} title={`IP: ${user.ipAddress}`}>
                          <RiMapPinLine />
                        </span>
                      )}
                    </div>
                  </td>
                  <td>
                    <div className={styles.statusCell}>
                      <span className={`${styles.statusBadge} ${status.class}`}>
                        {status.text}
                      </span>
                      {user.isVerified && (
                        <span className={styles.verifiedIndicator} title="Verified">
                          <RiShieldCheckLine />
                        </span>
                      )}
                      {user.twoFactorEnabled && (
                        <span className={styles.twoFactorIndicator} title="2FA enabled">
                          <RiLockLine />
                        </span>
                      )}
                    </div>
                  </td>
                  <td>{formatDate(user.createdAt)}</td>
                  <td>{user.lastLoginAt ? formatDate(user.lastLoginAt) : 'Never'}</td>
                  <td>
                    <div className={styles.actionsCell}>
                      <button
                        onClick={() => handleViewDetails(user)}
                        className={styles.actionButton}
                        title="View Details"
                      >
                        <RiEyeLine />
                      </button>
                      <button
                        onClick={() => handleEditUser(user)}
                        className={styles.actionButton}
                        title="Edit Profile"
                      >
                        <RiEditLine />
                      </button>
                      <button
                        onClick={() => handleEditBalance(user)}
                        className={styles.actionButton}
                        title="Edit Balance"
                      >
                        <RiWalletLine />
                      </button>
                      <button
                        onClick={() => {
                          setSelectedUser(user);
                          setShowDeleteConfirm(true);
                        }}
                        className={`${styles.actionButton} ${styles.deleteButton}`}
                        title="Delete"
                      >
                        <RiDeleteBinLine />
                      </button>
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    );
  };

  const renderBalanceEditModal = () => {
    if (!showBalanceEditModal || !selectedUser) return null;

    return (
      <div className={styles.modalBackdrop}>
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <h2>Edit User Balance</h2>
            <button onClick={handleCloseModals} className={styles.closeButton}>×</button>
          </div>
          
          <div className={styles.modalBody}>
            <div className={styles.userProfileSection}>
              <div className={styles.userAvatar}>
                <RiUserLine />
              </div>
              <div className={styles.userBasicInfo}>
                <h3>{selectedUser.email}</h3>
                <p>Current balance: <strong>${formatNumber(selectedUser.availableBalance)}</strong></p>
              </div>
            </div>

            <div className={styles.editFormGrid}>
              <div className={styles.editFormColumn}>
                <h3>New Balance</h3>
                
                <div className={styles.formGroup}>
                  <label className={styles.formLabel}>
                    <RiWalletLine className={styles.formIcon} />
                    Balance ($)
                  </label>
                  <input
                    type="number"
                    value={balanceEditForm.newBalance}
                    onChange={(e) => setBalanceEditForm({newBalance: Number(e.target.value)})}
                    className={styles.formInputNoSpinners}
                    step="0.01"
                    min="0"
                  />
                </div>
              </div>
            </div>
          </div>
          
          <div className={styles.modalFooter}>
            <button onClick={handleCloseModals} className={styles.cancelBtn}>Cancel</button>
            <button onClick={handleSaveBalance} className={styles.saveBtn}>
              <RiSaveLine /> Save Balance
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderDetailModal = () => {
    if (!showDetailModal || !userDetail) return null;
  
    const { user, statistics } = userDetail;
  
    return (
      <div className={styles.modalBackdrop}>
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <h2>User Details</h2>
            <button onClick={handleCloseModals} className={styles.closeButton}>×</button>
          </div>
          
          <div className={styles.modalBody}>
            <div className={styles.userProfileSection}>
              <div className={styles.userAvatar}>
                <RiUserLine />
              </div>
              <div className={styles.userBasicInfo}>
                <h3>{user.email}</h3>
                <p>ID: {user.id}</p>
                <p>Balance: <strong>${formatNumber(user.availableBalance)}</strong></p>
                <span className={`${styles.statusBadge} ${getUserStatus(user).class}`}>
                  {getUserStatus(user).text}
                </span>
              </div>
            </div>
  
            <div className={styles.detailInfo}>
              <div className={styles.detailColumn}>
                <h3>Basic Information</h3>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Email:</span>
                  <span className={styles.detailValue}>{user.email}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Balance:</span>
                  <span className={styles.detailValue}>${formatNumber(user.availableBalance)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Country:</span>
                  <span className={styles.detailValue}>{user.country || 'Not specified'}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Registration:</span>
                  <span className={styles.detailValue}>{formatDate(user.createdAt)}</span>
                </div>
              </div>
              
              <div className={styles.detailColumn}>
                <h3>Trading Statistics</h3>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Trades:</span>
                  <span className={styles.detailValue}>
                    {userPositions ? userPositions.totalTrades : statistics.totalTrades}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Volume:</span>
                  <span className={styles.detailValue}>
                    ${formatNumber(userPositions ? userPositions.totalVolume : statistics.totalVolume)}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>P&L:</span>
                  <span className={`${styles.detailValue} ${(userPositions ? userPositions.totalPnl : statistics.pnl) >= 0 ? styles.positiveValue : styles.negativeValue}`}>
                    ${formatNumber(userPositions ? userPositions.totalPnl : statistics.pnl)}
                  </span>
                </div>
                {userPositions && (
                  <div className={styles.detailRow}>
                    <span className={styles.detailLabel}>Win Rate:</span>
                    <span className={styles.detailValue}>{formatNumber(userPositions.winRate)}%</span>
                  </div>
                )}
              </div>
            </div>
  
            <div className={styles.positionsDetailSection}>
              <h3>Trading Positions</h3>
              {renderPositionsSection()}
            </div>
          </div>
          
          <div className={styles.modalFooter}>
            <button onClick={handleCloseModals} className={styles.closeBtn}>Close</button>
          </div>
        </div>
      </div>
    );
  };
  
  const renderEditModal = () => {
    if (!showEditModal || !selectedUser) return null;
  
    return (
      <div className={styles.modalBackdrop}>
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <h2>Edit User Profile</h2>
            <button onClick={handleCloseModals} className={styles.closeButton}>×</button>
          </div>
          
          <div className={styles.modalBody}>
            <div className={styles.editFormGrid}>
              <div className={styles.editFormColumn}>
                <h3>Basic Information</h3>
                
                <div className={styles.formGroup}>
                  <label className={styles.formLabel}>
                    <RiMailLine className={styles.formIcon} />
                    Email
                  </label>
                  <input
                    type="email"
                    value={editForm.email}
                    onChange={(e) => setEditForm({...editForm, email: e.target.value})}
                    className={styles.formInput}
                  />
                </div>
  
                <div className={styles.formRow}>
                  <div className={styles.formGroup}>
                    <label className={styles.formLabel}>
                      <RiUserLine className={styles.formIcon} />
                      First Name
                    </label>
                    <input
                      type="text"
                      value={editForm.firstName}
                      onChange={(e) => setEditForm({...editForm, firstName: e.target.value})}
                      className={styles.formInput}
                    />
                  </div>
                  
                  <div className={styles.formGroup}>
                    <label className={styles.formLabel}>
                      <RiUserLine className={styles.formIcon} />
                      Last Name
                    </label>
                    <input
                      type="text"
                      value={editForm.lastName}
                      onChange={(e) => setEditForm({...editForm, lastName: e.target.value})}
                      className={styles.formInput}
                    />
                  </div>
                </div>
  
                <div className={styles.formGroup}>
                  <label className={styles.formLabel}>
                    <RiPhoneLine className={styles.formIcon} />
                    Phone
                  </label>
                  <input
                    type="tel"
                    value={editForm.phone}
                    onChange={(e) => setEditForm({...editForm, phone: e.target.value})}
                    className={styles.formInput}
                    placeholder="+1234567890"
                  />
                </div>
  
                <div className={styles.formGroup}>
                  <label className={styles.formLabel}>
                    <RiLockLine className={styles.formIcon} />
                    New Password
                  </label>
                  <input
                    type="password"
                    value={editForm.password}
                    onChange={(e) => setEditForm({...editForm, password: e.target.value})}
                    className={styles.formInput}
                    placeholder="Leave empty to keep unchanged"
                    minLength={6}
                  />
                  {editForm.password && editForm.password.length < 6 && (
                    <span className={styles.formError}>
                      Password must be at least 6 characters
                    </span>
                  )}
                </div>
  
                <div className={styles.formGroup}>
                  <label className={styles.formLabel}>
                    <RiGlobalLine className={styles.formIcon} />
                    Country
                  </label>
                  <input
                    type="text"
                    value={editForm.country}
                    onChange={(e) => setEditForm({...editForm, country: e.target.value})}
                    className={styles.formInput}
                    placeholder="e.g., USA, Russia, Germany"
                  />
                </div>
  
                <div className={styles.formGroup}>
                  <div className={styles.checkboxRow}>
                    <label className={styles.checkboxLabel}>
                      <input
                        type="checkbox"
                        checked={editForm.isActive}
                        onChange={(e) => setEditForm({...editForm, isActive: e.target.checked})}
                        className={styles.checkbox}
                      />
                      <span className={styles.checkboxText}>Active User</span>
                    </label>
                    
                    <label className={styles.checkboxLabel}>
                      <input
                        type="checkbox"
                        checked={editForm.isVerified}
                        onChange={(e) => setEditForm({...editForm, isVerified: e.target.checked})}
                        className={styles.checkbox}
                      />
                      <span className={styles.checkboxText}>Verified</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div className={styles.modalFooter}>
            <button onClick={handleCloseModals} className={styles.cancelBtn}>Cancel</button>
            <button onClick={handleSaveUser} className={styles.saveBtn}>
              <RiSaveLine /> Save Profile
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  const renderDeleteConfirmModal = () => {
    if (!showDeleteConfirm || !selectedUser) return null;
  
    return (
      <div className={styles.modalBackdrop}>
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <h2>Confirm Deletion</h2>
            <button onClick={handleCloseModals} className={styles.closeButton}>×</button>
          </div>
          
          <div className={styles.modalBody}>
            <div className={styles.deleteConfirmContent}>
              <RiErrorWarningLine className={styles.warningIcon} />
              <p>Are you sure you want to delete user <strong>{selectedUser.email}</strong>?</p>
              <p className={styles.warningText}>This action cannot be undone!</p>
            </div>
          </div>
          
          <div className={styles.modalFooter}>
            <button onClick={handleCloseModals} className={styles.cancelBtn}>Cancel</button>
            <button onClick={handleDeleteUser} className={styles.deleteBtn}>
              <RiDeleteBinLine /> Delete
            </button>
          </div>
        </div>
      </div>
    );
  };
  
  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <div className={styles.titleSection}>
          <h1 className={styles.title}>User Management</h1>
          
          <div className={styles.statsOverview}>
            <div className={styles.statCard}>
              <div className={styles.statIcon}><RiTeamLine /></div>
              <div className={styles.statInfo}>
                <div className={styles.statValue}>{formatNumber(userStats.totalUsers)}</div>
                <div className={styles.statLabel}>Total Users</div>
              </div>
            </div>
            
            <div className={styles.statCard}>
              <div className={styles.statIcon}><RiUserLine /></div>
              <div className={styles.statInfo}>
                <div className={styles.statValue}>{formatNumber(userStats.activeUsers)}</div>
                <div className={styles.statLabel}>Active</div>
              </div>
            </div>
            
            <div className={styles.statCard}>
              <div className={styles.statIcon}><RiShieldCheckLine /></div>
              <div className={styles.statInfo}>
                <div className={styles.statValue}>{formatNumber(userStats.verifiedUsers)}</div>
                <div className={styles.statLabel}>Verified</div>
              </div>
            </div>
            
            <div className={styles.statCard}>
              <div className={styles.statIcon}><RiCalendarLine /></div>
              <div className={styles.statInfo}>
                <div className={styles.statValue}>{formatNumber(userStats.newUsersToday)}</div>
                <div className={styles.statLabel}>New Today</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className={styles.actionsRow}>
        <form onSubmit={handleSearch} className={styles.searchForm}>
          <div className={styles.searchContainer}>
            <RiSearchLine className={styles.searchIcon} />
            <input
              type="text"
              placeholder="Search by email or ID..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
          <button type="submit" className={styles.searchButton}>Search</button>
        </form>
        
        <button 
          className={styles.refreshButton}
          onClick={() => {
            fetchUsers();
            fetchUserStats();
          }}
          title="Refresh Data"
        >
          <RiRefreshLine />
        </button>
      </div>
      
      {error && <div className={styles.errorMessage}>❌ {error}</div>}
      
      {isLoading ? (
        <div className={styles.loadingContainer}>
          <RiLoader4Line className={styles.loadingSpinner} />
          <p>Loading users...</p>
        </div>
      ) : (
        <>
          <div className={styles.contentContainer}>
            {renderUsersTable()}
          </div>
          
          {users.length > 0 && (
            <div className={styles.pagination}>
              <button
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
                className={styles.paginationButton}
              >
                &lt; Previous
              </button>
              <span className={styles.pageInfo}>
                Page {page} of {totalPages}
              </span>
              <button
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className={styles.paginationButton}
              >
                Next &gt;
              </button>
            </div>
          )}
        </>
      )}
      
      {renderDetailModal()}
      {renderEditModal()}
      {renderBalanceEditModal()}
      {renderDeleteConfirmModal()}
    </div>
  );
  }