.container {
    padding: 1.5rem;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
  }
  
  .title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1a1a2e;
    margin: 0 0 1rem 0;
  }
  
  /* СТАТИСТИКА В ОДНУ СТРОКУ - ОСНОВНОЕ ИЗМЕНЕНИЕ */
  .statsOverview {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 1rem;
    margin-bottom: 1.5rem;
    /* УБИРАЕМ margin-top, добавляем bottom для отступа от таблицы */
  }
  
  .statCard {
    background-color: white;
    padding: 1.25rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.2s ease;
    min-width: 0; /* Позволяет элементам сжиматься */
  }
  
  .statCard:hover {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
  
  .statIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
    border-radius: 0.5rem;
    font-size: 1.25rem;
    flex-shrink: 0;
  }
  
  .statInfo {
    flex: 1;
    min-width: 0;
  }
  
  .statValue {
    font-size: 1.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
  }
  
  .statLabel {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: 500;
    line-height: 1.2;
  }
  
  .actionsRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .searchForm {
    display: flex;
    gap: 0.75rem;
  }
  
  .searchContainer {
    position: relative;
    width: 320px;
  }
  
  .searchIcon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
  }
  
  .searchInput {
    width: 100%;
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.95rem;
  }
  
  .searchButton {
    padding: 0.625rem 1.25rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .searchButton:hover {
    background-color: #4338ca;
  }
  
  .refreshButton {
    display: flex;
    align-items: center;  
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: #f3f4f6;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    color: #4b5563;
    transition: all 0.2s;
  }
  
  .refreshButton:hover {
    background-color: #e5e7eb;
  }
  
  .errorMessage {
    padding: 1rem;
    background-color: #fee2e2;  
    color: #b91c1c;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
  }
  
  .loadingSpinner {
    font-size: 2rem;
    color: #4f46e5;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  /* УЛУЧШЕННАЯ ТАБЛИЦА С ЛУЧШИМ РАСПРЕДЕЛЕНИЕМ КОЛОНОК */
  .tableContainer {
    width: 100%;
    background-color: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    border: 1px solid #f1f5f9;
    overflow: hidden; /* Добавляем для лучшего внешнего вида */
  }
  
  .table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
  }
  
  .table th {
    padding: 1rem 0.75rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 700;
    color: #374151;
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border-bottom: 2px solid #e2e8f0;
    text-transform: uppercase;
    letter-spacing: 0.05em;
  }
  
  /* УЛУЧШЕННОЕ РАСПРЕДЕЛЕНИЕ ШИРИН КОЛОНОК */  
  .table th:nth-child(1) { width: 28%; } /* Пользователь - уменьшили */
  .table th:nth-child(2) { width: 13%; } /* Баланс - увеличили */
  .table th:nth-child(3) { width: 12%; } /* Страна */
  .table th:nth-child(4) { width: 15%; } /* Статус */
  .table th:nth-child(5) { width: 13%; } /* Регистрация - увеличили */
  .table th:nth-child(6) { width: 13%; } /* Последний вход - увеличили */
  .table th:nth-child(7) { width: 12%; } /* Действия - УВЕЛИЧИЛИ для кнопок */
  
  .table td {
    padding: 0.875rem 0.75rem;
    font-size: 0.9rem;
    color: #1f2937;
    border-bottom: 1px solid #f1f5f9;
    vertical-align: middle;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  
  .table tr:last-child td {
    border-bottom: none;
  }
  
  .table tr:hover {
    background-color: #fafbfc;
    transition: background-color 0.2s;
  }
  
  .table tbody tr {
    transition: all 0.2s ease;
  }
  
  .table tbody tr:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }
  
  /* КОМПАКТНАЯ ячейка пользователя */
  .userInfoCell {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem;
    min-width: 0;
  }
  
  .userAvatar {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 50%;
    font-size: 1rem;
    color: white;
    font-weight: 600;
    flex-shrink: 0;
  }
  
  .verifiedBadge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 0.875rem;
    height: 0.875rem;
    background-color: #10b981;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.5rem;
    border: 2px solid white;
  }
  
  .userDetails {
    flex: 1;
    min-width: 0;
    overflow: hidden;
  }
  
  .userEmail {
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  
  .userMeta {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.75rem;
    color: #6b7280;
    overflow: hidden;
  }
  
  .userId {
    font-weight: 600;
    color: #4f46e5;
    background-color: #f0f4ff;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-size: 0.7rem;
    flex-shrink: 0;
  }
  
  .userName {
    color: #6b7280;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.75rem;
  }
  
  /* Улучшенная ячейка баланса */
  .balanceCell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
  }
  
  .balanceIcon {
    color: #10b981;
    font-size: 1.125rem;
    flex-shrink: 0;
  }
  
  .balanceAmount {
    font-weight: 700;
    color: #10b981;
    font-size: 0.95rem;
  }
  
  .balanceCurrency {
    font-size: 0.7rem;
    color: #6b7280;
    font-weight: 500;
    background-color: #f3f4f6;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
  }
  
  /* Улучшенная ячейка страны */
  .countryCell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem;
  }
  
  .countryIcon {
    color: #6b7280;
    font-size: 1rem;
    flex-shrink: 0;
  }
  
  .ipAddress {
    color: #9ca3af;
    font-size: 0.875rem;
    cursor: help;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s;
  }
  
  .ipAddress:hover {
    background-color: #f3f4f6;
    color: #4b5563;
  }
  
  /* Улучшенная ячейка статуса */
  .statusCell {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
    padding: 0.5rem;
  }
  
  .statusBadge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.7rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.025em;
  }
  
  .statusActive {
    background-color: #dcfce7;
    color: #166534;
    border: 1px solid #bbf7d0;
  }
  
  .statusVerified {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
  }
  
  .statusBlocked {
    background-color: #fee2e2;
    color: #991b1b;
    border: 1px solid #fecaca;
  }
  
  .verifiedIndicator {
    color: #10b981;
    font-size: 0.875rem;
    cursor: help;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s;
  }
  
  .verifiedIndicator:hover {
    background-color: #f0fdf4;
  }
  
  .twoFactorIndicator {
    color: #f59e0b;
    font-size: 0.875rem;
    cursor: help;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: all 0.2s;
  }
  
  .twoFactorIndicator:hover {
    background-color: #fffbeb;
  }
  
  /* УЛУЧШЕННЫЕ ДЕЙСТВИЯ - БОЛЬШЕ МЕСТА И ЛУЧШЕ РАЗМЕЩЕНИЕ */
  .actionsCell {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
    align-items: center;
    padding: 0.75rem 0.5rem;
  }
  
  .actionButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
  }
  
  .actionButton:hover {
    background-color: rgba(79, 70, 229, 0.2);
    color: #4338ca;
    transform: translateY(-1px);
  }
  
  .actionButton:active {
    transform: translateY(0);
  }
  
  .deleteButton {
    background-color: rgba(220, 38, 38, 0.1);
    color: #dc2626;
  }
  
  .deleteButton:hover {
    background-color: rgba(220, 38, 38, 0.2);
    color: #b91c1c;
  }
  
  /* Пагинация */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
  }
  
  .paginationButton {
    padding: 0.5rem 1rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .paginationButton:hover:not(:disabled) {
    background-color: #f3f4f6;
  }
  
  .paginationButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .pageInfo {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  /* Пустое состояние */
  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .emptyStateIcon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1.5rem;
  }
  
  /* Модальные окна */
  .modalBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modalContent {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
  }
  
  .modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .modalHeader h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }
  
  .closeButton {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .closeButton:hover {
    color: #1f2937;
  }
  
  .modalBody {
    padding: 1.5rem;
  }
  
  /* Профиль пользователя в модальном окне */
  .userProfileSection {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
  }
  
  .userBasicInfo h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }
  
  .userBasicInfo p {
    color: #6b7280;
    margin: 0 0 0.75rem 0;
  }
  
  /* Детальная информация */
  .detailInfo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }
  
  .detailColumn h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .detailRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
  }
  
  .detailLabel {
    font-size: 0.95rem;
    color: #6b7280;
  }
  
  .detailValue {
    font-size: 0.95rem;
    font-weight: 500;
    color: #1f2937;
  }
  
  .positiveValue {
    color: #10b981;
  }
  
  .negativeValue {
    color: #ef4444;
  }
  
  .verifiedBadge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #10b981;
    font-size: 0.875rem;
  }
  
  .unverifiedBadge {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    color: #f59e0b;
    font-size: 0.875rem;
  }
  
  /* Улучшенная форма редактирования */
  .editFormGrid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  
  .editFormColumn {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .editFormColumn h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
  }
  
  .formRow {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
  }
  
  .checkboxRow {
    display: flex;
    gap: 1.5rem;
  }
  
  .formHint {
    font-size: 0.75rem;
    color: #6b7280;
    margin-top: 0.25rem;
    font-style: italic;
  }
  
  .updateInfo {
    background-color: #f0f9ff;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #3b82f6;
  }
  
  .updateInfo h4 {
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 0.5rem 0;
  }
  
  .updateInfo ul {
    margin: 0;
    padding-left: 1rem;
    font-size: 0.75rem;
    color: #4b5563;
  }
  
  .updateInfo li {
    margin-bottom: 0.25rem;
  }
  
  .formGroup {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .formLabel {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    color: #4b5563;
  }
  
  .formIcon {
    color: #6b7280;
    font-size: 1rem;
  }
  
  .formInput {
    padding: 0.75rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.95rem;
    transition: border-color 0.2s;
  }
  
  .formInput:focus {
    outline: none;
    border-color: #4f46e5;
    box-shadow: 0 0 0 1px #4f46e5;
  }
  
  .checkboxLabel {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
  }
  
  .checkbox {
    width: 1rem;
    height: 1rem;
  }
  
  .checkboxText {
    font-size: 0.95rem;
    color: #4b5563;
  }
  
  /* Подтверждение удаления */
  .deleteConfirmContent {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 2rem;
  }
  
  .warningIcon {
    font-size: 3rem;
    color: #f59e0b;
    margin-bottom: 1rem;
  }
  
  .deleteConfirmContent p {
    font-size: 1rem;
    color: #4b5563;
    margin: 0 0 0.5rem 0;
  }
  
  .warningText {
    font-size: 0.875rem;
    color: #ef4444;
    font-weight: 500;
  }
  
  /* Футер модального окна */
  .modalFooter {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }
  
  .closeBtn {
    padding: 0.625rem 1.25rem;
    background-color: #f3f4f6;
    color: #4b5563;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .closeBtn:hover {
    background-color: #e5e7eb;
  }
  
  .cancelBtn {
    padding: 0.625rem 1.25rem;
    background-color: #f3f4f6;
    color: #4b5563;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .cancelBtn:hover {
    background-color: #e5e7eb;
  }
  
  .saveBtn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    background-color: #10b981;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .saveBtn:hover {
    background-color: #059669;
  }
  
  .deleteBtn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    background-color: #dc2626;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .deleteBtn:hover {
    background-color: #b91c1c;
  }
  
  /* Дополнительные стили для улучшения UX */
  .contentContainer {
    margin-bottom: 2rem;
  }
  
  .actionButton:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
  }
  
  .formInput:invalid {
    border-color: #ef4444;
  }
  
  /* Анимации */
  .modalBackdrop {
    animation: fadeIn 0.2s ease-out;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  .modalContent {
    animation: slideIn 0.3s ease-out;
  }
  
  @keyframes slideIn {
    from {
      transform: translateY(-20px);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
  
  /* АДАПТИВНОСТЬ */
  @media (max-width: 1200px) {
    .statsOverview {
      grid-template-columns: repeat(2, 1fr);
    }
  }
  
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .statsOverview {
      grid-template-columns: repeat(2, 1fr);
      gap: 0.75rem;
    }
    
    .statCard {
      padding: 1rem;
    }
    
    .statValue {
        font-size: 1.25rem;
    }
    
    .statLabel {
      font-size: 0.8rem;
    }
    
    .actionsRow {
      flex-direction: column;
      gap: 1rem;
    }
    
    .searchForm {
      width: 100%;
    }
    
    .searchContainer {
      width: 100%;
    }
    
    .table {
      font-size: 0.85rem;
    }
    
    .table th,
    .table td {
      padding: 0.75rem 0.5rem;
    }
    
    /* Для мобильных устройств делаем колонки более гибкими */
    .table th:nth-child(1) { width: 35%; } /* Пользователь - больше места */
    .table th:nth-child(2) { width: 15%; } /* Баланс */
    .table th:nth-child(3) { width: 10%; } /* Страна - меньше */
    .table th:nth-child(4) { width: 12%; } /* Статус */
    .table th:nth-child(5) { width: 10%; } /* Регистрация - меньше */
    .table th:nth-child(6) { width: 10%; } /* Последний вход - меньше */
    .table th:nth-child(7) { width: 15%; } /* Действия - больше для кнопок */
    
    .userInfoCell {
      gap: 0.5rem;
    }
    
    .userAvatar {
      width: 2.25rem;
      height: 2.25rem;
      font-size: 0.9rem;
    }
    
    .userEmail {
      font-size: 0.85rem;
    }
    
    .userMeta {
      gap: 0.25rem;
    }
    
    .userId {
      font-size: 0.65rem;
    }
    
    .userName {
      font-size: 0.7rem;
    }
    
    .balanceAmount {
      font-size: 0.85rem;
    }
    
    .balanceCurrency {
      font-size: 0.65rem;
    }
    
    .statusBadge {
      font-size: 0.65rem;
      padding: 0.2rem 0.4rem;
    }
    
    .actionButton {
      width: 1.75rem;
      height: 1.75rem;
      font-size: 0.8rem;
    }
    
    .actionsCell {
      gap: 0.375rem;
      padding: 0.5rem 0.25rem;
    }
    
    .editFormGrid {
      grid-template-columns: 1fr;
      gap: 1.5rem;
    }
    
    .formRow {
      grid-template-columns: 1fr;
      gap: 1rem;
    }
    
    .checkboxRow {
      flex-direction: column;
      gap: 1rem;
    }
    
    .modalContent {
      width: 95%;
      max-height: 95vh;
    }
    
    .userProfileSection {
      flex-direction: column;
      text-align: center;
    }
    
    .modalFooter {
      flex-direction: column;
    }
    
    .modalFooter button {
      width: 100%;
    }
  }
  
  @media (max-width: 480px) {
    .container {
      padding: 1rem;
    }
    
    .title {
      font-size: 1.5rem;
    }
    
    .statsOverview {
      grid-template-columns: 1fr;
    }
    
    .statCard {
      padding: 0.875rem;
    }
    
    .statIcon {
      width: 2rem;
      height: 2rem;
      font-size: 1rem;
    }
    
    .statValue {
      font-size: 1.125rem;
    }
    
    .table {
      font-size: 0.8rem;
    }
    
    .table th,
    .table td {
      padding: 0.5rem 0.375rem;
    }
    
    .userAvatar {
      width: 2rem;
      height: 2rem;
      font-size: 0.8rem;
    }
    
    .userEmail {
      font-size: 0.8rem;
    }
    
    .userMeta {
      flex-direction: column;
      gap: 0.125rem;
      align-items: flex-start;
    }
    
    .balanceCell {
      flex-direction: column;
      gap: 0.25rem;
      align-items: flex-start;
    }
    
    .countryCell {
      flex-direction: column;
      gap: 0.25rem;
      align-items: flex-start;
    }
    
    .statusCell {
      flex-direction: column;
      gap: 0.25rem;
      align-items: flex-start;
    }
    
    .actionButton {
      width: 1.5rem;
      height: 1.5rem;
      font-size: 0.75rem;
    }
    
    .actionsCell {
      flex-direction: column;
      gap: 0.25rem;
    }
  }
  
  /* Дополнительные улучшения для очень широких экранов */
  @media (min-width: 1400px) {
    .container {
      max-width: 1400px;
      margin: 0 auto;
    }
    
    .statsOverview {
      grid-template-columns: repeat(4, 1fr);
      max-width: 1200px;
    }
    
    .table th:nth-child(1) { width: 25%; } /* Пользователь */
    .table th:nth-child(2) { width: 12%; } /* Баланс */
    .table th:nth-child(3) { width: 12%; } /* Страна */
    .table th:nth-child(4) { width: 15%; } /* Статус */
    .table th:nth-child(5) { width: 15%; } /* Регистрация */
    .table th:nth-child(6) { width: 15%; } /* Последний вход */
    .table th:nth-child(7) { width: 14%; } /* Действия - еще больше места */
    
    .actionButton {
      width: 2.25rem;
      height: 2.25rem;
      font-size: 1rem;
    }
    
    .actionsCell {
      gap: 0.75rem;
    }
  }
  
  /* Дополнительные стили для улучшения читаемости */
  .table td {
    line-height: 1.4;
  }
  
  .userEmail {
    line-height: 1.3;
  }
  
  .userMeta {
    line-height: 1.2;
  }
  
  /* Улучшения для печати */
  @media print {
    .actionsCell,
    .refreshButton,
    .searchForm,
    .pagination {
      display: none;
    }
    
    .table {
      font-size: 0.8rem;
    }
    
    .statsOverview {
      grid-template-columns: repeat(4, 1fr);
      gap: 0.5rem;
      margin-bottom: 1rem;
    }
    
    .statCard {
      padding: 0.75rem;
      box-shadow: none;
      border: 1px solid #e5e7eb;
    }
  }
  
  /* Дополнительные состояния для интерактивности */
  .actionButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none;
  }
  
  .actionButton:disabled:hover {
    background-color: rgba(79, 70, 229, 0.1);
    transform: none;
  }
  
  .table tr:active {
    background-color: #f3f4f6;
  }
  
  /* Улучшения для скроллинга на мобильных */
  @media (max-width: 768px) {
    .tableContainer {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
    }
    
    .table {
      min-width: 800px; /* Минимальная ширина таблицы для горизонтального скролла */
    }
  }
  
  /* Состояния загрузки для кнопок */
  .actionButton.loading {
    position: relative;
    color: transparent;
  }
  
  .actionButton.loading::after {
    content: '';
    position: absolute;
    width: 1rem;
    height: 1rem;
    border: 2px solid #f3f4f6;
    border-top: 2px solid #4f46e5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  /* Выделение строк при фокусе */
  .table tr:focus-within {
    background-color: #f0f9ff;
    outline: 2px solid #3b82f6;
    outline-offset: -2px;
  }
  
  /* Улучшения для высококонтрастного режима */
  @media (prefers-contrast: high) {
    .statusBadge {
      border-width: 2px;
    }
    
    .actionButton {
      border: 1px solid currentColor;
    }
    
    .table th {
      border-bottom: 3px solid #374151;
    }
  }
  
  /* Улучшения для анимаций при предпочтении пользователя */
  @media (prefers-reduced-motion: reduce) {
    .actionButton,
    .statCard,
    .table tbody tr,
    .modalBackdrop,
    .modalContent,
    .loadingSpinner {
      animation: none;
      transition: none;
    }
    
    .actionButton:hover,
    .statCard:hover,
    .table tbody tr:hover {
      transform: none;
    }
  }




  /* Добавить эти стили в page.module.css для поддержки позиций */

/* Секция позиций в детальном окне */
.positionsDetailSection {
  margin-top: 2rem;
  padding: 1.5rem;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.positionsDetailSection h3 {
  margin-bottom: 1rem;
  color: #1e293b;
  font-size: 1.25rem;
  font-weight: 600;
}

/* Секция позиций */
.positionsSection {
  width: 100%;
}

/* Статистика позиций */
.positionsStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.statItem {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.statIcon {
  font-size: 1.5rem;
  color: #64748b;
}

.statIcon.positive {
  color: #10b981;
}

.statIcon.negative {
  color: #ef4444;
}

.statValue {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.statLabel {
  font-size: 0.875rem;
  color: #64748b;
}

/* Значения с цветовой индикацией */
.positiveValue {
  color: #10b981 !important;
}

.negativeValue {
  color: #ef4444 !important;
}

/* Вкладки позиций */
.positionsTabs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.positionsTab {
  background: white;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.positionsTab h4 {
  padding: 1rem;
  margin: 0;
  background: #f1f5f9;
  border-bottom: 1px solid #e2e8f0;
  color: #1e293b;
  font-size: 1rem;
  font-weight: 600;
}

/* Таблица позиций */
.positionsTable {
  max-height: 300px;
  overflow-y: auto;
}

.positionRow {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f1f5f9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: background-color 0.2s;
}

.positionRow:hover {
  background: #f8fafc;
}

.positionRow:last-child {
  border-bottom: none;
}

.positionInfo {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.tradingPair {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.direction {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  text-transform: uppercase;
  display: inline-block;
  width: fit-content;
}

.direction.long {
  background: #dcfce7;
  color: #166534;
}

.direction.short {
  background: #fee2e2;
  color: #991b1b;
}

.status {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.125rem 0.5rem;
  border-radius: 4px;
  background: #f1f5f9;
  color: #64748b;
  display: inline-block;
  width: fit-content;
  margin-top: 0.25rem;
}

.positionData {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.positionData span {
  font-size: 0.75rem;
  color: #64748b;
}

.morePositions {
  padding: 1rem;
  text-align: center;
  color: #64748b;
  font-size: 0.875rem;
  font-style: italic;
  margin: 0;
  border-top: 1px solid #f1f5f9;
}

/* Загрузка в секции позиций */
.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: #64748b;
}

.loadingSpinner {
  font-size: 2rem;
  animation: spin 1s linear infinite;
  margin-bottom: 0.5rem;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .positionsTabs {
    grid-template-columns: 1fr;
  }
  
  .positionsStats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }
  
  .positionRow {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .positionData {
    align-items: flex-start;
    width: 100%;
  }
}