"use client";

import React, { useState, useEffect } from "react";
import { getAllReferalsForAdmin, getReferalStatistics, getReferalDetail, getActiveReferrers, Referal, ReferalStatistics, ReferalPaginatedResponse } from "@/app/utils/api";
import styles from "./page.module.css";
import { format } from "date-fns";
import { enUS } from "date-fns/locale";
import { 
  RiFilter2Line,
  RiSearchLine,
  RiRefreshLine,
  RiCalendarLine,
  RiUserLine,
  RiLinkM,
  RiExchangeDollarLine,
  RiPercentLine,
  RiBarChartFill,
  RiLoader4Line,
  RiTrophyLine,
  RiTeamLine,
  RiMoneyDollarCircleLine,
  RiCoinLine
} from "react-icons/ri";

export default function ReferralsPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'referrers' | 'all'>('overview');
  const [referals, setReferals] = useState<Referal[]>([]);
  const [referrers, setReferrers] = useState<Referal[]>([]);
  const [statistics, setStatistics] = useState<ReferalStatistics | null>(null);
  const [selectedReferal, setSelectedReferal] = useState<Referal | null>(null);
  const [referalDetail, setReferalDetail] = useState<{ referal: Referal, referrals: Referal[] } | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [referrersPage, setReferrersPage] = useState(1);
  const [referrersTotalPages, setReferrersTotalPages] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1);
  const [searchTerm, setSearchTerm] = useState("");
  const [showDetailModal, setShowDetailModal] = useState(false);

  // Load data on mount and filter changes
  useEffect(() => {
    fetchData();
  }, [activeTab, page, referrersPage, selectedYear, selectedMonth]);

  // Function to fetch data
  const fetchData = async () => {
    setIsLoading(true);
    setError("");
    
    try {
      if (activeTab === 'overview') {
        const stats = await getReferalStatistics(selectedYear, selectedMonth);
        setStatistics(stats);
      } else if (activeTab === 'referrers') {
        const response = await getActiveReferrers(referrersPage, 20, selectedYear, selectedMonth);
        setReferrers(response.items || []);
        setReferrersTotalPages(response.pages || 1);
      } else {
        const response = await getAllReferalsForAdmin(page, 20, selectedYear, selectedMonth);
        setReferals(response.items || []);
        setTotalPages(response.pages || 1);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while loading data');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to open modal with detailed referral information
  const handleOpenDetail = async (referal: Referal) => {
    setSelectedReferal(referal);
    setIsLoading(true);
    
    try {
      const detail = await getReferalDetail(referal.userId, selectedYear, selectedMonth);
      setReferalDetail(detail);
      setShowDetailModal(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Error getting detailed information');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to close modal
  const handleCloseDetail = () => {
    setShowDetailModal(false);
    setReferalDetail(null);
    setSelectedReferal(null);
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'dd MMMM yyyy', { locale: enUS });
    } catch (e) {
      return dateString;
    }
  };

  // Format number with thousand separators
  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US', { maximumFractionDigits: 2 }).format(num);
  };

  // Search handler
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    setReferrersPage(1);
    fetchData();
  };

  // Get CSS class for tier level
  const getTierClass = (tierLevel: number) => {
    switch (tierLevel) {
      case 1: return styles.tier1;
      case 2: return styles.tier2;
      case 3: return styles.tier3;
      default: return styles.tier0;
    }
  };

  // Render overview screen
  const renderOverview = () => {
    if (!statistics) return null;

    return (
      <div className={styles.overviewContainer}>
        <div className={styles.statsRow}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <RiTeamLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>{formatNumber(statistics.totalReferals)}</div>
              <div className={styles.statLabel}>Total Referrals</div>
            </div>
          </div>
          
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <RiUserLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>{formatNumber(statistics.activeReferrers)}</div>
              <div className={styles.statLabel}>Active Referrers</div>
            </div>
          </div>
          
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <RiExchangeDollarLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>${formatNumber(statistics.totalVolume)}</div>
              <div className={styles.statLabel}>Total Trading Volume</div>
            </div>
          </div>
          
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <RiCoinLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>${formatNumber(statistics.totalCommission)}</div>
              <div className={styles.statLabel}>Total Commission</div>
            </div>
          </div>
        </div>
        
        <div className={styles.statsRow}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>
              <RiMoneyDollarCircleLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>${formatNumber(statistics.totalReward)}</div>
              <div className={styles.statLabel}>Total Payouts</div>
            </div>
          </div>
          
          <div className={styles.tierStats}>
            <div className={styles.tierTitle}>
              <RiTrophyLine /> Referrer Levels
            </div>
            <div className={styles.tierBars}>
              <div className={styles.tierBar}>
                <div className={styles.tierBarLabel}>Tier 1 (20%)</div>
                <div className={styles.tierBarContainer}>
                  <div 
                    className={`${styles.tierBarFill} ${styles.tier1}`} 
                    style={{ width: `${statistics.tierStatistics.tier1 / (statistics.tierStatistics.tier1 + statistics.tierStatistics.tier2 + statistics.tierStatistics.tier3 || 1) * 100}%` }}
                  ></div>
                </div>
                <div className={styles.tierBarValue}>{statistics.tierStatistics.tier1}</div>
              </div>
              
              <div className={styles.tierBar}>
                <div className={styles.tierBarLabel}>Tier 2 (40%)</div>
                <div className={styles.tierBarContainer}>
                  <div 
                    className={`${styles.tierBarFill} ${styles.tier2}`} 
                    style={{ width: `${statistics.tierStatistics.tier2 / (statistics.tierStatistics.tier1 + statistics.tierStatistics.tier2 + statistics.tierStatistics.tier3 || 1) * 100}%` }}
                  ></div>
                </div>
                <div className={styles.tierBarValue}>{statistics.tierStatistics.tier2}</div>
              </div>
              
              <div className={styles.tierBar}>
                <div className={styles.tierBarLabel}>Tier 3 (60%)</div>
                <div className={styles.tierBarContainer}>
                  <div 
                    className={`${styles.tierBarFill} ${styles.tier3}`} 
                    style={{ width: `${statistics.tierStatistics.tier3 / (statistics.tierStatistics.tier1 + statistics.tierStatistics.tier2 + statistics.tierStatistics.tier3 || 1) * 100}%` }}
                  ></div>
                </div>
                <div className={styles.tierBarValue}>{statistics.tierStatistics.tier3}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render active referrers table
  const renderReferrers = () => {
    if (referrers.length === 0) {
      return (
        <div className={styles.emptyState}>
          <RiTeamLine className={styles.emptyStateIcon} />
          <p>No active referrers for the selected period</p>
        </div>
      );
    }

    return (
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>ID</th>
              <th>Referral Code</th>
              <th>Number of Referrals</th>
              <th>Trading Volume</th>
              <th>Commission</th>
              <th>Level</th>
              <th>Percentage</th>
              <th>Reward</th>
              <th>Projection</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {referrers.map(referrer => (
              <tr key={referrer.id}>
                <td>{referrer.userId}</td>
                <td>{referrer.referralCode}</td>
                <td>
                  <div className={styles.referralsCounts}>
                    <span>{referrer.totalReferrals}</span>
                    <span className={styles.activeBadge}>{referrer.activeReferrals} active</span>
                  </div>
                </td>
                <td>${formatNumber(referrer.totalTradingVolume)}</td>
                <td>${formatNumber(referrer.totalCommission)}</td>
                <td>
                  <span className={`${styles.tierBadge} ${getTierClass(referrer.tierLevel)}`}>
                    {referrer.tierLevel > 0 ? `Tier ${referrer.tierLevel}` : 'No Level'}
                  </span>
                </td>
                <td>{formatNumber(referrer.rewardPercentage)}%</td>
                <td>${formatNumber(referrer.rewardAmount)}</td>
                <td>
                  {referrer.projectedTierLevel > referrer.tierLevel ? (
                    <div className={styles.projectionUp}>
                      <span className={`${styles.tierBadge} ${getTierClass(referrer.projectedTierLevel)}`}>
                        Tier {referrer.projectedTierLevel}
                      </span>
                      <span className={styles.projectionValue}>
                        ${formatNumber(referrer.projectedRewardAmount)}
                      </span>
                    </div>
                  ) : (
                    <span>${formatNumber(referrer.projectedRewardAmount)}</span>
                  )}
                </td>
                <td>
                  <button
                    onClick={() => handleOpenDetail(referrer)}
                    className={styles.detailButton}
                    title="Details"
                  >
                    <RiBarChartFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Render all referrals table
  const renderAllReferals = () => {
    if (referals.length === 0) {
      return (
        <div className={styles.emptyState}>
          <RiLinkM className={styles.emptyStateIcon} />
          <p>No referrals for the selected period</p>
        </div>
      );
    }

    return (
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>ID</th>
              <th>Referral Code</th>
              <th>Referrer ID</th>
              <th>Trading Volume</th>
              <th>Number of Orders</th>
              <th>Commission</th>
              <th>Status</th>
              <th>Registration Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {referals.map(referal => (
              <tr key={referal.id}>
                <td>{referal.userId}</td>
                <td>{referal.referralCode}</td>
                <td>{referal.referrerId || '-'}</td>
                <td>${formatNumber(referal.totalTradingVolume)}</td>
                <td>{referal.totalOrdersCount}</td>
                <td>${formatNumber(referal.totalCommission)}</td>
                <td>
                  <span className={`${styles.statusBadge} ${referal.totalOrdersCount > 0 ? styles.statusActive : styles.statusInactive}`}>
                    {referal.totalOrdersCount > 0 ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td>{formatDate(referal.createdAt)}</td>
                <td>
                  <button
                    onClick={() => handleOpenDetail(referal)}
                    className={styles.detailButton}
                    title="Details"
                  >
                    <RiBarChartFill />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Render modal with detailed information
  const renderDetailModal = () => {
    if (!showDetailModal || !referalDetail) return null;

    const { referal, referrals } = referalDetail;

    return (
      <div className={styles.modalBackdrop}>
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <h2>Detailed Referral Information</h2>
            <button onClick={handleCloseDetail} className={styles.closeButton}>×</button>
          </div>
          
          <div className={styles.modalBody}>
            <div className={styles.detailInfo}>
              <div className={styles.detailColumn}>
                <h3>Basic Information</h3>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>User ID:</span>
                  <span className={styles.detailValue}>{referal.userId}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Referral Code:</span>
                  <span className={styles.detailValue}>{referal.referralCode}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Referrer ID:</span>
                  <span className={styles.detailValue}>{referal.referrerId || '-'}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Total Referrals:</span>
                  <span className={styles.detailValue}>{referal.totalReferrals}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Active Referrals:</span>
                  <span className={styles.detailValue}>{referal.activeReferrals}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Registration Date:</span>
                  <span className={styles.detailValue}>{formatDate(referal.createdAt)}</span>
                </div>
              </div>
              
              <div className={styles.detailColumn}>
                <h3>Trading Metrics</h3>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Trading Volume:</span>
                  <span className={styles.detailValue}>${formatNumber(referal.totalTradingVolume)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Number of Orders:</span>
                  <span className={styles.detailValue}>{referal.totalOrdersCount}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Commission:</span>
                  <span className={styles.detailValue}>${formatNumber(referal.totalCommission)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Level (Tier):</span>
                  <span className={`${styles.detailValue} ${getTierClass(referal.tierLevel)}`}>
                    {referal.tierLevel > 0 ? `Tier ${referal.tierLevel}` : 'No Level'}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Reward Percentage:</span>
                  <span className={styles.detailValue}>{formatNumber(referal.rewardPercentage)}%</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Reward Amount:</span>
                  <span className={styles.detailValue}>${formatNumber(referal.rewardAmount)}</span>
                </div>
              </div>
              
              <div className={styles.detailColumn}>
                <h3>Projected Metrics</h3>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Projected Volume:</span>
                  <span className={styles.detailValue}>${formatNumber(referal.projectedTotalVolume)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Projected Commission:</span>
                  <span className={styles.detailValue}>${formatNumber(referal.projectedTotalCommission)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Projected Level:</span>
                  <span className={`${styles.detailValue} ${getTierClass(referal.projectedTierLevel)}`}>
                    {referal.projectedTierLevel > 0 ? `Tier ${referal.projectedTierLevel}` : 'No Level'}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Projected Percentage:</span>
                  <span className={styles.detailValue}>{formatNumber(referal.projectedRewardPercentage)}%</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Projected Reward:</span>
                  <span className={styles.detailValue}>${formatNumber(referal.projectedRewardAmount)}</span>
                </div>
              </div>
            </div>
            
            {referrals && referrals.length > 0 && (
              <div className={styles.referralsSection}>
                <h3>User's Referrals ({referrals.length})</h3>
                <table className={styles.referralsTable}>
                  <thead>
                    <tr>
                      <th>ID</th>
                      <th>Referral Code</th>
                      <th>Trading Volume</th>
                      <th>Orders</th>
                      <th>Commission</th>
                      <th>Status</th>
                      <th>Registration Date</th>
                    </tr>
                  </thead>
                  <tbody>
                    {referrals.map(ref => (
                      <tr key={ref.id}>
                        <td>{ref.userId}</td>
                        <td>{ref.referralCode}</td>
                        <td>${formatNumber(ref.totalTradingVolume)}</td>
                        <td>{ref.totalOrdersCount}</td>
                        <td>${formatNumber(ref.totalCommission)}</td>
                        <td>
                          <span className={`${styles.statusBadge} ${ref.totalOrdersCount > 0 ? styles.statusActive : styles.statusInactive}`}>
                            {ref.totalOrdersCount > 0 ? 'Active' : 'Inactive'}
                          </span>
                        </td>
                        <td>{formatDate(ref.createdAt)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
            
            {(!referrals || referrals.length === 0) && (
              <div className={styles.noReferrals}>
                <RiTeamLine className={styles.emptyStateIcon} />
                <p>User has no referrals</p>
              </div>
            )}
          </div>
          
          <div className={styles.modalFooter}>
            <button onClick={handleCloseDetail} className={styles.closeBtn}>Close</button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Referral Program Management</h1>
        
        <div className={styles.tabs}>
          <button 
            className={`${styles.tabButton} ${activeTab === 'overview' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <RiBarChartFill className={styles.tabIcon} />
            Overview
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'referrers' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('referrers')}
          >
            <RiTeamLine className={styles.tabIcon} />
            Referrers
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'all' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('all')}
          >
            <RiLinkM className={styles.tabIcon} />
            All Referrals
          </button>
        </div>
      </div>
      
      <div className={styles.actionsRow}>
        <div className={styles.periodSelector}>
          <RiCalendarLine className={styles.periodIcon} />
          <select 
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(Number(e.target.value))}
            className={styles.periodSelect}
          >
            <option value="1">January</option>
            <option value="2">February</option>
            <option value="3">March</option>
            <option value="4">April</option>
            <option value="5">May</option>
            <option value="6">June</option>
            <option value="7">July</option>
            <option value="8">August</option>
            <option value="9">September</option>
            <option value="10">October</option>
            <option value="11">November</option>
            <option value="12">December</option>
          </select>
          
          <select 
            value={selectedYear}
            onChange={(e) => setSelectedYear(Number(e.target.value))}
            className={styles.periodSelect}
          >
            <option value="2023">2023</option>
            <option value="2024">2024</option>
            <option value="2025">2025</option>
          </select>
          
          <button 
            className={styles.refreshButton}
            onClick={fetchData}
            title="Refresh data"
          >
            <RiRefreshLine />
          </button>
        </div>
        
        {activeTab !== 'overview' && (
          <form onSubmit={handleSearch} className={styles.searchForm}>
            <div className={styles.searchContainer}>
              <RiSearchLine className={styles.searchIcon} />
              <input
                type="text"
                placeholder="Search by ID or referral code..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
              />
            </div>
            <button type="submit" className={styles.searchButton}>Search</button>
          </form>
        )}
        
        {activeTab !== 'overview' && (
          <button 
            className={styles.filterButton} 
            onClick={() => setShowFilters(!showFilters)}
          >
            <RiFilter2Line />
            Filters
          </button>
        )}
      </div>
      
      {showFilters && activeTab !== 'overview' && (
        <div className={styles.filtersContainer}>
          <div className={styles.filterGroup}>
            <label>Status:</label>
            <select 
              className={styles.filterSelect}
            >
              <option value="">All statuses</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
          
          <div className={styles.filterGroup}>
            <label>Tier Level:</label>
            <select 
              className={styles.filterSelect}
            >
              <option value="">All levels</option>
              <option value="1">Tier 1</option>
              <option value="2">Tier 2</option>
              <option value="3">Tier 3</option>
              <option value="0">No Level</option>
            </select>
          </div>
          
          <div className={styles.filterGroup}>
            <label>Minimum Volume:</label>
            <input
              type="number"
              placeholder="Minimum volume"
              className={styles.filterInput}
            />
          </div>
          
          <button className={styles.resetButton}>
            Reset filters
          </button>
        </div>
      )}
      
      {error && <div className={styles.errorMessage}>{error}</div>}
      
      {isLoading ? (
        <div className={styles.loadingContainer}>
          <RiLoader4Line className={styles.loadingSpinner} />
          <p>Loading data...</p>
        </div>
      ) : (
        <>
          <div className={styles.contentContainer}>
            {activeTab === 'overview' && renderOverview()}
            {activeTab === 'referrers' && renderReferrers()}
            {activeTab === 'all' && renderAllReferals()}
          </div>
          
          {/* Pagination */}
          {activeTab === 'referrers' && referrers.length > 0 && (
            <div className={styles.pagination}>
              <button
                onClick={() => setReferrersPage(p => Math.max(1, p - 1))}
                disabled={referrersPage === 1}
                className={styles.paginationButton}
              >
                &lt; Previous
              </button>
              <span className={styles.pageInfo}>
                Page {referrersPage} of {referrersTotalPages}
              </span>
              <button
                onClick={() => setReferrersPage(p => Math.min(referrersTotalPages, p + 1))}
                disabled={referrersPage === referrersTotalPages}
                className={styles.paginationButton}
              >
                Next &gt;
              </button>
            </div>
          )}
          
          {activeTab === 'all' && referals.length > 0 && (
            <div className={styles.pagination}>
              <button
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
                className={styles.paginationButton}
              >
                &lt; Previous
              </button>
              <span className={styles.pageInfo}>
                Page {page} of {totalPages}
              </span>
              <button
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className={styles.paginationButton}
              >
                Next &gt;
              </button>
            </div>
          )}
        </>
      )}
      
      {/* Detail modal */}
      {renderDetailModal()}
    </div>
  );
}