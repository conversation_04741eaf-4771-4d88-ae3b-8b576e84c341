.container {
    padding: 1.5rem;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1a1a2e;
    margin: 0;
  }
  
  .tabs {
    display: flex;
    gap: 0.75rem;
  }
  
  .tabButton {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    background-color: #f3f4f6;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .tabIcon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
  }
  
  .activeTab {
    background-color: #4f46e5;
    color: white;
  }
  
  .actionsRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .periodSelector {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background-color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
  }
  
  .periodIcon {
    color: #6b7280;
    font-size: 1.25rem;
  }
  
  .periodSelect {
    padding: 0.375rem 0.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.95rem;
  }
  
  .refreshButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2rem;
    height: 2rem;
    background-color: #f3f4f6;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    color: #4b5563;
    transition: all 0.2s;
  }
  
  .refreshButton:hover {
    background-color: #e5e7eb;
  }
  
  .searchForm {
    display: flex;
    gap: 0.75rem;
  }
  
  .searchContainer {
    position: relative;
    width: 320px;
  }
  
  .searchIcon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
  }
  
  .searchInput {
    width: 100%;
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.95rem;
  }
  
  .searchButton {
    padding: 0.625rem 1.25rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .searchButton:hover {
    background-color: #4338ca;
  }
  
  .filterButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .filterButton:hover {
    background-color: #f9fafb;
  }
  
  .filtersContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    padding: 1.5rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
  }
  
  .filterGroup {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .filterGroup label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
  }
  
  .filterSelect {
    padding: 0.5rem;
    min-width: 180px;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
  }
  
  .filterInput {
    padding: 0.5rem;
    min-width: 180px;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
  }
  
  .resetButton {
    padding: 0.625rem 1.25rem;
    background-color: transparent;
    color: #6b7280;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: auto;
    align-self: flex-end;
  }
  
  .resetButton:hover {
    background-color: #f3f4f6;
  }
  
  .errorMessage {
    padding: 1rem;
    background-color: #fee2e2;
    color: #b91c1c;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
  }
  
  .loadingSpinner {
    font-size: 2rem;
    color: #4f46e5;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  /* Стили для обзорного экрана */
  .overviewContainer {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .statsRow {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
  }
  
  .statCard {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .statIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
    border-radius: 0.75rem;
    font-size: 1.5rem;
  }
  
  .statInfo {
    flex: 1;
  }
  
  .statValue {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
  }
  
  .statLabel {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  .tierStats {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    grid-column: span 3;
  }
  
  .tierTitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
  }
  
  .tierBars {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .tierBar {
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .tierBarLabel {
    width: 120px;
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
  }
  
  .tierBarContainer {
    flex: 1;
    height: 0.75rem;
    background-color: #f3f4f6;
    border-radius: 9999px;
    overflow: hidden;
  }
  
  .tierBarFill {
    height: 100%;
    border-radius: 9999px;
  }
  
  .tier0 {
    background-color: #9ca3af;
    color: white;
  }
  
  .tier1 {
    background-color: #3b82f6;
    color: white;
  }
  
  .tier2 {
    background-color: #8b5cf6;
    color: white;
  }
  
  .tier3 {
    background-color: #f59e0b;
    color: white;
  }
  
  .tierBarValue {
    width: 60px;
    text-align: right;
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
  }
  
  /* Таблица */
  .tableContainer {
    width: 100%;
    overflow-x: auto;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .table th {
    padding: 0.875rem 1rem;
    text-align: left;
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .table td {
    padding: 1rem;
    font-size: 0.95rem;
    color: #1f2937;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .table tr:last-child td {
    border-bottom: none;
  }
  
  .referralsCounts {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .activeBadge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    background-color: #dcfce7;
    color: #059669;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .tierBadge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .statusBadge {
    display: inline-block;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 600;
  }
  
  .statusActive {
    background-color: #dcfce7;
    color: #059669;
  }
  
  .statusInactive {
    background-color: #f3f4f6;
    color: #6b7280;
  }
  
  .projectionUp {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .projectionValue {
    font-size: 0.875rem;
    font-weight: 600;
    color: #10b981;
  }
  
  .detailButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
    border: none;
    border-radius: 9999px;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .detailButton:hover {
    background-color: rgba(79, 70, 229, 0.2);
  }
  
  /* Пагинация */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
  }
  
  .paginationButton {
    padding: 0.5rem 1rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .paginationButton:hover:not(:disabled) {
    background-color: #f3f4f6;
  }
  
  .paginationButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .pageInfo {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  /* Пустое состояние */
  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .emptyStateIcon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1.5rem;
  }
  
  /* Модальное окно */
  .modalBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modalContent {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 1200px;
    max-height: 90vh;
    overflow-y: auto;
  }
  
  .modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .modalHeader h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }
  
  .closeButton {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .closeButton:hover {
    color: #1f2937;
  }
  
  .modalBody {
    padding: 1.5rem;
  }
  
  .detailInfo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }
  
  .detailColumn h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .detailRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
  }
  
  .detailLabel {
    font-size: 0.95rem;
    color: #6b7280;
  }
  
  .detailValue {
    font-size: 0.95rem;
    font-weight: 500;
    color: #1f2937;
  }
  
  .referralsSection h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
  }
  
  .referralsSection h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.referralsTable {
  width: 100%;
  border-collapse: collapse;
}

.referralsTable th {
  padding: 0.75rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #4b5563;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.referralsTable td {
  padding: 0.75rem;
  font-size: 0.875rem;
  color: #4b5563;
  border-bottom: 1px solid #e5e7eb;
}

.noReferrals {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  margin-top: 1rem;
}

.noReferrals .emptyStateIcon {
  font-size: 2.5rem;
  color: #9ca3af;
  margin-bottom: 1rem;
}

.noReferrals p {
  font-size: 0.95rem;
  color: #6b7280;
}

.modalFooter {
  display: flex;
  justify-content: flex-end;
  padding: 1.5rem;
  border-top: 1px solid #e5e7eb;
}

.closeBtn {
  padding: 0.625rem 1.25rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.closeBtn:hover {
  background-color: #e5e7eb;
}

/* Адаптивность для мобильных устройств */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .tabs {
    width: 100%;
  }
  
  .tabButton {
    flex: 1;
    justify-content: center;
  }
  
  .actionsRow {
    flex-direction: column;
    gap: 1rem;
  }
  
  .periodSelector {
    width: 100%;
    justify-content: space-between;
  }
  
  .searchForm {
    width: 100%;
  }
  
  .searchContainer {
    width: 100%;
  }
  
  .filterButton {
    width: 100%;
    justify-content: center;
  }
  
  .filtersContainer {
    flex-direction: column;
  }
  
  .statsRow {
    grid-template-columns: 1fr;
  }
  
  .tierStats {
    grid-column: span 1;
  }
  
  .table {
    font-size: 0.85rem;
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
  
  .detailInfo {
    grid-template-columns: 1fr;
  }
  
  .modalContent {
    width: 95%;
    max-height: 95vh;
  }
}

/* Стили для содержимого таблицы */
.currencyCell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.currencyIcon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 50%;
  background-color: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

.positiveChange {
  color: #10b981;
}

.negativeChange {
  color: #ef4444;
}

/* Дополнительные стили для выделения прогресса к следующему уровню */
.progressToNext {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.progressBarContainer {
  width: 100%;
  height: 0.5rem;
  background-color: #f3f4f6;
  border-radius: 9999px;
  overflow: hidden;
}

.progressBarFill {
  height: 100%;
  border-radius: 9999px;
  background-color: #10b981;
}

.progressText {
  font-size: 0.75rem;
  color: #6b7280;
  display: flex;
  justify-content: space-between;
}

/* Стили для карточек с прогнозами в детальном виде */
.projectionCard {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-top: 1rem;
}

.projectionTitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.75rem;
}

.projectionItem {
  display: flex;
  justify-content: space-between;
  margin-bottom: 0.5rem;
}

.projectionLabel {
  font-size: 0.875rem;
  color: #6b7280;
}

.projectionValue {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1f2937;
}

.projectionDifference {
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 0.5rem;
}

.projectionUp {
  color: #10b981;
}

.projectionDown {
  color: #ef4444;
}

/* Стили для инфоблоков в обзоре */
.infoBlock {
  background-color: #f0f9ff;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #3b82f6;
  margin-bottom: 1.5rem;
}

.infoBlockTitle {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.infoBlockText {
  font-size: 0.875rem;
  color: #4b5563;
  line-height: 1.5;
}

/* Стили для выпадающего меню в таблице */
.actionDropdown {
  position: relative;
}

.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: #f3f4f6;
  border: none;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s;
}

.actionButton:hover {
  background-color: #e5e7eb;
}

.dropdownMenu {
  position: absolute;
  top: 100%;
  right: 0;
  width: 180px;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  overflow: hidden;
}

.dropdownItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s;
}

.dropdownItem:hover {
  background-color: #f9fafb;
}

.dropdownItemIcon {
  font-size: 1rem;
  color: #6b7280;
}

/* Дополнительные стили для детального просмотра */
.detailSubsection {
  margin-bottom: 1.5rem;
}

.detailSubsectionTitle {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.75rem;
}

.detailGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.detailGridItem {
  background-color: #f9fafb;
  padding: 1rem;
  border-radius: 0.5rem;
}

.detailGridLabel {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.detailGridValue {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
}

/* Стили для фильтров даты */
.dateFilterSection {
  margin-top: 1rem;
}

.dateFilterTitle {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  margin-bottom: 0.5rem;
}

.dateFilterGroup {
  display: flex;
  gap: 0.5rem;
}

.dateFilterInput {
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
}

/* Стили для тултипов */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 200px;
  background-color: #1f2937;
  color: white;
  text-align: center;
  border-radius: 0.375rem;
  padding: 0.5rem;
  position: absolute;
  z-index: 1;
  bottom: 125%;
  left: 50%;
  transform: translateX(-50%);
  opacity: 0;
  transition: opacity 0.3s;
  font-size: 0.75rem;
}

.tooltip:hover .tooltiptext {
  visibility: visible;
  opacity: 1;
}