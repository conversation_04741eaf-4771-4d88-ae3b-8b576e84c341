.adminLayout {
    display: flex;
    min-height: 100vh;
    background-color: #f5f7fa;
  }
  
  .mainContent {
    flex: 1;
    padding: 2rem;
    overflow-y: auto;
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background-color: #f5f7fa;
  }
  
  .loadingSpinner {
    width: 50px;
    height: 50px;
    border: 5px solid rgba(79, 70, 229, 0.2);
    border-radius: 50%;
    border-top-color: #4f46e5;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  @media (max-width: 768px) {
    .adminLayout {
      flex-direction: column;
    }
    
    .mainContent {
      padding: 1rem;
    }
  }