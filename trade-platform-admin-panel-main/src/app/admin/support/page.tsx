"use client";

import React, { useState, useEffect } from "react";
import {
  getAllSupportRequests,
  getSupportStatistics,
  getSupportRequestById,
  updateSupportRequestStatus,
  respondToSupportRequest,
  SupportRequest,
  SupportStatistics,
  SupportPaginatedResponse
} from "@/app/utils/api";
import styles from "./page.module.css";
import { format } from "date-fns";
import { enUS } from "date-fns/locale";
import {
  RiFilter2Line,
  RiSearchLine,
  RiRefreshLine,
  RiMailLine,
  RiMailOpenLine,
  RiCheckboxCircleLine,
  RiTimeLine,
  RiUserLine,
  RiLoader4Line,
  RiMessage2Line,
  RiQuestionAnswerLine,
  RiEyeLine,
  RiSendPlaneLine,
  RiErrorWarningLine,
  RiFileTextLine,
  RiWalletLine,         // For deposit and withdrawal
  RiExchangeLine,       // For trading
  RiTeamLine            // For referral program - fixed import
} from "react-icons/ri";

export default function SupportAdminPage() {
  const [activeTab, setActiveTab] = useState<'overview' | 'incoming' | 'processing' | 'completed'>('overview');
  const [supportRequests, setSupportRequests] = useState<SupportRequest[]>([]);
  const [statistics, setStatistics] = useState<SupportStatistics | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<SupportRequest | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState("");
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [showFilters, setShowFilters] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [responseText, setResponseText] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load data on mount and filter changes
  useEffect(() => {
    fetchData();
  }, [activeTab, page]);

  // Function to fetch data
  const fetchData = async () => {
    setIsLoading(true);
    setError("");

    try {
      if (activeTab === 'overview') {
        const stats = await getSupportStatistics();
        setStatistics(stats);
      } else {
        const status = activeTab as 'incoming' | 'processing' | 'completed';
        const response = await getAllSupportRequests(page, 10, status, searchTerm || undefined);
        setSupportRequests(response.items || []);
        setTotalPages(response.pages || 1);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while loading data');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to open modal with detailed request information
  const handleOpenDetail = async (request: SupportRequest) => {
    setSelectedRequest(request);
    setResponseText(request.response || "");
    setShowDetailModal(true);
  };

  // Function to close modal
  const handleCloseDetail = () => {
    setShowDetailModal(false);
    setSelectedRequest(null);
    setResponseText("");
  };

  // Function to update request status
  const handleUpdateStatus = async (requestId: string, newStatus: 'incoming' | 'processing' | 'completed') => {
    try {
      setIsSubmitting(true);
      const updatedRequest = await updateSupportRequestStatus(requestId, newStatus);
      
      // Update requests list
      setSupportRequests(prev => 
        prev.map(req => req.id === requestId ? updatedRequest : req)
      );
      
      // If modal is open, update data in it
      if (selectedRequest && selectedRequest.id === requestId) {
        setSelectedRequest(updatedRequest);
      }
      
      // Update data if status changed to one not matching current filter
      if (activeTab !== 'overview' && activeTab !== newStatus) {
        fetchData();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while updating status');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Function to send response to request
  const handleSendResponse = async () => {
    if (!selectedRequest || !responseText.trim()) return;
    
    try {
      setIsSubmitting(true);
      const updatedRequest = await respondToSupportRequest(selectedRequest.id, responseText);
      
      // Update requests list
      setSupportRequests(prev => 
        prev.map(req => req.id === selectedRequest.id ? updatedRequest : req)
      );
      
      // Update data in modal
      setSelectedRequest(updatedRequest);
      
      // If status changed after response, update table data
      if (activeTab !== 'overview' && activeTab !== updatedRequest.status) {
        fetchData();
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred while sending response');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'dd MMMM yyyy HH:mm', { locale: enUS });
    } catch (e) {
      return dateString;
    }
  };

  // Search handler
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setPage(1);
    fetchData();
  };

  // Get icon for status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'incoming': return <RiMailLine />;
      case 'processing': return <RiMailOpenLine />;
      case 'completed': return <RiCheckboxCircleLine />;
      default: return <RiMailLine />;
    }
  };

  // Get CSS class for status
  const getStatusClass = (status: string) => {
    switch (status) {
      case 'incoming': return styles.statusIncoming;
      case 'processing': return styles.statusProcessing;
      case 'completed': return styles.statusCompleted;
      default: return styles.statusIncoming;
    }
  };

  // Get status text
  const getStatusText = (status: string) => {
    switch (status) {
      case 'incoming': return 'Incoming';
      case 'processing': return 'Processing';
      case 'completed': return 'Completed';
      default: return 'Incoming';
    }
  };

  // Get topic name by code
  const getTopicName = (topic: string) => {
    switch (topic) {
      case 'account': return 'Account';
      case 'deposit': return 'Deposit';
      case 'withdrawal': return 'Withdrawal';
      case 'trading': return 'Trading';
      case 'referral': return 'Referral Program';
      case 'other': return 'Other';
      default: return 'Unknown';
    }
  };

  // Get icon for topic
  const getTopicIcon = (topic: string) => {
    switch (topic) {
      case 'account': return <RiUserLine />;
      case 'deposit': return <RiWalletLine />;
      case 'withdrawal': return <RiWalletLine />;
      case 'trading': return <RiExchangeLine />;
      case 'referral': return <RiTeamLine />;
      case 'other': return <RiFileTextLine />;
      default: return <RiFileTextLine />;
    }
  };

  // Render overview screen
  const renderOverview = () => {
    if (!statistics) return null;

    return (
      <div className={styles.overviewContainer}>
        <div className={styles.statsRow}>
          <div className={styles.statCard}>
            <div className={`${styles.statIcon} ${styles.statIconIncoming}`}>
              <RiMailLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>{statistics.totalIncoming}</div>
              <div className={styles.statLabel}>Incoming Requests</div>
            </div>
          </div>
          
          <div className={styles.statCard}>
            <div className={`${styles.statIcon} ${styles.statIconProcessing}`}>
              <RiMailOpenLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>{statistics.totalProcessing}</div>
              <div className={styles.statLabel}>Requests in Progress</div>
            </div>
          </div>
          
          <div className={styles.statCard}>
            <div className={`${styles.statIcon} ${styles.statIconCompleted}`}>
              <RiCheckboxCircleLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>{statistics.totalCompleted}</div>
              <div className={styles.statLabel}>Completed Requests</div>
            </div>
          </div>
          
          <div className={styles.statCard}>
            <div className={`${styles.statIcon} ${styles.statIconTime}`}>
              <RiTimeLine />
            </div>
            <div className={styles.statInfo}>
              <div className={styles.statValue}>{statistics.averageResponseTime.toFixed(1)} h</div>
              <div className={styles.statLabel}>Average Response Time</div>
            </div>
          </div>
        </div>
        
        <div className={styles.statsRow}>
          <div className={styles.responseRateCard}>
            <div className={styles.responseRateTitle}>
              <RiQuestionAnswerLine /> Response Rate Last Week
            </div>
            <div className={styles.responseRateContainer}>
              <div 
                className={styles.responseRateFill} 
                style={{ width: `${statistics.responseRateLastWeek}%` }}
              ></div>
              <div className={styles.responseRateValue}>{statistics.responseRateLastWeek}%</div>
            </div>
          </div>
          
          <div className={styles.topicsCard}>
            <div className={styles.topicsTitle}>
              <RiMessage2Line /> Popular Request Topics
            </div>
            <div className={styles.topicsList}>
              {statistics.mostCommonTopics.map((topic, index) => (
                <div key={index} className={styles.topicItem}>
                  <div className={styles.topicName}>{topic.topic}</div>
                  <div className={styles.topicCount}>{topic.count}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  // Render requests table
  const renderSupportRequests = () => {
    if (supportRequests.length === 0) {
      return (
        <div className={styles.emptyState}>
          <RiMailLine className={styles.emptyStateIcon} />
          <p>No requests with selected status</p>
        </div>
      );
    }

    return (
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>ID</th>
              <th>User</th>
              <th>Topic</th>
              <th>Message</th>
              <th>Status</th>
              <th>Created Date</th>
              <th>Updated Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {supportRequests.map(request => (
              <tr key={request.id}>
                <td>{request.id}</td>
                <td>
                  <div className={styles.userInfo}>
                    <RiUserLine className={styles.userIcon} />
                    <div>
                      <div className={styles.userName}>{request.userName}</div>
                      <div className={styles.userId}>ID: {request.userId}</div>
                    </div>
                  </div>
                </td>
                <td>
                  <div className={styles.topicBadge}>
                    {getTopicName(request.topic)}
                  </div>
                </td>
                <td>
                  <div className={styles.messagePreview}>
                    {request.message.length > 80 
                      ? `${request.message.substring(0, 80)}...` 
                      : request.message}
                  </div>
                </td>
                <td>
                  <span className={`${styles.statusBadge} ${getStatusClass(request.status)}`}>
                    {getStatusIcon(request.status)} {getStatusText(request.status)}
                  </span>
                </td>
                <td>{formatDate(request.createdAt)}</td>
                <td>{formatDate(request.updatedAt)}</td>
                <td>
                  <div className={styles.actionButtons}>
                    <button
                      className={`${styles.actionButton} ${styles.viewButton}`}
                      onClick={() => handleOpenDetail(request)}
                      title="View request"
                    >
                      <RiEyeLine />
                    </button>
                    
                    {request.status === 'incoming' && (
                      <button
                        className={`${styles.actionButton} ${styles.processButton}`}
                        onClick={() => handleUpdateStatus(request.id, 'processing')}
                        disabled={isSubmitting}
                        title="Take for processing"
                      >
                        <RiMailOpenLine />
                      </button>
                    )}
                    
                    {request.status === 'processing' && !request.response && (
                      <button
                        className={`${styles.actionButton} ${styles.respondButton}`}
                        onClick={() => handleOpenDetail(request)}
                        title="Reply"
                      >
                        <RiSendPlaneLine />
                      </button>
                    )}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Render modal with detailed information
  const renderDetailModal = () => {
    if (!showDetailModal || !selectedRequest) return null;

    return (
      <div className={styles.modalBackdrop}>
        <div className={styles.modalContent}>
          <div className={styles.modalHeader}>
            <h2>Support Request #{selectedRequest.id}</h2>
            <button onClick={handleCloseDetail} className={styles.closeButton}>×</button>
          </div>
          
          <div className={styles.modalBody}>
            <div className={styles.detailInfo}>
              <div className={styles.detailColumn}>
                <h3>Request Information</h3>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Request ID:</span>
                  <span className={styles.detailValue}>{selectedRequest.id}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Topic:</span>
                  <span className={styles.detailValue}>{getTopicName(selectedRequest.topic)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Status:</span>
                  <span className={`${styles.statusBadge} ${getStatusClass(selectedRequest.status)}`}>
                    {getStatusIcon(selectedRequest.status)} {getStatusText(selectedRequest.status)}
                  </span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Created Date:</span>
                  <span className={styles.detailValue}>{formatDate(selectedRequest.createdAt)}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Last Update:</span>
                  <span className={styles.detailValue}>{formatDate(selectedRequest.updatedAt)}</span>
                </div>
                {selectedRequest.completedAt && (
                  <div className={styles.detailRow}>
                    <span className={styles.detailLabel}>Completion Date:</span>
                    <span className={styles.detailValue}>{formatDate(selectedRequest.completedAt)}</span>
                  </div>
                )}
              </div>
              
              <div className={styles.detailColumn}>
                <h3>User Information</h3>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>User ID:</span>
                  <span className={styles.detailValue}>{selectedRequest.userId}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Username:</span>
                  <span className={styles.detailValue}>{selectedRequest.userName}</span>
                </div>
                <div className={styles.detailRow}>
                  <span className={styles.detailLabel}>Email:</span>
                  <span className={styles.detailValue}>{selectedRequest.email || 'Not specified'}</span>
                </div>
              </div>
            </div>
            
            <div className={styles.messageSection}>
              <h3>User Message</h3>
              <div className={styles.messageContent}>
                {selectedRequest.message}
              </div>
            </div>
            
            {selectedRequest.status !== 'completed' ? (
              <div className={styles.responseSection}>
                <h3>Response to Request</h3>
                <textarea
                  className={styles.responseTextarea}
                  placeholder="Enter response text to user request..."
                  value={responseText}
                  onChange={(e) => setResponseText(e.target.value)}
                />
                <div className={styles.responseActions}>
                  {selectedRequest.status === 'incoming' && (
                    <button
                      className={styles.processRequestButton}
                      onClick={() => handleUpdateStatus(selectedRequest.id, 'processing')}
                      disabled={isSubmitting}
                    >
                      <RiMailOpenLine /> Take for Processing
                    </button>
                  )}
                  <button
                    className={styles.sendResponseButton}
                    onClick={handleSendResponse}
                    disabled={!responseText.trim() || isSubmitting}
                  >
                    <RiSendPlaneLine /> Send Response
                  </button>
                </div>
              </div>
            ) : (
              <div className={styles.responseSection}>
                <h3>Response to Request</h3>
                <div className={styles.responseContent}>
                  {selectedRequest.response}
                </div>
              </div>
            )}
          </div>
          
          <div className={styles.modalFooter}>
            <button onClick={handleCloseDetail} className={styles.closeBtn}>Close</button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Support Request Management</h1>
        
        <div className={styles.tabs}>
          <button 
            className={`${styles.tabButton} ${activeTab === 'overview' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('overview')}
          >
            <RiQuestionAnswerLine className={styles.tabIcon} />
            Overview
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'incoming' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('incoming')}
          >
            <RiMailLine className={styles.tabIcon} />
            Incoming
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'processing' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('processing')}
          >
            <RiMailOpenLine className={styles.tabIcon} />
            In Progress
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'completed' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('completed')}
          >
            <RiCheckboxCircleLine className={styles.tabIcon} />
            Completed
          </button>
        </div>
      </div>
      
      {activeTab !== 'overview' && (
        <div className={styles.actionsRow}>
          <form onSubmit={handleSearch} className={styles.searchForm}>
            <div className={styles.searchContainer}>
              <RiSearchLine className={styles.searchIcon} />
              <input
                type="text"
                placeholder="Search by ID, username or message..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={styles.searchInput}
              />
            </div>
            <button type="submit" className={styles.searchButton}>Search</button>
          </form>
          
          <button 
            className={styles.refreshButton}
            onClick={fetchData}
            title="Refresh data"
          >
            <RiRefreshLine />
          </button>
          
          <button 
            className={styles.filterButton} 
            onClick={() => setShowFilters(!showFilters)}
          >
            <RiFilter2Line />
            Filters
          </button>
        </div>
      )}
      
      {showFilters && activeTab !== 'overview' && (
        <div className={styles.filtersContainer}>
          <div className={styles.filterGroup}>
            <label>Creation Date:</label>
            <div className={styles.dateInputs}>
              <input
                type="date"
                className={styles.filterInput}
                placeholder="From"
              />
              <input
                type="date"
                className={styles.filterInput}
                placeholder="To"
              />
            </div>
          </div>
          
          <div className={styles.filterGroup}>
            <label>Topic:</label>
            <select className={styles.filterInput}>
              <option value="">All Topics</option>
              <option value="account">Account</option>
              <option value="deposit">Deposit</option>
              <option value="withdrawal">Withdrawal</option>
              <option value="trading">Trading</option>
              <option value="referral">Referral Program</option>
              <option value="other">Other</option>
            </select>
          </div>
          
          <div className={styles.filterGroup}>
            <label>User ID:</label>
            <input
              type="text"
              className={styles.filterInput}
              placeholder="User ID"
            />
          </div>
          
          <button className={styles.resetButton}>
            Reset Filters
          </button>
        </div>
      )}
      
      {error && (
        <div className={styles.errorMessage}>
          <RiErrorWarningLine /> {error}
        </div>
      )}
      
      {isLoading ? (
        <div className={styles.loadingContainer}>
          <RiLoader4Line className={styles.loadingSpinner} />
          <p>Loading data...</p>
        </div>
      ) : (
        <>
          <div className={styles.contentContainer}>
            {activeTab === 'overview' && renderOverview()}
            {activeTab !== 'overview' && renderSupportRequests()}
          </div>
          
          {/* Pagination */}
          {activeTab !== 'overview' && supportRequests.length > 0 && (
            <div className={styles.pagination}>
              <button
                onClick={() => setPage(p => Math.max(1, p - 1))}
                disabled={page === 1}
                className={styles.paginationButton}
              >
                &lt; Previous
              </button>
              <span className={styles.pageInfo}>
                Page {page} of {totalPages}
              </span>
              <button
                onClick={() => setPage(p => Math.min(totalPages, p + 1))}
                disabled={page === totalPages}
                className={styles.paginationButton}
              >
                Next &gt;
              </button>
            </div>
          )}
        </>
      )}
      
      {/* Detail modal */}
      {renderDetailModal()}
    </div>
  );
}