.container {
    padding: 1.5rem;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1a1a2e;
    margin: 0;
  }
  
  .tabs {
    display: flex;
    gap: 0.75rem;
  }
  
  .tabButton {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    background-color: #f3f4f6;
    border: none;
    border-radius: 0.5rem;
    font-size: 0.95rem;
    font-weight: 500;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s ease;
  }
  
  .tabIcon {
    margin-right: 0.5rem;
    font-size: 1.25rem;
  }
  
  .activeTab {
    background-color: #4f46e5;
    color: white;
  }
  
  .actionsRow {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
  }
  
  .searchForm {
    display: flex;
    gap: 0.75rem;
    flex-grow: 1;
    max-width: 600px;
  }
  
  .searchContainer {
    position: relative;
    width: 100%;
  }
  
  .searchIcon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
  }
  
  .searchInput {
    width: 100%;
    padding: 0.625rem 1rem 0.625rem 2.5rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.95rem;
  }
  
  .searchButton {
    padding: 0.625rem 1.25rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .searchButton:hover {
    background-color: #4338ca;
  }
  
  .refreshButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    background-color: #f3f4f6;
    border: none;
    border-radius: 0.375rem;
    cursor: pointer;
    color: #4b5563;
    transition: all 0.2s;
    margin-left: 0.75rem;
  }
  
  .refreshButton:hover {
    background-color: #e5e7eb;
  }
  
  .filterButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.625rem 1.25rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: 0.75rem;
  }
  
  .filterButton:hover {
    background-color: #f9fafb;
  }
  
  .filtersContainer {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    padding: 1.5rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    margin-bottom: 2rem;
  }
  
  .filterGroup {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .filterGroup label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #4b5563;
  }
  
  .dateInputs {
    display: flex;
    gap: 0.5rem;
  }
  
  .filterInput {
    padding: 0.5rem;
    min-width: 180px;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
  }
  
  .resetButton {
    padding: 0.625rem 1.25rem;
    background-color: transparent;
    color: #6b7280;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
    margin-left: auto;
    align-self: flex-end;
  }
  
  .resetButton:hover {
    background-color: #f3f4f6;
  }
  
  .errorMessage {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 1rem;
    background-color: #fee2e2;
    color: #b91c1c;
    border-radius: 0.5rem;
    margin-bottom: 1.5rem;
    font-weight: 500;
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem 0;
  }
  
  .loadingSpinner {
    font-size: 2rem;
    color: #4f46e5;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  /* Стили для обзорного экрана */
  .overviewContainer {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }
  
  .statsRow {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.5rem;
  }
  
  .statCard {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
  }
  
  .statIcon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    font-size: 1.5rem;
  }
  
  .statIconIncoming {
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
  }
  
  .statIconProcessing {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
  
  .statIconCompleted {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }
  
  .statIconTime {
    background-color: rgba(99, 102, 241, 0.1);
    color: #6366f1;
  }
  
  .statInfo {
    flex: 1;
  }
  
  .statValue {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
  }
  
  .statLabel {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  .responseRateCard {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    grid-column: span 2;
  }
  
  .responseRateTitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
  }
  
  .responseRateContainer {
    height: 2rem;
    background-color: #f3f4f6;
    border-radius: 9999px;
    overflow: hidden;
    position: relative;
  }
  
  .responseRateFill {
    height: 100%;
    background-color: #10b981;
    border-radius: 9999px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 0.5rem;
  }
  
  .responseRateValue {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 0.875rem;
    font-weight: 600;
    color: #1f2937;
  }
  
  .topicsCard {
    background-color: white;
    padding: 1.5rem;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    grid-column: span 2;
  }
  
  .topicsTitle {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1.5rem;
  }
  
  .topicsList {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .topicItem {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 0.75rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .topicItem:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
  
  .topicName {
    font-size: 0.95rem;
    color: #4b5563;
  }
  
  .topicCount {
    font-size: 0.95rem;
    font-weight: 600;
    color: #1f2937;
    background-color: #f3f4f6;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
  }
  
  /* Таблица */
  .tableContainer {
    width: 100%;
    overflow-x: auto;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .table th {
    padding: 0.875rem 1rem;
    text-align: left;
    font-size: 0.875rem;
    font-weight: 600;
    color: #4b5563;
    background-color: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .table td {
    padding: 1rem;
    font-size: 0.95rem;
    color: #1f2937;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .table tr:last-child td {
    border-bottom: none;
  }
  
  .userInfo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
  }
  
  .userIcon {
    color: #6b7280;
    font-size: 1.25rem;
  }
  
  .userName {
    font-weight: 500;
    margin-bottom: 0.25rem;
  }
  
  .userId {
    font-size: 0.75rem;
    color: #6b7280;
  }
  
  .messagePreview {
    max-width: 300px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    color: #4b5563;
  }
  
  .statusBadge {
    display: inline-flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.375rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
  }
  
  .statusIncoming {
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
  }
  
  .statusProcessing {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
  
  .statusCompleted {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }
  
  .actionButtons {
    display: flex;
    gap: 0.5rem;
  }
  
  .actionButton {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.25rem;
    height: 2.25rem;
    border: none;
    border-radius: 9999px;
    cursor: pointer;
    font-size: 1.125rem;
    transition: all 0.2s;
  }
  
  .viewButton {
    background-color: rgba(99, 102, 241, 0.1);
    color: #6366f1;
  }
  
  .viewButton:hover {
    background-color: rgba(99, 102, 241, 0.2);
  }
  
  .processButton {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
  }
  
  .processButton:hover {
    background-color: rgba(245, 158, 11, 0.2);
  }
  
  .respondButton {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
  }
  
  .respondButton:hover {
    background-color: rgba(16, 185, 129, 0.2);
  }
  
  /* Пагинация */
  .pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 1.5rem;
  }
  
  .paginationButton {
    padding: 0.5rem 1rem;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    color: #4b5563;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .paginationButton:hover:not(:disabled) {
    background-color: #f3f4f6;
  }
  
  .paginationButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .pageInfo {
    font-size: 0.875rem;
    color: #6b7280;
  }
  
  /* Пустое состояние */
  .emptyState {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .emptyStateIcon {
    font-size: 3rem;
    color: #9ca3af;
    margin-bottom: 1.5rem;
  }
  
  /* Модальное окно */
  .modalBackdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .modalContent {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 90%;
    max-width: 1000px;
    max-height: 90vh;
    overflow-y: auto;
  }
  
  .modalHeader {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .modalHeader h2 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }
  
  .closeButton {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    transition: color 0.2s;
  }
  
  .closeButton:hover {
    color: #1f2937;
  }
  
  .modalBody {
    padding: 1.5rem;
  }
  
  .detailInfo {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
  }
  
  .detailColumn h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .detailRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 0.75rem;
  }
  
  .detailLabel {
    font-size: 0.95rem;
    color: #6b7280;
  }
  
  .detailValue {
    font-size: 0.95rem;
    font-weight: 500;
    color: #1f2937;
  }
  
  .messageSection {
    margin-bottom: 2rem;
  }
  
  .messageSection h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .messageContent {
    padding: 1.5rem;
    background-color: #f9fafb;
    border-radius: 0.5rem;
    color: #4b5563;
    line-height: 1.5;
    white-space: pre-wrap;
  }
  
  .responseSection h3 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid #e5e7eb;
  }
  
  .responseTextarea {
    width: 100%;
    min-height: 200px;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    resize: vertical;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.5;
    margin-bottom: 1rem;
  }
  
  .responseActions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
  }
  
  .processRequestButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #f59e0b;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .processRequestButton:hover:not(:disabled) {
    background-color: #d97706;
  }
  
  .sendResponseButton {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    background-color: #10b981;
    color: white;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .sendResponseButton:hover:not(:disabled) {
    background-color: #059669;
  }
  
  .sendResponseButton:disabled, 
  .processRequestButton:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  .responseContent {
    padding: 1.5rem;
    background-color: #f0fdf4;
    border-radius: 0.5rem;
    color: #065f46;
    line-height: 1.5;
    white-space: pre-wrap;
    border-left: 4px solid #10b981;
  }
  
  .modalFooter {
    display: flex;
    justify-content: flex-end;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
  }
  
  .closeBtn {
    padding: 0.625rem 1.25rem;
    background-color: #f3f4f6;
    color: #4b5563;
    border: none;
    border-radius: 0.375rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s;
  }
  
  .closeBtn:hover {
    background-color: #e5e7eb;
  }
  
  /* Адаптивность для мобильных устройств */
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 1rem;
    }
    
    .tabs {
      width: 100%;
      overflow-x: auto;
      padding-bottom: 0.5rem;
    }
    
    .tabButton {
      flex: 1;
      white-space: nowrap;
    }
    
    .actionsRow {
      flex-direction: column;
      gap: 1rem;
      align-items: stretch;
    }
    
    .searchForm {
      max-width: 100%;
    }
    
    .refreshButton,
    .filterButton {
      margin-left: 0;
    }
    
    .statsRow {
      grid-template-columns: 1fr;
    }
    
    .responseRateCard,
    .topicsCard {
      grid-column: span 1;
    }
    
    .detailInfo {
      grid-template-columns: 1fr;
    }
    
    .modalContent {
      width: 95%;
      height: 95vh;
    }
    
    .messagePreview {
      max-width: 150px;
    }
  }