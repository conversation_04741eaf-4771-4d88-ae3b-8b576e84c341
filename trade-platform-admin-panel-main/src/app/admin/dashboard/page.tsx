"use client";

import React, { useEffect, useState } from "react";
import styles from "./page.module.css";
// Import necessary API functions
import { 
  getAllWithdrawalsForAdmin,
  WithdrawalStatus,
  getUsersStatistics,
  getReferalStatistics,
  getAllUsers,
  getAllDepositsForAdmin  // ⭐ REPLACED getDeposits with getAllDepositsForAdmin
} from "@/app/utils/api"; // 👈 Replace with correct path to API file

// Interface for dashboard statistics
interface DashboardStats {
  totalDeposits: string;
  totalWithdrawals: string;
  pendingWithdrawals: number;
  usersCount: number;
  newUsersToday: number;
  activeReferrals: number;
  referralProfit: string;
}

export default function Dashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      setIsLoading(true);
      setError(null);

      // 🔥 GET REAL DATA IN PARALLEL - FIXED VERSION
      const [
        withdrawalsData,
        usersStats,
        referralStats,
        depositsData  // ⭐ Now using correct API
      ] = await Promise.allSettled([
        getAllWithdrawalsForAdmin(),
        getUsersStatistics(),
        getReferalStatistics(),
        getAllDepositsForAdmin({ limit: 1000 }) // ⭐ REPLACED with getAllDepositsForAdmin
      ]);

      // Process results
      let totalDepositsAmount = 0;
      let totalWithdrawalsAmount = 0;
      let pendingWithdrawalsCount = 0;

      // 💰 FIXED DEPOSITS CALCULATION
      if (depositsData.status === 'fulfilled' && depositsData.value?.items) {
        console.log('🔍 RAW deposits data:', depositsData.value.items.slice(0, 3));
        
        totalDepositsAmount = depositsData.value.items
          .filter(item => {
            // Filter only deposits (just in case)
            const isDeposit = item.event_type === 'deposit';
            const isProcessed = item.is_processed === true;
            
            console.log(`🔍 Deposit ${item.id}: isDeposit=${isDeposit}, isProcessed=${isProcessed}, amount=${item.amount_change}`);
            
            return isDeposit && isProcessed;
          })
          .reduce((sum, item) => {
            // Parse amount_change as number
            const amount = parseFloat(item.amount_change?.toString() || '0');
            
            if (isNaN(amount) || !isFinite(amount)) {
              console.warn(`⚠️ Invalid amount_change for deposit ${item.id}:`, item.amount_change);
              return sum;
            }
            
            // Check that it's a positive amount (deposits should be +)
            if (amount <= 0) {
              console.warn(`⚠️ Negative or zero deposit amount ${item.id}:`, amount);
              return sum;
            }
            
            console.log(`✅ Adding deposit ${item.id}: ${amount} ${item.currency}`);
            return sum + amount;
          }, 0);
          
        console.log('✅ Total deposits amount:', totalDepositsAmount);
      } else {
        console.error('❌ Failed to get deposits data:', depositsData);
      }

      // 💸 Calculate withdrawals - FIXED VERSION
      if (withdrawalsData.status === 'fulfilled' && withdrawalsData.value) {
        const withdrawals = withdrawalsData.value;
        
        console.log('🔍 RAW withdrawals data:', withdrawals.slice(0, 3));
        
        // Calculate sum of completed withdrawals with validation
        totalWithdrawalsAmount = withdrawals
          .filter(w => w.status === WithdrawalStatus.APPROVED_DONE)
          .reduce((sum, w) => {
            // Add validation for each amount
            const amount = parseFloat(w.amount?.toString() || '0');
            
            // Check that amount is a number and not NaN
            if (isNaN(amount) || !isFinite(amount)) {
              console.warn(`⚠️ Invalid amount for withdrawal ${w.id}:`, w.amount);
              return sum;
            }
            
            return sum + amount;
          }, 0);
        
        // Additional result validation
        if (isNaN(totalWithdrawalsAmount) || !isFinite(totalWithdrawalsAmount)) {
          console.error('❌ Got invalid withdrawals sum, setting to 0');
          totalWithdrawalsAmount = 0;
        }
        
        // Check for suspiciously large sum
        if (totalWithdrawalsAmount > 999999999) { // More than 999 million
          console.error('❌ Suspiciously large withdrawals sum:', totalWithdrawalsAmount);
          totalWithdrawalsAmount = 0;
        }
        
        // ⭐ FIXED PENDING WITHDRAWALS COUNT
        // Count all statuses that mean "in progress" or "pending"
        const pendingStatuses = [
          WithdrawalStatus.PENDING,           // Pending
          WithdrawalStatus.IN_PROGRESS,       // In Progress
          WithdrawalStatus.BLOCKCHAIN_CONFIRMING  // Blockchain Confirming
        ];
        
        pendingWithdrawalsCount = withdrawals
          .filter(w => pendingStatuses.includes(w.status)).length;
        
        // Detailed status statistics for debugging
        const statusCounts = {
          pending: withdrawals.filter(w => w.status === WithdrawalStatus.PENDING).length,
          inProgress: withdrawals.filter(w => w.status === WithdrawalStatus.IN_PROGRESS).length,
          blockchainConfirming: withdrawals.filter(w => w.status === WithdrawalStatus.BLOCKCHAIN_CONFIRMING).length,
          approved: withdrawals.filter(w => w.status === WithdrawalStatus.APPROVED_DONE).length,
          rejected: withdrawals.filter(w => w.status === WithdrawalStatus.REJECTED).length
        };
          
        console.log('✅ Withdrawals calculated:', {
          totalAmount: totalWithdrawalsAmount,
          pendingCount: pendingWithdrawalsCount,
          totalWithdrawals: withdrawals.length,
          statusBreakdown: statusCounts
        });
      }

      // Additional validation for deposits
      if (isNaN(totalDepositsAmount) || !isFinite(totalDepositsAmount)) {
        console.error('❌ Got invalid deposits sum, setting to 0');
        totalDepositsAmount = 0;
      }

      // Check for suspiciously large deposits sum
      if (totalDepositsAmount > 999999999) { // More than 999 million
        console.error('❌ Suspiciously large deposits sum:', totalDepositsAmount);
        totalDepositsAmount = 0;
      }

      // 👥 Get user statistics
      const userStats = usersStats.status === 'fulfilled' ? usersStats.value : {
        totalUsers: 0,
        newUsersToday: 0
      };

      // 🔄 Get referral statistics
      const refStats = referralStats.status === 'fulfilled' ? referralStats.value : {
        activeReferrers: 0,
        totalReward: 0
      };

      // Validate referral data
      const totalReward = parseFloat(refStats.totalReward?.toString() || '0');
      const validatedTotalReward = isNaN(totalReward) || !isFinite(totalReward) ? 0 : totalReward;

      // 🎯 FORM FINAL STATISTICS
      const dashboardStats: DashboardStats = {
        totalDeposits: `${totalDepositsAmount.toLocaleString('en-US', { 
          minimumFractionDigits: 2, 
          maximumFractionDigits: 2 
        })} USD`,
        totalWithdrawals: `${totalWithdrawalsAmount.toLocaleString('en-US', { 
          minimumFractionDigits: 2, 
          maximumFractionDigits: 2 
        })} USD`,
        pendingWithdrawals: pendingWithdrawalsCount,
        usersCount: userStats.totalUsers || 0,
        newUsersToday: userStats.newUsersToday || 0,
        activeReferrals: refStats.activeReferrers || 0,
        referralProfit: `${validatedTotalReward.toLocaleString('en-US', { 
          minimumFractionDigits: 2, 
          maximumFractionDigits: 2 
        })} USD`
      };

      setStats(dashboardStats);
      console.log('✅ Dashboard data loaded successfully:', dashboardStats);

    } catch (error) {
      console.error('❌ Error loading dashboard data:', error);
      setError('Failed to load dashboard data');
    } finally {
      setIsLoading(false);
    }
  };

  // Data refresh function
  const handleRefresh = () => {
    fetchDashboardData();
  };

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Loading dashboard data...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.dashboard}>
        <div className={styles.errorContainer}>
          <h2>Loading Error</h2>
          <p>{error}</p>
          <button onClick={handleRefresh} className={styles.refreshButton}>
            Try Again
          </button>
        </div>
      </div>
    );
  }

  if (!stats) {
    return (
      <div className={styles.dashboard}>
        <div className={styles.errorContainer}>
          <p>No data to display</p>
          <button onClick={handleRefresh} className={styles.refreshButton}>
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={styles.dashboard}>
      <div className={styles.header}>
        <h1 className={styles.title}>Admin Dashboard</h1>
        <div className={styles.headerActions}>
          <p className={styles.date}>
            {new Date().toLocaleDateString('en-US', { 
              day: 'numeric', 
              month: 'long', 
              year: 'numeric' 
            })}
          </p>
          <button onClick={handleRefresh} className={styles.refreshButton}>
            🔄 Refresh
          </button>
        </div>
      </div>

      <div className={styles.statsGrid}>
        <div className={styles.statsCard}>
          <div className={styles.statsIcon}>💰</div>
          <div className={styles.statsContent}>
            <h3>Total Deposits</h3>
            <p className={styles.statsValue}>{stats.totalDeposits}</p>
          </div>
        </div>
        
        <div className={styles.statsCard}>
          <div className={styles.statsIcon}>💸</div>
          <div className={styles.statsContent}>
            <h3>Total Withdrawals</h3>
            <p className={styles.statsValue}>{stats.totalWithdrawals}</p>
          </div>
        </div>
        
        <div className={styles.statsCard}>
          <div className={styles.statsIcon}>⏳</div>
          <div className={styles.statsContent}>
            <h3>Pending Withdrawals</h3>
            <p className={styles.statsValue}>{stats.pendingWithdrawals}</p>
          </div>
        </div>
        
        <div className={styles.statsCard}>
          <div className={styles.statsIcon}>👥</div>
          <div className={styles.statsContent}>
            <h3>Total Users</h3>
            <p className={styles.statsValue}>{stats.usersCount}</p>
            <span className={styles.subValue}>+{stats.newUsersToday} today</span>
          </div>
        </div>
        
        <div className={styles.statsCard}>
          <div className={styles.statsIcon}>🔄</div>
          <div className={styles.statsContent}>
            <h3>Active Referrals</h3>
            <p className={styles.statsValue}>{stats.activeReferrals}</p>
          </div>
        </div>
        
        <div className={styles.statsCard}>
          <div className={styles.statsIcon}>💵</div>
          <div className={styles.statsContent}>
            <h3>Referral Revenue</h3>
            <p className={styles.statsValue}>{stats.referralProfit}</p>
          </div>
        </div>
      </div>

      <div className={styles.recentActivityContainer}>
        <h2 className={styles.sectionTitle}>Recent Activity</h2>
        <div className={styles.activityList}>
          <p className={styles.emptyState}>
            Recent user operations will be displayed here.
            <br />
            <button onClick={handleRefresh} className={styles.refreshButton}>
              Refresh Data
            </button>
          </p>
        </div>
      </div>
    </div>
  );
}