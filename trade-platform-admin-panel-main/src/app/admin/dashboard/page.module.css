.dashboard {
    padding: 1.5rem;
  }
  
  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
  }
  
  .title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #1a1a2e;
    margin: 0;
  }
  
  .date {
    color: #6b7280;
    font-size: 0.9rem;
    margin: 0;
  }
  
  .statsGrid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2.5rem;
  }
  
  .statsCard {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    display: flex;
    align-items: center;
    transition: transform 0.2s, box-shadow 0.2s;
  }
  
  .statsCard:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
  }
  
  .statsIcon {
    font-size: 2rem;
    margin-right: 1rem;
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
    height: 50px;
    width: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  .statsContent {
    flex: 1;
  }
  
  .statsContent h3 {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    margin: 0;
    margin-bottom: 0.25rem;
  }
  
  .statsValue {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
  }
  
  .subValue {
    font-size: 0.75rem;
    color: #10b981;
    font-weight: 500;
    display: block;
    margin-top: 0.25rem;
  }
  
  .recentActivityContainer {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  }
  
  .sectionTitle {
    font-size: 1.25rem;
    font-weight: 600;
    margin-top: 0;
    margin-bottom: 1.5rem;
    color: #1a1a2e;
  }
  
  .emptyState {
    text-align: center;
    color: #6b7280;
    padding: 3rem 0;
  }
  
  .refreshButton {
    margin-top: 1rem;
    background-color: #4f46e5;
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;
  }
  
  .refreshButton:hover {
    background-color: #4338ca;
  }
  
  .loadingContainer {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 300px;
  }
  
  .loadingSpinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(79, 70, 229, 0.2);
    border-radius: 50%;
    border-top-color: #4f46e5;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
  }
  
  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
  
  @media (max-width: 768px) {
    .header {
      flex-direction: column;
      align-items: flex-start;
      gap: 0.5rem;
    }
    
    .statsCard {
      padding: 1.25rem;
    }
    
    .statsValue {
      font-size: 1.25rem;
    }
  }