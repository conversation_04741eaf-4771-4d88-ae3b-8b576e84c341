"use client";

import React, { useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import styles from "./layout.module.css";
import Sidebar from "@/app/components/Sidebar";

interface AdminLayoutProps {
  children: React.ReactNode;
}

export default function AdminLayout({ children }: AdminLayoutProps) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(true);
  
  useEffect(() => {
    // Проверяем авторизацию
    if (typeof window !== 'undefined') { // Проверка на клиентскую сторону
      const token = localStorage.getItem("token");
      const userRole = localStorage.getItem("userRole");
      
      if (!token || userRole !== "admin") {
        // Если нет токена или роль не администратор, перенаправляем на страницу входа
        router.push("/");
        return;
      }
      
      setIsLoading(false);
    }
  }, [router]);
  
  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <div className={styles.loadingSpinner}></div>
        <p>Загрузка...</p>
      </div>
    );
  }
  
  return (
    <div className={styles.adminLayout}>
      <Sidebar currentPath={pathname || ''} />
      <main className={styles.mainContent}>
        {children}
      </main>
    </div>
  );
}