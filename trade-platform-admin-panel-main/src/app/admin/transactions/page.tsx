"use client";

import React, { useState, useEffect } from "react";
import { 
  getDeposits, 
  getAllWithdrawalsPaginated, 
  updateWithdrawalStatus, 
  AdminWithdrawalRequest, 
  WithdrawalStatus,
  AdminWithdrawalsResponse, 
  getAllDepositsForAdmin
} from "@/app/utils/api";
import styles from "./page.module.css";
import { format } from "date-fns";
import { enUS } from "date-fns/locale";
import { 
  RiFilter2Line, 
  RiSearchLine, 
  RiCheckLine, 
  RiCloseLine,
  RiLoader4Line,
  RiExchangeDollarLine,
  RiArrowUpCircleLine,
  RiArrowDownCircleLine,
  RiTimeLine,
  RiEditLine,
  RiSaveLine,
  RiRefreshLine
} from "react-icons/ri";

// Types for transactions
interface HistoryItem {
  id: string;
  userId: number;
  userEmail?: string;
  currency: string;
  amount_change: string;
  transaction_hash?: string;
  event_type: string;
  timestamp: string;
  is_processed: boolean;
}

export default function TransactionsPage() {
  const [activeTab, setActiveTab] = useState<'deposits' | 'withdrawals'>('deposits');
  const [deposits, setDeposits] = useState<HistoryItem[]>([]);
  const [withdrawals, setWithdrawals] = useState<AdminWithdrawalRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [error, setError] = useState("");
  
  // Pagination for deposits
  const [depositsPage, setDepositsPage] = useState(1);
  const [depositsTotalPages, setDepositsTotalPages] = useState(1);
  
  // Pagination for withdrawals
  const [withdrawalsPage, setWithdrawalsPage] = useState(1);
  const [withdrawalsTotalPages, setWithdrawalsTotalPages] = useState(1);
  
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [showFilters, setShowFilters] = useState(false);
  const [dateRange, setDateRange] = useState({ from: "", to: "" });
  const [searchTerm, setSearchTerm] = useState("");
  const [processingItem, setProcessingItem] = useState<string | null>(null);
  const [editingItemId, setEditingItemId] = useState<string | null>(null);
  const [selectedStatus, setSelectedStatus] = useState<WithdrawalStatus | null>(null);
  
  // Statuses for transactions
  const withdrawalStatuses = [
    { value: WithdrawalStatus.PENDING, label: 'Pending', color: '#eab308' },
    { value: WithdrawalStatus.IN_PROGRESS, label: 'In Progress', color: '#3b82f6' },
    { value: WithdrawalStatus.APPROVED_DONE, label: 'Completed', color: '#10b981' },
    { value: WithdrawalStatus.BLOCKCHAIN_CONFIRMING, label: 'Confirming', color: '#8b5cf6' },
    { value: WithdrawalStatus.REJECTED, label: 'Rejected', color: '#ef4444' }
  ];
  
  const depositStatuses = [
    { value: 'pending', label: 'Pending', color: '#eab308' },
    { value: 'confirmed', label: 'Confirmed', color: '#10b981' },
    { value: 'failed', label: 'Failed', color: '#ef4444' }
  ];

  // Load data on mount and filter changes
  useEffect(() => {
    fetchData();
  }, [activeTab, depositsPage, withdrawalsPage, statusFilter, dateRange]);

  // Function to fetch data
  const fetchData = async (refresh = false) => {
    if (refresh) {
      setIsRefreshing(true);
    } else {
      setIsLoading(true);
    }
    setError("");
    
    try {
      if (activeTab === 'deposits') {
        const params = {
          page: depositsPage,
          limit: 20,
          status: statusFilter,
          from_date: dateRange.from,
          to_date: dateRange.to,
          search: searchTerm
        };
        
        console.log('📤 Requesting ALL deposits for admin');
        
        const res = await getAllDepositsForAdmin(params); 
        setDeposits(res.items || []);
        setDepositsTotalPages(res.pages || 1);
        
        console.log('✅ Received deposits:', res.items);
      } else {
        // 🔥 FOR WITHDRAWALS USE NEW API
        const params = {
          page: withdrawalsPage,
          limit: 20,
          status: statusFilter as WithdrawalStatus | null,
          userId: searchTerm ? searchTerm : undefined
        };
        
        console.log('📤 Requesting admin withdrawals with params:', params);
        
        const withdrawalResponse: AdminWithdrawalsResponse = await getAllWithdrawalsPaginated(params);
        
        console.log('📥 API Response:', withdrawalResponse);
        
        if (withdrawalResponse.data) {
          setWithdrawals(withdrawalResponse.data);
          
          // Set pagination
          if (withdrawalResponse.pagination) {
            setWithdrawalsTotalPages(withdrawalResponse.pagination.totalPages);
          } else {
            setWithdrawalsTotalPages(1);
          }
        } else {
          setWithdrawals([]);
          setWithdrawalsTotalPages(1);
        }
      }
    } catch (err) {
      console.error('❌ Error loading data:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while loading data');
    } finally {
      setIsLoading(false);
      setIsRefreshing(false);
    }
  };

  // Refresh data handler
  const handleRefresh = () => {
    console.log('🔄 Forcing data refresh');
    fetchData(true);
  };

  // Start editing status handler
  const handleStartEdit = (item: AdminWithdrawalRequest) => {
    setEditingItemId(item.id);
    setSelectedStatus(item.status);
  };

  // Cancel editing handler
  const handleCancelEdit = () => {
    setEditingItemId(null);
    setSelectedStatus(null);
  };

  // Withdrawal status change handler
  const handleStatusChange = async (withdrawalId: string) => {
    if (!selectedStatus) return;
    
    setProcessingItem(withdrawalId);
    
    try {
      console.log(`🔄 Updating request ${withdrawalId} status to ${selectedStatus}`);
      
      await updateWithdrawalStatus(withdrawalId, selectedStatus);
      
      // Reset editing state
      setEditingItemId(null);
      setSelectedStatus(null);
      
      console.log('✅ Status successfully updated, reloading data');
      
      // Refresh transaction list after status change
      fetchData();
    } catch (err) {
      console.error('❌ Error updating status:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while updating status');
    } finally {
      setProcessingItem(null);
    }
  };

  // Get color for status
  const getStatusColor = (status: string) => {
    const statusList = activeTab === 'deposits' ? depositStatuses : withdrawalStatuses;
    const statusObj = statusList.find(s => s.value === status);
    return statusObj?.color || '#6b7280';
  };

  // Get label for status
  const getStatusLabel = (status: string) => {
    const statusList = activeTab === 'deposits' ? depositStatuses : withdrawalStatuses;
    const statusObj = statusList.find(s => s.value === status);
    return statusObj?.label || status;
  };

  // Format date
  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, 'dd MMMM yyyy, HH:mm', { locale: enUS });
    } catch (e) {
      return dateString;
    }
  };

  // Search handler
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Reset pages to first when searching
    setDepositsPage(1);
    setWithdrawalsPage(1);
    fetchData();
  };

  // Reset filters handler
  const resetFilters = () => {
    setStatusFilter(null);
    setDateRange({ from: "", to: "" });
    setSearchTerm("");
    setDepositsPage(1);
    setWithdrawalsPage(1);
  };

  // Copy to clipboard
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  // 🔥 DEPOSITS TABLE WITH SEPARATE USER ID COLUMN
  const renderDepositsTable = () => {
    if (deposits.length === 0) {
      return (
        <div className={styles.emptyState}>
          <RiExchangeDollarLine className={styles.emptyStateIcon} />
          <p>No deposit data available</p>
        </div>
      );
    }

    return (
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>Transaction ID</th>
              <th>User ID</th>
              <th>Amount</th>
              <th>Currency</th>
              <th>Date</th>
              <th>Transaction Hash</th>
            </tr>
          </thead>
          <tbody>
            {deposits.map(item => (
              <tr key={item.id}>
                <td>
                  <span 
                    className={styles.copyableId}
                    onClick={() => copyToClipboard(item.id)}
                    title="Click to copy"
                  >
                    {item.id ? `${item.id.substring(0, 8)}...` : 'ID not available'}
                  </span>
                </td>
                <td>
                  <span 
                    className={styles.copyableId}
                    onClick={() => copyToClipboard(item.userId.toString())}
                    title="Click to copy User ID"
                    style={{
                      fontWeight: 'bold',
                      color: '#2563eb',
                      cursor: 'pointer',
                      padding: '4px 8px',
                      backgroundColor: '#eff6ff',
                      borderRadius: '4px'
                    }}
                  >
                    {item.userId}
                  </span>
                </td>
                <td className={styles.amountCell}>
                  <span className={styles.amountValue}>
                    +{item.amount_change}
                  </span>
                </td>
                <td>
                  <span className={styles.currencyBadge}>
                    {item.currency}
                  </span>
                </td>
                <td className={styles.dateCell}>
                  {formatDate(item.timestamp)}
                </td>
                <td className={styles.hashCell}>
                  {item.transaction_hash ? (
                    <div className={styles.hashContainer}>
                      <a 
                        href={`https://etherscan.io/tx/${item.transaction_hash}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className={styles.txLink}
                      >
                        {item.transaction_hash.substring(0, 10)}...
                      </a>
                      <button
                        className={styles.copyButton}
                        onClick={() => copyToClipboard(item.transaction_hash!)}
                        title="Copy hash"
                      >
                        📋
                      </button>
                    </div>
                  ) : (
                    <span className={styles.noHash}>No hash</span>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // 🔥 UPDATED withdrawals table render with addresses and correct amounts
  const renderWithdrawalsTable = () => {
    if (withdrawals.length === 0) {
      return (
        <div className={styles.emptyState}>
          <RiExchangeDollarLine className={styles.emptyStateIcon} />
          <p>No withdrawal data available</p>
        </div>
      );
    }

    return (
      <div className={styles.tableContainer}>
        <table className={styles.table}>
          <thead>
            <tr>
              <th>Request ID</th>
              <th>User</th>
              <th>Original Amount</th>
              <th>Net Amount</th>
              <th>Fee</th>
              <th>Currency</th>
              <th>Withdrawal Address</th>
              <th>Status</th>
              <th>Date</th>
              <th>Transaction Hash</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {withdrawals.map(item => (
              <tr key={item.id}>
                <td>
                  <span 
                    className={styles.copyableId}
                    onClick={() => copyToClipboard(item.internalTxReference || item.id)}
                    title="Click to copy"
                  >
                    {item.internalTxReference ? 
                      `${item.internalTxReference.substring(0, 8)}...` : 
                      (item.id ? `${item.id.substring(0, 8)}...` : 'ID not available')
                    }
                  </span>
                </td>
                <td>
                  <div className={styles.userCell}>
                    <div>{item.userEmail || item.email || 'No email'}</div>
                    <div className={styles.userId}>ID: {item.userId}</div>
                  </div>
                </td>
                <td className={styles.amountCell}>
                  <span className={styles.originalAmount}>
                    {item.originalAmount || (item.amount + (item.fee || 0))}
                  </span>
                </td>
                <td className={styles.amountCell}>
                  <span className={styles.amountValue}>
                    -{item.netAmount || item.amount}
                  </span>
                </td>
                <td className={styles.feeCell}>
                  <span className={styles.feeValue}>
                    {item.feeAmount || item.fee || 0}
                  </span>
                </td>
                <td>
                  <span className={styles.currencyBadge}>
                    {item.currency}
                  </span>
                </td>
                <td className={styles.addressCell}>
                  <div className={styles.addressContainer}>
                    {(() => {
                      // Determine address from different possible fields
                      const address = item.destinationWallet || 
                                    (item.destinationWallets && item.destinationWallets.length > 0 ? item.destinationWallets[0] : null);
                      
                      if (!address) {
                        return <span className={styles.noAddress}>No address</span>;
                      }
                      
                      return (
                        <>
                          <span 
                            className={styles.address}
                            title={address}
                          >
                            {`${address.substring(0, 8)}...${address.substring(-6)}`}
                          </span>
                          <button
                            className={styles.copyButton}
                            onClick={() => copyToClipboard(address)}
                            title="Copy address"
                          >
                            📋
                          </button>
                        </>
                      );
                    })()}
                  </div>
                </td>
                <td>
                  <span
                    className={styles.statusBadge}
                    style={{ backgroundColor: getStatusColor(item.status) }}
                  >
                    {getStatusLabel(item.status)}
                  </span>
                </td>
                <td className={styles.dateCell}>
                  {formatDate(item.createdAt)}
                </td>
                <td className={styles.hashCell}>
                  {item.blockchainTxHash ? (
                    <div className={styles.hashContainer}>
                      <a 
                        href={`https://etherscan.io/tx/${item.blockchainTxHash}`} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className={styles.txLink}
                      >
                        {item.blockchainTxHash.substring(0, 10)}...
                      </a>
                      <button
                        className={styles.copyButton}
                        onClick={() => copyToClipboard(item.blockchainTxHash!)}
                        title="Copy hash"
                      >
                        📋
                      </button>
                    </div>
                  ) : (
                    <span className={styles.noHash}>No hash</span>
                  )}
                </td>
                <td>
                  {editingItemId === item.id ? (
                    <div className={styles.editStatusContainer}>
                      <select 
                        value={selectedStatus || ''}
                        onChange={(e) => setSelectedStatus(e.target.value as WithdrawalStatus)}
                        className={styles.statusSelect}
                      >
                        {withdrawalStatuses.map(status => (
                          <option key={status.value} value={status.value}>
                            {status.label}
                          </option>
                        ))}
                      </select>
                      
                      <div className={styles.editButtons}>
                        <button
                          className={`${styles.actionButton} ${styles.saveButton}`}
                          onClick={() => handleStatusChange(item.id)}
                          disabled={processingItem === item.id || !selectedStatus}
                          title="Save"
                        >
                          {processingItem === item.id ? (
                            <RiLoader4Line className={styles.buttonSpinner} />
                          ) : (
                            <RiSaveLine />
                          )}
                        </button>
                        
                        <button
                          className={`${styles.actionButton} ${styles.cancelButton}`}
                          onClick={handleCancelEdit}
                          disabled={processingItem === item.id}
                          title="Cancel"
                        >
                          <RiCloseLine />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <button
                      className={`${styles.actionButton} ${styles.editButton}`}
                      onClick={() => handleStartEdit(item)}
                      title="Change status"
                    >
                      <RiEditLine />
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    );
  };

  // Render pagination (unchanged)
  const renderPagination = () => {
    const currentPage = activeTab === 'deposits' ? depositsPage : withdrawalsPage;
    const totalPages = activeTab === 'deposits' ? depositsTotalPages : withdrawalsTotalPages;
    const setPage = activeTab === 'deposits' ? setDepositsPage : setWithdrawalsPage;

    if (totalPages <= 1) return null;

    return (
      <div className={styles.pagination}>
        <button
          onClick={() => setPage(p => Math.max(1, p - 1))}
          disabled={currentPage === 1}
          className={styles.paginationButton}
        >
          &lt; Previous
        </button>
        
        <div className={styles.paginationNumbers}>
          {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
            let pageNum;
            if (totalPages <= 5) {
              pageNum = i + 1;
            } else if (currentPage <= 3) {
              pageNum = i + 1;
            } else if (currentPage >= totalPages - 2) {
              pageNum = totalPages - 4 + i;
            } else {
              pageNum = currentPage - 2 + i;
            }
            
            return (
              <button
                key={pageNum}
                onClick={() => setPage(pageNum)}
                className={`${styles.paginationNumber} ${currentPage === pageNum ? styles.activePage : ''}`}
              >
                {pageNum}
              </button>
            );
          })}
        </div>
        
        <span className={styles.pageInfo}>
          Page {currentPage} of {totalPages}
        </span>
        
        <button
          onClick={() => setPage(p => Math.min(totalPages, p + 1))}
          disabled={currentPage === totalPages}
          className={styles.paginationButton}
        >
          Next &gt;
        </button>
      </div>
    );
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1 className={styles.title}>Transaction Management</h1>
        
        <div className={styles.headerActions}>
          <button 
            className={`${styles.refreshButton} ${isRefreshing ? styles.refreshing : ''}`}
            onClick={handleRefresh}
            disabled={isRefreshing}
            title="Refresh data"
          >
            <RiRefreshLine className={isRefreshing ? styles.spin : ''} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>
        
        <div className={styles.tabs}>
          <button 
            className={`${styles.tabButton} ${activeTab === 'deposits' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('deposits')}
          >
            <RiArrowDownCircleLine className={styles.tabIcon} />
            Deposits
            {deposits.length > 0 && (
              <span className={styles.tabBadge}>{deposits.length}</span>
            )}
          </button>
          <button 
            className={`${styles.tabButton} ${activeTab === 'withdrawals' ? styles.activeTab : ''}`}
            onClick={() => setActiveTab('withdrawals')}
          >
            <RiArrowUpCircleLine className={styles.tabIcon} />
            Withdrawals
            {withdrawals.length > 0 && (
              <span className={styles.tabBadge}>{withdrawals.length}</span>
            )}
          </button>
        </div>
      </div>
      
      <div className={styles.actionsRow}>
        <form onSubmit={handleSearch} className={styles.searchForm}>
          <div className={styles.searchContainer}>
            <RiSearchLine className={styles.searchIcon} />
            <input
              type="text"
              placeholder={activeTab === 'deposits' ? "Search by User ID..." : "Search by User ID..."}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={styles.searchInput}
            />
          </div>
          <button type="submit" className={styles.searchButton}>Search</button>
        </form>
        
        <button 
          className={styles.filterButton} 
          onClick={() => setShowFilters(!showFilters)}
        >
          <RiFilter2Line />
          Filters
          {(statusFilter || dateRange.from || dateRange.to) && (
            <span className={styles.filterActiveBadge}>●</span>
          )}
        </button>
      </div>
      
      {showFilters && (
        <div className={styles.filtersContainer}>
          <div className={styles.filterGroup}>
            <label>Status:</label>
            <select 
              value={statusFilter || ''} 
              onChange={(e) => setStatusFilter(e.target.value || null)}
              className={styles.filterSelect}
            >
              <option value="">All statuses</option>
              {(activeTab === 'deposits' ? depositStatuses : withdrawalStatuses).map(status => (
                <option key={status.value} value={status.value}>{status.label}</option>
              ))}
            </select>
          </div>
          
          <div className={styles.filterGroup}>
            <label>Period:</label>
            <div className={styles.dateInputs}>
              <input
                type="date"
                value={dateRange.from}
                onChange={(e) => setDateRange(prev => ({ ...prev, from: e.target.value }))}
                className={styles.dateInput}
              />
              <span>to</span>
              <input
                type="date"
                value={dateRange.to}
                onChange={(e) => setDateRange(prev => ({ ...prev, to: e.target.value }))}
                className={styles.dateInput}
              />
            </div>
          </div>
          
          <button onClick={resetFilters} className={styles.resetButton}>
            Reset filters
          </button>
        </div>
      )}
      
      {error && <div className={styles.errorMessage}>{error}</div>}
      
      {isLoading ? (
        <div className={styles.loadingContainer}>
          <RiLoader4Line className={styles.loadingSpinner} />
          <p>Loading data...</p>
        </div>
      ) : (
        <>
          <div className={styles.transactionsContainer}>
            {activeTab === 'deposits' ? renderDepositsTable() : renderWithdrawalsTable()}
          </div>
          
          {/* Pagination for both tabs */}
          {renderPagination()}
        </>
      )}
    </div>
  );
}