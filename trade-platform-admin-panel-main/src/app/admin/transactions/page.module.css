.container {
  padding: 1.5rem;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.title {
  font-size: 1.8rem;
  font-weight: 600;
  color: #1a1a2e;
  margin: 0;
}

/* ====== ОБНОВЛЕННЫЕ СТИЛИ ДЛЯ HEADER ACTIONS ====== */
.headerActions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.refreshButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1.25rem;
  background-color: #f3f4f6;
  color: #4b5563;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.refreshButton:hover:not(:disabled) {
  background-color: #e5e7eb;
  transform: translateY(-1px);
}

.refreshButton:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.refreshing {
  background-color: #dbeafe !important;
  color: #3b82f6 !important;
  border-color: #93c5fd !important;
}

.spin {
  animation: spin 1s linear infinite;
}

/* ====== ОБНОВЛЕННЫЕ СТИЛИ ДЛЯ ТАБОВ ====== */
.tabs {
  display: flex;
  gap: 0.75rem;
}

.tabButton {
  display: flex;
  align-items: center;
  position: relative;
  padding: 0.75rem 1.25rem;
  background-color: #f3f4f6;
  border: none;
  border-radius: 0.5rem;
  font-size: 0.95rem;
  font-weight: 500;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tabIcon {
  margin-right: 0.5rem;
  font-size: 1.25rem;
}

.activeTab {
  background-color: #4f46e5;
  color: white;
}

/* Счетчики в табах */
.tabBadge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.25rem;
  height: 1.25rem;
  margin-left: 0.5rem;
  background-color: rgba(255, 255, 255, 0.2);
  color: inherit;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0 0.375rem;
}

.activeTab .tabBadge {
  background-color: rgba(255, 255, 255, 0.25);
  color: white;
}

.actionsRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.searchForm {
  display: flex;
  gap: 0.75rem;
}

.searchContainer {
  position: relative;
  width: 320px;
}

.searchIcon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
}

.searchInput {
  width: 100%;
  padding: 0.625rem 1rem 0.625rem 2.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.95rem;
}

.searchButton {
  padding: 0.625rem 1.25rem;
  background-color: #4f46e5;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.searchButton:hover {
  background-color: #4338ca;
}

.filterButton {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  position: relative;
  padding: 0.625rem 1.25rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.filterButton:hover {
  background-color: #f9fafb;
}

/* Индикатор активных фильтров */
.filterActiveBadge {
  position: absolute;
  top: -0.25rem;
  right: -0.25rem;
  width: 0.75rem;
  height: 0.75rem;
  background-color: #ef4444;
  border-radius: 50%;
  font-size: 0.5rem;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filtersContainer {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  padding: 1.5rem;
  background-color: #f9fafb;
  border-radius: 0.5rem;
  margin-bottom: 2rem;
}

.filterGroup {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.filterGroup label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
}

.filterSelect {
  padding: 0.5rem;
  min-width: 180px;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
}

.dateInputs {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dateInput {
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
}

.resetButton {
  padding: 0.625rem 1.25rem;
  background-color: transparent;
  color: #6b7280;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  margin-left: auto;
  align-self: flex-end;
}

.resetButton:hover {
  background-color: #f3f4f6;
}

.errorMessage {
  padding: 1rem;
  background-color: #fee2e2;
  color: #b91c1c;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
  border: 1px solid #fecaca;
}

.loadingContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

.loadingSpinner {
  font-size: 2rem;
  color: #4f46e5;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.transactionsContainer {
  margin-bottom: 2rem;
}

.tableContainer {
  width: 100%;
  overflow-x: auto;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table {
  width: 100%;
  border-collapse: collapse;
  min-width: 1200px; /* Минимальная ширина для новых колонок */
}

.table th {
  padding: 0.875rem 1rem;
  text-align: left;
  font-size: 0.875rem;
  font-weight: 600;
  color: #4b5563;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  white-space: nowrap; /* Предотвращаем перенос заголовков */
}

.table td {
  padding: 1rem;
  font-size: 0.95rem;
  color: #1f2937;
  border-bottom: 1px solid #e5e7eb;
  vertical-align: top;
}

.table tr:last-child td {
  border-bottom: none;
}

.table tr:hover {
  background-color: #f9fafb;
}

/* ====== ID И КОПИРУЕМЫЕ ЭЛЕМЕНТЫ ====== */
.copyableId {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.copyableId:hover {
  background-color: #e5e7eb;
  transform: translateY(-1px);
}

/* ====== ЯЧЕЙКИ ПОЛЬЗОВАТЕЛЕЙ ====== */
.userCell {
  display: flex;
  flex-direction: column;
  min-width: 160px;
}

.userId {
  font-size: 0.8rem;
  color: #6b7280;
  margin-top: 0.25rem;
  font-weight: 500;
}

/* ====== СУММЫ И ФИНАНСОВЫЕ ДАННЫЕ ====== */
.amountCell {
  font-weight: 600;
  text-align: right;
  min-width: 100px;
}

.amountValue {
  color: #dc2626; /* Красный для выводов */
  font-size: 1rem;
}

.originalAmount {
  color: #6b7280; /* Серый для исходной суммы */
  font-size: 0.9rem;
  text-decoration: line-through;
}

.feeCell {
  text-align: right;
  min-width: 80px;
}

.feeValue {
  color: #f59e0b; /* Оранжевый для комиссии */
  font-weight: 500;
  font-size: 0.9rem;
}

/* Для пополнений (зеленый цвет) */
.amountCell .amountValue[style*="+"]:first-child,
.amountValue:first-child {
  color: #059669; /* Зеленый для пополнений */
}

/* ====== ВАЛЮТНЫЕ БЕЙДЖИ ====== */
.currencyBadge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  background-color: #dbeafe;
  color: #1e40af;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

/* ====== АДРЕСА КОШЕЛЬКОВ ====== */
.addressCell {
  min-width: 200px;
  max-width: 250px;
}

.addressContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.address {
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.85rem;
  color: #4b5563;
  background-color: #f9fafb;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #e5e7eb;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.noAddress {
  color: #9ca3af;
  font-style: italic;
  font-size: 0.85rem;
}

/* ====== КОПИРОВАНИЕ ====== */
.copyButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  background-color: transparent;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
}

.copyButton:hover {
  background-color: #f3f4f6;
  border-color: #d1d5db;
  transform: translateY(-1px);
}

/* ====== СТАТУСЫ ====== */
.statusBadge {
  display: inline-block;
  padding: 0.375rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* ====== ХЕШИ ТРАНЗАКЦИЙ ====== */
.hashCell {
  min-width: 180px;
  max-width: 200px;
}

.hashContainer {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.txLink {
  color: #4f46e5;
  text-decoration: none;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 0.85rem;
  background-color: #f3f4f6;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  border: 1px solid #e5e7eb;
  transition: all 0.2s ease;
}

.txLink:hover {
  background-color: #e5e7eb;
  text-decoration: underline;
}

.noHash {
  color: #9ca3af;
  font-style: italic;
  font-size: 0.85rem;
}

/* ====== ДАТЫ ====== */
.dateCell {
  min-width: 140px;
  font-size: 0.9rem;
  color: #6b7280;
}

/* ====== ДЕЙСТВИЯ И КНОПКИ ====== */
.actionButton {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border: none;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;
}

.actionButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.buttonSpinner {
  animation: spin 1s linear infinite;
}

/* ====== РЕДАКТИРОВАНИЕ СТАТУСОВ ====== */
.editStatusContainer {
  display: flex;
  flex-direction: column;
  gap: 8px;
  min-width: 180px;
}

.statusSelect {
  padding: 6px 8px;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  width: 100%;
  color: #333;
}

.editButtons {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

/* Кнопка редактирования */
.editButton {
  background-color: #e0f2fe !important;
  color: #0284c7 !important;
  border: 1px solid #0284c7 !important;
}

.editButton:hover {
  background-color: #bae6fd !important;
  transform: translateY(-1px);
}

/* Кнопка сохранения */
.saveButton {
  background-color: #dcfce7 !important;
  color: #059669 !important;
  border: 1px solid #059669 !important;
}

.saveButton:hover:not(:disabled) {
  background-color: #bbf7d0 !important;
  transform: translateY(-1px);
}

/* Кнопка отмены */
.cancelButton {
  background-color: #f3f4f6 !important;
  color: #4b5563 !important;
  border: 1px solid #9ca3af !important;
}

.cancelButton:hover:not(:disabled) {
  background-color: #e5e7eb !important;
  transform: translateY(-1px);
}

/* ====== ПУСТОЕ СОСТОЯНИЕ ====== */
.emptyState {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.emptyStateIcon {
  font-size: 3rem;
  color: #9ca3af;
  margin-bottom: 1.5rem;
}

/* ====== ПАГИНАЦИЯ ====== */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-top: 1.5rem;
  flex-wrap: wrap;
}

.paginationButton {
  padding: 0.5rem 1rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
}

.paginationButton:hover:not(:disabled) {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.paginationButton:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.paginationNumbers {
  display: flex;
  gap: 0.25rem;
}

.paginationNumber {
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;
  min-width: 2.5rem;
  text-align: center;
}

.paginationNumber:hover {
  background-color: #f3f4f6;
  transform: translateY(-1px);
}

.activePage {
  background-color: #4f46e5 !important;
  color: white !important;
  border-color: #4f46e5 !important;
}

.pageInfo {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

/* ====== АДАПТИВНОСТЬ ДЛЯ МОБИЛЬНЫХ УСТРОЙСТВ ====== */
@media (max-width: 1024px) {
  .table {
    min-width: 1000px; /* Уменьшаем минимальную ширину для планшетов */
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .headerActions {
    width: 100%;
    justify-content: flex-start;
  }
  
  .tabs {
    width: 100%;
  }
  
  .tabButton {
    flex: 1;
    justify-content: center;
  }
  
  .tabBadge {
    margin-left: 0.25rem;
  }
  
  .actionsRow {
    flex-direction: column;
    gap: 1rem;
  }
  
  .searchForm {
    width: 100%;
  }
  
  .searchContainer {
    width: 100%;
  }
  
  .filterButton {
    width: 100%;
    justify-content: center;
  }
  
  .filtersContainer {
    flex-direction: column;
  }
  
  .table {
    font-size: 0.85rem;
    min-width: 900px; /* Еще меньше для мобильных */
  }
  
  .table th,
  .table td {
    padding: 0.75rem 0.5rem;
  }
  
  /* Скрываем некоторые колонки на мобильных */
  .table th:nth-child(3), /* Исходная сумма */
  .table td:nth-child(3),
  .table th:nth-child(5), /* Комиссия */
  .table td:nth-child(5) {
    display: none;
  }
  
  .editStatusContainer {
    min-width: 140px;
  }
  
  .editButtons {
    flex-direction: column;
    gap: 4px;
  }
  
  .actionButton {
    width: 2rem;
    height: 2rem;
    font-size: 0.9rem;
  }
  
  .pagination {
    gap: 0.5rem;
    padding: 0 1rem;
  }
  
  .paginationNumbers {
    gap: 0.125rem;
  }
  
  .paginationNumber {
    padding: 0.375rem 0.5rem;
    min-width: 2rem;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 1rem;
  }
  
  .title {
    font-size: 1.5rem;
  }
  
  .refreshButton {
    padding: 0.5rem 1rem;
    font-size: 0.85rem;
  }
  
  .table {
    min-width: 700px; /* Еще более компактно */
  }
  
  /* Скрываем еще больше колонок на очень маленьких экранах */
  .table th:nth-child(9), /* Дата */
  .table td:nth-child(9),
  .table th:nth-child(10), /* Хеш */
  .table td:nth-child(10) {
    display: none;
  }
  
  .searchInput {
    font-size: 0.9rem;
  }
  
  .filterSelect,
  .dateInput {
    font-size: 0.9rem;
  }
}