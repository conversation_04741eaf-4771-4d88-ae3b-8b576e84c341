# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
.env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.idea/
.vscode/
*.swp
*.swo
*~
.project
.settings
.classpath
.c9/
*.launch
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# OS
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msm
*.msp
*.lnk

# Logs
logs/
*.log
lerna-debug.log*
.pnpm-store/

# Package managers
pnpm-lock.yaml
yarn.lock
bun.lockb

# Temporary files
*.tmp
*.temp
.cache/
.parcel-cache/
.turbo/

# Build outputs
dist/
dist-ssr/
*.local

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
cypress/videos/
cypress/screenshots/
.nyc_output/
junit.xml

# Documentation
docs/.vitepress/dist/
docs/.vitepress/cache/
/CLAUDE.md

# Miscellaneous
.eslintcache
.stylelintcache
*.pid
*.seed
*.pid.lock
.grunt/
bower_components/
/.claude/
