# 🔥 WebSocket Position Events - Техническая документация

## 📋 Обзор

Данная документация описывает систему real-time уведомлений для событий позиций в Trade Service с использованием WebSocket соединений. Система обеспечивает мгновенные уведомления о ликвидации, активации ордеров, предупреждениях о рисках и других критических событиях торговли.

---

## 🏗️ Архитектура системы

### **Компоненты бэкенда:**

1. **PnL Updater** (`app/core/pnl_updater.py`) - обновляет PnL каждые 5 секунд и отправляет WebSocket события
2. **WebSocket Connection Manager** (`app/api/websocket/connection_manager.py`) - управляет подключениями и подписками
3. **Position Events Handler** (`app/api/websocket/handlers.py`) - обрабатывает отправку событий позиций
4. **Price Service** (`app/core/price_service.py`) - предоставляет актуальные цены для расчетов

### **Поток данных:**
```
Price Updates (Binance) → PnL Updater → Position Events → WebSocket → Frontend
```

---

## 🔌 WebSocket API

### **Endpoint подключения**
```
wss://api/ws/user-events
```

### **Подписки при подключении**

#### 1. Подписка на события позиций
```javascript
ws.send(JSON.stringify({
  action: "subscribe_position_events",
  data: { 
    user_id: currentUserId 
  }
}));

// Ответ сервера
{
  status: "success",
  action: "subscribe_position_events", 
  message: "Subscribed to position events for user 123"
}
```

#### 2. Подписка на алерты ликвидации
```javascript
ws.send(JSON.stringify({
  action: "subscribe_liquidation_alerts",
  data: { 
    user_id: currentUserId,
    position_ids: [456, 789] // позиции для мониторинга
  }
}));

// Ответ сервера
{
  status: "success",
  action: "subscribe_liquidation_alerts",
  message: "Subscribed to liquidation alerts for user 123",
  monitored_positions: [456, 789]
}
```

#### 3. Подписка на обновления цен
```javascript
ws.send(JSON.stringify({
  action: "subscribe_prices",
  data: { 
    user_id: currentUserId 
  }
}));
```

---

## 📨 Типы событий

### **1. 💥 Ликвидация позиции**

**Когда отправляется:** Позиция достигла ликвидационной цены

```javascript
{
  type: "position_liquidated",
  data: {
    position_id: 123,
    trading_pair: "BTC/USDT",
    direction: "LONG",
    liquidation_price: 45000.00,
    current_price: 44999.50,
    pnl: -500.00,
    margin: 500.00,
    timestamp: "2024-01-15T10:30:00.000Z"
  },
  timestamp: "2024-01-15T10:30:00.000Z"
}
```

**Действия фронтенда:**
- ✅ Удалить позицию из локального стейта
- ✅ Показать критическое уведомление (красный toast)
- ✅ Воспроизвести звуковой сигнал
- ✅ Обновить баланс пользователя
- ✅ Логировать событие для аналитики

### **2. ⚠️ Предупреждение о близкой ликвидации**

**Когда отправляется:** Позиция находится в 5% от ликвидационной цены

```javascript
{
  type: "position_near_liquidation",
  data: {
    position_id: 123,
    trading_pair: "BTC/USDT",
    direction: "LONG",
    current_price: 45500.00,
    liquidation_price: 45000.00,
    distance_percent: 1.1, // 1.1% до ликвидации
    pnl: -450.00,
    timestamp: "2024-01-15T10:25:00.000Z"
  },
  timestamp: "2024-01-15T10:25:00.000Z"
}
```

**Действия фронтенда:**
- ⚠️ Показать предупреждение (желтый toast)
- ⚠️ Добавить визуальный индикатор к позиции (красная рамка)
- ⚠️ Предложить добавить маржу или закрыть позицию
- ⚠️ Включить более частое обновление для этой позиции

### **3. 🚀 Активация лимитного ордера**

**Когда отправляется:** Лимитный ордер был активирован при достижении entry_price

```javascript
{
  type: "limit_order_filled",
  data: {
    position_id: 124,
    trading_pair: "ETH/USDT",
    direction: "SHORT",
    entry_price: 3000.00,
    fill_price: 3000.50,
    position_size: 1000.00,
    margin: 200.00,
    leverage: 5,
    timestamp: "2024-01-15T10:35:00.000Z"
  },
  timestamp: "2024-01-15T10:35:00.000Z"
}
```

**Действия фронтенда:**
- 🚀 Переместить из "Отложенные ордера" в "Активные позиции"
- 🚀 Показать успешное уведомление (зеленый toast)
- 🚀 Начать локальные расчеты PnL для новой позиции
- 🚀 Обновить счетчики ордеров и позиций

### **4. ✅ Закрытие позиции**

**Когда отправляется:** Позиция закрыта по take profit, stop loss или вручную

```javascript
{
  type: "position_closed",
  data: {
    position_id: 125,
    trading_pair: "BTC/USDT",
    direction: "LONG",
    entry_price: 44000.00,
    exit_price: 46000.00,
    pnl: 500.00,
    return_percentage: 25.0,
    close_reason: "take_profit", // "manual", "take_profit", "stop_loss"
    timestamp: "2024-01-15T10:40:00.000Z"
  },
  timestamp: "2024-01-15T10:40:00.000Z"
}
```

**Действия фронтенда:**
- ✅ Удалить из активных позиций
- ✅ Показать результат (зеленый/красный toast в зависимости от PnL)
- ✅ Обновить баланс и историю торгов
- ✅ Записать в статистику торговли

### **5. 📊 Обновления цен в реальном времени**

**Когда отправляется:** При каждом обновлении цен от Binance API

```javascript
{
  status: "success",
  action: "price_update",
  data: {
    "BTC/USDT": 45234.56,
    "ETH/USDT": 3001.23,
    "LTC/USDT": 178.90
    // ... другие торговые пары
  }
}
```

**Действия фронтенда:**
- 📊 Обновить локальные PnL расчеты
- 📊 Обновить отображение цен на графиках
- 📊 Проверить условия для алертов

---

## 🧮 Локальные расчеты PnL на фронтенде

### **Основные формулы:**

```javascript
// Для LONG позиций
const calculateLongPnL = (currentPrice, entryPrice, positionSize) => {
  const priceChange = currentPrice - entryPrice;
  const pnl = (priceChange / entryPrice) * positionSize;
  return pnl;
};

// Для SHORT позиций
const calculateShortPnL = (currentPrice, entryPrice, positionSize) => {
  const priceChange = entryPrice - currentPrice;
  const pnl = (priceChange / entryPrice) * positionSize;
  return pnl;
};

// Процент доходности
const calculateReturnPercentage = (pnl, margin) => {
  return (pnl / margin) * 100;
};

// Проверка близости к ликвидации
const checkNearLiquidation = (currentPrice, liquidationPrice) => {
  const distance = Math.abs(currentPrice - liquidationPrice) / liquidationPrice;
  return {
    isNear: distance <= 0.05, // 5%
    distancePercent: distance * 100
  };
};
```

### **Пример real-time обновления:**

```javascript
const useRealTimePnL = (positions, currentPrices) => {
  return useMemo(() => {
    return positions.map(position => {
      const currentPrice = currentPrices[position.trading_pair];
      if (!currentPrice) return position;

      let pnl;
      if (position.direction === 'LONG') {
        pnl = calculateLongPnL(currentPrice, position.entry_price, position.position_size);
      } else {
        pnl = calculateShortPnL(currentPrice, position.entry_price, position.position_size);
      }

      const returnPercentage = calculateReturnPercentage(pnl, position.margin);
      const liquidationCheck = checkNearLiquidation(currentPrice, position.liquidation_price);

      return {
        ...position,
        current_price: currentPrice,
        pnl,
        return_percentage: returnPercentage,
        is_near_liquidation: liquidationCheck.isNear,
        liquidation_distance_percent: liquidationCheck.distancePercent
      };
    });
  }, [positions, currentPrices]);
};
```

---

## 🎨 UI/UX Требования

### **Типы уведомлений:**

#### 💥 Критические (Ликвидация)
- **Цвет:** Красный (#FF4444)
- **Длительность:** Постоянное (требует подтверждения)
- **Звук:** Критический сигнал
- **Позиция:** Центр экрана (modal)
- **Действия:** [Понятно] [Подробнее]

#### ⚠️ Предупреждения (Близкая ликвидация)
- **Цвет:** Желтый (#FFB347)
- **Длительность:** 15 секунд
- **Звук:** Предупреждающий сигнал
- **Позиция:** Верх экрана
- **Действия:** [Добавить маржу] [Закрыть позицию] [x]

#### 🚀 Успешные (Активация ордера)
- **Цвет:** Зеленый (#4CAF50)
- **Длительность:** 5 секунд
- **Звук:** Успешный сигнал
- **Позиция:** Верх экрана
- **Действия:** [Посмотреть] [x]

#### ✅ Информационные (Закрытие позиции)
- **Цвет:** Зеленый/Красный (в зависимости от PnL)
- **Длительность:** 7 секунд
- **Звук:** Нейтральный сигнал
- **Позиция:** Верх экрана
- **Действия:** [История] [x]

### **Визуальные индикаторы позиций:**

```css
/* Нормальная позиция */
.position-card {
  border: 1px solid #e0e0e0;
  background: #ffffff;
}

/* Позиция близкая к ликвидации */
.position-card.near-liquidation {
  border: 2px solid #ff4444;
  background: #fff5f5;
  animation: pulse-warning 2s infinite;
}

/* Анимация предупреждения */
@keyframes pulse-warning {
  0%, 100% { border-color: #ff4444; }
  50% { border-color: #ff8888; }
}

/* PnL с цветовой индикацией */
.pnl-positive { color: #4CAF50; }
.pnl-negative { color: #FF4444; }
.pnl-neutral { color: #666666; }
```

### **Индикатор подключения WebSocket:**

```javascript
const WebSocketStatus = ({ connected }) => (
  <div className={`ws-status ${connected ? 'connected' : 'disconnected'}`}>
    <div className="status-dot" />
    <span>{connected ? 'Real-time' : 'Reconnecting...'}</span>
  </div>
);
```

---

## 🛠️ Реализация на фронтенде

### **1. WebSocket Hook**

```javascript
import { useState, useEffect, useRef } from 'react';

export const useWebSocketPositionEvents = (userId) => {
  const [connected, setConnected] = useState(false);
  const [lastEvent, setLastEvent] = useState(null);
  const wsRef = useRef(null);
  const reconnectTimeoutRef = useRef(null);

  const connect = () => {
    try {
      const ws = new WebSocket('wss://api/ws/user-events');
      
      ws.onopen = () => {
        setConnected(true);
        console.log('🔌 WebSocket connected');
        
        // Подписываемся на события
        ws.send(JSON.stringify({
          action: "subscribe_position_events",
          data: { user_id: userId }
        }));
        
        ws.send(JSON.stringify({
          action: "subscribe_liquidation_alerts", 
          data: { user_id: userId }
        }));
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        setLastEvent(data);
      };

      ws.onclose = () => {
        setConnected(false);
        console.log('🔌 WebSocket disconnected, attempting reconnect...');
        
        // Переподключение через 5 секунд
        reconnectTimeoutRef.current = setTimeout(() => {
          connect();
        }, 5000);
      };

      ws.onerror = (error) => {
        console.error('❌ WebSocket error:', error);
      };

      wsRef.current = ws;
    } catch (error) {
      console.error('❌ Failed to connect WebSocket:', error);
    }
  };

  useEffect(() => {
    if (userId) {
      connect();
    }

    return () => {
      if (wsRef.current) {
        wsRef.current.close();
      }
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, [userId]);

  return { connected, lastEvent };
};
```

### **2. Position Events Handler**

```javascript
import { useToast } from '@chakra-ui/react';

export const usePositionEventsHandler = () => {
  const toast = useToast();

  const handleEvent = (event, setPositions, refetchBalance) => {
    switch (event.type) {
      case 'position_liquidated':
        handleLiquidation(event.data, setPositions, refetchBalance);
        break;
      
      case 'position_near_liquidation':
        handleNearLiquidationWarning(event.data);
        break;
      
      case 'limit_order_filled':
        handleLimitOrderFilled(event.data, setPositions);
        break;
      
      case 'position_closed':
        handlePositionClosed(event.data, setPositions, refetchBalance);
        break;
        
      default:
        console.log('Unknown event type:', event.type);
    }
  };

  const handleLiquidation = (data, setPositions, refetchBalance) => {
    // Удаляем позицию из стейта
    setPositions(prev => prev.filter(p => p.id !== data.position_id));
    
    // Критическое уведомление
    toast({
      title: "💥 Позиция ликвидирована",
      description: `${data.trading_pair} ${data.direction} по цене ${data.liquidation_price}`,
      status: "error",
      duration: null, // Постоянное
      isClosable: true,
      position: "top"
    });
    
    // Звуковой сигнал
    playSound('liquidation');
    
    // Обновляем баланс
    refetchBalance();
    
    // Аналитика
    trackEvent('position_liquidated', {
      position_id: data.position_id,
      trading_pair: data.trading_pair,
      pnl: data.pnl
    });
  };

  const handleNearLiquidationWarning = (data) => {
    toast({
      title: "⚠️ Позиция близка к ликвидации",
      description: `${data.trading_pair} - осталось ${data.distance_percent}%`,
      status: "warning",
      duration: 15000,
      isClosable: true,
      position: "top"
    });
    
    playSound('warning');
  };

  const handleLimitOrderFilled = (data, setPositions) => {
    // Обновляем позицию со статусом OPEN
    setPositions(prev => prev.map(p => 
      p.id === data.position_id 
        ? { ...p, status: 'OPEN', current_price: data.fill_price }
        : p
    ));
    
    toast({
      title: "🚀 Лимитный ордер исполнен",
      description: `${data.trading_pair} ${data.direction} по цене ${data.fill_price}`,
      status: "success",
      duration: 5000,
      isClosable: true
    });
    
    playSound('success');
  };

  const handlePositionClosed = (data, setPositions, refetchBalance) => {
    // Удаляем закрытую позицию
    setPositions(prev => prev.filter(p => p.id !== data.position_id));
    
    const isProfit = data.pnl > 0;
    
    toast({
      title: `✅ Позиция закрыта ${isProfit ? '📈' : '📉'}`,
      description: `${data.trading_pair}: ${data.pnl > 0 ? '+' : ''}${data.pnl.toFixed(2)} USDT (${data.return_percentage.toFixed(1)}%)`,
      status: isProfit ? "success" : "error",
      duration: 7000,
      isClosable: true
    });
    
    playSound(isProfit ? 'profit' : 'loss');
    refetchBalance();
  };

  const playSound = (type) => {
    // Реализация звуковых уведомлений
    const audio = new Audio(`/sounds/${type}.mp3`);
    audio.play().catch(e => console.log('Sound play failed:', e));
  };

  return { handleEvent };
};
```

### **3. Интеграция в компонент позиций**

```javascript
import React from 'react';
import { useWebSocketPositionEvents, usePositionEventsHandler } from './hooks';

const PositionsPage = () => {
  const { user } = useAuth();
  const [positions, setPositions] = useState([]);
  const { connected, lastEvent } = useWebSocketPositionEvents(user.id);
  const { handleEvent } = usePositionEventsHandler();
  const { refetch: refetchBalance } = useBalance();

  // Обработка WebSocket событий
  useEffect(() => {
    if (lastEvent) {
      handleEvent(lastEvent, setPositions, refetchBalance);
    }
  }, [lastEvent]);

  // Real-time PnL расчеты
  const positionsWithPnL = useRealTimePnL(positions, currentPrices);

  return (
    <div>
      <WebSocketStatus connected={connected} />
      
      <div className="positions-grid">
        {positionsWithPnL.map(position => (
          <PositionCard 
            key={position.id} 
            position={position}
            isNearLiquidation={position.is_near_liquidation}
          />
        ))}
      </div>
    </div>
  );
};
```

---

## 🔒 Безопасность и производительность

### **Ограничения частоты (Rate Limiting):**
- Максимум 1 предупреждение о ликвидации в 30 секунд на позицию
- Throttling PnL обновлений до 60 FPS
- Debounce визуальных обновлений при частых изменениях

### **Проверка целостности данных:**
```javascript
const validateEventData = (event) => {
  const requiredFields = {
    'position_liquidated': ['position_id', 'trading_pair', 'liquidation_price'],
    'position_near_liquidation': ['position_id', 'distance_percent'],
    'limit_order_filled': ['position_id', 'fill_price'],
    'position_closed': ['position_id', 'pnl']
  };
  
  const required = requiredFields[event.type];
  if (!required) return false;
  
  return required.every(field => event.data.hasOwnProperty(field));
};
```

### **Fallback стратегии:**
```javascript
// При отключении WebSocket - polling
const useFallbackPolling = (connected, userId) => {
  useEffect(() => {
    if (!connected) {
      const interval = setInterval(() => {
        fetchPositionsUpdate(userId);
      }, 10000);
      
      return () => clearInterval(interval);
    }
  }, [connected, userId]);
};
```

---

## 🧪 Тестирование

### **Тестовые сценарии для WebSocket событий:**

```javascript
// Jest тесты
describe('Position WebSocket Events', () => {
  test('should handle liquidation event correctly', () => {
    const mockEvent = {
      type: 'position_liquidated',
      data: {
        position_id: 123,
        trading_pair: 'BTC/USDT',
        liquidation_price: 45000
      }
    };
    
    const { result } = renderHook(() => usePositionEventsHandler());
    const setPositions = jest.fn();
    const refetchBalance = jest.fn();
    
    result.current.handleEvent(mockEvent, setPositions, refetchBalance);
    
    expect(setPositions).toHaveBeenCalledWith(expect.any(Function));
    expect(refetchBalance).toHaveBeenCalled();
  });
});
```

### **Эмуляция событий для разработки:**
```javascript
// Для тестирования в development режиме
const emulatePositionEvents = () => {
  setInterval(() => {
    const mockEvents = [
      {
        type: 'position_near_liquidation',
        data: {
          position_id: 123,
          trading_pair: 'BTC/USDT',
          distance_percent: 2.5
        }
      }
    ];
    
    const randomEvent = mockEvents[Math.floor(Math.random() * mockEvents.length)];
    handleEvent(randomEvent, setPositions, refetchBalance);
  }, 30000);
};
```

---

## 📊 Мониторинг и логирование

### **Метрики для отслеживания:**
- Время отклика WebSocket событий
- Количество отключений/переподключений
- Частота различных типов событий
- Точность локальных PnL расчетов

### **Логирование событий:**
```javascript
const logPositionEvent = (event) => {
  console.log(`📊 Position Event: ${event.type}`, {
    position_id: event.data.position_id,
    trading_pair: event.data.trading_pair,
    timestamp: event.timestamp,
    user_id: getCurrentUserId()
  });
  
  // Отправка в аналитику
  analytics.track('position_event', {
    event_type: event.type,
    position_id: event.data.position_id,
    trading_pair: event.data.trading_pair
  });
};
```

---

## 🔄 Миграция и обратная совместимость

### **Поэтапная миграция:**
1. **Фаза 1:** Добавить WebSocket подключение без изменения текущей логики
2. **Фаза 2:** Реализовать обработку событий ликвидации
3. **Фаза 3:** Добавить локальные PnL расчеты
4. **Фаза 4:** Оптимизировать и убрать старые polling запросы

### **Feature flags:**
```javascript
const useWebSocketEvents = useFeatureFlag('websocket_position_events');

if (useWebSocketEvents) {
  // Новая WebSocket логика
} else {
  // Старая polling логика
}
```

---

## 📋 Чек-лист готовности

### **Бэкенд ✅**
- [x] WebSocket endpoints реализованы
- [x] Position event handlers готовы  
- [x] PnL Updater интегрирован с WebSocket
- [x] Все типы событий поддерживаются
- [x] Error handling и reconnection logic

### **Фронтенд (TODO)**
- [ ] WebSocket connection hook
- [ ] Position events handler
- [ ] Real-time PnL calculations
- [ ] UI notifications system
- [ ] Sound notifications
- [ ] Visual position indicators
- [ ] Fallback polling logic
- [ ] Error handling
- [ ] Unit tests
- [ ] Integration tests

---

## 🚀 Запуск и деплой

### **Локальная разработка:**
```bash
# Запуск Trade Service
cd trade-platform-trade-service
uvicorn app.main:app --reload --port 8003

# WebSocket endpoint будет доступен на:
# ws://localhost:8003/ws/user-events
```

### **Production настройки:**
```yaml
# docker-compose.yml
version: '3.8'
services:
  trade-service:
    environment:
      - WEBSOCKET_MAX_CONNECTIONS=1000
      - WEBSOCKET_PING_INTERVAL=30
      - WEBSOCKET_PING_TIMEOUT=10
      - PNL_UPDATE_INTERVAL=5
```

---

## 📞 Поддержка

Для вопросов по реализации WebSocket Position Events:

- **Backend Lead:** [Ваше имя]
- **Frontend Lead:** [Имя фронтенд лида]
- **DevOps:** [Имя DevOps инженера]

**Slack канал:** #trade-service-websocket

---

*Документация обновлена: 2024-01-15*
*Версия API: 1.0.0*
*Статус: Ready for Implementation* 🚀