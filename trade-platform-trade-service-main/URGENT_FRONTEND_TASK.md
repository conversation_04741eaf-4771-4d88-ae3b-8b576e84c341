# 🚨 СРОЧНАЯ ЗАДАЧА ДЛЯ ФРОНТЕНД КОМАНДЫ

## 🔴 КРИТИЧЕСКАЯ ОШИБКА: Неправильные API URLs

**Дата:** 8 июня 2025  
**Приоритет:** КРИТИЧЕСКИЙ  
**Срок:** НЕМЕДЛЕННО  

## 📋 Проблема

Фронтенд использует **неправильные URL** для API запросов к Trade Service, что вызывает ошибки 404.

### ❌ ЧТО НЕ РАБОТАЕТ:
```
GET /api/v1/positions?user_id=13&status=OPEN          → 404 Not Found
POST /api/v1/orders/update-entry-price                → 404 Not Found  
GET /api/v1/orders/open?user_id=13                    → 404 Not Found
```

### ✅ ЧТО ДОЛЖНО БЫТЬ:
```
GET /api/positions?user_id=13&status=OPEN             → 200 OK
POST /api/orders/update-entry-price                   → 200 OK
GET /api/orders/open?user_id=13                       → 200 OK
```

## 🔍 Причина ошибки

**В нашей документации была ошибка!** В файле `FRONTEND_POSITION_UPDATE_GUIDE.md` все эндпоинты были указаны с префиксом `/api/v1`, хотя Trade Service **никогда не использовал версионирование API**.

## 📝 ЗАДАЧИ ДЛЯ ИСПРАВЛЕНИЯ

### 1. ⚡ НЕМЕДЛЕННО - Исправить базовый URL

В вашем коде найдите и замените:

```javascript
❌ const API_BASE_URL = '/api/v1'           // НЕПРАВИЛЬНО
❌ const API_BASE_URL = 'https://trading-api.eu.ngrok.io/api/v1'

✅ const API_BASE_URL = '/api'              // ПРАВИЛЬНО
✅ const API_BASE_URL = 'https://trading-api.eu.ngrok.io/api'
```

### 2. 🔧 Обновить все эндпоинты

Найдите ВСЕ упоминания `/api/v1` в коде и замените на `/api`:

```diff
- fetch('/api/v1/positions?user_id=' + userId)
+ fetch('/api/positions?user_id=' + userId)

- fetch('/api/v1/orders/open?user_id=' + userId) 
+ fetch('/api/orders/open?user_id=' + userId)

- fetch('/api/v1/orders/update-entry-price', {...})
+ fetch('/api/orders/update-entry-price', {...})

- fetch('/api/v1/orders/cancel', {...})
+ fetch('/api/orders/cancel', {...})

- fetch('/api/v1/positions/pnl-only/' + userId)
+ fetch('/api/positions/pnl-only/' + userId)
```

### 3. 🔍 Проверить конфигурацию

Убедитесь, что в файлах конфигурации убран `/v1`:

```javascript
// config.js, constants.js, api.js и т.д.
❌ API_VERSION: 'v1'
❌ TRADE_API_URL: '/api/v1'
✅ TRADE_API_URL: '/api'
```

### 4. 🧪 Протестировать

После исправления проверьте:

1. Загрузка позиций пользователя
2. Получение открытых ордеров  
3. Редактирование entry_price лимитных ордеров
4. Отмена ордеров
5. Обновление PnL

## 📊 Список файлов для проверки

Найдите и исправьте **все файлы**, содержащие:
- `api/v1`
- `/api/v1`
- `'api/v1'`  
- `"api/v1"`

Возможные файлы:
```
src/services/tradeApi.js
src/api/trade.js
src/constants/api.js
src/config/endpoints.js
src/utils/api.js
src/hooks/usePositions.js
src/hooks/useOrders.js
```

## ✅ Критерии готовности

- [ ] Все запросы к Trade Service используют `/api` вместо `/api/v1`
- [ ] Позиции загружаются без ошибок 404
- [ ] Редактирование entry_price работает
- [ ] Отмена ордеров работает  
- [ ] PnL обновляется корректно
- [ ] В браузерной консоли нет ошибок 404 для Trade API

## 🚀 Правильные эндпоинты

| Действие | Метод | URL |
|----------|--------|------|
| Получить позиции | GET | `/api/positions?user_id={id}&status={status}` |
| Получить открытые ордера | GET | `/api/orders/open?user_id={id}` |
| Получить исполненные ордера | GET | `/api/orders/filled?user_id={id}` |
| Обновить entry_price | POST | `/api/orders/update-entry-price` |
| Отменить ордер | POST | `/api/orders/cancel` |
| Получить только PnL | GET | `/api/positions/pnl-only/{user_id}` |

## 💬 Связь

После исправления сообщите в канал разработки:
- ✅ "Trade API URLs исправлены, тестирование пройдено"
- ❌ "Нужна помощь с исправлением Trade API URLs"

## 📄 Обновленная документация

Документация исправлена в файле `FRONTEND_POSITION_UPDATE_GUIDE.md` - теперь содержит правильные URL без `/v1`.

---

**Извиняемся за неудобства!** Ошибка в документации привела к некорректной реализации на фронтенде.