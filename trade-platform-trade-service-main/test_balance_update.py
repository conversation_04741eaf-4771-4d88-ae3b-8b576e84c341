#!/usr/bin/env python3
"""
Простой тест для проверки обновления баланса в /trades/execute
"""
import asyncio
import json
import httpx

async def test_trade_execution_with_balance():
    """Тестирует endpoint /trades/execute на возврат обновленного баланса"""
    
    print("🧪 ТЕСТ: Проверка обновления баланса при выполнении сделки")
    print("=" * 60)
    
    # URL Trade Service (замените на свой)
    trade_service_url = "http://localhost:8008"
    
    # Тестовые данные для сделки
    test_request = {
        "user_id": "123",
        "trading_pair": "BTC/USDT",
        "entry_price": 50000.0,
        "current_price": 50000.0,
        "order_type": "Long",
        "order_execution_type": "MARKET",
        "leverage": 2,
        "margin": 100.0,
        "stop_loss": 45000.0,
        "take_profit": 55000.0
    }
    
    try:
        print(f"📤 Отправка запроса на {trade_service_url}/api/trades/execute")
        print(f"📋 Данные: {json.dumps(test_request, indent=2)}")
        print("-" * 40)
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{trade_service_url}/api/trades/execute",
                json=test_request
            )
            
            print(f"📥 Статус ответа: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ Успешный ответ!")
                print(f"📊 Структура ответа:")
                print(f"   - success: {data.get('success')}")
                print(f"   - position: {'есть' if data.get('position') else 'нет'}")
                print(f"   - updated_balance: {'есть' if data.get('updated_balance') else 'НЕТ!'}")
                print(f"   - error: {data.get('error')}")
                
                if data.get('updated_balance'):
                    balance = data['updated_balance']
                    print(f"💰 Данные баланса:")
                    print(f"   - Available USD: {balance.get('available_usd_balance')}")
                    print(f"   - Locked USD: {balance.get('locked_usd_balance')}")
                    print(f"   - Fixed USD: {balance.get('fixed_usd_balance')}")
                    print("✅ ТЕСТ ПРОЙДЕН: updated_balance присутствует в ответе!")
                else:
                    print("❌ ТЕСТ НЕ ПРОЙДЕН: updated_balance отсутствует в ответе!")
                    
                print("-" * 40)
                print("📄 Полный ответ:")
                print(json.dumps(data, indent=2))
                
            else:
                print(f"❌ Ошибка: {response.status_code}")
                print(f"📄 Текст ответа: {response.text}")
                
    except httpx.ConnectError:
        print("❌ ОШИБКА: Не удалось подключиться к Trade Service")
        print("💡 Убедитесь что Trade Service запущен на порту 8008")
    except Exception as e:
        print(f"❌ НЕОЖИДАННАЯ ОШИБКА: {str(e)}")
    
    print("=" * 60)

if __name__ == "__main__":
    asyncio.run(test_trade_execution_with_balance())