from alembic import command
from alembic.config import Config
import os
import logging
from sqlalchemy.sql import text
from app.db.base import engine

logger = logging.getLogger(__name__)


def run_migrations():
    """
    Запускает миграции базы данных при старте приложения
    """
    try:
        # Получаем путь к конфигурационному файлу alembic
        alembic_cfg = Config(os.path.join(os.path.dirname(__file__), "../../alembic.ini"))

        # Запускаем миграции
        command.upgrade(alembic_cfg, "head")
        logger.info("Migrations completed successfully")
    except Exception as e:
        logger.error(f"Error running migrations: {str(e)}")
        raise


def check_db_connection():
    """
    Проверяет подключение к базе данных
    """
    try:
        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
        return True
    except Exception as e:
        logger.error(f"Database connection error: {str(e)}")
        return False
