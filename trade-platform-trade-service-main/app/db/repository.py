from typing import List, Optional, Dict, Any
from datetime import datetime
from sqlalchemy.orm import Session
from sqlalchemy import and_, desc
import logging

from app.db.models import Position, MarketPrice, KafkaEvent, User
from app.core.models import Position as PositionModel

logger = logging.getLogger(__name__)


class PositionRepository:
    @staticmethod
    def create_position(
        db: Session,
        user_id: str,
        trading_pair: str,
        direction: str,
        entry_price: float,
        current_price: float,
        position_size: float,
        margin: float,
        leverage: int,
        liquidation_price: float,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
        fee: float = 0,
    ) -> Position:
        """
        Создает новую позицию в базе данных
        """
        # Проверяем существование пользователя
        user = db.query(User).filter(User.id == user_id).first()
        if not user:
            # Создаем пользователя, если его нет (упрощенная логика)
            user = User(id=user_id, username=f"user_{user_id}", email=f"user_{user_id}@example.com")
            db.add(user)
            db.flush()

        # Создаем новую позицию
        position = Position(
            user_id=user_id,
            trading_pair=trading_pair,
            direction=direction.upper(),
            entry_price=entry_price,
            current_price=current_price,
            position_size=position_size,
            margin=margin,
            leverage=leverage,
            liquidation_price=liquidation_price,
            stop_loss=stop_loss,
            take_profit=take_profit,
            fee=fee,
            status="OPEN",
            opened_at=datetime.now(),
        )

        db.add(position)
        db.commit()
        db.refresh(position)
        return position

    @staticmethod
    def get_position(db: Session, position_id: int) -> Optional[Position]:
        """
        Получает позицию по ID
        """
        return db.query(Position).filter(Position.id == position_id).first()

    @staticmethod
    def get_positions_by_user(db: Session, user_id: str, status: Optional[str] = None) -> List[Position]:
        """
        Получает все позиции пользователя с опциональной фильтрацией по статусу
        """
        query = db.query(Position).filter(Position.user_id == user_id)

        if status:
            query = query.filter(Position.status == status.upper())

        return query.order_by(desc(Position.opened_at)).all()

    @staticmethod
    def get_open_positions(db: Session) -> List[Position]:
        """
        Получает все открытые позиции для обновления
        """
        return db.query(Position).filter(Position.status == "OPEN").all()

    @staticmethod
    def update_position_price(
        db: Session, position_id: int, current_price: float, pnl: float, return_percentage: float
    ) -> Position:
        """
        Обновляет текущую цену и PnL позиции
        """
        position = db.query(Position).filter(Position.id == position_id).first()
        if position:
            position.current_price = current_price
            position.pnl = pnl
            position.return_percentage = return_percentage
            db.commit()
            db.refresh(position)
        return position

    @staticmethod
    def close_position(
        db: Session, position_id: int, current_price: float, pnl: float, return_percentage: float, close_reason: str
    ) -> Position:
        """
        Закрывает позицию с указанием причины
        """
        position = db.query(Position).filter(Position.id == position_id).first()
        if position and position.status == "OPEN":
            position.status = "CLOSED"
            position.current_price = current_price
            position.pnl = pnl
            position.return_percentage = return_percentage
            position.closed_at = datetime.now()
            position.close_reason = close_reason

            db.commit()
            db.refresh(position)
        return position

    @staticmethod
    def liquidate_position(db: Session, position_id: int) -> Position:
        """
        Ликвидирует позицию
        """
        position = db.query(Position).filter(Position.id == position_id).first()
        if position and position.status == "OPEN":
            position.status = "LIQUIDATED"
            position.pnl = -position.margin  # Полная потеря маржи
            position.return_percentage = -100
            position.closed_at = datetime.now()
            position.close_reason = "liquidation"

            db.commit()
            db.refresh(position)
        return position


class PriceRepository:
    @staticmethod
    def update_market_price(db: Session, trading_pair: str, price: float) -> MarketPrice:
        """
        Обновляет рыночную цену торговой пары
        """
        market_price = MarketPrice(trading_pair=trading_pair, price=price)

        db.add(market_price)
        db.commit()
        db.refresh(market_price)
        return market_price

    @staticmethod
    def get_latest_price(db: Session, trading_pair: str) -> Optional[float]:
        """
        Получает последнюю цену для торговой пары
        """
        market_price = (
            db.query(MarketPrice)
            .filter(MarketPrice.trading_pair == trading_pair)
            .order_by(desc(MarketPrice.timestamp))
            .first()
        )

        return market_price.price if market_price else None

    @staticmethod
    def get_price_history(db: Session, trading_pair: str, limit: int = 100) -> List[MarketPrice]:
        """
        Получает историю цен для торговой пары
        """
        return (
            db.query(MarketPrice)
            .filter(MarketPrice.trading_pair == trading_pair)
            .order_by(desc(MarketPrice.timestamp))
            .limit(limit)
            .all()
        )


class KafkaEventRepository:
    @staticmethod
    def create_event(db: Session, event_type: str, payload: Dict[str, Any]) -> KafkaEvent:
        """
        Создает запись о событии Kafka
        """
        from json import dumps

        event = KafkaEvent(event_type=event_type, payload=dumps(payload), status="PENDING")

        db.add(event)
        db.commit()
        db.refresh(event)
        return event

    @staticmethod
    def update_event_status(db: Session, event_id: int, status: str, error: Optional[str] = None) -> KafkaEvent:
        """
        Обновляет статус события
        """
        event = db.query(KafkaEvent).filter(KafkaEvent.id == event_id).first()
        if event:
            event.status = status
            if status in ["SENT", "FAILED"]:
                event.processed_at = datetime.now()
            if error:
                event.error = error
                event.retry_count += 1

            db.commit()
            db.refresh(event)
        return event

    @staticmethod
    def get_pending_events(db: Session, limit: int = 10) -> List[KafkaEvent]:
        """
        Получает список ожидающих событий для обработки
        """
        return (
            db.query(KafkaEvent)
            .filter(and_(KafkaEvent.status == "PENDING", KafkaEvent.retry_count < 3))  # Максимум 3 попытки
            .order_by(KafkaEvent.created_at)
            .limit(limit)
            .all()
        )

    @staticmethod
    def get_positions_by_status(db: Session, status: str) -> List[Position]:
        """
        Получает все позиции с указанным статусом

        Args:
            db: Сессия базы данных
            status: Статус позиций ("OPEN", "CLOSED", "PENDING", "CANCELLED", "LIQUIDATED")

        Returns:
            Список позиций с указанным статусом
        """
        try:
            positions = db.query(Position).filter(Position.status == status).all()
            return positions
        except Exception as e:
            logger.error(f"Error getting positions by status {status}: {str(e)}")
            db.rollback()
            return []
