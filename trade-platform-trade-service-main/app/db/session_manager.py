from app.db.base import get_db
from sqlalchemy.orm import Session
import logging
from contextlib import contextmanager

logger = logging.getLogger(__name__)


@contextmanager
def get_db_session():
    """
    Контекстный менеджер для сессий БД, гарантирующий правильное закрытие
    """
    session = None
    try:
        session = next(get_db())
        yield session
        session.commit()
    except Exception as e:
        logger.error(f"DB session error: {str(e)}")
        if session:
            try:
                session.rollback()
            except Exception as rollback_error:
                logger.error(f"Error during rollback: {str(rollback_error)}")
        raise
    finally:
        if session:
            try:
                session.close()
                logger.debug("Database session closed successfully")
            except Exception as close_error:
                logger.error(f"Error closing database session: {str(close_error)}")
