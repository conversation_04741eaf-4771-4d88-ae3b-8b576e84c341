from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.types import DECIMAL
from datetime import datetime

from app.db.base import Base


class User(Base):
    __tablename__ = "users"

    id = Column(String(36), primary_key=True, index=True)
    username = Column(String(100), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    created_at = Column(DateTime, server_default=func.now())

    # Отношения
    positions = relationship("Position", back_populates="user")


class Position(Base):
    __tablename__ = "positions"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    user_id = Column(String(36), ForeignKey("users.id"), nullable=False)
    trading_pair = Column(String(20), nullable=False, index=True)
    direction = Column(String(10), nullable=False)  # LONG или SHORT
    entry_price = Column(DECIMAL(18, 8), nullable=False)
    current_price = Column(DECIMAL(18, 8), nullable=False)
    position_size = Column(DECIMAL(18, 8), nullable=False)
    margin = Column(DECIMAL(18, 8), nullable=False)
    leverage = Column(Integer, nullable=False)
    liquidation_price = Column(DECIMAL(18, 8), nullable=False)
    stop_loss = Column(DECIMAL(18, 8), nullable=True)
    take_profit = Column(DECIMAL(18, 8), nullable=True)
    pnl = Column(DECIMAL(18, 8), nullable=False, default=0)
    order_execution_type = Column(String(10), nullable=False, default="MARKET")
    status = Column(String(20), nullable=False, default="OPEN", index=True)  # OPEN, CLOSED, LIQUIDATED
    opened_at = Column(DateTime, default=datetime.now, nullable=False)
    closed_at = Column(DateTime, nullable=True)
    close_reason = Column(String(20), nullable=True)
    fee = Column(DECIMAL(18, 8), nullable=False)
    return_percentage = Column(Float, nullable=False, default=0)

    # Отношения
    user = relationship("User", back_populates="positions")


# Только две необходимые таблицы для хранения рыночных цен и событий Kafka
class MarketPrice(Base):
    __tablename__ = "market_prices"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    trading_pair = Column(String(20), nullable=False, index=True)
    price = Column(DECIMAL(18, 8), nullable=False)
    timestamp = Column(DateTime, default=datetime.now, nullable=False, index=True)


class KafkaEvent(Base):
    __tablename__ = "kafka_events"

    id = Column(Integer, primary_key=True, index=True, autoincrement=True)
    event_type = Column(String(50), nullable=False, index=True)
    payload = Column(Text, nullable=False)
    status = Column(String(20), nullable=False, index=True)  # PENDING, SENT, FAILED
    created_at = Column(DateTime, default=datetime.now, nullable=False)
    processed_at = Column(DateTime, nullable=True)
    retry_count = Column(Integer, default=0, nullable=False)
    error = Column(Text, nullable=True)
