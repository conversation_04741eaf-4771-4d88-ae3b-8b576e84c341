from fastapi import WebSocket
import logging
from app.core.models import TradeExecutionRequest
from app.core.position_manager import PositionManager
from app.core.utils import datetime_serializer
import json
from datetime import datetime
from typing import Dict, Any

logger = logging.getLogger(__name__)


async def handle_trade_execution(websocket: WebSocket, data: dict, position_manager: PositionManager):
    """
    Handler for executing a trade operation via WebSocket
    """
    try:
        # Add logging
        logger.info(f"Received request to open position: {data}")

        # Parse request data
        user_id = data.get("user_id")
        if not user_id:
            await websocket.send_json({"status": "error", "action": "execute", "error": "User ID is required"})
            return

        # Get required parameters from request
        trading_pair = data.get("trading_pair")
        entry_price = data.get("entry_price")
        current_price = data.get("current_price")
        order_type = data.get("order_type")  # 'Long' or 'Short'
        order_execution_type = data.get("order_execution_type")  # 'LIMIT' or 'MARKET'
        margin = data.get("margin")
        leverage = data.get("leverage")
        stop_loss = data.get("stop_loss")
        take_profit = data.get("take_profit")

        # Convert order_type to uppercase for backend
        direction = order_type.upper() if order_type else None

        # Log converted data
        logger.info(f"Converted data: direction={direction}, execution_type={order_execution_type}")

        # Open position
        try:
            position = await position_manager.open_position(
                user_id=user_id,
                trading_pair=trading_pair,
                direction=direction,  # Use converted value
                entry_price=entry_price,
                current_price=current_price,
                execution_type=order_execution_type,
                margin=margin,
                leverage=leverage,
                stop_loss=stop_loss,
                take_profit=take_profit,
            )
            logger.info(f"Position successfully opened: {position}")
        except Exception as e:
            logger.error(f"Error opening position: {str(e)}")
            await websocket.send_json(
                {"status": "error", "action": "execute", "error": f"Failed to open position: {str(e)}"}
            )
            return

        # Get position data
        position_data = position.dict() if hasattr(position, "dict") else position

        # If position_data is not a dictionary (but a model object), convert it to dictionary
        if not isinstance(position_data, dict):
            position_data = position_data.__dict__.copy()
            # Remove SQLAlchemy internal attributes
            if "_sa_instance_state" in position_data:
                del position_data["_sa_instance_state"]

        # Serialize all datetime to strings
        serialized_data = json.loads(json.dumps(position_data, default=datetime_serializer))

        # Check if this is a pending order
        is_pending_order = serialized_data.get("status") == "PENDING"

        # Send result to client in format expected by frontend
        await websocket.send_json(
            {"status": "success", "action": "execute", "data": {"success": True, "position": serialized_data}}
        )

        # If this is a pending order, send additional "order_created" event
        # to update open orders list on client
        if is_pending_order:
            from app.api.websocket.connection_manager import ConnectionManager

            connection_manager = ConnectionManager()

            try:
                # Отправка уведомления о создании ордера
                await connection_manager.send_to_user(
                    user_id, {"status": "success", "action": "order_created", "data": serialized_data}
                )
                logger.info(f"Отправлено уведомление order_created для ордера {position.id} пользователю {user_id}")
            except Exception as e:
                logger.error(f"Ошибка отправки уведомления order_created: {str(e)}")

    except Exception as e:
        logger.error(f"Error executing trade: {str(e)}")
        await websocket.send_json({"status": "error", "action": "execute", "error": str(e)})


# 🔥 НОВЫЕ HANDLERS ДЛЯ POSITION EVENTS

class PositionEventsHandler:
    """
    Обработчик событий позиций для WebSocket уведомлений
    """

    @staticmethod
    async def send_liquidation_event(connection_manager, user_id: int, position_data: Dict[str, Any]):
        """
        Отправляет событие ликвидации позиции пользователю
        """
        try:
            message = {
                "type": "position_liquidated",
                "data": {
                    "position_id": position_data["position_id"],
                    "trading_pair": position_data["trading_pair"],
                    "direction": position_data["direction"],
                    "liquidation_price": position_data["liquidation_price"],
                    "current_price": position_data["current_price"],
                    "pnl": position_data["pnl"],
                    "margin": position_data["margin"],
                    "timestamp": datetime.now().isoformat()
                },
                "timestamp": datetime.now().isoformat()
            }

            await connection_manager.send_to_user(str(user_id), message)
            logger.info(f"💥 Отправлено событие ликвидации позиции {position_data['position_id']} пользователю {user_id}")

        except Exception as e:
            logger.error(f"❌ Ошибка отправки события ликвидации: {str(e)}")

    @staticmethod
    async def send_near_liquidation_warning(connection_manager, user_id: int, position_data: Dict[str, Any]):
        """
        Отправляет предупреждение о близкой ликвидации (при 5% до ликвидационной цены)
        """
        try:
            message = {
                "type": "position_near_liquidation",
                "data": {
                    "position_id": position_data["position_id"],
                    "trading_pair": position_data["trading_pair"],
                    "direction": position_data["direction"],
                    "current_price": position_data["current_price"],
                    "liquidation_price": position_data["liquidation_price"],
                    "distance_percent": position_data["distance_percent"],
                    "pnl": position_data["pnl"],
                    "timestamp": datetime.now().isoformat()
                },
                "timestamp": datetime.now().isoformat()
            }

            await connection_manager.send_to_user(str(user_id), message)
            logger.warning(f"⚠️ Отправлено предупреждение о близкой ликвидации позиции {position_data['position_id']} пользователю {user_id}")

        except Exception as e:
            logger.error(f"❌ Ошибка отправки предупреждения о ликвидации: {str(e)}")

    @staticmethod
    async def send_limit_order_filled(connection_manager, user_id: int, position_data: Dict[str, Any]):
        """
        Отправляет событие активации лимитного ордера
        """
        try:
            message = {
                "type": "limit_order_filled",
                "data": {
                    "position_id": position_data["position_id"],
                    "trading_pair": position_data["trading_pair"],
                    "direction": position_data["direction"],
                    "entry_price": position_data["entry_price"],
                    "fill_price": position_data["current_price"],
                    "position_size": position_data["position_size"],
                    "margin": position_data["margin"],
                    "leverage": position_data["leverage"],
                    "timestamp": datetime.now().isoformat()
                },
                "timestamp": datetime.now().isoformat()
            }

            await connection_manager.send_to_user(str(user_id), message)
            logger.info(f"🚀 Отправлено событие активации лимитного ордера {position_data['position_id']} пользователю {user_id}")

        except Exception as e:
            logger.error(f"❌ Ошибка отправки события активации ордера: {str(e)}")

    @staticmethod
    async def send_position_closed(connection_manager, user_id: int, position_data: Dict[str, Any]):
        """
        Отправляет событие закрытия позиции (take profit / stop loss)
        """
        try:
            message = {
                "type": "position_closed",
                "data": {
                    "position_id": position_data["position_id"],
                    "trading_pair": position_data["trading_pair"],
                    "direction": position_data["direction"],
                    "entry_price": position_data["entry_price"],
                    "exit_price": position_data["current_price"],
                    "pnl": position_data["pnl"],
                    "return_percentage": position_data.get("return_percentage", 0),
                    "close_reason": position_data.get("close_reason", "manual"),  # manual, take_profit, stop_loss
                    "timestamp": datetime.now().isoformat()
                },
                "timestamp": datetime.now().isoformat()
            }

            await connection_manager.send_to_user(str(user_id), message)
            logger.info(f"✅ Отправлено событие закрытия позиции {position_data['position_id']} пользователю {user_id}")

        except Exception as e:
            logger.error(f"❌ Ошибка отправки события закрытия позиции: {str(e)}")


# Глобальный экземпляр для использования в других модулях
position_events_handler = PositionEventsHandler()
