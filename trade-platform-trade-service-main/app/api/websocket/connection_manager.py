from fastapi import WebSocket
from typing import Dict, List, Optional
import json
import logging
import asyncio
import time
from datetime import datetime, timedelta
from app.core.position_manager import PositionManager
from app.core.utils import datetime_serializer
from weakref import WeakSet

logger = logging.getLogger(__name__)


class ConnectionInfo:
    """Информация о WebSocket соединении с метриками здоровья"""
    def __init__(self, websocket: WebSocket, user_id: str = None):
        self.websocket = websocket
        self.user_id = user_id
        self.connected_at = datetime.now()
        self.last_ping_sent = None
        self.last_pong_received = None
        self.ping_count = 0
        self.failed_ping_count = 0
        self.is_alive = True
        self.message_count = 0
        self.last_message_time = datetime.now()


class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[WebSocket, ConnectionInfo] = {}
        self.user_connections: Dict[str, List[WebSocket]] = {}
        self.position_manager = PositionManager()
        
        # Настройки для ping/pong и health monitoring
        self.ping_interval = 30  # секунд между ping сообщениями
        self.pong_timeout = 10   # секунд ожидания pong ответа
        self.max_failed_pings = 3  # максимум неудачных ping до отключения
        self.connection_timeout = 300  # секунд до принудительного отключения неактивного соединения
        self.max_connections_per_user = 5  # максимум соединений на пользователя
        
        # Фоновые задачи
        self._cleanup_task = None
        self._ping_task = None
        self._running = False

    async def connect(self, websocket: WebSocket, user_id: str = None):
        """
        Подключает новый WebSocket с улучшенным контролем качества
        """
        try:
            # Проверяем лимит соединений для пользователя
            if user_id and user_id in self.user_connections:
                if len(self.user_connections[user_id]) >= self.max_connections_per_user:
                    logger.warning(f"⚠️ User {user_id} exceeded max connections limit ({self.max_connections_per_user})")
                    await websocket.close(code=1008, reason="Too many connections")
                    return False
            
            await websocket.accept()
            
            # Создаем информацию о соединении
            connection_info = ConnectionInfo(websocket, user_id)
            self.active_connections[websocket] = connection_info
            
            if user_id:
                if user_id not in self.user_connections:
                    self.user_connections[user_id] = []
                self.user_connections[user_id].append(websocket)
                
                logger.info(f"✅ User {user_id} connected. Total connections: {len(self.user_connections[user_id])}")

                await websocket.send_json({
                    "status": "connected", 
                    "message": f"Connected as user {user_id} for PnL updates",
                    "connection_id": id(websocket),
                    "ping_interval": self.ping_interval,
                    "features": ["ping_pong", "heartbeat", "auto_cleanup"]
                })
            
            # Запускаем фоновые задачи если еще не запущены
            if not self._running:
                await self._start_background_tasks()
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error connecting WebSocket: {str(e)}")
            return False

    def disconnect(self, websocket: WebSocket, user_id: str = None):
        """
        Отключает WebSocket с логированием метрик
        """
        try:
            # Получаем информацию о соединении для логирования
            connection_info = self.active_connections.get(websocket)
            if connection_info:
                connection_duration = datetime.now() - connection_info.connected_at
                logger.info(f"📊 Connection closed. Duration: {connection_duration}, Messages: {connection_info.message_count}, Pings: {connection_info.ping_count}")
            
            # Удаляем из активных соединений
            if websocket in self.active_connections:
                del self.active_connections[websocket]

            # Удаляем из пользовательских соединений
            if user_id and user_id in self.user_connections:
                if websocket in self.user_connections[user_id]:
                    self.user_connections[user_id].remove(websocket)
                    logger.info(f"🔌 User {user_id} disconnected. Remaining connections: {len(self.user_connections[user_id])}")

                if not self.user_connections[user_id]:
                    del self.user_connections[user_id]
                    logger.info(f"👤 User {user_id} has no active connections")
                    
        except Exception as e:
            logger.error(f"❌ Error during disconnect: {str(e)}")

    async def _start_background_tasks(self):
        """Запускает фоновые задачи для мониторинга здоровья соединений"""
        if self._running:
            return
            
        self._running = True
        self._ping_task = asyncio.create_task(self._ping_loop())
        self._cleanup_task = asyncio.create_task(self._cleanup_loop())
        logger.info("🔄 Background tasks started: ping/pong monitoring and connection cleanup")

    async def _stop_background_tasks(self):
        """Останавливает фоновые задачи"""
        self._running = False
        
        if self._ping_task:
            self._ping_task.cancel()
            try:
                await self._ping_task
            except asyncio.CancelledError:
                pass
                
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
                
        logger.info("🛑 Background tasks stopped")

    async def _ping_loop(self):
        """Фоновая задача для отправки ping сообщений"""
        while self._running:
            try:
                await asyncio.sleep(self.ping_interval)
                
                if not self.active_connections:
                    continue
                    
                logger.debug(f"🏓 Sending ping to {len(self.active_connections)} connections")
                
                for websocket, connection_info in list(self.active_connections.items()):
                    if not connection_info.is_alive:
                        continue
                        
                    try:
                        # Проверяем, не слишком ли много неуспешных ping
                        if connection_info.failed_ping_count >= self.max_failed_pings:
                            logger.warning(f"⚠️ Connection exceeded max failed pings ({self.max_failed_pings}), closing")
                            connection_info.is_alive = False
                            await self._cleanup_dead_connection(websocket)
                            continue
                            
                        # Отправляем ping
                        await websocket.ping()
                        connection_info.last_ping_sent = datetime.now()
                        connection_info.ping_count += 1
                        
                    except Exception as e:
                        logger.error(f"❌ Error sending ping: {str(e)}")
                        connection_info.failed_ping_count += 1
                        
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in ping loop: {str(e)}")

    async def _cleanup_loop(self):
        """Фоновая задача для очистки мертвых соединений"""
        while self._running:
            try:
                await asyncio.sleep(60)  # Очистка каждую минуту
                
                current_time = datetime.now()
                dead_connections = []
                
                for websocket, connection_info in list(self.active_connections.items()):
                    # Проверяем таймаут соединения
                    connection_age = current_time - connection_info.connected_at
                    if connection_age.total_seconds() > self.connection_timeout:
                        logger.warning(f"⏰ Connection timeout after {connection_age}")
                        dead_connections.append(websocket)
                        continue
                    
                    # Проверяем таймаут pong ответа
                    if connection_info.last_ping_sent:
                        pong_timeout = current_time - connection_info.last_ping_sent
                        if pong_timeout.total_seconds() > self.pong_timeout:
                            if not connection_info.last_pong_received or \
                               connection_info.last_ping_sent > connection_info.last_pong_received:
                                logger.warning(f"🏓 Pong timeout after {pong_timeout}")
                                connection_info.failed_ping_count += 1
                    
                    # Проверяем активность соединения
                    if not connection_info.is_alive:
                        dead_connections.append(websocket)
                
                # Очищаем мертвые соединения
                for websocket in dead_connections:
                    await self._cleanup_dead_connection(websocket)
                
                if dead_connections:
                    logger.info(f"🧹 Cleaned up {len(dead_connections)} dead connections")
                    
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"❌ Error in cleanup loop: {str(e)}")

    async def _cleanup_dead_connection(self, websocket: WebSocket):
        """Очищает мертвое соединение"""
        try:
            connection_info = self.active_connections.get(websocket)
            user_id = connection_info.user_id if connection_info else None
            
            # Закрываем WebSocket если он еще открыт
            try:
                await websocket.close(code=1000, reason="Connection cleanup")
            except:
                pass  # Соединение уже может быть закрыто
            
            # Удаляем из всех списков
            self.disconnect(websocket, user_id)
            
        except Exception as e:
            logger.error(f"❌ Error cleaning up dead connection: {str(e)}")

    async def handle_pong(self, websocket: WebSocket):
        """Обрабатывает pong ответ от клиента"""
        connection_info = self.active_connections.get(websocket)
        if connection_info:
            connection_info.last_pong_received = datetime.now()
            connection_info.failed_ping_count = 0  # Сбрасываем счетчик неудачных ping
            logger.debug(f"🏓 Pong received from connection {id(websocket)}")

    async def get_connection_stats(self) -> dict:
        """Возвращает статистику соединений"""
        total_connections = len(self.active_connections)
        user_count = len(self.user_connections)
        alive_connections = sum(1 for info in self.active_connections.values() if info.is_alive)
        
        # Группируем по пользователям
        user_stats = {}
        for user_id, connections in self.user_connections.items():
            user_stats[user_id] = len(connections)
        
        return {
            "total_connections": total_connections,
            "alive_connections": alive_connections,
            "dead_connections": total_connections - alive_connections,
            "unique_users": user_count,
            "connections_per_user": user_stats,
            "ping_interval": self.ping_interval,
            "pong_timeout": self.pong_timeout,
            "max_failed_pings": self.max_failed_pings,
            "background_tasks_running": self._running
        }

    async def broadcast(self, message: str):
        """Отправляет сообщение всем подключенным клиентам с улучшенной обработкой ошибок"""
        dead_connections = []
        
        for websocket, connection_info in self.active_connections.items():
            if not connection_info.is_alive:
                dead_connections.append(websocket)
                continue
                
            try:
                await websocket.send_text(message)
                connection_info.message_count += 1
                connection_info.last_message_time = datetime.now()
            except Exception as e:
                logger.error(f"Error broadcasting message: {str(e)}")
                connection_info.is_alive = False
                dead_connections.append(websocket)
        
        # Очищаем мертвые соединения
        for websocket in dead_connections:
            await self._cleanup_dead_connection(websocket)

    async def broadcast_json(self, message: dict):
        """Отправляет JSON сообщение всем подключенным клиентам с улучшенной обработкой ошибок"""
        dead_connections = []
        
        for websocket, connection_info in self.active_connections.items():
            if not connection_info.is_alive:
                dead_connections.append(websocket)
                continue
                
            try:
                await websocket.send_json(message)
                connection_info.message_count += 1
                connection_info.last_message_time = datetime.now()
            except Exception as e:
                logger.error(f"Error broadcasting JSON message: {str(e)}")
                connection_info.is_alive = False
                dead_connections.append(websocket)
        
        # Очищаем мертвые соединения
        for websocket in dead_connections:
            await self._cleanup_dead_connection(websocket)

    async def send_to_user(self, user_id: str, message: dict):
        """Отправляет сообщение конкретному пользователю с улучшенной обработкой ошибок"""
        if user_id not in self.user_connections:
            return
            
        dead_connections = []

        for websocket in self.user_connections[user_id]:
            connection_info = self.active_connections.get(websocket)
            if not connection_info or not connection_info.is_alive:
                dead_connections.append(websocket)
                continue
                
            try:
                await websocket.send_json(message)
                connection_info.message_count += 1
                connection_info.last_message_time = datetime.now()
            except Exception as e:
                logger.error(f"Ошибка при отправке сообщения пользователю {user_id}: {str(e)}")
                connection_info.is_alive = False
                dead_connections.append(websocket)

        # Очищаем мертвые соединения
        for websocket in dead_connections:
            await self._cleanup_dead_connection(websocket)

    async def handle_message(self, websocket: WebSocket, message: str):
        """Обрабатывает входящие сообщения от клиента с поддержкой ping/pong"""
        try:
            # Обновляем время последнего сообщения
            connection_info = self.active_connections.get(websocket)
            if connection_info:
                connection_info.last_message_time = datetime.now()
                connection_info.message_count += 1
            
            data = json.loads(message)
            action = data.get("action")
            request_data = data.get("data", {})

            if action == "ping":
                # Обрабатываем ping от клиента
                await websocket.send_json({
                    "action": "pong", 
                    "timestamp": datetime.now().isoformat(),
                    "server_time": int(time.time())
                })
                
            elif action == "pong":
                # Обрабатываем pong от клиента
                await self.handle_pong(websocket)
                
            elif action == "subscribe_prices":
                await self.handle_subscribe_prices(websocket, request_data)
                
            elif action == "subscribe_position_events":
                await self.handle_subscribe_position_events(websocket, request_data)
                
            elif action == "subscribe_liquidation_alerts":
                await self.handle_subscribe_liquidation_alerts(websocket, request_data)
                
            elif action == "get_connection_info":
                # Возвращаем информацию о соединении
                if connection_info:
                    await websocket.send_json({
                        "action": "connection_info",
                        "data": {
                            "connected_at": connection_info.connected_at.isoformat(),
                            "message_count": connection_info.message_count,
                            "ping_count": connection_info.ping_count,
                            "failed_ping_count": connection_info.failed_ping_count,
                            "is_alive": connection_info.is_alive,
                            "user_id": connection_info.user_id
                        }
                    })
                    
            else:
                await websocket.send_json(
                    {
                        "status": "error",
                        "error": f"Action '{action}' not supported via WebSocket. Use REST API instead.",
                        "supported_actions": ["ping", "pong", "subscribe_prices", "subscribe_position_events", "subscribe_liquidation_alerts", "get_connection_info"]
                    }
                )

        except json.JSONDecodeError:
            await websocket.send_json({
                "status": "error", 
                "error": "Invalid JSON",
                "expected_format": {"action": "action_name", "data": {}}
            })
        except Exception as e:
            logger.error(f"Error handling message: {str(e)}")
            await websocket.send_json({"status": "error", "error": str(e)})

    async def handle_subscribe_prices(self, websocket: WebSocket, request_data: dict):
        """Обрабатывает запрос на подписку обновлений цен для PnL"""
        try:
            user_id = request_data.get("user_id")

            if not user_id:
                await websocket.send_json(
                    {"status": "error", "action": "subscribe_prices", "error": "User ID is required"}
                )
                return

            await websocket.send_json(
                {"status": "success", "action": "subscribe_prices", "message": "Subscribed to PnL updates"}
            )

        except Exception as e:
            logger.error(f"Error subscribing to prices: {str(e)}")
            await websocket.send_json({"status": "error", "action": "subscribe_prices", "error": str(e)})

    # 🔥 КЛЮЧЕВОЙ МЕТОД: Обрабатывает обновления цен и отправляет PnL
    async def broadcast_price_updates(self, prices: Dict[str, float]):
        """
        Получает обновления цен от Price Service и обновляет PnL позиций
        """
        if not prices:
            return

        try:
            logger.debug(f"📊 Получены обновления цен: {len(prices)} пар")

            # 🔥 ГЛАВНОЕ: Обновляем PnL через Position Manager
            updated_positions = await self.position_manager.update_positions_prices(prices)

            if updated_positions:
                logger.debug(f"✅ Обновлено {len(updated_positions)} позиций с новым PnL")

            # Отправляем обновления цен всем клиентам
            await self.broadcast_json({"status": "success", "action": "price_update", "data": prices})

        except Exception as e:
            logger.error(f"❌ Error in broadcast_price_updates: {str(e)}")

    async def handle_subscribe_position_events(self, websocket: WebSocket, request_data: dict):
        """
        🔥 НОВЫЙ МЕТОД: Обрабатывает подписку на события позиций (ликвидация, закрытие, активация ордеров)
        """
        try:
            user_id = request_data.get("user_id")

            if not user_id:
                await websocket.send_json(
                    {"status": "error", "action": "subscribe_position_events", "error": "User ID is required"}
                )
                return

            # Добавляем пользователя в список для получения событий позиций
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []

            if websocket not in self.user_connections[user_id]:
                self.user_connections[user_id].append(websocket)

            await websocket.send_json(
                {
                    "status": "success",
                    "action": "subscribe_position_events",
                    "message": f"Subscribed to position events for user {user_id}"
                }
            )

            logger.info(f"🔔 User {user_id} subscribed to position events")

        except Exception as e:
            logger.error(f"Error subscribing to position events: {str(e)}")
            await websocket.send_json({"status": "error", "action": "subscribe_position_events", "error": str(e)})

    async def handle_subscribe_liquidation_alerts(self, websocket: WebSocket, request_data: dict):
        """
        🔥 НОВЫЙ МЕТОД: Обрабатывает подписку на алерты ликвидации (для критических позиций)
        """
        try:
            user_id = request_data.get("user_id")
            position_ids = request_data.get("position_ids", [])  # Список ID позиций для мониторинга

            if not user_id:
                await websocket.send_json(
                    {"status": "error", "action": "subscribe_liquidation_alerts", "error": "User ID is required"}
                )
                return

            # Добавляем пользователя в список для получения алертов ликвидации
            if user_id not in self.user_connections:
                self.user_connections[user_id] = []

            if websocket not in self.user_connections[user_id]:
                self.user_connections[user_id].append(websocket)

            await websocket.send_json(
                {
                    "status": "success",
                    "action": "subscribe_liquidation_alerts",
                    "message": f"Subscribed to liquidation alerts for user {user_id}",
                    "monitored_positions": position_ids
                }
            )

            logger.info(f"⚠️ User {user_id} subscribed to liquidation alerts for positions: {position_ids}")

        except Exception as e:
            logger.error(f"Error subscribing to liquidation alerts: {str(e)}")
            await websocket.send_json({"status": "error", "action": "subscribe_liquidation_alerts", "error": str(e)})

    async def send_real_time_pnl_update(self, user_id: str, positions_data: list):
        """
        🔥 НОВЫЙ МЕТОД: Отправляет real-time обновления PnL для позиций пользователя
        (Используется для отправки вычисленного на фронтенде PnL для синхронизации)
        """
        try:
            if user_id in self.user_connections:
                message = {
                    "type": "realtime_pnl_update",
                    "data": {
                        "positions": positions_data,
                        "timestamp": datetime.now().isoformat()
                    },
                    "timestamp": datetime.now().isoformat()
                }

                await self.send_to_user(user_id, message)
                logger.debug(f"📊 Sent real-time PnL update to user {user_id} for {len(positions_data)} positions")

        except Exception as e:
            logger.error(f"❌ Error sending real-time PnL update: {str(e)}")

    async def broadcast_market_event(self, event_type: str, event_data: dict):
        """
        🔥 НОВЫЙ МЕТОД: Отправляет рыночные события всем подключенным пользователям
        (Например, сильные движения цен, важные новости)
        """
        try:
            message = {
                "type": "market_event",
                "event_type": event_type,
                "data": event_data,
                "timestamp": datetime.now().isoformat()
            }

            await self.broadcast_json(message)
            logger.info(f"📢 Broadcasted market event '{event_type}' to all users")

        except Exception as e:
            logger.error(f"❌ Error broadcasting market event: {str(e)}")
