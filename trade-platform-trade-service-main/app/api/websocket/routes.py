from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from app.api.websocket.connection_manager import ConnectionManager
import logging
import asyncio

logger = logging.getLogger(__name__)
router = APIRouter()
manager = ConnectionManager()

# Graceful shutdown handler
async def shutdown_websocket_manager():
    """Gracefully shuts down WebSocket manager"""
    try:
        logger.info("🛑 Shutting down WebSocket manager...")
        await manager._stop_background_tasks()
        
        # Закрываем все активные соединения
        if manager.active_connections:
            logger.info(f"🔌 Closing {len(manager.active_connections)} active connections...")
            
            for websocket, connection_info in list(manager.active_connections.items()):
                try:
                    await websocket.send_json({
                        "type": "server_shutdown",
                        "message": "Server is shutting down. Please reconnect in a moment.",
                        "timestamp": connection_info.connected_at.isoformat() if connection_info else None
                    })
                    await websocket.close(code=1001, reason="Server shutdown")
                except:
                    pass  # Ignore errors during shutdown
            
            # Очищаем все соединения
            manager.active_connections.clear()
            manager.user_connections.clear()
            
        logger.info("✅ WebSocket manager shutdown completed")
        
    except Exception as e:
        logger.error(f"❌ Error during WebSocket manager shutdown: {str(e)}")


@router.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """
    Улучшенная конечная точка WebSocket с поддержкой ping/pong и rate limiting
    """
    connection_successful = False
    
    try:
        # Принимаем соединение и регистрируем пользователя
        connection_successful = await manager.connect(websocket, user_id)
        
        if not connection_successful:
            logger.warning(f"⚠️ Failed to establish connection for user {user_id}")
            return

        logger.info(f"✅ WebSocket connection established for user {user_id}")

        # После подключения получаем открытые позиции пользователя
        try:
            positions = await manager.position_manager.get_positions(user_id, status="OPEN")

            # Преобразуем позиции в сериализуемый формат
            from app.core.utils import serialize_position
            serialized_positions = [serialize_position(pos) for pos in positions]

            # Отправляем текущие открытые позиции пользователю
            if serialized_positions:
                await websocket.send_json({
                    "status": "success", 
                    "action": "positions", 
                    "data": serialized_positions,
                    "count": len(serialized_positions)
                })

            # Получаем историю позиций (для вкладки Order History)
            closed_positions = await manager.position_manager.get_positions(user_id, status="CLOSED")
            liquidated_positions = await manager.position_manager.get_positions(user_id, status="LIQUIDATED")
            all_closed_positions = closed_positions + liquidated_positions

            # Сортируем по времени закрытия (от новых к старым)
            all_closed_positions.sort(key=lambda x: x.closed_at if x.closed_at else x.opened_at, reverse=True)

            # Отправляем историю позиций (ограничиваем до 50 последних)
            if all_closed_positions:
                serialized_history = [serialize_position(pos) for pos in all_closed_positions[:50]]
                await websocket.send_json({
                    "status": "success", 
                    "action": "position_history", 
                    "data": serialized_history,
                    "count": len(serialized_history),
                    "total_available": len(all_closed_positions)
                })

        except Exception as data_error:
            logger.error(f"❌ Error loading initial data for user {user_id}: {str(data_error)}")
            await websocket.send_json({
                "status": "warning", 
                "message": "Connected but failed to load initial data",
                "error": str(data_error)
            })

        # Обрабатываем сообщения от клиента с улучшенной обработкой ошибок
        message_count = 0
        last_message_time = asyncio.get_event_loop().time()
        
        try:
            while True:
                # Проверяем rate limiting (максимум 100 сообщений в минуту)
                current_time = asyncio.get_event_loop().time()
                if current_time - last_message_time < 60:
                    if message_count > 100:
                        logger.warning(f"⚠️ Rate limit exceeded for user {user_id}")
                        await websocket.send_json({
                            "status": "error",
                            "error": "Rate limit exceeded. Maximum 100 messages per minute."
                        })
                        await asyncio.sleep(5)  # Принудительная пауза
                        continue
                else:
                    # Сбрасываем счетчик каждую минуту
                    message_count = 0
                    last_message_time = current_time

                # Получаем сообщение с таймаутом
                try:
                    message = await asyncio.wait_for(websocket.receive_text(), timeout=300.0)  # 5 минут таймаут
                    message_count += 1
                    await manager.handle_message(websocket, message)
                    
                except asyncio.TimeoutError:
                    logger.info(f"⏰ Message timeout for user {user_id}, sending ping")
                    await websocket.send_json({
                        "action": "ping",
                        "timestamp": asyncio.get_event_loop().time()
                    })
                    
        except WebSocketDisconnect:
            logger.info(f"🔌 User {user_id} disconnected normally")
            
        except Exception as message_error:
            logger.error(f"❌ Error in message loop for user {user_id}: {str(message_error)}")
            await websocket.send_json({
                "status": "error",
                "error": "Message processing error",
                "details": str(message_error)
            })

    except Exception as e:
        # Если возникла ошибка при подключении, логируем и закрываем соединение
        logger.error(f"❌ Critical error in WebSocket connection for user {user_id}: {str(e)}")
        
        if connection_successful:
            try:
                await websocket.send_json({
                    "status": "error",
                    "error": "Critical connection error",
                    "message": "Connection will be closed"
                })
            except:
                pass  # Ignore errors when sending error message
                
    finally:
        # Всегда очищаем соединение при выходе
        try:
            manager.disconnect(websocket, user_id)
            logger.info(f"🧹 Cleaned up connection for user {user_id}")
        except Exception as cleanup_error:
            logger.error(f"❌ Error during connection cleanup for user {user_id}: {str(cleanup_error)}")


# Добавляем эндпоинт для мониторинга WebSocket соединений
@router.get("/ws/stats")
async def websocket_stats():
    """Возвращает статистику WebSocket соединений"""
    try:
        stats = await manager.get_connection_stats()
        return {
            "status": "success",
            "data": stats,
            "message": "WebSocket connection statistics"
        }
    except Exception as e:
        logger.error(f"❌ Error getting WebSocket stats: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "message": "Failed to get WebSocket statistics"
        }
