from fastapi import APIRouter, HTTPException, Body, Request
from typing import List, Optional
from app.core.models import (
    Position,
    UpdateTPSLRequest,
    UpdateOrderEntryPriceRequest,
    TradeExecutionRequest,
    TradeExecutionResponse,
)
from app.core.position_manager import PositionManager
from app.core.balance_client import balance_client
import logging

logger = logging.getLogger(__name__)
router = APIRouter()
position_manager = PositionManager()


# ===== ИСПРАВЛЕННЫЙ ENDPOINT ДЛЯ ИСПОЛНЕНИЯ СДЕЛОК =====
@router.post("/trades/execute", response_model=TradeExecutionResponse)
async def execute_trade(request: TradeExecutionRequest, http_request: Request):
    """
    Исполняет торговую сделку (открывает позицию)
    ✅ ИСПРАВЛЕНО: Убрано двойное списание средств
    """
    # НАЧАЛЬНЫЕ ЛОГИ
    logger.info("=" * 50)
    logger.info("🚀 EXECUTE TRADE ENDPOINT HIT!")
    logger.info(f"📍 Client IP: {http_request.client.host}")
    logger.info(f"📍 Headers: {dict(http_request.headers)}")
    logger.info(f"📍 Method: {http_request.method}")
    logger.info(f"📍 URL: {http_request.url}")

    try:
        logger.info(f"📨 Received REST trade execution request:")
        logger.info(f"   - user_id: {request.user_id}")
        logger.info(f"   - trading_pair: {request.trading_pair}")
        logger.info(f"   - entry_price: {request.entry_price}")
        logger.info(f"   - current_price: {request.current_price}")
        logger.info(f"   - order_type: {request.order_type}")
        logger.info(f"   - order_execution_type: {request.order_execution_type}")
        logger.info(f"   - leverage: {request.leverage}")
        logger.info(f"   - margin: {request.margin}")
        logger.info(f"   - stop_loss: {request.stop_loss}")
        logger.info(f"   - take_profit: {request.take_profit}")

        logger.info("🔄 Calling position_manager.open_position()...")

        # Открываем позицию через position_manager
        position = await position_manager.open_position(
            user_id=request.user_id,
            trading_pair=request.trading_pair,
            direction=request.order_type.upper(),  # "Long" -> "LONG", "Short" -> "SHORT"
            entry_price=request.entry_price,
            current_price=request.current_price,
            execution_type=request.order_execution_type,
            margin=request.margin,
            leverage=request.leverage,
            stop_loss=request.stop_loss,
            take_profit=request.take_profit,
        )

        logger.info(f"✅ Position successfully opened via REST:")
        logger.info(f"   - Position ID: {position.id}")
        logger.info(f"   - Status: {position.status}")
        logger.info(f"   - Direction: {position.direction}")
        logger.info(f"   - Position Size: {position.position_size}")

        # ✅ ИСПРАВЛЕНИЕ: Получаем баланс ТОЛЬКО для отображения (БЕЗ списания)
        # Списание произойдет автоматически через Kafka в Balance Service
        updated_balance = None

        try:
            # ❌ УБРАНО: НЕ списываем средства здесь!
            # lock_success = await balance_client.lock_user_balance(
            #     user_id=request.user_id,
            #     amount=request.margin,
            #     reason=f"position_open_{position.id}"
            # )

            # ✅ ТОЛЬКО получаем текущий баланс для ответа (без списания)
            logger.info("💰 Getting current balance for response (no deduction here)")
            updated_balance = await balance_client.get_user_balance(request.user_id)
            logger.info("✅ Balance retrieved successfully for display")

        except Exception as e:
            logger.error(f"❌ Failed to get balance for display: {str(e)}")

        logger.info("ℹ️  NOTE: Balance deduction will happen automatically via Kafka in Balance Service")

        return TradeExecutionResponse(success=True, position=position, updated_balance=updated_balance)

    except ValueError as e:
        logger.error(f"❌ VALIDATION ERROR in trade execution: {str(e)}")
        logger.error("=" * 50)
        return TradeExecutionResponse(success=False, error=str(e))
    except Exception as e:
        logger.error(f"❌ FATAL ERROR executing trade via REST: {str(e)}")
        logger.error(f"❌ Exception type: {type(e)}")
        import traceback

        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        logger.error("=" * 50)
        return TradeExecutionResponse(success=False, error=f"Failed to execute trade: {str(e)}")


# ===== ОСТАЛЬНЫЕ REST ENDPOINTS =====


@router.get("/positions", response_model=List[Position])
async def get_positions(user_id: str, status: Optional[str] = None, http_request: Request = None):
    """
    Получает список позиций пользователя с актуальным PnL из базы данных
    """
    logger.info(f"📍 GET POSITIONS called - user_id: {user_id}, status: {status}")
    try:
        # 🔥 ИСПРАВЛЕНИЕ: Просто возвращаем позиции из базы данных
        # PnL уже обновлен фоновой задачей и сохранен в БД
        positions = await position_manager.get_positions(user_id, status)

        # Логируем PnL для отладки
        if status == "OPEN" or status is None:
            open_positions = [pos for pos in positions if pos.status == "OPEN"]
            if open_positions:
                logger.info(f"📊 Returning {len(open_positions)} open positions with updated PnL:")
                for pos in open_positions[:3]:  # Показываем первые 3 для отладки
                    logger.info(
                        f"   💰 Position {pos.id} ({pos.trading_pair}): PnL = {pos.pnl}, Return = {pos.return_percentage}%"
                    )

        logger.info(f"✅ Found {len(positions)} positions (PnL updated from database)")
        return positions
    except Exception as e:
        logger.error(f"❌ Error getting positions: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get positions: {str(e)}")


@router.get("/positions/history", response_model=List[Position])
async def get_position_history(user_id: str, http_request: Request = None):
    """
    Получает историю закрытых и ликвидированных позиций пользователя
    """
    logger.info(f"📍 GET POSITION HISTORY called - user_id: {user_id}")
    try:
        # Получаем закрытые позиции
        closed_positions = await position_manager.get_positions(user_id, status="CLOSED")

        # Получаем ликвидированные позиции
        liquidated_positions = await position_manager.get_positions(user_id, status="LIQUIDATED")

        # Объединяем все закрытые позиции
        all_closed_positions = closed_positions + liquidated_positions

        # Сортируем по времени закрытия (от новых к старым)
        all_closed_positions.sort(key=lambda x: x.closed_at if x.closed_at else x.opened_at, reverse=True)

        logger.info(f"✅ Found {len(all_closed_positions)} historical positions")
        return all_closed_positions
    except Exception as e:
        logger.error(f"❌ Error getting position history: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get position history: {str(e)}")


@router.post("/positions/close", response_model=Position)
async def close_position(data: dict = Body(...), http_request: Request = None):
    """
    Закрывает позицию
    """
    logger.info(f"📍 CLOSE POSITION called - data: {data}")
    try:
        user_id = data.get("user_id")
        position_id = data.get("position_id")

        if not user_id:
            logger.error("❌ User ID is required")
            raise HTTPException(status_code=400, detail="User ID is required")
        if not position_id:
            logger.error("❌ Position ID is required")
            raise HTTPException(status_code=400, detail="Position ID is required")

        logger.info(f"🔄 Closing position {position_id} for user {user_id}")
        position = await position_manager.close_position(user_id, position_id)
        logger.info(f"✅ Position {position_id} closed successfully")
        return position
    except ValueError as e:
        logger.error(f"❌ Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Error closing position: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to close position: {str(e)}")


@router.post("/positions/update-tpsl", response_model=Position)
async def update_position_tpsl(request: UpdateTPSLRequest, http_request: Request = None):
    """
    Обновляет значения Take Profit и Stop Loss для позиции
    """
    logger.info(f"📍 UPDATE TP/SL called - position_id: {request.position_id}")
    logger.info(f"📍 Received take_profit: {request.take_profit}, stop_loss: {request.stop_loss}")
    try:
        position = await position_manager.update_position_tpsl(
            position_id=request.position_id, take_profit=request.take_profit, stop_loss=request.stop_loss
        )
        logger.info(f"✅ TP/SL updated for position {request.position_id}")
        return position
    except ValueError as e:
        logger.error(f"❌ Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Error updating position TP/SL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update position TP/SL: {str(e)}")


# Маршруты для ордеров


@router.get("/orders/open", response_model=List[Position])
async def get_open_orders(user_id: str, http_request: Request = None):
    """
    Получает список открытых ордеров пользователя
    """
    logger.info(f"📍 GET OPEN ORDERS called - user_id: {user_id}")
    try:
        # Получаем отложенные ордера (PENDING)
        pending_orders = await position_manager.get_pending_orders(user_id)
        logger.info(f"✅ Found {len(pending_orders)} open orders")
        return pending_orders
    except Exception as e:
        logger.error(f"❌ Error getting open orders: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get open orders: {str(e)}")


@router.get("/orders/filled", response_model=List[Position])
async def get_filled_orders(user_id: str, http_request: Request = None):
    """
    Получает список исполненных ордеров пользователя
    """
    logger.info(f"📍 GET FILLED ORDERS called - user_id: {user_id}")
    try:
        # Получаем открытые позиции (исполненные ордера)
        filled_orders = await position_manager.get_positions(user_id, status="OPEN")
        logger.info(f"✅ Found {len(filled_orders)} filled orders")
        return filled_orders
    except Exception as e:
        logger.error(f"❌ Error getting filled orders: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get filled orders: {str(e)}")


@router.post("/orders/cancel")
async def cancel_order(data: dict = Body(...), http_request: Request = None):
    """
    Отменяет ордер
    """
    logger.info(f"📍 CANCEL ORDER called - data: {data}")
    try:
        user_id = data.get("user_id")
        order_id = data.get("order_id")

        if not user_id:
            logger.error("❌ User ID is required")
            raise HTTPException(status_code=400, detail="User ID is required")
        if not order_id:
            logger.error("❌ Order ID is required")
            raise HTTPException(status_code=400, detail="Order ID is required")

        logger.info(f"🔄 Cancelling order {order_id} for user {user_id}")
        # Отменяем ордер через position_manager
        success = await position_manager.cancel_order(user_id, order_id)

        if success:
            logger.info(f"✅ Order {order_id} cancelled successfully")
            return {"status": "success", "message": f"Order {order_id} successfully cancelled"}
        else:
            logger.error(f"❌ Failed to cancel order {order_id}")
            raise HTTPException(status_code=500, detail="Failed to cancel order")

    except ValueError as e:
        logger.error(f"❌ Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Error cancelling order: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to cancel order: {str(e)}")


@router.post("/orders/update-entry-price", response_model=Position)
async def update_order_entry_price(request: UpdateOrderEntryPriceRequest, http_request: Request = None):
    """
    Обновляет entry_price для лимитного ордера со статусом PENDING
    """
    logger.info(f"📍 UPDATE ORDER ENTRY PRICE called - order_id: {request.order_id}")
    logger.info(f"📍 New entry_price: {request.entry_price}")
    try:
        updated_order = await position_manager.update_order_entry_price(
            user_id=request.user_id, order_id=request.order_id, new_entry_price=request.entry_price
        )
        logger.info(f"✅ Entry price updated for order {request.order_id}")
        return updated_order
    except ValueError as e:
        logger.error(f"❌ Validation error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"❌ Error updating order entry price: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to update order entry price: {str(e)}")


@router.get("/positions/pnl-only/{user_id}")
async def get_pnl_updates_only(user_id: str, http_request: Request = None):
    """
    Получает только PnL данные для открытых позиций пользователя (легковесный эндпоинт)
    """
    logger.info(f"📍 GET PNL ONLY called - user_id: {user_id}")
    try:
        # Получаем открытые позиции пользователя
        logger.info(f"🔍 Getting open positions for user {user_id}")
        open_positions = await position_manager.get_positions(user_id, status="OPEN")
        logger.info(f"📊 Found {len(open_positions)} open positions")

        if not open_positions:
            logger.info(f"✅ No open positions for user {user_id}")
            return []

        # Получаем актуальные цены
        logger.info(f"🔍 Getting price service...")
        from app.main import price_service

        if not price_service:
            logger.error("❌ Price service not available")
            raise HTTPException(status_code=503, detail="Price service not available")

        logger.info(f"🔍 Price service status: connected={price_service.is_connected}")
        current_prices = price_service.get_all_prices()
        logger.info(f"📊 Got {len(current_prices)} current prices")
        logger.info(f"📊 Available trading pairs: {list(current_prices.keys())}")

        if not current_prices:
            logger.warning("⚠️ No current prices available from price service")
            return []

        # Возвращаем только PnL данные
        pnl_updates = []
        for position in open_positions:
            try:
                trading_pair = position.trading_pair
                logger.info(f"🔍 Processing position {position.id} for pair {trading_pair}")

                if trading_pair in current_prices:
                    current_price = current_prices[trading_pair]
                    logger.info(f"📊 Found price for {trading_pair}: {current_price}")

                    # Валидация данных перед расчетом
                    if not position.entry_price or position.entry_price <= 0:
                        logger.error(f"❌ Invalid entry_price for position {position.id}: {position.entry_price}")
                        continue
                    
                    if not position.margin or position.margin <= 0:
                        logger.error(f"❌ Invalid margin for position {position.id}: {position.margin}")
                        continue
                    
                    if not position.position_size or position.position_size <= 0:
                        logger.error(f"❌ Invalid position_size for position {position.id}: {position.position_size}")
                        continue

                    # Рассчитываем актуальный PnL с защитой от деления на ноль
                    if position.direction == "LONG":
                        pnl = position.position_size * ((current_price - position.entry_price) / position.entry_price)
                    else:  # SHORT
                        pnl = position.position_size * ((position.entry_price - current_price) / position.entry_price)

                    return_percentage = (pnl / position.margin) * 100

                    pnl_update = {
                        "id": position.id,
                        "current_price": current_price,
                        "pnl": pnl,
                        "return_percentage": return_percentage,
                    }
                    pnl_updates.append(pnl_update)

                    logger.info(f"💰 Calculated PnL for position {position.id}: {pnl} ({return_percentage:.2f}%)")
                else:
                    logger.warning(f"⚠️ No current price for {trading_pair}")
                    
            except ZeroDivisionError as zde:
                logger.error(f"❌ Division by zero for position {position.id}: {str(zde)}")
                logger.error(f"   Position data: entry_price={position.entry_price}, margin={position.margin}, position_size={position.position_size}")
                continue
            except Exception as pos_error:
                logger.error(f"❌ Error calculating PnL for position {position.id}: {str(pos_error)}")
                continue

        logger.info(f"✅ Returned PnL updates for {len(pnl_updates)} positions")
        return pnl_updates

    except Exception as e:
        logger.error(f"❌ Error getting PnL updates: {str(e)}")
        import traceback

        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to get PnL updates: {str(e)}")


@router.get("/positions/pnl/{user_id}")
async def get_positions_with_current_pnl(user_id: str, http_request: Request = None):
    """
    Получает позиции пользователя с актуальным PnL на основе текущих рыночных цен
    """
    logger.info(f"📍 GET POSITIONS PNL called - user_id: {user_id}")
    try:
        # Получаем актуальные цены из Price Service
        logger.info(f"🔍 Getting price service...")
        from app.main import price_service

        if not price_service:
            logger.error("❌ Price service not available")
            raise HTTPException(status_code=503, detail="Price service not available")

        logger.info(f"🔍 Price service status: connected={price_service.is_connected}")
        current_prices = price_service.get_all_prices()
        logger.info(f"📊 Got {len(current_prices)} current prices")

        if not current_prices:
            logger.warning("⚠️ No current prices available from price service")
            # Возвращаем позиции без обновленного PnL
            return await position_manager.get_positions(user_id, status="OPEN")

        # Получаем позиции с актуальным PnL
        logger.info(f"🔍 Getting positions with current PnL...")
        positions_with_pnl = await position_manager.get_positions_with_current_pnl(user_id, current_prices)

        logger.info(f"✅ Returned {len(positions_with_pnl)} positions with updated PnL")
        return positions_with_pnl

    except Exception as e:
        logger.error(f"❌ Error getting positions with PnL: {str(e)}")
        import traceback

        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail=f"Failed to get positions PnL: {str(e)}")


# 🔥 НОВЫЙ ENDPOINT: Проверка и исполнение лимитных ордеров
@router.post("/orders/check-pending")
async def check_pending_orders(http_request: Request = None):
    """
    Проверяет и исполняет лимитные ордера на основе текущих цен
    """
    logger.info(f"📍 CHECK PENDING ORDERS called")
    try:
        # Получаем актуальные цены
        from app.main import price_service

        if not price_service:
            raise HTTPException(status_code=503, detail="Price service not available")

        current_prices = price_service.get_all_prices()

        # Проверяем и исполняем отложенные ордера
        executed_orders = await position_manager.check_and_execute_pending_orders(current_prices)

        logger.info(f"✅ Executed {len(executed_orders)} pending orders")
        return {"executed_count": len(executed_orders), "executed_orders": executed_orders}

    except Exception as e:
        logger.error(f"❌ Error checking pending orders: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check pending orders: {str(e)}")


# 🔥 НОВЫЙ ENDPOINT: Проверка условий ликвидации
@router.post("/positions/check-liquidation")
async def check_liquidation_conditions(http_request: Request = None):
    """
    Проверяет условия ликвидации для открытых позиций
    """
    logger.info(f"📍 CHECK LIQUIDATION called")
    try:
        # Получаем актуальные цены
        from app.main import price_service

        if not price_service:
            raise HTTPException(status_code=503, detail="Price service not available")

        current_prices = price_service.get_all_prices()

        # Проверяем условия ликвидации
        liquidated_positions = await position_manager.check_liquidation_conditions(current_prices)

        logger.info(f"✅ Liquidated {len(liquidated_positions)} positions")
        return {"liquidated_count": len(liquidated_positions), "liquidated_positions": liquidated_positions}

    except Exception as e:
        logger.error(f"❌ Error checking liquidation: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check liquidation: {str(e)}")


# 🔥 НОВЫЙ ENDPOINT: Проверка срабатывания TP/SL
@router.post("/positions/check-tpsl")
async def check_tpsl_triggers(http_request: Request = None):
    """
    Проверяет срабатывание Take Profit и Stop Loss для открытых позиций
    """
    logger.info(f"📍 CHECK TP/SL called")
    try:
        # Получаем актуальные цены
        from app.main import price_service

        if not price_service:
            raise HTTPException(status_code=503, detail="Price service not available")

        current_prices = price_service.get_all_prices()

        # Проверяем срабатывание TP/SL
        closed_positions = await position_manager.check_tpsl_triggers(current_prices)

        logger.info(f"✅ Closed {len(closed_positions)} positions by TP/SL")
        return {"closed_count": len(closed_positions), "closed_positions": closed_positions}

    except Exception as e:
        logger.error(f"❌ Error checking TP/SL: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to check TP/SL: {str(e)}")
