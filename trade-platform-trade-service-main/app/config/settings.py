from pydantic import BaseModel, field_validator
from typing import List, Any
import json
import os


class Settings(BaseModel):
    # Основные настройки
    APP_NAME: str = "Trade Service"
    DEBUG: bool = False
    PORT: int = 8008
    LOG_LEVEL: str = "INFO"
    API_PREFIX: str = "/api"

    # Настройки базы данных
    DATABASE_URL: str = (
        "postgresql://postgres:<EMAIL>/trade_platform_db"
    )
    DATABASE_POOL_SIZE: int = 5
    DATABASE_MAX_OVERFLOW: int = 10
    DATABASE_POOL_TIMEOUT: int = 30
    DATABASE_POOL_RECYCLE: int = 1800

    # Настройки для подключения к другим сервисам
    BALANCE_SERVICE_URL: str = "http://*************:8005"

    # Настройки CORS
    CORS_ORIGINS: List[str] = [
    "http://**************:3000", 
    "http://localhost:8080",
    "https://app.bitmei.com",
    "https://admin.bitmei.com",
    "http://localhost:3000",  # Добавь это
    "http://localhost:3001",  # И это если нужно
    ]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    # Настройки Kafka
    KAFKA_BOOTSTRAP_SERVERS: str = "*************:9093"
    KAFKA_BALANCE_TOPIC: str = "balance-operations"
    KAFKA_GROUP_ID: str = "trade-service-group"

    # Настройки Binance WebSocket
    BINANCE_WS_URL: str = "wss://stream.binance.com:9443/ws"

    # Поддерживаемые торговые пары
    SUPPORTED_PAIRS: List[str] = ["BTC/USDT", "ETH/USDT", "SOL/USDT", "BNB/USDT", "DOGE/USDT", "XRP/USDT"]

    # Интервал обновления цен (в секундах)
    PRICE_UPDATE_INTERVAL: int = 5

    # Интервал сохранения цен в базе данных (в секундах)
    PRICE_SAVE_INTERVAL: int = 60

    # Настройки для работы с позициями
    MAX_LEVERAGE: int = 100
    BASE_FEE_PERCENTAGE: float = 0.1

    @field_validator("CORS_ORIGINS", "CORS_ALLOW_METHODS", "CORS_ALLOW_HEADERS", "SUPPORTED_PAIRS", mode="before")
    @classmethod
    def parse_json_string(cls, v):
        if isinstance(v, str):
            try:
                # Пытаемся распарсить как JSON
                return json.loads(v)
            except json.JSONDecodeError:
                # Если не JSON, разбиваем по запятой
                return [item.strip() for item in v.split(",")]
        return v

    model_config = {"env_file": ".env", "env_file_encoding": "utf-8"}


# Загрузка настроек из .env файла
def get_settings():
    return Settings()


# Создаем экземпляр настроек
settings = get_settings()
