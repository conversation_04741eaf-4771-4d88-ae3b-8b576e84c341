from pydantic import BaseModel, Field
from typing import Optional, List
from datetime import datetime


class TradeExecutionRequest(BaseModel):
    user_id: str
    trading_pair: str
    entry_price: float
    current_price: float
    order_type: str  # "Long" или "Short"
    order_execution_type: str = "LIMIT"  # "LIMIT" или "MARKET"
    fee_percentage: float = 0.1
    leverage: int
    margin: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None


class Position(BaseModel):
    id: Optional[int] = None
    user_id: str
    trading_pair: str
    direction: str  # "LONG" или "SHORT"
    entry_price: float
    current_price: float
    position_size: float
    margin: float
    leverage: int
    liquidation_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    pnl: float = 0
    status: str = "OPEN"  # "OPEN", "CLOSED", "LIQUIDATED"
    opened_at: datetime = Field(default_factory=datetime.now)
    closed_at: Optional[datetime] = None
    close_reason: Optional[str] = None
    fee: float
    return_percentage: float = 0


class BalanceInfo(BaseModel):
    """Информация о балансе пользователя"""

    available_usd_balance: float
    locked_usd_balance: float
    fixed_usd_balance: float


class TradeExecutionResponse(BaseModel):
    success: bool
    position: Optional[Position] = None
    updated_balance: Optional[BalanceInfo] = None
    error: Optional[str] = None


# Добавляем модель для обновления TP/SL
class UpdateTPSLRequest(BaseModel):
    user_id: str
    position_id: int
    take_profit: Optional[float] = None
    stop_loss: Optional[float] = None


# Добавляем модель для обновления entry_price лимитного ордера
class UpdateOrderEntryPriceRequest(BaseModel):
    user_id: str
    order_id: int
    entry_price: float
