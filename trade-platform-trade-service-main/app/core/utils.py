from datetime import datetime
import json
from typing import Any, Dict, Union


def datetime_serializer(obj):
    """
    Функция-сериализатор для объектов datetime
    """
    if isinstance(obj, datetime):
        return obj.isoformat()
    raise TypeError(f"Type {type(obj)} not serializable")


def serialize_position(position: Any) -> Dict:
    """
    Преобразует объект позиции в сериализуемый словарь
    """
    # Если у объекта есть метод dict
    if hasattr(position, "dict"):
        position_dict = position.dict()
    else:
        # Иначе берём атрибуты напрямую
        position_dict = position.__dict__.copy()
        if "_sa_instance_state" in position_dict:
            del position_dict["_sa_instance_state"]

    # Сериализуем datetime поля
    return json.loads(json.dumps(position_dict, default=datetime_serializer))


def convert_to_decimal(value: Union[float, str, None]) -> Any:
    """
    Конвертирует значение в decimal для сохранения в БД
    """
    if value is None:
        return None
    from decimal import Decimal

    return Decimal(str(value))
