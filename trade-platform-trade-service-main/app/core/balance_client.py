"""
HTTP клиент для взаимодействия с Balance Service
"""

import httpx
import logging
from typing import Optional, Dict, Any
from app.config.settings import Settings
from app.core.models import BalanceInfo

logger = logging.getLogger(__name__)
settings = Settings()


class BalanceServiceClient:
    """Клиент для работы с Balance Service"""

    def __init__(self):
        self.base_url = settings.BALANCE_SERVICE_URL
        self.timeout = 10.0  # 10 секунд таймаут

    async def lock_user_balance(self, user_id: str, amount: float, reason: str = "position_open") -> bool:
        """
        Блокирует средства пользователя через Balance Service API

        Args:
            user_id: ID пользователя
            amount: Сумма для блокировки
            reason: Причина блокировки

        Returns:
            bool: True если блокировка успешна
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                url = f"{self.base_url}/api/kafka/send"

                kafka_message = {
                    "topic": "balance-operations",
                    "key": str(user_id),
                    "value": {
                        "event_type": "trade_operation",
                        "operation_type": "open_position",
                        "user_id": int(user_id),
                        "amount": float(amount),
                        "currency": "USD",
                        "margin_usd": float(amount),
                        "pnl_usd": 0.0,
                    },
                }

                response = await client.post(url, json=kafka_message)

                if response.status_code == 202:
                    logger.info(f"✅ Balance deducted for user {user_id}: ${amount}")
                    return True
                else:
                    logger.error(f"❌ Balance deduction failed: HTTP {response.status_code}")
                    return False

        except Exception as e:
            logger.error(f"❌ BALANCE CLIENT - Ошибка при блокировке средств: {str(e)}")
            import traceback

            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            return False

    async def get_user_balance(self, user_id: str) -> Optional[BalanceInfo]:
        """
        Получает актуальный баланс пользователя из Balance Service

        Args:
            user_id: ID пользователя

        Returns:
            BalanceInfo: Информация о балансе или None при ошибке
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                url = f"{self.base_url}/api/balance/fixed"
                params = {"user_id": user_id}

                response = await client.get(url, params=params)

                if response.status_code == 200:
                    data = response.json()

                    balance_info = BalanceInfo(
                        available_usd_balance=float(data.get("available_usd_balance", 0)),
                        locked_usd_balance=float(data.get("locked_usd_balance", 0)),
                        fixed_usd_balance=float(data.get("fixed_usd_balance", 0)),
                    )

                    return balance_info
                else:
                    logger.error(f"❌ Failed to get balance: HTTP {response.status_code}")
                    return None

        except httpx.TimeoutException:
            logger.error(f"❌ BALANCE CLIENT - Таймаут при запросе баланса для пользователя {user_id}")
            return None
        except httpx.RequestError as e:
            logger.error(f"❌ BALANCE CLIENT - Ошибка сети при запросе баланса: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"❌ BALANCE CLIENT - Неожиданная ошибка: {str(e)}")
            import traceback

            logger.error(f"📍 Traceback: {traceback.format_exc()}")
            return None


# Создаем глобальный экземпляр клиента
balance_client = BalanceServiceClient()
