from typing import List, Optional, Dict
from app.core.models import Position as PositionModel
from app.db.repository import PositionRepository, PriceRepository, KafkaEventRepository
from datetime import datetime
import logging
from sqlalchemy.orm import Session
from app.core.utils import convert_to_decimal
from app.db.models import Position
from sqlalchemy import text
from contextlib import contextmanager

# Настраиваем логгер
logger = logging.getLogger(__name__)


# Контекстный менеджер для работы с сессией БД
@contextmanager
def get_db_session():
    """
    Контекстный менеджер для сессий БД, гарантирующий правильное закрытие
    """
    from app.db.base import get_db

    session = None
    try:
        session = next(get_db())
        yield session
        session.commit()
    except Exception as e:
        logger.error(f"DB session error: {str(e)}")
        if session:
            try:
                session.rollback()
            except Exception as rollback_error:
                logger.error(f"Error during rollback: {str(rollback_error)}")
        raise
    finally:
        if session:
            try:
                session.close()
                logger.debug("Database session closed successfully")
            except Exception as close_error:
                logger.error(f"Error closing database session: {str(close_error)}")


class PositionManager:
    def __init__(self):
        # Не храним сессию БД в атрибутах класса
        pass

    @staticmethod
    def get_fee_percentage_by_leverage(leverage: float) -> float:
        """
        Возвращает процент комиссии в зависимости от плеча согласно таблице.

        Формула: Fee = Trading Fee% * Leverage * 2
        Где Trading Fee% = 0.06% (базовая торговая комиссия)
        """
        # Таблица комиссий согласно предоставленным данным
        fee_table = {
            1: 0.12,
            3: 0.36,
            5: 0.60,
            10: 1.20,
            12: 1.80,
            20: 2.40,
            25: 3.00,
            30: 3.60,
            40: 4.80,
            50: 6.00,
            75: 9.00,
            100: 12.00,
        }

        # Если точное значение есть в таблице
        if leverage in fee_table:
            return fee_table[leverage]

        # Если нет точного значения, используем формулу: 0.06% * leverage * 2
        base_trading_fee = 0.06  # 0.06% базовая торговая комиссия
        calculated_fee = base_trading_fee * leverage * 2

        # Ограничиваем максимум до 12% (как в таблице для x100)
        return min(calculated_fee, 12.0)

    def _model_to_pydantic(self, db_position) -> PositionModel:
        """
        Преобразует модель SQLAlchemy в Pydantic модель
        """
        return PositionModel(
            id=db_position.id,
            user_id=db_position.user_id,
            trading_pair=db_position.trading_pair,
            direction=db_position.direction,
            entry_price=float(db_position.entry_price),
            current_price=float(db_position.current_price),
            position_size=float(db_position.position_size),
            margin=float(db_position.margin),
            leverage=db_position.leverage,
            liquidation_price=float(db_position.liquidation_price),
            stop_loss=float(db_position.stop_loss) if db_position.stop_loss else None,
            take_profit=float(db_position.take_profit) if db_position.take_profit else None,
            pnl=float(db_position.pnl),
            status=db_position.status,
            opened_at=db_position.opened_at,
            closed_at=db_position.closed_at,
            close_reason=db_position.close_reason,
            fee=float(db_position.fee),
            return_percentage=db_position.return_percentage,
            order_execution_type=(
                db_position.order_execution_type if hasattr(db_position, "order_execution_type") else "MARKET"
            ),
        )

    async def open_position(
        self,
        user_id: str,
        trading_pair: str,
        direction: str,
        entry_price: float,
        current_price: float,
        execution_type: str,
        margin: float,
        leverage: int,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None,
    ) -> PositionModel:
        """Открывает новую позицию"""
        try:
            # 🔥 ДОБАВЛЯЕМ ВАЛИДАЦИЮ ВХОДНЫХ ДАННЫХ
            logger.info(f"🔍 Validating position data:")
            logger.info(f"   📍 entry_price: {entry_price} (type: {type(entry_price)})")
            logger.info(f"   📍 current_price: {current_price} (type: {type(current_price)})")
            logger.info(f"   📍 margin: {margin} (type: {type(margin)})")
            logger.info(f"   📍 leverage: {leverage}")

            # 🔥 ВАЛИДАЦИЯ - КРИТИЧНО!
            if not entry_price or entry_price <= 0:
                logger.error(f"❌ INVALID entry_price: {entry_price}")
                raise ValueError(f"Invalid entry_price: {entry_price}. Must be > 0")

            if not current_price or current_price <= 0:
                logger.error(f"❌ INVALID current_price: {current_price}")
                raise ValueError(f"Invalid current_price: {current_price}. Must be > 0")

            if not margin or margin <= 0:
                logger.error(f"❌ INVALID margin: {margin}")
                raise ValueError(f"Invalid margin: {margin}. Must be > 0")

            if not leverage or leverage <= 0:
                logger.error(f"❌ INVALID leverage: {leverage}")
                raise ValueError(f"Invalid leverage: {leverage}. Must be > 0")

            logger.info("✅ All input data validated successfully")

            # Используем контекстный менеджер вместо _get_db()
            with get_db_session() as db:
                # Logging для отладки
                logger.info(
                    f"Открытие позиции: user_id={user_id}, direction={direction}, execution_type={execution_type}"
                )
                logger.info(f"Параметры: entry_price={entry_price}, current_price={current_price}")

                # Флаг, указывающий, должен ли ордер быть отложенным
                create_pending_order = False

                # Проверка для лимитных ордеров - все лимитные ордера создаются как PENDING
                if execution_type.upper() == "LIMIT":
                    create_pending_order = True
                    logger.info(f"Создаем отложенный {direction} ордер с ценой входа {entry_price}")

                # Если нужно создать отложенный ордер
                if create_pending_order:
                    # Создаем отложенный ордер со статусом "PENDING"
                    pending_order = await self._create_pending_order(
                        db=db,
                        user_id=user_id,
                        trading_pair=trading_pair,
                        direction=direction,
                        entry_price=entry_price,
                        current_price=current_price,
                        execution_type=execution_type,
                        margin=margin,
                        leverage=leverage,
                        stop_loss=stop_loss,
                        take_profit=take_profit,
                    )

                    return pending_order

                # Код для обычного MARKET ордера остается без изменений
                quote_currency = trading_pair.split("/")[1]

                # Расчет маржи в USD для Kafka
                margin_usd = margin * leverage

                # 🔥 ДОПОЛНИТЕЛЬНАЯ ПРОВЕРКА ПЕРЕД KAFKA
                logger.info(f"📊 Final values before Kafka:")
                logger.info(f"   📍 margin: {margin}")
                logger.info(f"   📍 leverage: {leverage}")
                logger.info(f"   📍 margin_usd: {margin_usd}")

                # Отправляем событие в Kafka для списания средств (сохраняем в БД)
                try:
                    kafka_event = KafkaEventRepository.create_event(
                        db=db,
                        event_type="open_position",
                        payload={
                            "operation_type": "open_position",
                            "user_id": user_id,
                            "amount": margin,
                            "currency": quote_currency,
                            "trading_pair": trading_pair,
                            "leverage": leverage,
                            "margin_usd": margin_usd,  # Добавляем маржу в USD
                            "pnl_usd": 0,  # При открытии позиции PnL = 0
                            "timestamp": datetime.now().isoformat(),
                        },
                    )

                    # Пытаемся отправить событие через Kafka
                    try:
                        from app.kafka.producer import KafkaProducer

                        logger.info(f"📤 Отправляем сообщение в Kafka: open_position для user {user_id}")
                        logger.info(f"   📍 Amount: {margin} {quote_currency}")
                        logger.info(f"   📍 Margin USD: {margin_usd}")

                        kafka_result = await KafkaProducer.send_trade_operation(
                            operation_type="open_position",
                            user_id=user_id,
                            amount=margin,
                            currency=quote_currency,
                            margin_usd=margin_usd,  # Добавляем маржу в USD
                            pnl_usd=0,  # При открытии позиции PnL = 0
                        )

                        if kafka_result:
                            KafkaEventRepository.update_event_status(db, kafka_event.id, "SENT")
                            logger.info(f"✅ Kafka сообщение УСПЕШНО отправлено для списания {margin} {quote_currency}")
                        else:
                            KafkaEventRepository.update_event_status(
                                db, kafka_event.id, "FAILED", "Failed to send to Kafka"
                            )
                            logger.error(f"❌ Kafka сообщение НЕ ОТПРАВЛЕНО для пользователя {user_id}")
                    except ImportError:
                        logger.error("❌ Модуль Kafka Producer недоступен, средства не будут списаны автоматически")
                        KafkaEventRepository.update_event_status(
                            db, kafka_event.id, "FAILED", "Kafka Producer module not available"
                        )
                    except Exception as e:
                        logger.error(f"❌ ОШИБКА при отправке события в Kafka: {str(e)}")
                        KafkaEventRepository.update_event_status(db, kafka_event.id, "FAILED", str(e))
                except Exception as e:
                    logger.error(f"Ошибка при создании события Kafka: {str(e)}")

                # Расчет размера позиции
                position_size = margin * leverage

                # 🔥 ПРОВЕРЯЕМ РАССЧИТАННЫЕ ЗНАЧЕНИЯ
                logger.info(f"📊 Calculated values:")
                logger.info(f"   📍 position_size: {position_size}")

                # Расчет цены ликвидации
                if direction.upper() == "LONG":
                    liquidation_price = entry_price * (1 - 1 / leverage)
                else:  # SHORT
                    liquidation_price = entry_price * (1 + 1 / leverage)

                # 🔥 ПРОВЕРЯЕМ ЦЕНУ ЛИКВИДАЦИИ
                logger.info(f"   📍 liquidation_price: {liquidation_price}")

                # Расчет комиссии (базовая комиссия 0.1%)
                adjusted_fee_percentage = self.get_fee_percentage_by_leverage(leverage)

                # Расчет комиссии (открытие + закрытие)
                fee = margin * (adjusted_fee_percentage / 100)

                logger.info(f"   📍 fee: {fee}")

                # 🔥 ФИНАЛЬНАЯ ПРОВЕРКА ПЕРЕД СОЗДАНИЕМ В БД
                logger.info(f"📊 Final data for DB creation:")
                logger.info(f"   📍 entry_price: {entry_price}")
                logger.info(f"   📍 current_price: {current_price}")
                logger.info(f"   📍 position_size: {position_size}")
                logger.info(f"   📍 margin: {margin}")
                logger.info(f"   📍 liquidation_price: {liquidation_price}")
                logger.info(f"   📍 fee: {fee}")

                # Создание позиции в базе данных без параметра order_execution_type
                db_position = PositionRepository.create_position(
                    db=db,
                    user_id=user_id,
                    trading_pair=trading_pair,
                    direction=direction.upper(),
                    entry_price=entry_price,
                    current_price=current_price,
                    position_size=position_size,
                    margin=margin,
                    leverage=leverage,
                    liquidation_price=liquidation_price,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    fee=fee,
                    # Удален параметр order_execution_type
                )

                # 🔥 ПРОВЕРЯЕМ ЧТО СОХРАНИЛОСЬ В БД
                logger.info(f"✅ Position created in DB:")
                logger.info(f"   📍 ID: {db_position.id}")
                logger.info(f"   📍 DB entry_price: {db_position.entry_price}")
                logger.info(f"   📍 DB current_price: {db_position.current_price}")
                logger.info(f"   📍 DB margin: {db_position.margin}")
                logger.info(f"   📍 DB position_size: {db_position.position_size}")

                # Пытаемся сохранить тип исполнения, если поле существует
                try:
                    if hasattr(db_position, "order_execution_type"):
                        db_position.order_execution_type = execution_type.upper()
                        db.flush()
                except Exception as e:
                    logger.warning(f"Не удалось сохранить тип исполнения ордера: {str(e)}")

                logger.info(f"Открыта позиция {db_position.id} для пользователя {user_id}")

                # Преобразуем в Pydantic модель для API
                return self._model_to_pydantic(db_position)

        except Exception as e:
            logger.error(f"❌ Error opening position: {str(e)}")
            import traceback

            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            raise ValueError(f"Failed to open position: {str(e)}")

    async def _create_pending_order(
        self,
        db,
        user_id,
        trading_pair,
        direction,
        entry_price,
        current_price,
        execution_type,
        margin,
        leverage,
        stop_loss,
        take_profit,
    ):
        """Создает отложенный лимитный ордер - РЕЗЕРВИРУЕТ средства как open_position"""
        try:
            # Расчет размера позиции
            position_size = margin * leverage

            # Расчет цены ликвидации
            if direction.upper() == "LONG":
                liquidation_price = entry_price * (1 - 1 / leverage)
            else:  # SHORT
                liquidation_price = entry_price * (1 + 1 / leverage)

            # Расчет комиссии
            adjusted_fee_percentage = PositionManager.get_fee_percentage_by_leverage(leverage)

            # Расчет комиссии (открытие + закрытие)
            fee = margin * (adjusted_fee_percentage / 100)

            # 🔥 РЕЗЕРВИРУЕМ средства ТОЧНО КАК ПРИ open_position
            quote_currency = trading_pair.split("/")[1]
            margin_usd = margin * leverage

            logger.info(f"💰 РЕЗЕРВИРУЕМ средства для лимитного ордера (как open_position):")
            logger.info(f"   📍 User: {user_id}")
            logger.info(f"   📍 Amount: {margin} {quote_currency}")
            logger.info(f"   📍 Margin USD: {margin_usd}")

            # Отправляем событие в Kafka для списания средств (ТОЧНО КАК ДЛЯ ОБЫЧНОЙ ПОЗИЦИИ)
            try:
                kafka_event = KafkaEventRepository.create_event(
                    db=db,
                    event_type="open_position",  # ТОТ ЖЕ ТИП!
                    payload={
                        "operation_type": "open_position",  # ТА ЖЕ ОПЕРАЦИЯ!
                        "user_id": user_id,
                        "amount": margin,
                        "currency": quote_currency,
                        "trading_pair": trading_pair,
                        "leverage": leverage,
                        "margin_usd": margin_usd,
                        "pnl_usd": 0,
                        "timestamp": datetime.now().isoformat(),
                    },
                )

                # Пытаемся отправить событие через Kafka
                from app.kafka.producer import KafkaProducer

                logger.info(f"📤 Отправляем open_position для резервирования средств")

                kafka_result = await KafkaProducer.send_trade_operation(
                    operation_type="open_position",  # СТАНДАРТНАЯ ОПЕРАЦИЯ
                    user_id=user_id,
                    amount=margin,
                    currency=quote_currency,
                    margin_usd=margin_usd,
                    pnl_usd=0,
                )

                if kafka_result:
                    KafkaEventRepository.update_event_status(db, kafka_event.id, "SENT")
                    logger.info(f"✅ Средства зарезервированы для лимитного ордера")
                else:
                    KafkaEventRepository.update_event_status(db, kafka_event.id, "FAILED", "Failed to send to Kafka")
                    logger.error(f"❌ Не удалось зарезервировать средства")
                    raise ValueError("Не удалось зарезервировать средства для ордера")

            except Exception as e:
                logger.error(f"❌ ОШИБКА при резервировании средств: {str(e)}")
                raise ValueError(f"Ошибка резервирования средств: {str(e)}")

            # Создаем запись в БД ТОЛЬКО после успешного резервирования
            from sqlalchemy import text

            try:
                insert_query = text(
                    """
                    INSERT INTO positions 
                    (user_id, trading_pair, direction, entry_price, current_price, 
                    position_size, margin, leverage, liquidation_price, stop_loss, 
                    take_profit, fee, status, order_execution_type, opened_at, pnl, return_percentage) 
                    VALUES 
                    (:user_id, :trading_pair, :direction, :entry_price, :current_price, 
                    :position_size, :margin, :leverage, :liquidation_price, :stop_loss, 
                    :take_profit, :fee, :status, :order_execution_type, :opened_at, :pnl, :return_percentage)
                    RETURNING id
                """
                )

                result = db.execute(
                    insert_query,
                    {
                        "user_id": user_id,
                        "trading_pair": trading_pair,
                        "direction": direction.upper(),
                        "entry_price": entry_price,
                        "current_price": current_price,
                        "position_size": position_size,
                        "margin": margin,
                        "leverage": leverage,
                        "liquidation_price": liquidation_price,
                        "stop_loss": stop_loss,
                        "take_profit": take_profit,
                        "fee": fee,
                        "status": "PENDING",
                        "order_execution_type": execution_type.upper(),
                        "opened_at": datetime.now(),
                        "pnl": 0,
                        "return_percentage": 0,
                    },
                )

                position_id = result.fetchone()[0]
                db.commit()

                position = db.query(Position).filter(Position.id == position_id).first()
                logger.info(f"✅ Лимитный ордер {position.id} создан, средства зарезервированы")

            except Exception as sql_error:
                logger.error(f"Ошибка создания позиции: {str(sql_error)}")

                # Если не удалось создать - возвращаем зарезервированные средства
                try:
                    logger.error("❌ Возвращаем зарезервированные средства")

                    return_kafka_result = await KafkaProducer.send_trade_operation(
                        operation_type="close_position",  # ВОЗВРАЩАЕМ КАК close_position
                        user_id=user_id,
                        amount=0,
                        currency=quote_currency,
                        return_amount=margin,  # Просто возвращаем зарезервированную сумму
                        margin_usd=margin_usd,
                        pnl_usd=0,
                    )

                    if return_kafka_result:
                        logger.info("✅ Зарезервированные средства возвращены")
                    else:
                        logger.error("❌ Не удалось вернуть средства")

                except Exception as return_error:
                    logger.error(f"❌ Ошибка возврата средств: {str(return_error)}")

                raise ValueError(f"Failed to create pending order: {str(sql_error)}")

            # Создаем Pydantic модель
            model = PositionModel(
                id=position.id,
                user_id=position.user_id,
                trading_pair=position.trading_pair,
                direction=position.direction,
                entry_price=float(position.entry_price),
                current_price=float(position.current_price),
                position_size=float(position.position_size),
                margin=float(position.margin),
                leverage=position.leverage,
                liquidation_price=float(position.liquidation_price),
                stop_loss=float(position.stop_loss) if position.stop_loss else None,
                take_profit=float(position.take_profit) if position.take_profit else None,
                pnl=float(position.pnl),
                status="PENDING",
                opened_at=position.opened_at,
                closed_at=position.closed_at,
                close_reason=position.close_reason,
                fee=float(position.fee),
                return_percentage=position.return_percentage,
                order_execution_type=(
                    position.order_execution_type if hasattr(position, "order_execution_type") else "LIMIT"
                ),
            )

            return model
        except Exception as e:
            logger.error(f"Error creating pending order: {str(e)}")
            raise ValueError(f"Failed to create pending order: {str(e)}")

    async def close_position(self, user_id: str, position_id: int, current_price: float = None) -> PositionModel:
        """Закрывает существующую позицию"""
        try:
            with get_db_session() as db:
                # Поиск позиции
                db_position = PositionRepository.get_position(db, position_id)
                if not db_position:
                    raise ValueError(f"Position {position_id} not found")

                # Проверка принадлежности позиции пользователю
                if db_position.user_id != user_id:
                    raise ValueError("Position does not belong to this user")

                # Проверка статуса позиции
                if db_position.status != "OPEN":
                    raise ValueError(f"Position {position_id} is already {db_position.status}")

                # Используем текущую цену, если она предоставлена, иначе берем из базы
                if current_price is None:
                    current_price = float(db_position.current_price)

                # Валидация данных перед расчетом PnL
                entry_price = float(db_position.entry_price)
                margin = float(db_position.margin)
                position_size = float(db_position.position_size)
                
                if entry_price <= 0:
                    logger.error(f"❌ Invalid entry_price for position {position_id}: {entry_price}")
                    raise ValueError(f"Invalid entry_price for position {position_id}: {entry_price}")
                
                if margin <= 0:
                    logger.error(f"❌ Invalid margin for position {position_id}: {margin}")
                    raise ValueError(f"Invalid margin for position {position_id}: {margin}")
                
                if position_size <= 0:
                    logger.error(f"❌ Invalid position_size for position {position_id}: {position_size}")
                    raise ValueError(f"Invalid position_size for position {position_id}: {position_size}")

                # Расчет PnL с защитой от деления на ноль
                if db_position.direction == "LONG":
                    pnl = position_size * ((current_price - entry_price) / entry_price)
                else:  # SHORT
                    pnl = position_size * ((entry_price - current_price) / entry_price)

                # Расчет процента доходности
                return_percentage = (pnl / margin) * 100

                # Обновление статуса позиции в базе данных
                db_position = PositionRepository.close_position(
                    db=db,
                    position_id=position_id,
                    current_price=current_price,
                    pnl=pnl,
                    return_percentage=return_percentage,
                    close_reason="manual",
                )

                # 🔥 НОВАЯ ЛОГИКА: Подготавливаем данные для Balance Service
                quote_currency = db_position.trading_pair.split("/")[1]

                # ВАЖНО: margin_usd - это изначально заблокированная маржа (реальный вклад пользователя)
                # При открытии позиции мы блокировали amount (реальный вклад), а не полный margin * leverage
                original_user_investment = float(db_position.margin)  # Реальный вклад пользователя

                # return_amount = изначальный вклад + PnL - комиссия
                return_amount = original_user_investment + pnl - float(db_position.fee)

                # Для логирования и информации
                full_position_size = float(db_position.margin) * float(
                    db_position.leverage
                )  # Полный размер позиции с плечом

                logger.info(f"📊 CLOSE POSITION - Расчет средств для возврата:")
                logger.info(f"   📍 Изначальный вклад пользователя: {original_user_investment} {quote_currency}")
                logger.info(f"   📍 Полный размер позиции с плечом: {full_position_size} {quote_currency}")
                logger.info(f"   📍 PnL: {pnl} {quote_currency}")
                logger.info(f"   📍 Комиссия: {float(db_position.fee)} {quote_currency}")
                logger.info(f"   📍 Сумма к возврату: {return_amount} {quote_currency}")

                try:
                    # Сохраняем событие в БД
                    kafka_event = KafkaEventRepository.create_event(
                        db=db,
                        event_type="close_position",
                        payload={
                            "operation_type": "close_position",
                            "user_id": user_id,
                            "position_id": position_id,
                            "margin_usd": original_user_investment,  # 🔥 НОВОЕ: Изначальная маржа для разблокировки
                            "return_amount": return_amount,  # 🔥 НОВОЕ: Сумма к добавлению в fixed_balance
                            "pnl_usd": pnl,  # Для логирования
                            "currency": quote_currency,
                            "timestamp": datetime.now().isoformat(),
                        },
                    )

                    # Пытаемся отправить через Kafka
                    logger.info(f"📤 Отправляем сообщение close_position в Balance Service:")
                    logger.info(f"   📍 margin_usd (для разблокировки): {original_user_investment} USD")
                    logger.info(f"   📍 return_amount (к добавлению): {return_amount} USD")
                    logger.info(f"   📍 pnl_usd: {pnl} USD")
                    logger.info(f"   📍 User: {user_id}, Position: {position_id}")

                    from app.kafka.producer import KafkaProducer

                    kafka_result = await KafkaProducer.send_trade_operation(
                        operation_type="close_position",
                        user_id=user_id,
                        amount=0,  # При закрытии не списываем средства
                        currency=quote_currency,
                        position_id=str(position_id),
                        margin_usd=original_user_investment,  # 🔥 НОВОЕ: Маржа для разблокировки
                        return_amount=return_amount,  # 🔥 НОВОЕ: Сумма к возврату
                        pnl_usd=pnl,  # PnL для логирования
                    )

                    if kafka_result:
                        KafkaEventRepository.update_event_status(db, kafka_event.id, "SENT")
                        logger.info(f"✅ Kafka сообщение close_position УСПЕШНО отправлено")
                    else:
                        KafkaEventRepository.update_event_status(
                            db, kafka_event.id, "FAILED", "Failed to send to Kafka"
                        )
                        logger.error(f"❌ Kafka сообщение close_position НЕ ОТПРАВЛЕНО")
                except Exception as e:
                    logger.error(f"Error sending return funds event: {str(e)}")

                logger.info(f"Closed position {position_id} for {user_id}")
                return self._model_to_pydantic(db_position)

        except Exception as e:
            logger.error(f"Error closing position: {str(e)}")
            raise ValueError(f"Failed to close position: {str(e)}")

    async def get_positions(self, user_id: str, status: str = None) -> List[PositionModel]:
        """Получает список позиций пользователя"""
        try:
            with get_db_session() as db:
                db_positions = PositionRepository.get_positions_by_user(db, user_id, status)

                # Преобразуем в Pydantic модели
                return [self._model_to_pydantic(pos) for pos in db_positions]
        except Exception as e:
            logger.error(f"Error getting positions: {str(e)}")
            return []

    # 🔥 НОВЫЙ МЕТОД: Получение позиций с актуальным PnL
    async def get_positions_with_current_pnl(
        self, user_id: str, current_prices: Dict[str, float]
    ) -> List[PositionModel]:
        """
        Получает открытые позиции пользователя с актуальным PnL на основе текущих цен

        Args:
            user_id: ID пользователя
            current_prices: Словарь с текущими ценами {trading_pair: price}

        Returns:
            Список позиций с обновленным PnL
        """
        try:
            with get_db_session() as db:
                # Получаем только открытые позиции
                db_positions = PositionRepository.get_positions_by_user(db, user_id, "OPEN")

                updated_positions = []
                for db_position in db_positions:
                    trading_pair = db_position.trading_pair

                    # Создаем базовую Pydantic модель
                    position_model = self._model_to_pydantic(db_position)

                    # Если есть актуальная цена, пересчитываем PnL
                    if trading_pair in current_prices:
                        current_price = current_prices[trading_pair]

                        try:
                            # Валидация данных перед расчетом
                            entry_price = float(db_position.entry_price)
                            margin = float(db_position.margin)
                            position_size = float(db_position.position_size)
                            
                            if entry_price <= 0:
                                logger.error(f"❌ Invalid entry_price for position {db_position.id}: {entry_price}")
                                updated_positions.append(position_model)
                                continue
                            
                            if margin <= 0:
                                logger.error(f"❌ Invalid margin for position {db_position.id}: {margin}")
                                updated_positions.append(position_model)
                                continue
                            
                            if position_size <= 0:
                                logger.error(f"❌ Invalid position_size for position {db_position.id}: {position_size}")
                                updated_positions.append(position_model)
                                continue

                            # Рассчитываем актуальный PnL с защитой от деления на ноль
                            if db_position.direction == "LONG":
                                pnl = position_size * ((current_price - entry_price) / entry_price)
                            else:  # SHORT
                                pnl = position_size * ((entry_price - current_price) / entry_price)

                            # Рассчитываем процент доходности
                            return_percentage = (pnl / margin) * 100

                            # Обновляем модель с актуальными данными
                            position_model.current_price = current_price
                            position_model.pnl = pnl
                            position_model.return_percentage = return_percentage

                            logger.debug(f"Updated PnL for position {db_position.id}: {pnl}")
                            
                        except ZeroDivisionError as zde:
                            logger.error(f"❌ Division by zero for position {db_position.id}: {str(zde)}")
                            logger.error(f"   Position data: entry_price={entry_price}, margin={margin}, position_size={position_size}")
                        except Exception as calc_error:
                            logger.error(f"❌ Error calculating PnL for position {db_position.id}: {str(calc_error)}")

                    updated_positions.append(position_model)

                return updated_positions
        except Exception as e:
            logger.error(f"Error getting positions with current PnL: {str(e)}")
            return []

    async def get_pending_orders(self, user_id: str) -> List[PositionModel]:
        """Получает список отложенных ордеров пользователя"""
        try:
            with get_db_session() as db:
                # Используем прямой SQL запрос для получения отложенных ордеров
                from sqlalchemy import text

                result = db.execute(
                    text("SELECT * FROM positions WHERE user_id = :user_id AND status = 'PENDING'"),
                    {"user_id": user_id},
                ).fetchall()

                # Преобразуем результаты в список позиций
                positions = []
                for row in result:
                    # Создаем объект Position из строки результата
                    position = Position()

                    # Заполняем данные по индексам или именам колонок
                    column_names = row._mapping.keys()
                    for i, name in enumerate(column_names):
                        # Устанавливаем атрибуты объекта position
                        setattr(position, name, row[i])

                    positions.append(position)

                # Если не удалось получить позиции через SQL, пробуем через ORM
                if not positions:
                    try:
                        # Пытаемся получить через ORM запрос
                        positions = (
                            db.query(Position).filter(Position.user_id == user_id, Position.status == "PENDING").all()
                        )
                    except Exception as orm_error:
                        logger.error(f"Ошибка при получении отложенных ордеров через ORM: {str(orm_error)}")

                # Преобразуем в Pydantic модели
                return [self._model_to_pydantic(pos) for pos in positions]
        except Exception as e:
            logger.error(f"Error getting pending orders: {str(e)}")
            return []

    async def cancel_order(self, user_id: str, order_id: int) -> bool:
        """Отменяет отложенный ордер - ВОЗВРАЩАЕТ средства как close_position"""
        try:
            with get_db_session() as db:
                # Проверяем существование ордера
                from sqlalchemy import text

                order_check = db.execute(
                    text("SELECT id, user_id, status FROM positions WHERE id = :id"), {"id": order_id}
                ).fetchone()

                if not order_check:
                    raise ValueError(f"Order {order_id} not found")

                if str(order_check[1]) != user_id:
                    raise ValueError("Order does not belong to this user")

                if order_check[2] != "PENDING":
                    raise ValueError(f"Order {order_id} is not pending (status: {order_check[2]})")

                # Получаем полную информацию об ордере
                cancelled_order = db.query(Position).filter(Position.id == order_id).first()

                if not cancelled_order:
                    raise ValueError(f"Order {order_id} not found in database")

                # Отменяем ордер - меняем статус на CANCELLED
                db.execute(text("UPDATE positions SET status = 'CANCELLED' WHERE id = :id"), {"id": order_id})

                db.flush()

                # 🔥 ВОЗВРАЩАЕМ средства КАК close_position, но return_amount = margin (без расчета PnL)
                quote_currency = cancelled_order.trading_pair.split("/")[1]
                margin_amount = float(cancelled_order.margin)  # Просто возвращаем зарезервированную маржу
                margin_usd = margin_amount * float(cancelled_order.leverage)

                logger.info(f"💰 Возвращаем зарезервированные средства за отмененный ордер {order_id}:")
                logger.info(f"   📍 User: {user_id}")
                logger.info(f"   📍 Return amount: {margin_amount} {quote_currency} (БЕЗ расчета PnL)")
                logger.info(f"   📍 Margin USD: {margin_usd}")

                try:
                    # Сохраняем событие в БД
                    kafka_event = KafkaEventRepository.create_event(
                        db=db,
                        event_type="close_position",  # ТОТ ЖЕ ТИП КАК ДЛЯ ЗАКРЫТИЯ ПОЗИЦИИ
                        payload={
                            "operation_type": "close_position",  # ТА ЖЕ ОПЕРАЦИЯ
                            "user_id": user_id,
                            "position_id": order_id,
                            "return_amount": margin_amount,  # ПРОСТО ВОЗВРАЩАЕМ МАРЖУ
                            "currency": quote_currency,
                            "margin_usd": margin_usd,
                            "pnl_usd": 0,  # При отмене ордера PnL = 0
                            "timestamp": datetime.now().isoformat(),
                        },
                    )

                    # Отправляем КАК close_position
                    from app.kafka.producer import KafkaProducer

                    kafka_result = await KafkaProducer.send_trade_operation(
                        operation_type="close_position",  # СТАНДАРТНАЯ ОПЕРАЦИЯ ЗАКРЫТИЯ
                        user_id=user_id,
                        amount=0,
                        currency=quote_currency,
                        position_id=str(order_id),
                        return_amount=margin_amount,  # ВОЗВРАЩАЕМ ЗАРЕЗЕРВИРОВАННУЮ МАРЖУ
                        margin_usd=margin_usd,
                        pnl_usd=0,  # БЕЗ ПРИБЫЛИ/УБЫТКА
                    )

                    if kafka_result:
                        KafkaEventRepository.update_event_status(db, kafka_event.id, "SENT")
                        logger.info(
                            f"✅ Зарезервированные средства успешно возвращены для отмененного ордера {order_id}"
                        )
                    else:
                        KafkaEventRepository.update_event_status(
                            db, kafka_event.id, "FAILED", "Failed to send to Kafka"
                        )
                        logger.error(f"❌ Не удалось вернуть средства для ордера {order_id}")

                except Exception as e:
                    logger.error(f"❌ Error sending cancel order event: {str(e)}")

                logger.info(f"✅ Cancelled order {order_id} for user {user_id}, зарезервированные средства возвращены")
                return True

        except Exception as e:
            logger.error(f"❌ Error cancelling order: {str(e)}")
            raise ValueError(f"Failed to cancel order: {str(e)}")

    async def get_position(self, position_id: int) -> Optional[PositionModel]:
        """Получает позицию по ID"""
        try:
            with get_db_session() as db:
                db_position = PositionRepository.get_position(db, position_id)
                if db_position:
                    return self._model_to_pydantic(db_position)
                return None
        except Exception as e:
            logger.error(f"Error getting position {position_id}: {str(e)}")
            return None

    async def update_order_entry_price(self, user_id: str, order_id: int, new_entry_price: float) -> PositionModel:
        """
        Обновляет entry_price для лимитного ордера со статусом PENDING

        Args:
            user_id: ID пользователя
            order_id: ID ордера
            new_entry_price: Новая цена входа

        Returns:
            Обновленную позицию/ордер
        """
        try:
            with get_db_session() as db:
                # Получаем ордер
                order = PositionRepository.get_position(db, order_id)
                if not order:
                    raise ValueError(f"Order {order_id} not found")

                # Проверяем принадлежность пользователю
                if order.user_id != user_id:
                    raise ValueError("Order does not belong to this user")

                # Проверяем статус - можно редактировать только PENDING ордера
                if order.status != "PENDING":
                    raise ValueError(
                        f"Cannot update entry_price for order with status {order.status}. Only PENDING orders can be updated."
                    )

                # Валидация новой цены
                if new_entry_price <= 0:
                    raise ValueError("Entry price must be greater than 0")

                # Обновляем entry_price в базе данных
                order.entry_price = new_entry_price
                db.add(order)
                db.commit()

                # Обновляем объект из базы данных чтобы получить актуальные данные
                db.refresh(order)

                logger.info(f"✅ Updated entry_price for order {order_id}: {order.entry_price} → {new_entry_price}")

                # Конвертируем в Pydantic модель
                return self._model_to_pydantic(order)

        except Exception as e:
            logger.error(f"Error updating order entry_price: {str(e)}")
            raise ValueError(f"Failed to update order entry_price: {str(e)}")

    async def update_position_tpsl(
        self, position_id: int, take_profit: Optional[float] = None, stop_loss: Optional[float] = None
    ) -> PositionModel:
        """
        Обновляет значения Take Profit и/или Stop Loss для указанной позиции

        Args:
            position_id: ID позиции
            take_profit: Новое значение Take Profit (None для удаления)
            stop_loss: Новое значение Stop Loss (None для удаления)

        Returns:
            Обновленную позицию
        """
        try:
            with get_db_session() as db:
                # Получаем позицию
                position = PositionRepository.get_position(db, position_id)
                if not position:
                    raise ValueError(f"Position {position_id} not found")

                # Проверяем статус позиции
                if position.status != "OPEN":
                    raise ValueError(f"Position {position_id} is already {position.status}")

                # Обрабатываем TP/SL значения
                from app.core.utils import convert_to_decimal

                # Для take_profit
                logger.info(f"🔍 Processing take_profit={take_profit} (type: {type(take_profit)})")
                if take_profit is None:
                    tp_decimal = None  # Удаляем
                    logger.info(f"🔍 TP set to None - will remove")
                else:
                    tp_decimal = convert_to_decimal(take_profit)  # Обновляем значением
                    logger.info(f"🔍 TP converted to decimal: {tp_decimal}")

                # Для stop_loss
                logger.info(f"🔍 Processing stop_loss={stop_loss} (type: {type(stop_loss)})")
                if stop_loss is None:
                    sl_decimal = None  # Удаляем
                    logger.info(f"🔍 SL set to None - will remove")
                else:
                    sl_decimal = convert_to_decimal(stop_loss)  # Обновляем значением
                    logger.info(f"🔍 SL converted to decimal: {sl_decimal}")

                # Проверяем валидность значений TP/SL (только если они устанавливаются)
                current_price = float(position.current_price)

                # Валидация для LONG позиций
                if position.direction == "LONG":
                    # TP должен быть выше текущей цены
                    if tp_decimal is not None and float(tp_decimal) <= current_price:
                        raise ValueError(
                            f"Take Profit for LONG position must be higher than current price ({current_price})"
                        )
                    # SL должен быть ниже текущей цены и выше цены ликвидации
                    if sl_decimal is not None:
                        if float(sl_decimal) >= current_price:
                            raise ValueError(
                                f"Stop Loss for LONG position must be lower than current price ({current_price})"
                            )
                        if float(sl_decimal) <= float(position.liquidation_price):
                            raise ValueError(
                                f"Stop Loss for LONG position must be higher than liquidation price ({position.liquidation_price})"
                            )

                # Валидация для SHORT позиций
                elif position.direction == "SHORT":
                    # TP должен быть ниже текущей цены
                    if tp_decimal is not None and float(tp_decimal) >= current_price:
                        raise ValueError(
                            f"Take Profit for SHORT position must be lower than current price ({current_price})"
                        )
                    # SL должен быть выше текущей цены и ниже цены ликвидации
                    if sl_decimal is not None:
                        if float(sl_decimal) <= current_price:
                            raise ValueError(
                                f"Stop Loss for SHORT position must be higher than current price ({current_price})"
                            )
                        if float(sl_decimal) >= float(position.liquidation_price):
                            raise ValueError(
                                f"Stop Loss for SHORT position must be lower than liquidation price ({position.liquidation_price})"
                            )

                # Обновляем позицию в базе данных
                updated_position = self._update_position_tpsl_in_db(
                    db=db, position_id=position_id, take_profit=tp_decimal, stop_loss=sl_decimal
                )

                # Конвертируем обратно в Pydantic модель
                return self._model_to_pydantic(updated_position)

        except Exception as e:
            logger.error(f"Error updating position TP/SL: {str(e)}")
            raise ValueError(f"Failed to update position TP/SL: {str(e)}")

    def _update_position_tpsl_in_db(self, db, position_id, take_profit, stop_loss):
        """
        Вспомогательный метод для обновления TP/SL в базе данных
        """
        position = db.query(Position).filter(Position.id == position_id).first()
        if position:
            # Всегда обновляем take_profit
            position.take_profit = take_profit
            if take_profit is None:
                logger.info(f"Удален Take Profit для позиции {position_id}")
            else:
                logger.info(f"Обновлен Take Profit для позиции {position_id}: {take_profit}")

            # Всегда обновляем stop_loss
            position.stop_loss = stop_loss
            if stop_loss is None:
                logger.info(f"Удален Stop Loss для позиции {position_id}")
            else:
                logger.info(f"Обновлен Stop Loss для позиции {position_id}: {stop_loss}")

            db.flush()  # Обновляем объект

        return position

    # 🔥 УДАЛЕНО: update_positions_prices - больше не нужен автоматический PnL
    # 🔥 PnL теперь рассчитывается только по запросу через REST API

    # 🔥 УДАЛЕНО: _send_pnl_updates_to_users - WebSocket PnL обновления убраны

    # 🔥 УДАЛЕНО: _liquidate_position_with_db и связанные методы
    # 🔥 Ликвидация теперь не происходит автоматически

    # 🔥 УДАЛЕНО: _close_position_by_trigger_with_db и связанные методы
    # 🔥 TP/SL не срабатывают автоматически

    # 🔥 ИСПРАВЛЕННЫЙ МЕТОД: Проверка и обработка лимитных ордеров по запросу
    async def check_and_execute_pending_orders(self, current_prices: Dict[str, float]) -> List[PositionModel]:
        """
        Проверяет и исполняет лимитные ордера на основе текущих цен
        🔥 ИСПРАВЛЕНА ПОГРЕШНОСТЬ - ордер активируется ТОЛЬКО при точном пересечении цены

        Args:
            current_prices: Словарь с текущими ценами {trading_pair: price}

        Returns:
            Список исполненных ордеров
        """
        executed_orders = []
        try:
            with get_db_session() as db:
                # Получаем отложенные ордера через прямой SQL запрос
                from sqlalchemy import text

                result = db.execute(text("SELECT * FROM positions WHERE status = 'PENDING'")).fetchall()

                for row in result:
                    try:
                        # Извлекаем нужные данные из результата запроса
                        order_id = row[0]  # id
                        trading_pair = None
                        direction = None
                        entry_price = None
                        user_id = None

                        # Находим индексы нужных колонок
                        column_names = row._mapping.keys()
                        for i, name in enumerate(column_names):
                            if name == "trading_pair":
                                trading_pair = row[i]
                            elif name == "direction":
                                direction = row[i]
                            elif name == "entry_price":
                                entry_price = float(row[i])
                            elif name == "user_id":
                                user_id = row[i]

                        # Если не все данные найдены, получаем позицию через ORM
                        if not all([trading_pair, direction, entry_price, user_id]):
                            logger.warning(f"Не удалось извлечь все данные из строки результата для ордера {order_id}")
                            position = PositionRepository.get_position(db, order_id)
                            if position:
                                trading_pair = position.trading_pair
                                direction = position.direction
                                entry_price = float(position.entry_price)
                                user_id = position.user_id
                            else:
                                logger.error(f"Не удалось получить данные для ордера {order_id}")
                                continue

                        if trading_pair in current_prices:
                            current_price = current_prices[trading_pair]

                            # 🔥 ИСПРАВЛЕННАЯ ЛОГИКА: Абсолютная погрешность вместо процентной
                            execute_order = False

                            # Проверяем пересечение с ОЧЕНЬ МАЛЕНЬКОЙ погрешностью (сотые центов)
                            price_difference = abs(current_price - entry_price)

                            if price_difference <= 0.01:  # Погрешность 1 цент (сотые)
                                execute_order = True
                                logger.info(
                                    f"🚀 Активация ордера {order_id}: цена {current_price} пересекла entry_price {entry_price} (разница: {price_difference})"
                                )
                            else:
                                logger.debug(
                                    f"Ордер {order_id} ждет: current_price={current_price}, entry_price={entry_price}, разница={price_difference}"
                                )

                            if execute_order:
                                # Активируем ордер БЕЗ дополнительного списания средств
                                logger.info(
                                    f"💰 Активация ордера {order_id}: средства уже зарезервированы при создании"
                                )

                                # Активируем ордер - меняем статус на OPEN через SQL
                                db.execute(
                                    text("UPDATE positions SET status = 'OPEN' WHERE id = :id"), {"id": order_id}
                                )

                                # Обновляем цену исполнения
                                db.execute(
                                    text(
                                        "UPDATE positions SET current_price = :price, pnl = 0, return_percentage = 0 WHERE id = :id"
                                    ),
                                    {"id": order_id, "price": current_price},
                                )

                                db.flush()

                                # Получаем обновленную позицию
                                updated_order = db.query(Position).filter(Position.id == order_id).first()

                                if updated_order:
                                    # Добавляем в список исполненных ордеров
                                    executed_model = self._model_to_pydantic(updated_order)
                                    executed_orders.append(executed_model)

                                    logger.info(
                                        f"✅ Лимитный ордер {order_id} исполнен при цене {current_price} (разница была: {price_difference})"
                                    )

                    except Exception as order_error:
                        logger.error(f"❌ Ошибка при обработке ордера {order_id}: {str(order_error)}")
                        # Продолжаем обработку других ордеров
                        continue

        except Exception as e:
            logger.error(f"❌ Error checking pending orders: {str(e)}")

        return executed_orders

    # 🔥 НОВЫЙ МЕТОД: Проверка условий ликвидации по запросу
    async def check_liquidation_conditions(self, current_prices: Dict[str, float]) -> List[PositionModel]:
        """
        Проверяет условия ликвидации для открытых позиций
        Вызывается только по запросу

        Args:
            current_prices: Словарь с текущими ценами {trading_pair: price}

        Returns:
            Список позиций, подлежащих ликвидации
        """
        positions_to_liquidate = []
        try:
            with get_db_session() as db:
                # Получаем открытые позиции
                open_positions = PositionRepository.get_open_positions(db)

                for position in open_positions:
                    trading_pair = position.trading_pair
                    if trading_pair in current_prices:
                        current_price = current_prices[trading_pair]

                        # Проверка на ликвидацию
                        should_liquidate = False
                        if position.direction == "LONG" and current_price <= float(position.liquidation_price):
                            should_liquidate = True
                            logger.warning(
                                f"LONG позиция {position.id} подлежит ликвидации: цена {current_price} <= ликвидационная цена {position.liquidation_price}"
                            )
                        elif position.direction == "SHORT" and current_price >= float(position.liquidation_price):
                            should_liquidate = True
                            logger.warning(
                                f"SHORT позиция {position.id} подлежит ликвидации: цена {current_price} >= ликвидационная цена {position.liquidation_price}"
                            )

                        if should_liquidate:
                            # Ликвидируем позицию
                            liquidated_position = PositionRepository.liquidate_position(db, position.id)

                            # Создаем запись о ликвидации в Kafka
                            try:
                                quote_currency = position.trading_pair.split("/")[1]

                                # Сохраняем событие в БД
                                kafka_event = KafkaEventRepository.create_event(
                                    db=db,
                                    event_type="liquidate_position",
                                    payload={
                                        "operation_type": "liquidate_position",
                                        "user_id": position.user_id,
                                        "position_id": position.id,
                                        "currency": quote_currency,
                                        "timestamp": datetime.now().isoformat(),
                                    },
                                )

                                # Отправляем через Kafka
                                from app.kafka.producer import KafkaProducer

                                kafka_result = await KafkaProducer.send_trade_operation(
                                    operation_type="liquidate_position",
                                    user_id=position.user_id,
                                    amount=0,
                                    currency=quote_currency,
                                    position_id=str(position.id),
                                    return_amount=0,  # При ликвидации не возвращаем средства
                                )

                                if kafka_result:
                                    KafkaEventRepository.update_event_status(db, kafka_event.id, "SENT")
                                else:
                                    KafkaEventRepository.update_event_status(
                                        db, kafka_event.id, "FAILED", "Failed to send to Kafka"
                                    )
                            except Exception as e:
                                logger.error(f"Error sending liquidation event: {str(e)}")

                            positions_to_liquidate.append(self._model_to_pydantic(liquidated_position))
                            logger.info(f"Position {position.id} liquidated for {position.user_id}")

        except Exception as e:
            logger.error(f"Error checking liquidation conditions: {str(e)}")

        return positions_to_liquidate

        # 🔥 НОВЫЙ МЕТОД: Проверка срабатывания TP/SL по запросу

    async def _close_position_by_trigger_manual(self, db, position, trigger_type: str, trigger_price: float):
        """Закрывает позицию по триггеру Stop Loss или Take Profit (ручной вызов)"""
        try:
            # Валидация данных перед расчетом PnL
            entry_price = float(position.entry_price)
            margin = float(position.margin)
            position_size = float(position.position_size)
            
            if entry_price <= 0:
                logger.error(f"❌ Invalid entry_price for position {position.id}: {entry_price}")
                raise ValueError(f"Invalid entry_price for position {position.id}: {entry_price}")
            
            if margin <= 0:
                logger.error(f"❌ Invalid margin for position {position.id}: {margin}")
                raise ValueError(f"Invalid margin for position {position.id}: {margin}")
            
            if position_size <= 0:
                logger.error(f"❌ Invalid position_size for position {position.id}: {position_size}")
                raise ValueError(f"Invalid position_size for position {position.id}: {position_size}")

            # Расчет PnL с защитой от деления на ноль
            if position.direction == "LONG":
                pnl = position_size * ((trigger_price - entry_price) / entry_price)
            else:  # SHORT
                pnl = position_size * ((entry_price - trigger_price) / entry_price)

            # Расчет процента доходности
            return_percentage = (pnl / margin) * 100

            # Закрываем позицию в базе
            db_position = PositionRepository.close_position(
                db=db,
                position_id=position.id,
                current_price=trigger_price,
                pnl=pnl,
                return_percentage=return_percentage,
                close_reason=trigger_type,
            )

            # 🔥 НОВАЯ ЛОГИКА: Подготавливаем данные для Balance Service
            quote_currency = position.trading_pair.split("/")[1]

            # ВАЖНО: margin_usd - это изначально заблокированная маржа (реальный вклад пользователя)
            original_user_investment = float(position.margin)  # Реальный вклад пользователя

            # return_amount = изначальный вклад + PnL - комиссия
            return_amount = original_user_investment + pnl - float(position.fee)

            logger.info(f"📊 TRIGGER CLOSE - Расчет средств для возврата:")
            logger.info(f"   📍 Триггер: {trigger_type} при цене {trigger_price}")
            logger.info(f"   📍 Изначальный вклад пользователя: {original_user_investment} {quote_currency}")
            logger.info(f"   📍 PnL: {pnl} {quote_currency}")
            logger.info(f"   📍 Комиссия: {float(position.fee)} {quote_currency}")
            logger.info(f"   📍 Сумма к возврату: {return_amount} {quote_currency}")

            try:
                # Сохраняем событие в БД
                kafka_event = KafkaEventRepository.create_event(
                    db=db,
                    event_type="close_position",
                    payload={
                        "operation_type": "close_position",
                        "user_id": position.user_id,
                        "position_id": position.id,
                        "margin_usd": original_user_investment,  # 🔥 НОВОЕ: Изначальная маржа для разблокировки
                        "return_amount": return_amount,  # 🔥 НОВОЕ: Сумма к добавлению в fixed_balance
                        "pnl_usd": pnl,  # Для логирования
                        "currency": quote_currency,
                        "close_reason": trigger_type,
                        "timestamp": datetime.now().isoformat(),
                    },
                )

                # Отправляем через Kafka
                from app.kafka.producer import KafkaProducer

                kafka_result = await KafkaProducer.send_trade_operation(
                    operation_type="close_position",
                    user_id=position.user_id,
                    amount=0,
                    currency=quote_currency,
                    position_id=str(position.id),
                    margin_usd=original_user_investment,  # 🔥 НОВОЕ: Маржа для разблокировки
                    return_amount=return_amount,  # 🔥 НОВОЕ: Сумма к возврату
                    pnl_usd=pnl,  # PnL для логирования
                )

                if kafka_result:
                    KafkaEventRepository.update_event_status(db, kafka_event.id, "SENT")
                    logger.info(f"✅ Kafka сообщение {trigger_type} УСПЕШНО отправлено")
                else:
                    KafkaEventRepository.update_event_status(db, kafka_event.id, "FAILED", "Failed to send to Kafka")
                    logger.error(f"❌ Kafka сообщение {trigger_type} НЕ ОТПРАВЛЕНО")
            except Exception as e:
                logger.error(f"Error sending {trigger_type} event: {str(e)}")

            logger.info(f"Position {position.id} closed by {trigger_type} for {position.user_id}")
            return db_position
        except Exception as e:
            logger.error(f"Error closing position by trigger: {str(e)}")
            return None
