# app/core/pnl_updater.py - ИСПРАВЛЕННАЯ ВЕРСИЯ С АВТОМАТИЧЕСКОЙ ЛИКВИДАЦИЕЙ

import asyncio
import logging
from typing import Dict
from datetime import datetime
from app.db.session_manager import get_db_session
from app.db.repository import KafkaEventRepository, PositionRepository

logger = logging.getLogger(__name__)


class PnLUpdater:
    """
    Сервис для автоматического обновления PnL позиций в базе данных
    🔥 НОВОЕ: Добавлена автоматическая ликвидация при достижении ликвидационной цены
    """

    def __init__(self):
        self.running = False
        self.update_task = None
        self.price_service = None

    async def start(self, price_service):
        """Запускает фоновое обновление PnL"""
        self.price_service = price_service
        self.running = True

        # Запускаем фоновую задачу
        self.update_task = asyncio.create_task(self._update_loop())
        logger.info("🔥 PnL Updater started - будет обновлять PnL каждые 5 секунд")

    async def stop(self):
        """Останавливает обновление PnL"""
        self.running = False
        if self.update_task:
            self.update_task.cancel()
            try:
                await self.update_task
            except asyncio.CancelledError:
                pass
        logger.info("🛑 PnL Updater stopped")

    async def _update_loop(self):
        """Основной цикл обновления PnL"""
        while self.running:
            try:
                # 🔥 НОВОЕ: Проверяем и активируем отложенные ордера
                await self._check_and_activate_pending_orders()

                # 🔥 ОБНОВЛЯЕМ PnL И ПРОВЕРЯЕМ ЛИКВИДАЦИЮ
                await self._update_all_positions_pnl_with_liquidation()
                await asyncio.sleep(5)  # 5 секунд между обновлениями

            except asyncio.CancelledError:
                logger.info("🛑 PnL update loop cancelled")
                break
            except Exception as e:
                logger.error(f"❌ Error in PnL update loop: {str(e)}")
                await asyncio.sleep(10)  # При ошибке ждем дольше

    async def _check_and_activate_pending_orders(self):
        """🔥 ПРАВИЛЬНАЯ ЛОГИКА: Активация ордеров ТОЛЬКО при пересечении цены входа"""
        try:
            # Проверяем что price service подключен
            if not self.price_service or not self.price_service.is_connected:
                logger.debug("Price service not connected, skipping pending orders check")
                return

            # Получаем актуальные цены
            current_prices = self.price_service.get_all_prices()
            if not current_prices:
                logger.debug("No current prices available, skipping pending orders check")
                return

            activated_count = 0

            with get_db_session() as db:
                # Получаем отложенные ордера через SQL
                from sqlalchemy import text

                result = db.execute(text("SELECT * FROM positions WHERE status = 'PENDING'")).fetchall()

                for row in result:
                    try:
                        # Извлекаем данные из результата
                        order_id = row[0]  # id
                        trading_pair = None
                        direction = None
                        entry_price = None
                        user_id = None

                        # Находим нужные колонки
                        column_names = row._mapping.keys()
                        for i, name in enumerate(column_names):
                            if name == "trading_pair":
                                trading_pair = row[i]
                            elif name == "direction":
                                direction = row[i]
                            elif name == "entry_price":
                                entry_price = float(row[i])
                            elif name == "user_id":
                                user_id = row[i]

                        if trading_pair in current_prices:
                            current_price = current_prices[trading_pair]

                            # 🔥 ИСПРАВЛЕННАЯ ЛОГИКА: Абсолютная погрешность в сотые центов
                            should_activate = False

                            # Проверяем пересечение с ОЧЕНЬ МАЛЕНЬКОЙ погрешностью (сотые центов)
                            price_difference = abs(current_price - entry_price)

                            if price_difference <= 0.01:  # Погрешность 1 цент (сотые)
                                should_activate = True
                                logger.info(
                                    f"🚀 Активация ордера {order_id}: цена {current_price} пересекла entry_price {entry_price} (разница: {price_difference})"
                                )
                            else:
                                logger.debug(
                                    f"Ордер {order_id} ждет: current_price={current_price}, entry_price={entry_price}, разница={price_difference}"
                                )

                            if should_activate:
                                # 🔥 ПРОСТО меняем статус БЕЗ отправки в Kafka
                                # Средства уже списаны при создании ордера через open_position
                                db.execute(
                                    text("UPDATE positions SET status = 'OPEN', current_price = :price WHERE id = :id"),
                                    {"id": order_id, "price": current_price},
                                )

                                # 🔥 ОТПРАВЛЯЕМ WEBSOCKET СОБЫТИЕ АКТИВАЦИИ ЛИМИТНОГО ОРДЕРА
                                try:
                                    from app.api.websocket.handlers import position_events_handler
                                    from app.api.websocket.connection_manager import ConnectionManager

                                    connection_manager = ConnectionManager()

                                    limit_order_data = {
                                        "position_id": order_id,
                                        "trading_pair": trading_pair,
                                        "direction": direction,
                                        "entry_price": entry_price,
                                        "current_price": current_price,
                                        "position_size": 0,  # Получим из БД если нужно
                                        "margin": 0,  # Получим из БД если нужно
                                        "leverage": 0  # Получим из БД если нужно
                                    }

                                    await position_events_handler.send_limit_order_filled(
                                        connection_manager, user_id, limit_order_data
                                    )

                                except Exception as websocket_error:
                                    logger.error(f"❌ Ошибка отправки WebSocket события активации ордера: {str(websocket_error)}")

                                activated_count += 1
                                logger.info(
                                    f"✅ Ордер {order_id} активирован при цене {current_price} (средства уже списаны при создании)"
                                )

                    except Exception as order_error:
                        logger.error(f"❌ Ошибка при проверке ордера {order_id}: {str(order_error)}")
                        continue

            if activated_count > 0:
                logger.info(
                    f"🚀 Активировано {activated_count} отложенных ордеров (без дополнительного списания средств)"
                )

        except Exception as e:
            logger.error(f"❌ Error checking pending orders: {str(e)}")

    async def _update_all_positions_pnl_with_liquidation(self):
        """
        🔥 НОВЫЙ МЕТОД: Обновляет PnL для всех открытых позиций И проверяет ликвидацию
        """
        try:
            # Проверяем что price service подключен
            if not self.price_service or not self.price_service.is_connected:
                logger.debug("Price service not connected, skipping PnL update")
                return

            # Получаем актуальные цены
            current_prices = self.price_service.get_all_prices()
            if not current_prices:
                logger.debug("No current prices available, skipping PnL update")
                return

            # Обновляем PnL в базе данных
            updated_count = 0
            liquidated_count = 0
            error_count = 0

            with get_db_session() as db:
                # Получаем все открытые позиции
                open_positions = PositionRepository.get_open_positions(db)

                for position in open_positions:
                    try:
                        trading_pair = position.trading_pair

                        if trading_pair in current_prices:
                            current_price = current_prices[trading_pair]

                            # 🔥 ПРОВЕРЯЕМ данные позиции перед расчетом
                            entry_price = float(position.entry_price) if position.entry_price else 0
                            position_size = float(position.position_size) if position.position_size else 0
                            margin = float(position.margin) if position.margin else 0
                            liquidation_price = float(position.liquidation_price) if position.liquidation_price else 0

                            # 🔥 ЗАЩИТА от деления на ноль и некорректных данных
                            if entry_price <= 0:
                                logger.warning(
                                    f"⚠️ Position {position.id} has invalid entry_price: {entry_price} - SKIPPING"
                                )
                                error_count += 1
                                continue

                            if position_size <= 0:
                                logger.warning(
                                    f"⚠️ Position {position.id} has invalid position_size: {position_size} - SKIPPING"
                                )
                                error_count += 1
                                continue

                            if margin <= 0:
                                logger.warning(f"⚠️ Position {position.id} has invalid margin: {margin} - SKIPPING")
                                error_count += 1
                                continue

                            if current_price <= 0:
                                logger.warning(
                                    f"⚠️ Position {position.id} has invalid current_price: {current_price} - SKIPPING"
                                )
                                error_count += 1
                                continue

                            if liquidation_price <= 0:
                                logger.warning(
                                    f"⚠️ Position {position.id} has invalid liquidation_price: {liquidation_price} - SKIPPING"
                                )
                                error_count += 1
                                continue

                            # 🔥 ПРОВЕРКА ЛИКВИДАЦИИ ПЕРЕД ОБНОВЛЕНИЕМ PnL
                            should_liquidate = False
                            liquidation_reason = ""

                            if position.direction == "LONG":
                                if current_price <= liquidation_price:
                                    should_liquidate = True
                                    liquidation_reason = (
                                        f"LONG позиция: цена {current_price} <= ликвидационная цена {liquidation_price}"
                                    )
                            elif position.direction == "SHORT":
                                if current_price >= liquidation_price:
                                    should_liquidate = True
                                    liquidation_reason = f"SHORT позиция: цена {current_price} >= ликвидационная цена {liquidation_price}"

                            # 🔥 ЛИКВИДАЦИЯ ПОЗИЦИИ
                            if should_liquidate:
                                logger.warning(f"💥 ЛИКВИДАЦИЯ позиции {position.id}: {liquidation_reason}")

                                try:
                                    # Закрываем позицию со статусом LIQUIDATED
                                    liquidated_position = PositionRepository.liquidate_position(db, position.id)

                                    # 🔥 ОТПРАВЛЯЕМ WEBSOCKET СОБЫТИЕ ЛИКВИДАЦИИ
                                    try:
                                        from app.api.websocket.handlers import position_events_handler
                                        from app.api.websocket.connection_manager import ConnectionManager

                                        connection_manager = ConnectionManager()

                                        liquidation_data = {
                                            "position_id": position.id,
                                            "trading_pair": position.trading_pair,
                                            "direction": position.direction,
                                            "liquidation_price": liquidation_price,
                                            "current_price": current_price,
                                            "pnl": -margin,  # При ликвидации PnL = -100% от маржи
                                            "margin": margin
                                        }

                                        await position_events_handler.send_liquidation_event(
                                            connection_manager, position.user_id, liquidation_data
                                        )

                                    except Exception as websocket_error:
                                        logger.error(f"❌ Ошибка отправки WebSocket события ликвидации: {str(websocket_error)}")

                                    # Создаем событие для Kafka (НЕ возвращаем средства при ликвидации)
                                    quote_currency = position.trading_pair.split("/")[1]
                                    margin_usd = margin * float(position.leverage)

                                    # При ликвидации PnL = -100% от маржи (полная потеря)
                                    liquidation_pnl = -margin

                                    kafka_event = KafkaEventRepository.create_event(
                                        db=db,
                                        event_type="liquidate_position",
                                        payload={
                                            "operation_type": "liquidate_position",
                                            "user_id": position.user_id,
                                            "position_id": position.id,
                                            "currency": quote_currency,
                                            "margin_usd": margin_usd,
                                            "pnl_usd": liquidation_pnl,  # Убыток при ликвидации
                                            "liquidation_price": liquidation_price,
                                            "current_price": current_price,
                                            "timestamp": datetime.now().isoformat(),
                                        },
                                    )

                                    # Пытаемся отправить через Kafka
                                    try:
                                        from app.kafka.producer import KafkaProducer

                                        kafka_result = await KafkaProducer.send_trade_operation(
                                            operation_type="liquidate_position",
                                            user_id=position.user_id,
                                            amount=0,
                                            currency=quote_currency,
                                            position_id=str(position.id),
                                            return_amount=0,  # При ликвидации НЕ возвращаем средства
                                            margin_usd=margin_usd,
                                            pnl_usd=liquidation_pnl,
                                        )

                                        if kafka_result:
                                            KafkaEventRepository.update_event_status(db, kafka_event.id, "SENT")
                                            logger.info(
                                                f"✅ Kafka событие ликвидации отправлено для позиции {position.id}"
                                            )
                                        else:
                                            KafkaEventRepository.update_event_status(
                                                db, kafka_event.id, "FAILED", "Failed to send to Kafka"
                                            )
                                            logger.error(
                                                f"❌ Не удалось отправить событие ликвидации для позиции {position.id}"
                                            )
                                    except ImportError:
                                        logger.error("❌ Модуль Kafka Producer недоступен для ликвидации")
                                        KafkaEventRepository.update_event_status(
                                            db, kafka_event.id, "FAILED", "Kafka Producer module not available"
                                        )
                                    except Exception as kafka_error:
                                        logger.error(f"❌ Ошибка Kafka при ликвидации: {str(kafka_error)}")
                                        KafkaEventRepository.update_event_status(
                                            db, kafka_event.id, "FAILED", str(kafka_error)
                                        )

                                    liquidated_count += 1
                                    logger.info(
                                        f"💥 Позиция {position.id} ЛИКВИДИРОВАНА пользователя {position.user_id}"
                                    )

                                    # Переходим к следующей позиции (не обновляем PnL для ликвидированной)
                                    continue

                                except Exception as liquidation_error:
                                    logger.error(
                                        f"❌ Ошибка при ликвидации позиции {position.id}: {str(liquidation_error)}"
                                    )
                                    error_count += 1
                                    # Продолжаем обработку (попробуем хотя бы обновить PnL)

                            # 🔥 ОБЫЧНОЕ ОБНОВЛЕНИЕ PnL (если позиция НЕ ликвидирована)
                            try:
                                if position.direction == "LONG":
                                    price_change_ratio = (current_price - entry_price) / entry_price
                                    pnl = position_size * price_change_ratio
                                else:  # SHORT
                                    price_change_ratio = (entry_price - current_price) / entry_price
                                    pnl = position_size * price_change_ratio

                                # Рассчитываем процент доходности
                                return_percentage = (pnl / margin) * 100

                                # 🔥 ПРОВЕРЯЕМ результат расчета
                                if not (isinstance(pnl, (int, float)) and isinstance(return_percentage, (int, float))):
                                    logger.warning(
                                        f"⚠️ Position {position.id} calculation resulted in invalid values: pnl={pnl}, return_percentage={return_percentage}"
                                    )
                                    error_count += 1
                                    continue

                                # Проверяем, изменился ли PnL значительно (больше чем на 0.01)
                                current_pnl = float(position.pnl) if position.pnl else 0
                                current_price_db = float(position.current_price) if position.current_price else 0

                                # 🔥 ПРОВЕРЯЕМ ПРЕДУПРЕЖДЕНИЕ О БЛИЗКОЙ ЛИКВИДАЦИИ (5% до ликвидационной цены)
                                distance_to_liquidation = abs(current_price - liquidation_price) / liquidation_price
                                if distance_to_liquidation <= 0.05:  # 5% до ликвидации
                                    try:
                                        from app.api.websocket.handlers import position_events_handler
                                        from app.api.websocket.connection_manager import ConnectionManager

                                        connection_manager = ConnectionManager()

                                        warning_data = {
                                            "position_id": position.id,
                                            "trading_pair": trading_pair,
                                            "direction": position.direction,
                                            "current_price": current_price,
                                            "liquidation_price": liquidation_price,
                                            "distance_percent": round(distance_to_liquidation * 100, 2),
                                            "pnl": pnl
                                        }

                                        await position_events_handler.send_near_liquidation_warning(
                                            connection_manager, position.user_id, warning_data
                                        )

                                    except Exception as websocket_error:
                                        logger.error(f"❌ Ошибка отправки предупреждения о ликвидации: {str(websocket_error)}")

                                if abs(current_pnl - pnl) > 0.01 or abs(current_price_db - current_price) > 0.01:
                                    # Обновляем позицию в базе данных
                                    PositionRepository.update_position_price(
                                        db=db,
                                        position_id=position.id,
                                        current_price=current_price,
                                        pnl=pnl,
                                        return_percentage=return_percentage,
                                    )
                                    updated_count += 1

                                    logger.debug(
                                        f"💰 Updated PnL for position {position.id} ({trading_pair}): {pnl:.2f} ({return_percentage:.2f}%)"
                                    )

                            except ZeroDivisionError as div_error:
                                logger.error(
                                    f"❌ Division by zero for position {position.id}: entry_price={entry_price}, margin={margin}"
                                )
                                error_count += 1
                                continue
                            except (ValueError, TypeError, OverflowError) as calc_error:
                                logger.error(f"❌ Calculation error for position {position.id}: {str(calc_error)}")
                                error_count += 1
                                continue

                    except Exception as pos_error:
                        logger.error(f"❌ Error processing position {position.id}: {str(pos_error)}")
                        error_count += 1
                        continue

                # Логируем результат
                if updated_count > 0:
                    logger.info(f"💰 PnL updated for {updated_count} positions in database")
                if liquidated_count > 0:
                    logger.warning(f"💥 ЛИКВИДИРОВАНО {liquidated_count} позиций")
                if error_count > 0:
                    logger.warning(f"⚠️ Skipped {error_count} positions due to data errors")

        except Exception as e:
            logger.error(f"❌ Error updating positions PnL with liquidation: {str(e)}")
            import traceback

            logger.error(f"❌ Traceback: {traceback.format_exc()}")


# Глобальный экземпляр
pnl_updater = PnLUpdater()
