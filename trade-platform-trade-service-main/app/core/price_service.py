import websocket
import json
import threading
import time
import logging
from typing import Dict, Callable, List, Optional
import asyncio
import queue

logger = logging.getLogger(__name__)


class PriceService:
    def __init__(self, binance_ws_url: str = "wss://stream.binance.com:9443/ws"):
        self.binance_ws_url = binance_ws_url
        self.ws = None
        self.connected = False
        self.prices = {}  # Кэш текущих цен
        self.callbacks = []  # Коллбэки для обновления цен
        self.subscribed_pairs = []  # Список подписанных пар
        self.connect_thread = None
        self._stop_event = threading.Event()
        self._price_updates_queue = queue.Queue()
        self._process_updates_task = None

    async def start(self):
        """
        Асинхронный метод для запуска соединения - ИСПРАВЛЕННАЯ ВЕРСИЯ
        """
        logger.info("Запуск сервиса цен...")
        self._stop_event.clear()

        # Запускаем соединение в отдельном потоке
        self.connect_thread = threading.Thread(target=self._connect_websocket)
        self.connect_thread.daemon = True
        self.connect_thread.start()

        # Запускаем задачу обработки обновлений цен
        self._process_updates_task = asyncio.create_task(self._process_price_updates())

        # 🔥 ИСПРАВЛЕНИЕ: Ждем КОРОТКОЕ время, не блокируем startup
        for attempt in range(3):  # Максимум 3 секунды вместо 10
            if self.connected:
                logger.info("Соединение с Binance WebSocket установлено")
                return
            await asyncio.sleep(1)

        # 🔥 ИСПРАВЛЕНИЕ: Не падаем, если не подключились сразу - работаем в фоне
        logger.warning("WebSocket подключение займет больше времени, продолжаем в фоне...")

    async def stop(self):
        """Останавливает сервис цен"""
        logger.info("Остановка сервиса цен...")
        self._stop_event.set()

        # Останавливаем WebSocket
        if self.ws:
            self.ws.close()

        # Отменяем задачу обработки обновлений цен
        if self._process_updates_task:
            self._process_updates_task.cancel()
            try:
                await self._process_updates_task
            except asyncio.CancelledError:
                pass

        # Ожидаем завершения потока
        if self.connect_thread and self.connect_thread.is_alive():
            self.connect_thread.join(timeout=5)

        self.connected = False
        logger.info("Сервис цен остановлен")

    async def _process_price_updates(self):
        """
        Асинхронная задача для обработки обновлений цен из очереди - ИСПРАВЛЕННАЯ ВЕРСИЯ
        """
        logger.info("Запущена задача обработки обновлений цен")
        while not self._stop_event.is_set():
            try:
                # Проверяем очередь на наличие обновлений, не блокируя основной поток
                try:
                    prices = self._price_updates_queue.get_nowait()

                    # Вызываем все обратные вызовы с обновленными ценами
                    for callback in self.callbacks:
                        try:
                            # 🔥 ИСПРАВЛЕНИЕ: Проверяем что callback это coroutine
                            if asyncio.iscoroutinefunction(callback):
                                await callback(prices)
                            else:
                                callback(prices)
                        except Exception as e:
                            logger.error(f"Ошибка в обработчике обновления цен: {str(e)}")

                    self._price_updates_queue.task_done()
                except queue.Empty:
                    # Если очередь пуста, просто ждем
                    await asyncio.sleep(0.1)
            except asyncio.CancelledError:
                logger.info("Задача обработки обновлений цен отменена")
                break
            except Exception as e:
                logger.error(f"Ошибка при обработке обновлений цен: {str(e)}")
                await asyncio.sleep(1)  # Пауза при ошибке

    def _start_connection(self):
        """Запускает соединение в отдельном потоке"""
        if not self._stop_event.is_set():
            self.connect_thread = threading.Thread(target=self._connect_websocket)
            self.connect_thread.daemon = True
            self.connect_thread.start()

    def _connect_websocket(self):
        """Подключается к WebSocket Binance - УЛУЧШЕННАЯ ВЕРСИЯ"""
        retry_count = 0
        max_retries = 10

        while not self._stop_event.is_set() and retry_count < max_retries:
            try:
                logger.info(f"🔄 Попытка подключения к Binance WebSocket #{retry_count + 1}")

                # Вместо стандартного клиента websocket используем библиотеку websocket-client
                websocket.enableTrace(False)
                self.ws = websocket.WebSocketApp(
                    self.binance_ws_url,
                    on_message=self._on_message,
                    on_error=self._on_error,
                    on_close=self._on_close,
                    on_open=self._on_open,
                )

                # Запускаем WebSocket клиент
                self.ws.run_forever(
                    ping_interval=30,  # Ping каждые 30 секунд
                    ping_timeout=10,  # Таймаут ping 10 секунд
                    reconnect=5,  # Интервал переподключения
                )

            except Exception as e:
                retry_count += 1
                logger.error(f"❌ WebSocket connection error (attempt {retry_count}): {str(e)}")
                self.connected = False

                if retry_count < max_retries and not self._stop_event.is_set():
                    # Exponential backoff
                    wait_time = min(2**retry_count, 60)
                    logger.info(f"🔄 Retrying WebSocket in {wait_time} seconds...")
                    time.sleep(wait_time)
                else:
                    logger.error("❌ Max WebSocket retries reached, giving up")
                    break

        self.connected = False
        logger.info("🛑 WebSocket connection thread finished")

    def _on_open(self, ws):
        """Callback при открытии соединения"""
        logger.info("Connected to Binance WebSocket")
        self.connected = True

        # Подписываемся на изменения цен
        self._subscribe_to_pairs()

    def _on_message(self, ws, message):
        """Callback при получении сообщения"""
        try:
            data = json.loads(message)

            # Обрабатываем сообщения с изменением цены
            if "e" in data and data["e"] == "24hrTicker":
                symbol = data["s"]  # Например, "BTCUSDT"
                price = float(data["c"])  # Текущая цена закрытия

                # Преобразуем формат символа в наш формат (BTC/USDT)
                traditional_symbol = self._convert_symbol_format(symbol)

                # Обновляем кэш цен
                old_price = self.prices.get(traditional_symbol)
                if old_price != price:
                    logger.debug(f"Обновление цены {traditional_symbol}: {old_price} -> {price}")
                    self.prices[traditional_symbol] = price

                    # 🔥 ИСПРАВЛЕНИЕ: Добавляем обновленные цены в очередь безопасно
                    try:
                        self._price_updates_queue.put_nowait(self.prices.copy())
                    except queue.Full:
                        logger.warning("Price updates queue is full, skipping update")

            # Обрабатываем ответ на подписку
            elif "result" in data and data["result"] is None:
                logger.info("Successfully subscribed to price updates")

        except Exception as e:
            logger.error(f"Error processing WebSocket message: {str(e)}")

    def _on_error(self, ws, error):
        """Callback при ошибке"""
        logger.error(f"WebSocket error: {str(error)}")

    def _on_close(self, ws, close_status_code, close_msg):
        """Callback при закрытии соединения - УЛУЧШЕННАЯ ВЕРСИЯ"""
        logger.info(f"WebSocket connection closed: {close_status_code} {close_msg}")
        self.connected = False

        # 🔥 ИСПРАВЛЕНИЕ: Переподключаемся только если сервис не останавливается
        if not self._stop_event.is_set():
            logger.info("🔄 Attempting to reconnect WebSocket...")
            time.sleep(5)
            self._start_connection()
        else:
            logger.info("🛑 WebSocket service is stopping, not reconnecting")

    def _subscribe_to_pairs(self):
        """Подписывается на обновления цен для выбранных пар"""
        if not self.connected or not self.ws:
            logger.warning("Cannot subscribe: WebSocket not connected")
            return

        # Используем список пар из настроек
        try:
            from app.config.settings import settings

            default_pairs = settings.SUPPORTED_PAIRS
        except (ImportError, AttributeError):
            # Если не удалось получить из настроек, используем стандартные
            default_pairs = ["BTC/USDT", "ETH/USDT", "SOL/USDT", "XRP/USDT", "ADA/USDT"]

        # Формируем запрос на подписку
        binance_symbols = [self._convert_to_binance_format(pair) for pair in default_pairs]
        streams = [f"{symbol.lower()}@ticker" for symbol in binance_symbols]

        subscription = {"method": "SUBSCRIBE", "params": streams, "id": 1}

        logger.info(f"Подписка на потоки цен: {streams}")

        # 🔥 ИСПРАВЛЕНИЕ: Отправляем запрос безопасно
        try:
            self.ws.send(json.dumps(subscription))
            self.subscribed_pairs = default_pairs
        except Exception as e:
            logger.error(f"❌ Failed to send subscription: {str(e)}")

    def _convert_to_binance_format(self, traditional_symbol: str) -> str:
        """Преобразует символ из формата BTC/USDT в формат BTCUSDT"""
        return traditional_symbol.replace("/", "")

    def _convert_symbol_format(self, binance_symbol: str) -> str:
        """Преобразует символ из формата BTCUSDT в формат BTC/USDT"""
        # Находим место, где заканчивается базовая валюта и начинается котировочная
        # Для большинства случаев это будет "USDT"
        if binance_symbol.endswith("USDT"):
            base = binance_symbol[:-4]
            quote = "USDT"
        elif binance_symbol.endswith("USDC"):
            base = binance_symbol[:-4]
            quote = "USDC"
        elif binance_symbol.endswith("BUSD"):
            base = binance_symbol[:-4]
            quote = "BUSD"
        elif binance_symbol.endswith("BTC"):
            base = binance_symbol[:-3]
            quote = "BTC"
        elif binance_symbol.endswith("ETH"):
            base = binance_symbol[:-3]
            quote = "ETH"
        else:
            # Для других случаев просто разделяем по середине
            mid = len(binance_symbol) // 2
            base = binance_symbol[:mid]
            quote = binance_symbol[mid:]

        return f"{base}/{quote}"

    def get_price(self, symbol: str) -> Optional[float]:
        """Получает текущую цену для указанного символа"""
        return self.prices.get(symbol)

    def get_all_prices(self) -> Dict[str, float]:
        """Получает все текущие цены"""
        return self.prices.copy()

    def subscribe_to_price_updates(self, callback: Callable[[Dict[str, float]], None]):
        """Подписывается на обновления цен - ИСПРАВЛЕННАЯ ВЕРСИЯ"""
        if callback not in self.callbacks:
            self.callbacks.append(callback)

        # 🔥 ИСПРАВЛЕНИЕ: Сразу отправляем текущие цены, если они есть (безопасно)
        if self.prices:
            try:
                # Проверяем что callback это coroutine
                if asyncio.iscoroutinefunction(callback):
                    # Создаем задачу только если есть running event loop
                    try:
                        loop = asyncio.get_running_loop()
                        asyncio.create_task(callback(self.prices.copy()))
                    except RuntimeError:
                        # Нет running loop - ничего не делаем
                        pass
                else:
                    # Обычная функция - вызываем сразу
                    callback(self.prices.copy())
            except Exception as e:
                logger.error(f"❌ Error calling initial price callback: {str(e)}")

    def unsubscribe_from_price_updates(self, callback: Callable[[Dict[str, float]], None]):
        """Отписывается от обновлений цен"""
        if callback in self.callbacks:
            self.callbacks.remove(callback)

    @property
    def is_connected(self) -> bool:
        """Возвращает статус подключения"""
        return self.connected
