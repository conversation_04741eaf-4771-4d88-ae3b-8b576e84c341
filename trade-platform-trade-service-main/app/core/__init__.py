# Экспортируем сервис цен для возможности импорта из других модулей
# Создаем глобальный объект для доступа из других модулей
price_service = None


def get_price_service():
    """
    Получает глобальный экземпляр PriceService
    """
    global price_service
    return price_service


def set_price_service(service):
    """
    Устанавливает глобальный экземпляр PriceService
    """
    global price_service
    price_service = service
