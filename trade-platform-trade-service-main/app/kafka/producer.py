import json
from typing import Dict, Any
import logging
from datetime import datetime
from aiokafka import AIOKafkaProducer
from app.config.settings import Settings

logger = logging.getLogger(__name__)
settings = Settings()


class KafkaProducer:
    """Класс для отправки событий в Kafka"""

    _producer = None

    @classmethod
    async def get_producer(cls):
        """Получение экземпляра Kafka producer с ленивой инициализацией"""
        if cls._producer is None:
            try:
                cls._producer = AIOKafkaProducer(
                    bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                    value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                    key_serializer=lambda k: k.encode("utf-8"),  # Properly encode string keys
                )
                await cls._producer.start()
            except Exception as e:
                logger.error(f"Error starting Kafka producer: {str(e)}")
                raise
        return cls._producer

    @classmethod
    async def close(cls):
        """Закрытие Kafka producer"""
        if cls._producer is not None:
            await cls._producer.stop()
            cls._producer = None

    @classmethod
    async def send_trade_operation(
        cls,
        operation_type: str,
        user_id: str,
        amount: float,
        currency: str,
        position_id: str = None,
        return_amount: float = 0,
        margin_usd: float = 0,
        pnl_usd: float = 0,
    ) -> bool:
        """
        Отправляет информацию о торговой операции в Kafka

        Args:
            operation_type: Тип операции (open_position, close_position, cancel_order)
            user_id: ID пользователя
            amount: Сумма операции
            currency: Валюта
            position_id: ID позиции/ордера (опционально)
            return_amount: Сумма к возврату (для close_position)
            margin_usd: Маржа в USD
            pnl_usd: Прибыль/убыток в USD

        Returns:
            bool: True, если сообщение отправлено успешно
        """
        try:
            # Подготавливаем данные сообщения
            message = {
                "event_type": "trade_operation",
                "operation_type": operation_type,
                "user_id": user_id,
                "amount": amount,
                "currency": currency,
                "timestamp": datetime.now().isoformat(),
            }

            # Добавляем опциональные поля
            if position_id:
                message["position_id"] = position_id

            if return_amount:
                message["return_amount"] = return_amount

            if margin_usd:
                message["margin_usd"] = margin_usd

            if pnl_usd:
                message["pnl_usd"] = pnl_usd

            # Отправляем сообщение в Kafka
            producer = await cls.get_producer()

            # Make sure user_id is a string
            key = str(user_id)

            await producer.send_and_wait(topic=settings.KAFKA_BALANCE_TOPIC, key=key, value=message)

            return True
        except Exception as e:
            logger.error(f"Error sending trade operation to Kafka: {str(e)}")
            return False
