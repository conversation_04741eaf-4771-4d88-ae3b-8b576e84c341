import asyncio
import json
import logging
from typing import Dict, Any, Optional
from aiokafka import AIOKafkaConsumer
from datetime import datetime, date
from calendar import monthrange
from decimal import Decimal
from sqlalchemy import func, and_, extract
from app.db.session_manager import get_db_session
from app.db.models import Position
from app.db.repository import PositionRepository
from app.config.settings import Settings
from app.kafka.producer import KafkaProducer

logger = logging.getLogger(__name__)
settings = Settings()


class TradeStatsConsumer:
    """Класс для обработки запросов статистики по торговым операциям из реферального сервиса"""

    _consumer = None

    @classmethod
    async def get_consumer(cls):
        """Получение экземпляра Kafka consumer с ленивой инициализацией"""
        if cls._consumer is None:
            try:
                cls._consumer = AIOKafkaConsumer(
                    "trade.request.stats",  # Слушаем топик запросов на статистику
                    bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
                    group_id="trade-service-stats",
                    value_deserializer=lambda m: json.loads(m.decode("utf-8")),
                    key_deserializer=lambda m: m.decode("utf-8") if m else None,
                    auto_offset_reset="latest",
                )
                await cls._consumer.start()
                logger.info("Kafka consumer для статистики торгов запущен")
            except Exception as e:
                logger.error(f"Ошибка запуска Kafka consumer: {str(e)}")
                raise
        return cls._consumer

    @classmethod
    async def close(cls):
        """Закрытие Kafka consumer"""
        if cls._consumer is not None:
            await cls._consumer.stop()
            cls._consumer = None
            logger.info("Kafka consumer для статистики торгов остановлен")

    @classmethod
    async def start_listening(cls):
        """Начать прослушивание запросов на статистику - ИСПРАВЛЕННАЯ ВЕРСИЯ"""
        consumer = await cls.get_consumer()

        logger.info("🎧 Kafka consumer started listening for stats requests...")

        try:
            while True:
                try:
                    # 🔥 ИСПРАВЛЕНИЕ: Получаем сообщения с таймаутом вместо бесконечного цикла
                    message_batch = await asyncio.wait_for(
                        consumer.getmany(timeout_ms=1000, max_records=10), timeout=5.0
                    )

                    # Обрабатываем полученные сообщения
                    if message_batch:
                        for topic_partition, messages in message_batch.items():
                            for message in messages:
                                try:
                                    # Получаем данные из сообщения
                                    key = message.key
                                    value = message.value

                                    logger.info(f"📨 Получен запрос на статистику: {value}")

                                    # Обрабатываем запрос
                                    response = await cls.process_stats_request(value)

                                    # Отправляем ответ
                                    await cls.send_stats_response(value.get("userId"), response)

                                except Exception as e:
                                    logger.error(f"❌ Ошибка обработки сообщения: {str(e)}")
                                    # Продолжаем обработку других сообщений
                                    continue

                    # Если нет сообщений, просто продолжаем
                    else:
                        # Короткая пауза если нет сообщений
                        await asyncio.sleep(0.1)

                except asyncio.TimeoutError:
                    # Таймаут - это нормально, просто нет новых сообщений
                    logger.debug("🔍 No new messages, continuing...")
                    continue

                except Exception as e:
                    logger.error(f"❌ Ошибка в цикле прослушивания Kafka: {str(e)}")
                    # При ошибке ждем немного перед повтором
                    await asyncio.sleep(5)

                    # Пробуем переподключиться к consumer
                    try:
                        await cls.close()
                        consumer = await cls.get_consumer()
                        logger.info("🔄 Kafka consumer reconnected")
                    except Exception as reconnect_error:
                        logger.error(f"❌ Failed to reconnect consumer: {str(reconnect_error)}")
                        await asyncio.sleep(10)

        except asyncio.CancelledError:
            logger.info("🛑 Kafka consumer listening cancelled")
            raise
        except Exception as e:
            logger.error(f"❌ Fatal error in Kafka consumer: {str(e)}")
            raise
        finally:
            try:
                await cls.close()
                logger.info("✅ Kafka consumer closed")
            except Exception as e:
                logger.error(f"❌ Error closing consumer: {str(e)}")

    @staticmethod
    async def process_stats_request(request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Обрабатывает запрос на получение статистики торговли

        Args:
            request_data: Данные запроса (userId, year, month)

        Returns:
            Dict с рассчитанной статистикой
        """
        user_id = request_data.get("userId")
        year = request_data.get("year")
        month = request_data.get("month")

        if not all([user_id, year, month]):
            logger.error("Неполные данные запроса")
            return {
                "userId": user_id,
                "year": year,
                "month": month,
                "tradingVolume": 0,
                "ordersCount": 0,
                "commissionAmount": 0,
                "error": "Неполные данные запроса",
            }

        try:
            # Определяем начало и конец месяца
            start_date = datetime(year, month, 1)
            _, last_day = monthrange(year, month)
            end_date = datetime(year, month, last_day, 23, 59, 59)

            with get_db_session() as db:
                # Запрос для подсчета объема торговли
                positions = (
                    db.query(Position)
                    .filter(
                        and_(
                            Position.user_id == str(user_id),
                            Position.opened_at >= start_date,
                            Position.opened_at <= end_date,
                        )
                    )
                    .all()
                )

                # Рассчитываем статистику
                trading_volume = sum(float(p.position_size) for p in positions) if positions else 0
                orders_count = len(positions)
                commission_amount = sum(float(p.fee) for p in positions) if positions else 0

                # Формируем ответ
                response = {
                    "userId": user_id,
                    "year": year,
                    "month": month,
                    "tradingVolume": round(trading_volume, 8),
                    "ordersCount": orders_count,
                    "commissionAmount": round(commission_amount, 8),
                }

                logger.info(f"Рассчитана статистика для пользователя {user_id} за {month}/{year}: {response}")
                return response

        except Exception as e:
            logger.error(f"Ошибка при обработке запроса статистики: {str(e)}")
            return {
                "userId": user_id,
                "year": year,
                "month": month,
                "tradingVolume": 0,
                "ordersCount": 0,
                "commissionAmount": 0,
                "error": f"Ошибка обработки: {str(e)}",
            }

    @staticmethod
    async def send_stats_response(user_id: str, stats_data: Dict[str, Any]):
        """
        Отправляет рассчитанную статистику в ответ на запрос

        Args:
            user_id: ID пользователя
            stats_data: Данные статистики
        """
        try:
            producer = await KafkaProducer.get_producer()

            # Отправляем данные в топик с ответами
            await producer.send_and_wait(
                topic="trade.stats", key=str(user_id), value=stats_data  # Топик для ответов по статистике
            )

            logger.info(f"Статистика отправлена в Kafka для пользователя {user_id}")
        except Exception as e:
            logger.error(f"Ошибка отправки статистики в Kafka: {str(e)}")


# Функция для запуска прослушивания в фоновом режиме
async def start_consumer():
    """Запускает прослушивание запросов статистики в фоновом режиме"""
    logger.info("🚀 Starting Kafka consumer for trade statistics...")

    retry_count = 0
    max_retries = 5

    while retry_count < max_retries:
        try:
            # Пытаемся запустить consumer
            await TradeStatsConsumer.start_listening()

        except asyncio.CancelledError:
            logger.info("🛑 Kafka consumer cancelled")
            break

        except Exception as e:
            retry_count += 1
            logger.error(f"❌ Kafka consumer error (attempt {retry_count}/{max_retries}): {str(e)}")

            if retry_count < max_retries:
                # Exponential backoff
                wait_time = min(2**retry_count, 60)
                logger.info(f"🔄 Retrying Kafka consumer in {wait_time} seconds...")
                await asyncio.sleep(wait_time)
            else:
                logger.error("❌ Max retries reached for Kafka consumer, giving up")
                break

    logger.info("🛑 Kafka consumer task finished")
