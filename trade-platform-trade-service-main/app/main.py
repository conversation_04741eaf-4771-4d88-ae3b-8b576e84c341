from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from app.api.websocket.routes import router as websocket_router, manager as connection_manager
from app.api.rest.routes import router as rest_router
from app.config.settings import settings
import logging
import asyncio
import time

# Настройка логирования
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

app = FastAPI(
    title=settings.APP_NAME, description="Сервис для торговли с использованием кредитного плеча.", version="0.1.0"
)


# Middleware для логирования всех запросов
@app.middleware("http")
async def log_requests(request: Request, call_next):
    start_time = time.time()

    logger.info("🌐 HTTP REQUEST RECEIVED")
    logger.info(f"   📍 Method: {request.method}")
    logger.info(f"   📍 URL: {request.url}")
    logger.info(f"   📍 Client: {request.client.host}:{request.client.port}")
    logger.info(f"   📍 Headers: {dict(request.headers)}")

    # Если это POST запрос, логируем body
    if request.method == "POST":
        try:
            body = await request.body()
            if body:
                logger.info(f"   📍 Body: {body.decode('utf-8')}")
        except Exception as e:
            logger.error(f"   ❌ Error reading body: {e}")

    response = await call_next(request)

    process_time = time.time() - start_time
    logger.info(f"🌐 HTTP RESPONSE SENT")
    logger.info(f"   📍 Status: {response.status_code}")
    logger.info(f"   📍 Process time: {process_time:.4f}s")
    logger.info("=" * 50)

    return response


# Настройка CORS
logger.info("🔧 Setting up CORS...")
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)
logger.info(f"✅ CORS configured with origins: {settings.CORS_ORIGINS}")

# Регистрация маршрутов
logger.info("🔧 Registering routers...")
app.include_router(websocket_router, prefix=settings.API_PREFIX)
app.include_router(rest_router, prefix=settings.API_PREFIX)
logger.info(f"✅ Routers registered with prefix: {settings.API_PREFIX}")

# 🔥 ОБНОВЛЕННЫЕ глобальные сервисы
price_service = None
consumer_task = None
position_manager = None
pnl_updater = None  # Добавляем PnL Updater


@app.on_event("startup")
async def startup_event():
    global price_service, consumer_task, position_manager, pnl_updater
    logger.info("🚀 APPLICATION STARTUP STARTED (AUTO PnL UPDATE MODE)")
    logger.info("=" * 50)

    try:
        # ✅ ШАГ 1: Проверка настроек базы данных
        logger.info("🔧 Checking database configuration...")
        logger.info(f"   📍 DATABASE_URL: {settings.DATABASE_URL}")
        logger.info(f"   📍 POOL_SIZE: {settings.DATABASE_POOL_SIZE}")
        logger.info(f"   📍 MAX_OVERFLOW: {settings.DATABASE_MAX_OVERFLOW}")
        logger.info(f"   📍 POOL_TIMEOUT: {settings.DATABASE_POOL_TIMEOUT}")

        # ✅ ШАГ 2: Импорт модулей базы данных
        logger.info("🔧 Importing database modules...")
        try:
            from app.db.base import Base, engine, SessionLocal

            logger.info("✅ Database modules imported successfully")
        except Exception as import_error:
            logger.error(f"❌ Error importing database modules: {str(import_error)}")
            raise

        # ✅ ШАГ 3: Проверка подключения к базе данных
        logger.info("🔧 Testing database connection...")
        try:
            from sqlalchemy.sql import text

            with engine.connect() as connection:
                result = connection.execute(text("SELECT 1"))
                logger.info(f"✅ Database connection successful: {result.fetchone()}")
        except Exception as conn_error:
            logger.error(f"❌ Database connection failed: {str(conn_error)}")
            logger.error("❌ Please check:")
            logger.error("   - Database server is running")
            logger.error("   - DATABASE_URL is correct")
            logger.error("   - Database exists")
            logger.error("   - User has permissions")
            raise

        # ✅ ШАГ 4: Создание таблиц (с подробными логами)
        logger.info("🔧 Creating database tables...")
        try:
            # Логируем какие таблицы будут созданы
            logger.info(f"   📍 Tables to create: {list(Base.metadata.tables.keys())}")

            logger.info("   📍 Starting Base.metadata.create_all()...")
            Base.metadata.create_all(bind=engine)
            logger.info("✅ Database tables created or verified successfully")

            # Проверяем что таблицы действительно созданы
            logger.info("🔧 Verifying table creation...")
            with engine.connect() as connection:
                # Проверяем основные таблицы
                tables_to_check = ["users", "positions", "market_prices", "kafka_events"]
                for table_name in tables_to_check:
                    try:
                        result = connection.execute(text(f"SELECT COUNT(*) FROM {table_name}"))
                        count = result.fetchone()[0]
                        logger.info(f"   ✅ Table '{table_name}' exists with {count} records")
                    except Exception as table_error:
                        logger.warning(f"   ⚠️  Table '{table_name}' check failed: {str(table_error)}")

        except Exception as table_error:
            logger.error(f"❌ Error creating database tables: {str(table_error)}")
            import traceback

            logger.error(f"❌ Traceback: {traceback.format_exc()}")
            raise

        # ✅ ШАГ 5: Тест создания сессии базы данных
        logger.info("🔧 Testing database session creation...")
        try:
            test_session = SessionLocal()
            logger.info("   📍 Test session created")
            test_session.close()
            logger.info("✅ Database session test successful")
        except Exception as session_error:
            logger.error(f"❌ Database session test failed: {str(session_error)}")
            raise

        # ✅ ШАГ 6: КРИТИЧНО - Инициализация Kafka Producer
        logger.info("🔧 Initializing Kafka Producer...")
        logger.info(f"   📍 Bootstrap servers: {settings.KAFKA_BOOTSTRAP_SERVERS}")
        logger.info(f"   📍 Balance topic: {settings.KAFKA_BALANCE_TOPIC}")

        from app.kafka.producer import KafkaProducer

        # Инициализируем producer при старте приложения - БЕЗ try/catch!
        producer = await KafkaProducer.get_producer()
        logger.info("✅ Kafka Producer initialized successfully")

        # ОБЯЗАТЕЛЬНО тестируем отправку сообщения
        logger.info("🔧 Testing Kafka Producer with test message...")
        test_result = await KafkaProducer.send_trade_operation(
            operation_type="test_connection", user_id="system", amount=0, currency="USDT"
        )

        if not test_result:
            raise Exception("Kafka Producer test message failed - trading will not work!")

        logger.info("✅ Kafka Producer test message sent successfully")

        # ✅ ШАГ 7: КРИТИЧНО - Инициализация Kafka Consumer
        logger.info("🔧 Initializing Kafka Consumer...")
        logger.info(f"   📍 Stats request topic: trade.request.stats")
        logger.info(f"   📍 Stats response topic: trade.stats")

        from app.kafka.consumer import TradeStatsConsumer, start_consumer

        # Запускаем consumer в фоновом режиме БЕЗ ожидания!
        consumer_task = asyncio.create_task(start_consumer())
        logger.info("✅ Kafka Consumer started successfully")

        # ✅ ШАГ 8: Инициализация Position Manager
        logger.info("🔧 Initializing Position Manager...")
        from app.core.position_manager import PositionManager

        position_manager = PositionManager()
        logger.info("✅ Position Manager initialized successfully")

        # ✅ ШАГ 9: Инициализация Price Service
        logger.info("🔧 Initializing Price Service...")
        logger.info(f"   📍 Binance WebSocket URL: wss://stream.binance.com:9443/ws")
        logger.info(f"   📍 Supported pairs: {getattr(settings, 'SUPPORTED_PAIRS', ['BTC/USDT', 'ETH/USDT'])}")

        from app.core.price_service import PriceService

        price_service = PriceService()

        # Запускаем Price Service
        await price_service.start()
        logger.info("✅ Price Service started successfully")

        # 🔥 ШАГ 10: НОВОЕ - Инициализация PnL Updater
        logger.info("🔧 Initializing PnL Updater...")
        logger.info("   📍 PnL will be updated every 5 seconds in database")
        logger.info("   📍 Frontend will receive updated PnL from database")

        from app.core.pnl_updater import pnl_updater

        # Запускаем PnL Updater с Price Service
        await pnl_updater.start(price_service)
        logger.info("✅ PnL Updater started successfully")
        logger.info("💰 Automatic PnL updates enabled - every 5 seconds")

        logger.info("=" * 50)
        logger.info("🎉 APPLICATION STARTUP COMPLETED!")
        logger.info("🎯 ENABLED: Database, REST API, Kafka Producer, Kafka Consumer, Price Service, PnL Updater")
        logger.info("🚀 AUTO PnL UPDATE MODE - PnL automatically updated in database every 5 seconds")
        logger.info("📍 Ready to handle trade requests with real-time PnL updates")
        logger.info("💰 Frontend will receive updated PnL from database every 10 seconds")
        logger.info("⚡ Total latency: Price updates (1s) + PnL calculation (5s) + Frontend request (10s)")
        logger.info("=" * 50)

    except Exception as e:
        logger.error("❌ FATAL ERROR DURING STARTUP!")
        logger.error(f"❌ Error: {str(e)}")
        import traceback

        logger.error(f"❌ Traceback: {traceback.format_exc()}")
        logger.error("=" * 50)
        logger.error("🚨 APPLICATION STARTUP FAILED - ALL SERVICES REQUIRED!")
        logger.error("🚨 Fix connections and restart the application")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    logger.info("🛑 APPLICATION SHUTDOWN STARTED")
    logger.info("=" * 50)

    # 🔥 НОВОЕ: Останавливаем WebSocket соединения ПЕРВЫМИ
    try:
        logger.info("🔧 Stopping WebSocket connections...")
        from app.api.websocket.routes import shutdown_websocket_manager
        await shutdown_websocket_manager()
        logger.info("✅ WebSocket connections closed successfully")
    except Exception as e:
        logger.error(f"❌ Error stopping WebSocket connections: {str(e)}")

    # Останавливаем PnL Updater
    if pnl_updater:
        try:
            logger.info("🔧 Stopping PnL Updater...")
            await pnl_updater.stop()
            logger.info("✅ PnL Updater stopped successfully")
        except Exception as e:
            logger.error(f"❌ Error stopping PnL Updater: {str(e)}")

    # Закрываем Price Service
    if price_service:
        try:
            logger.info("🔧 Stopping Price Service...")
            await price_service.stop()
            logger.info("✅ Price Service stopped successfully")
        except Exception as e:
            logger.error(f"❌ Error stopping Price Service: {str(e)}")

    # Закрываем Kafka Consumer
    if consumer_task:
        try:
            logger.info("🔧 Stopping Kafka Consumer...")
            from app.kafka.consumer import TradeStatsConsumer

            await TradeStatsConsumer.close()

            consumer_task.cancel()
            try:
                await consumer_task
            except asyncio.CancelledError:
                pass
            logger.info("✅ Kafka Consumer stopped successfully")
        except Exception as e:
            logger.error(f"❌ Error stopping Kafka Consumer: {str(e)}")

    # Закрываем Kafka Producer
    try:
        logger.info("🔧 Stopping Kafka Producer...")
        from app.kafka.producer import KafkaProducer

        await KafkaProducer.close()
        logger.info("✅ Kafka Producer closed successfully")
    except Exception as e:
        logger.error(f"❌ Error closing Kafka Producer: {str(e)}")

    logger.info("✅ Database connections will be closed automatically")

    logger.info("=" * 50)
    logger.info("🎉 APPLICATION SHUTDOWN COMPLETED")
    logger.info("=" * 50)


@app.get("/")
def read_root():
    logger.info("📍 ROOT ENDPOINT HIT")
    return {
        "status": "ok",
        "service": settings.APP_NAME,
        "mode": "auto_pnl_update_mode",
        "message": "Trading with Kafka balance updates + Auto PnL updates every 5 seconds",
    }


@app.get("/health")
def health_check():
    logger.info("📍 HEALTH CHECK ENDPOINT HIT")

    # Проверяем подключение к базе данных
    db_status = "disconnected"
    try:
        from app.db.base import engine
        from sqlalchemy.sql import text

        with engine.connect() as connection:
            connection.execute(text("SELECT 1"))
            db_status = "connected"
            logger.info("✅ Database health check passed")
    except Exception as e:
        logger.error(f"❌ Database health check failed: {str(e)}")
        db_status = f"error: {str(e)}"

    # Проверяем статус Kafka Producer
    kafka_producer_status = "disconnected"
    try:
        from app.kafka.producer import KafkaProducer

        if KafkaProducer._producer is not None:
            kafka_producer_status = "connected"
        else:
            kafka_producer_status = "not_initialized"
    except Exception as e:
        kafka_producer_status = f"error: {str(e)}"

    # Проверяем статус Price Service
    price_service_status = "disconnected"
    prices_count = 0
    try:
        if price_service and price_service.is_connected:
            price_service_status = "connected"
            prices_count = len(price_service.get_all_prices())
        else:
            price_service_status = "not_connected"
    except Exception as e:
        price_service_status = f"error: {str(e)}"

    # 🔥 НОВОЕ: Проверяем статус PnL Updater
    pnl_updater_status = "stopped"
    try:
        if pnl_updater and pnl_updater.running:
            pnl_updater_status = "running"
        else:
            pnl_updater_status = "stopped"
    except Exception as e:
        pnl_updater_status = f"error: {str(e)}"

    health_status = {
        "status": (
            "healthy"
            if (
                db_status == "connected"
                and kafka_producer_status == "connected"
                and price_service_status == "connected"
                and pnl_updater_status == "running"
            )
            else "unhealthy"
        ),
        "database": db_status,
        "kafka_producer": kafka_producer_status,
        "kafka_consumer": "running" if consumer_task and not consumer_task.done() else "stopped",
        "price_service": price_service_status,
        "prices_loaded": prices_count,
        "pnl_updater": pnl_updater_status,  # 🔥 НОВОЕ
        "trading": (
            "enabled" if (kafka_producer_status == "connected" and pnl_updater_status == "running") else "disabled"
        ),
        "pnl_updates": "automatic_every_5_seconds",  # 🔥 ОБНОВЛЯЕМ
        "mode": "auto_pnl_update_mode",  # 🔥 ОБНОВЛЯЕМ
    }

    logger.info(f"✅ Health status: {health_status}")
    return health_status


@app.get("/kafka-test")
async def test_kafka():
    """Тестовый эндпоинт для проверки Kafka Producer"""
    logger.info("📍 KAFKA TEST ENDPOINT HIT")

    try:
        from app.kafka.producer import KafkaProducer

        # Тестируем отправку сообщения
        result = await KafkaProducer.send_trade_operation(
            operation_type="test", user_id="test_user", amount=100, currency="USDT", margin_usd=1000, pnl_usd=0
        )

        return {
            "status": "success" if result else "failed",
            "kafka_producer_initialized": KafkaProducer._producer is not None,
            "test_message_sent": result,
            "message": "Kafka test completed",
        }

    except Exception as e:
        logger.error(f"❌ Kafka test failed: {str(e)}")
        return {"status": "error", "error": str(e), "message": "Kafka test failed"}


@app.get("/price-test")
def test_price_service():
    """Тестовый эндпоинт для проверки Price Service"""
    logger.info("📍 PRICE SERVICE TEST ENDPOINT HIT")

    try:
        if not price_service:
            return {"status": "error", "error": "Price service not initialized", "message": "Price service test failed"}

        all_prices = price_service.get_all_prices()

        return {
            "status": "success" if price_service.is_connected else "connecting",
            "connected": price_service.is_connected,
            "prices_count": len(all_prices),
            "sample_prices": dict(list(all_prices.items())[:5]),  # Показываем первые 5 цен
            "message": f"Price service {'connected' if price_service.is_connected else 'connecting'}",
        }

    except Exception as e:
        logger.error(f"❌ Price service test failed: {str(e)}")
        return {"status": "error", "error": str(e), "message": "Price service test failed"}


@app.get("/db-test")
def test_database():
    """
    Специальный эндпоинт для тестирования базы данных
    """
    logger.info("📍 DATABASE TEST ENDPOINT HIT")

    try:
        from app.db.base import SessionLocal
        from app.db.models import User, Position
        from sqlalchemy.sql import text

        session = SessionLocal()

        # Тест 1: Проверка подключения
        result = session.execute(text("SELECT 1")).fetchone()
        logger.info(f"✅ Connection test: {result}")

        # Тест 2: Проверка таблиц
        users_count = session.query(User).count()
        positions_count = session.query(Position).count()

        logger.info(f"✅ Users table: {users_count} records")
        logger.info(f"✅ Positions table: {positions_count} records")

        session.close()

        return {
            "status": "success",
            "connection": True,
            "users_count": users_count,
            "positions_count": positions_count,
            "message": "Database is working correctly",
        }

    except Exception as e:
        logger.error(f"❌ Database test failed: {str(e)}")
        return {"status": "error", "connection": False, "error": str(e), "message": "Database test failed"}


# 🔥 НОВЫЙ ЭНДПОИНТ: Тест PnL Updater
@app.get("/pnl-test/{user_id}")
def test_pnl_updates(user_id: str):
    """Тестовый эндпоинт для проверки PnL обновлений"""
    logger.info(f"📍 PNL TEST ENDPOINT HIT for user: {user_id}")

    try:
        from app.db.base import SessionLocal
        from app.db.models import Position

        session = SessionLocal()

        # Получаем открытые позиции пользователя
        positions = session.query(Position).filter(Position.user_id == str(user_id), Position.status == "OPEN").all()

        result = []
        for pos in positions:
            result.append(
                {
                    "id": pos.id,
                    "trading_pair": pos.trading_pair,
                    "direction": pos.direction,
                    "entry_price": float(pos.entry_price),
                    "current_price": float(pos.current_price),
                    "pnl": float(pos.pnl),
                    "return_percentage": pos.return_percentage,
                    "last_updated": "from_database",
                }
            )

        session.close()

        # Получаем текущие цены для сравнения
        current_prices = {}
        pnl_updater_status = "unknown"

        try:
            if price_service and price_service.is_connected:
                current_prices = price_service.get_all_prices()

            pnl_updater_status = "running" if (pnl_updater and pnl_updater.running) else "stopped"
        except Exception as e:
            logger.error(f"Error getting price service status: {str(e)}")

        logger.info(f"✅ PnL test for user {user_id}: {len(result)} positions")

        return {
            "user_id": user_id,
            "positions_count": len(result),
            "positions": result,
            "current_prices_available": len(current_prices),
            "sample_prices": dict(list(current_prices.items())[:3]) if current_prices else {},
            "pnl_updater_status": pnl_updater_status,
            "message": f"PnL data from database (auto-updated every 5 seconds)",
        }

    except Exception as e:
        logger.error(f"❌ PnL test failed: {str(e)}")
        return {"status": "error", "user_id": user_id, "error": str(e), "message": "PnL test failed"}


@app.get("/debug-positions/{user_id}")
def debug_user_positions(user_id: str):
    """
    Дебаг эндпоинт для проверки статусов позиций пользователя
    """
    logger.info(f"📍 DEBUG POSITIONS ENDPOINT HIT for user: {user_id}")

    try:
        from app.db.base import SessionLocal
        from app.db.models import Position

        session = SessionLocal()

        # Получаем последние 10 позиций пользователя
        positions = (
            session.query(Position)
            .filter(Position.user_id == str(user_id))
            .order_by(Position.opened_at.desc())
            .limit(10)
            .all()
        )

        result = []
        for pos in positions:
            result.append(
                {
                    "id": pos.id,
                    "status": pos.status,
                    "trading_pair": pos.trading_pair,
                    "direction": pos.direction,
                    "current_price": float(pos.current_price),
                    "pnl": float(pos.pnl),
                    "return_percentage": pos.return_percentage,
                    "opened_at": str(pos.opened_at),
                    "closed_at": str(pos.closed_at) if pos.closed_at else None,
                    "close_reason": pos.close_reason,
                }
            )

        session.close()

        # Подсчитываем статусы
        status_counts = {}
        for pos in result:
            status = pos["status"]
            status_counts[status] = status_counts.get(status, 0) + 1

        logger.info(f"✅ Debug result for user {user_id}: {len(result)} positions")
        logger.info(f"✅ Status distribution: {status_counts}")

        return {
            "user_id": user_id,
            "total_positions": len(result),
            "status_distribution": status_counts,
            "recent_positions": result,
            "pnl_updater_running": pnl_updater.running if pnl_updater else False,
        }

    except Exception as e:
        logger.error(f"❌ Debug positions failed: {str(e)}")
        return {"status": "error", "user_id": user_id, "error": str(e), "message": "Debug failed"}


@app.get("/websocket-health")
async def websocket_health():
    """Эндпоинт для проверки здоровья WebSocket системы"""
    logger.info("📍 WEBSOCKET HEALTH CHECK ENDPOINT HIT")
    
    try:
        from app.api.websocket.routes import manager
        
        # Получаем статистику соединений
        stats = await manager.get_connection_stats()
        
        # Проверяем состояние фоновых задач
        background_status = {
            "ping_task_running": manager._ping_task is not None and not manager._ping_task.done() if manager._ping_task else False,
            "cleanup_task_running": manager._cleanup_task is not None and not manager._cleanup_task.done() if manager._cleanup_task else False,
            "manager_running": manager._running
        }
        
        # Определяем общее состояние здоровья
        health_status = "healthy" if (
            background_status["manager_running"] and
            stats["alive_connections"] >= 0 and
            stats["dead_connections"] == 0
        ) else "degraded"
        
        return {
            "status": health_status,
            "websocket_stats": stats,
            "background_tasks": background_status,
            "features": {
                "ping_pong_enabled": True,
                "heartbeat_monitoring": True,
                "auto_cleanup": True,
                "rate_limiting": True,
                "connection_pooling": True,
                "graceful_shutdown": True
            },
            "configuration": {
                "ping_interval_seconds": manager.ping_interval,
                "pong_timeout_seconds": manager.pong_timeout,
                "max_failed_pings": manager.max_failed_pings,
                "connection_timeout_seconds": manager.connection_timeout,
                "max_connections_per_user": manager.max_connections_per_user
            },
            "message": f"WebSocket system is {health_status}"
        }
        
    except Exception as e:
        logger.error(f"❌ WebSocket health check failed: {str(e)}")
        return {
            "status": "error",
            "error": str(e),
            "message": "WebSocket health check failed"
        }
