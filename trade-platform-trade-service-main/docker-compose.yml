version: '3.8'

services:
  trade-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8008:8008"
    environment:
      - DATABASE_URL=********************************************/trade_service
      - KAFKA_BROKERS=kafka:9092
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - kafka
      - redis
    networks:
      - bitmei-network
    restart: unless-stopped

  postgres:
    image: postgres:14-alpine
    environment:
      - POSTGRES_DB=trade_service
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - "5439:5432"
    volumes:
      - postgres_trade_data:/var/lib/postgresql/data
    networks:
      - bitmei-network

  redis:
    image: redis:7-alpine
    ports:
      - "6385:6379"
    volumes:
      - redis_trade_data:/data
    networks:
      - bitmei-network

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9099:9092"
    depends_on:
      - zookeeper
    networks:
      - bitmei-network

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2188:2181"
    networks:
      - bitmei-network

volumes:
  postgres_trade_data:
  redis_trade_data:

networks:
  bitmei-network:
    driver: bridge