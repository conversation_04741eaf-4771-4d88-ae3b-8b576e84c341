/venv
.env
/logs

# Claude-related files
CLAUDE.md
FRONTEND_POSITION_UPDATE_GUIDE.md

# Claude Code specific files
.claude/
claude.md
*claude*
*CLAUDE*

# Documentation generated by Claude
*_GUIDE.md
*_guide.md
*_documentation.md
*_DOCUMENTATION.md

# Python cache and temporary files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/
log/

# Database
*.db
*.sqlite
*.sqlite3

# Testing
.pytest_cache/
.coverage
htmlcov/
.tox/

# Virtual environments
venv/
env/
ENV/
env.bak/
venv.bak/