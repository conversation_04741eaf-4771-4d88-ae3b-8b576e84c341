version: '3.8'

services:
  balance-service:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - "8003:8003"
    environment:
      - DATABASE_URL=********************************************************/balance_service
      - REDIS_URL=redis://redis:6379
    depends_on:
      - postgres
      - redis
      - kafka
    networks:
      - bitmei-network
    restart: unless-stopped

  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: balance_service
      POSTGRES_USER: balance_user
      POSTGRES_PASSWORD: balance_password
    ports:
      - "5434:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bitmei-network
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis_data:/data
    networks:
      - bitmei-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:latest
    environment:
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
    ports:
      - "9094:9092"
    depends_on:
      - zookeeper
    networks:
      - bitmei-network
    restart: unless-stopped

  zookeeper:
    image: confluentinc/cp-zookeeper:latest
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    ports:
      - "2183:2181"
    networks:
      - bitmei-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  bitmei-network:
    driver: bridge