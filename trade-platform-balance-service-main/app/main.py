import asyncio
import logging
import time
from fastapi import FastAPI, APIRouter, Depends, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.config import settings
from app.api.routes import balance, kafka  # Убрали импорт health
from app.db.session import get_db, engine, Base
from app.db.models.balance import UserBalance, BalanceHistory, BalanceLock
from app.kafka.consumer import KafkaConsumer
from app.kafka.event_handlers import EVENT_HANDLERS
from app.websocket import setup_websocket_routes  # Импорт функции настройки WebSocket


# Настройка логирования
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


# Инициализация приложения FastAPI
app = FastAPI(
    title=settings.APP_NAME,
    description="Сервис управления балансами пользователей",
    version="0.1.0",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
)


# Добавление middleware для CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=settings.CORS_ALLOW_CREDENTIALS,
    allow_methods=settings.CORS_ALLOW_METHODS,
    allow_headers=settings.CORS_ALLOW_HEADERS,
)


# Middleware для логирования HTTP запросов
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Middleware для логирования всех HTTP запросов и ответов"""
    start_time = time.time()
    
    # Логирование входящего запроса
    logger.info("🌐 HTTP REQUEST RECEIVED")
    logger.info(f"   📍 Method: {request.method}")
    logger.info(f"   📍 URL: {str(request.url)}")
    
    # Безопасное получение информации о клиенте
    try:
        client_info = f"{request.client.host}:{request.client.port}" if request.client else "Unknown"
        logger.info(f"   📍 Client: {client_info}")
    except Exception as e:
        logger.warning(f"   📍 Client: Unable to get client info - {str(e)}")
    
    # Логирование заголовков (исключаем чувствительные данные)
    try:
        headers = dict(request.headers)
        # Маскируем чувствительные заголовки
        if 'authorization' in headers:
            auth_header = headers['authorization']
            if len(auth_header) > 20:
                headers['authorization'] = auth_header[:20] + "..."
        logger.info(f"   📍 Headers: {headers}")
    except Exception as e:
        logger.warning(f"   📍 Headers: Unable to log headers - {str(e)}")
    
    try:
        # Выполнение запроса
        response = await call_next(request)
        
        # Логирование ответа
        process_time = time.time() - start_time
        logger.info("🌐 HTTP RESPONSE SENT")
        logger.info(f"   📍 Status: {response.status_code}")
        logger.info(f"   📍 Process time: {process_time:.4f}s")
        logger.info("=" * 50)
        
        return response
        
    except Exception as e:
        # Логирование ошибок
        process_time = time.time() - start_time
        logger.error("❌ HTTP REQUEST ERROR")
        logger.error(f"   📍 Error: {str(e)}")
        logger.error(f"   📍 Process time: {process_time:.4f}s")
        logger.error("=" * 50)
        
        # Повторно выбрасываем исключение
        raise


# Глобальный обработчик ошибок валидации
@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request, exc):
    logger.error(f"❌ Ошибка валидации: {str(exc)}")
    logger.error(f"   📍 URL: {str(request.url)}")
    logger.error(f"   📍 Method: {request.method}")
    return JSONResponse(
        status_code=422,
        content={"detail": str(exc)},
    )


# Глобальный обработчик для всех остальных исключений
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    logger.error(f"❌ Необработанное исключение: {str(exc)}")
    logger.error(f"   📍 URL: {str(request.url)}")
    logger.error(f"   📍 Method: {request.method}")
    
    # Импортируем traceback для полной трассировки
    import traceback
    logger.error(f"   📍 Traceback: {traceback.format_exc()}")
    
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Внутренняя ошибка сервера",
            "error": str(exc) if settings.DEBUG else "Internal server error"
        },
    )


# Основной маршрутизатор API
api_router = APIRouter(prefix=settings.API_PREFIX)
api_router.include_router(balance.router)
# Убрали health router
api_router.include_router(kafka.router)
app.include_router(api_router)


# Информация о WebSocket для отладки
@app.get("/ws-info")
async def websocket_info():
    return {"message": "WebSocket доступен по адресу /ws/balance?user_id=<user_id>"}


# Создание таблиц в базе данных и настройка WebSocket
@app.on_event("startup")
async def init_app():
    """Инициализация приложения при запуске"""
    logger.info("🚀 ЗАПУСК ПРИЛОЖЕНИЯ - Начало инициализации")
    
    try:
        logger.info("📊 Инициализация базы данных")
        
        # Проверка моделей
        logger.info(f"📋 Модели в метаданных: {list(Base.metadata.tables.keys())}")
        
        # Создание таблиц
        Base.metadata.create_all(bind=engine)
        logger.info("✅ Таблицы успешно созданы")
        
    except Exception as e:
        logger.error(f"❌ Ошибка при создании таблиц: {str(e)}")
        # Печатаем полный стек-трейс
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
        # НЕ прерываем запуск приложения - позволяем ему продолжить
    
    try:
        logger.info("🔌 Настройка WebSocket (начало)")
        await setup_websocket_routes(app)
        logger.info("✅ Настройка WebSocket (завершено)")
        
    except Exception as e:
        logger.error(f"❌ Ошибка при настройке WebSocket: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
    
    logger.info("🎉 ЗАПУСК ПРИЛОЖЕНИЯ - Инициализация завершена")


# Kafka consumer
kafka_consumer = None


@app.on_event("startup")
async def start_kafka_consumer():
    """Запуск Kafka потребителя при старте приложения"""
    global kafka_consumer
    
    logger.info("📨 KAFKA - Начало запуска потребителя")
    
    try:
        # ОБНОВЛЕННЫЙ список топиков включая balance-operations
        kafka_topics = [
            settings.KAFKA_TRANSACTION_TOPIC,    # transaction-events
            "balance-operations",                # НОВЫЙ топик от Trade Service
            "check.locked.balance",              # Топик для проверки locked_balance
            "deduct.fixed.balance",              # Топик для уменьшения фиксированного баланса
            "balance.add.referal.reward",         # Топик для реферальных наград
            "balance.add.funds"   
        ]
        
        logger.info(f"📋 Список топиков Kafka: {kafka_topics}")
        logger.info(f"📋 Группа потребителей: {settings.KAFKA_GROUP_ID}")
        logger.info(f"📋 Bootstrap серверы: {settings.KAFKA_BOOTSTRAP_SERVERS}")
        
        kafka_consumer = KafkaConsumer(
            topics=kafka_topics,
            group_id=settings.KAFKA_GROUP_ID,
            bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
            handler_map=EVENT_HANDLERS
        )
        
        # Запускаем потребителя в отдельной асинхронной задаче
        asyncio.create_task(kafka_consumer.start())
        
        logger.info("✅ KAFKA - Потребитель успешно запущен")
        
    except Exception as e:
        logger.error(f"❌ KAFKA - Ошибка при запуске потребителя: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
        # НЕ прерываем запуск приложения


@app.on_event("shutdown")
async def stop_kafka_consumer():
    """Остановка Kafka потребителя при остановке приложения"""
    global kafka_consumer
    
    logger.info("🛑 KAFKA - Начало остановки потребителя")
    
    try:
        if kafka_consumer:
            await kafka_consumer.stop()
            logger.info("✅ KAFKA - Потребитель успешно остановлен")
        else:
            logger.info("ℹ️ KAFKA - Потребитель не был инициализирован")
            
    except Exception as e:
        logger.error(f"❌ KAFKA - Ошибка при остановке потребителя: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")


@app.on_event("shutdown")
async def shutdown_event():
    """Обработчик события остановки приложения"""
    logger.info("🛑 ПРИЛОЖЕНИЕ - Начало процедуры остановки")
    
    try:
        # Здесь можно добавить дополнительную логику очистки ресурсов
        logger.info("🧹 Очистка ресурсов...")
        
        logger.info("✅ ПРИЛОЖЕНИЕ - Остановка завершена успешно")
        
    except Exception as e:
        logger.error(f"❌ ПРИЛОЖЕНИЕ - Ошибка при остановке: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")


# Дополнительные эндпоинты для мониторинга
@app.get("/status")
async def get_status():
    """Расширенная информация о статусе сервиса"""
    return {
        "status": "running",
        "app_name": settings.APP_NAME,
        "debug_mode": settings.DEBUG,
        "kafka_consumer_active": kafka_consumer is not None,
        "supported_currencies": settings.SUPPORTED_CURRENCIES
    }


# if __name__ == "__main__":
#     import uvicorn
    
#     logger.info("🚀 Запуск сервера через uvicorn")
#     logger.info(f"📍 Host: 0.0.0.0")
#     logger.info(f"📍 Port: {settings.PORT}")
#     logger.info(f"📍 Debug: {settings.DEBUG}")
    
#     uvicorn.run(
#         "app.main:app",
#         host="0.0.0.0",
#         port=settings.PORT,
#         reload=settings.DEBUG,
#     )