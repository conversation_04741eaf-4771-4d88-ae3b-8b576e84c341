from datetime import datetime
from typing import Optional, List
from fastapi import APIRouter, Depends, Query, HTTPException, Request
from sqlalchemy.orm import Session
import logging

from app.api.models.balance import BalanceItem, BalanceResponse, FixedBalanceResponse, HistoryResponse, SummaryResponse, PeriodEnum
from app.core.services.balance_service import BalanceService
from app.db.session import get_db
from app.api.dependencies import get_user_id
from pydantic import BaseModel

logger = logging.getLogger(__name__)

# СОЗДАЕМ РОУТЕР БЕЗ ПРЕФИКСА - ПРЕФИКС БУДЕТ В ГЛАВНОМ ПРИЛОЖЕНИИ
router = APIRouter(tags=["balance"])

# Модели данных
class UpdateFixedBalanceRequest(BaseModel):
    fixed_usd_balance: float
    reason: str = "Административное изменение"

class BulkBalanceRequest(BaseModel):
    user_ids: List[int]

# ==============================================
# РОУТЫ ДЛЯ АДМИНКИ (ПРИОРИТЕТ)
# ==============================================

@router.get("/balance/available/{user_id}")
async def get_user_available_balance(
    user_id: int,
    db: Session = Depends(get_db)
):
    """
    ПОЛУЧЕНИЕ ДОСТУПНОГО БАЛАНСА ПОЛЬЗОВАТЕЛЯ ПО ID
    Возвращает только available_usd_balance для таблицы
    """
    logger.info(f"🎯 API: Запрос доступного баланса для пользователя {user_id}")
    
    try:
        available_balance = await BalanceService.get_user_available_balance_only(db, user_id)
        logger.info(f"✅ API: Доступный баланс: {available_balance}")
        
        return {"available_usd_balance": available_balance}
        
    except Exception as e:
        logger.error(f"❌ API: Ошибка получения баланса для {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ошибка получения баланса: {str(e)}")

@router.post("/balance/bulk/available")
async def get_bulk_available_balances(
    request: BulkBalanceRequest,
    db: Session = Depends(get_db)
):
    """
    МАССОВОЕ ПОЛУЧЕНИЕ ДОСТУПНЫХ БАЛАНСОВ
    Возвращает {user_id: available_balance} для таблицы
    """
    logger.info(f"🎯 API: Запрос массовых балансов для {len(request.user_ids)} пользователей")
    logger.info(f"📋 API: ID пользователей: {request.user_ids}")
    
    try:
        if not request.user_ids:
            logger.warning("⚠️ API: Пустой список пользователей")
            return {}
            
        if len(request.user_ids) > 100:
            logger.warning(f"⚠️ API: Слишком много пользователей: {len(request.user_ids)}")
            raise HTTPException(status_code=400, detail="Максимум 100 пользователей за один запрос")
        
        balances = await BalanceService.get_bulk_available_balances(db, request.user_ids)
        logger.info(f"✅ API: Получено балансов: {len(balances)}")
        
        # Убеждаемся что все запрошенные пользователи есть в ответе
        result = {}
        for user_id in request.user_ids:
            result[user_id] = balances.get(user_id, 0.0)
        
        logger.info(f"🎉 API: Отправляем {len(result)} балансов")
        logger.info(f"📊 API: Первые 3 баланса: {dict(list(result.items())[:3])}")
        
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"❌ API: Ошибка при получении массовых балансов: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ошибка при получении балансов: {str(e)}")

# Обновленная модель БЕЗ поля reason
class UpdateFixedBalanceRequest(BaseModel):
    fixed_usd_balance: float

@router.put("/balance/admin/update/{user_id}")
async def update_user_balance(
    user_id: int,
    request: UpdateFixedBalanceRequest,
    db: Session = Depends(get_db)
):
    """
    ОБНОВЛЕНИЕ ФИКСИРОВАННОГО БАЛАНСА ПОЛЬЗОВАТЕЛЯ
    Устанавливает новое значение фиксированного баланса
    """
    logger.info(f"🎯 API: Обновление баланса пользователя {user_id}")
    logger.info(f"💰 API: Новый баланс: {request.fixed_usd_balance}")
    
    try:
        # Получаем текущий баланс
        current_balance = await BalanceService.get_user_available_balance_only(db, user_id)
        logger.info(f"📊 API: Текущий баланс: {current_balance}")
        
        # Обновляем баланс БЕЗ reason
        await BalanceService.update_user_fixed_balance(
            db=db,
            user_id=user_id,
            new_balance=request.fixed_usd_balance
        )
        
        # Получаем новый баланс
        new_balance = await BalanceService.get_user_available_balance_only(db, user_id)
        logger.info(f"✅ API: Баланс обновлен до: {new_balance}")
        
        return {
            "success": True,
            "user_id": user_id,
            "old_balance": current_balance,
            "new_balance": new_balance,
            "message": "Баланс успешно обновлен"
        }
        
    except Exception as e:
        logger.error(f"❌ API: Ошибка при обновлении баланса пользователя {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ошибка при обновлении баланса: {str(e)}")
    

@router.get("/balance/admin/deposits/all", response_model=HistoryResponse)
async def get_all_deposits_for_admin(
    user_id: Optional[int] = Query(None, description="Фильтр по ID пользователя"),
    search: Optional[str] = Query(None, description="Поиск по email пользователя"),
    status: Optional[str] = Query(None, description="Фильтр по статусу"),
    currency: Optional[str] = Query(None, description="Фильтр по валюте"),
    from_date: Optional[datetime] = Query(None, description="Начальная дата"),
    to_date: Optional[datetime] = Query(None, description="Конечная дата"),
    page: int = Query(1, ge=1, description="Номер страницы"),
    limit: int = Query(20, ge=1, le=1000, description="Размер страницы"),
    db: Session = Depends(get_db)
):
    """
    АДМИНСКИЙ РОУТ: Получение ВСЕХ депозитов (пополнений)
    
    Возвращает депозиты всех пользователей без ограничения по user_id.
    Специально для админской панели.
    
    Параметры:
    - user_id: опциональный фильтр по конкретному пользователю
    - search: поиск по email пользователя
    - status: фильтр по статусу (processed/pending)
    - остальные стандартные фильтры
    """
    logger.info(f"🎯 ADMIN API: Запрос всех депозитов для админки")
    logger.info(f"📋 ADMIN API: Фильтры - user_id: {user_id}, search: {search}, status: {status}")
    
    try:
        # Используем админский метод сервиса для депозитов
        result = BalanceService.get_all_deposits_for_admin(
            db=db,
            user_id=user_id,
            search=search,
            status=status,
            currency=currency,
            from_date=from_date,
            to_date=to_date,
            page=page,
            limit=limit
        )
        
        logger.info(f"✅ ADMIN API: Найдено {len(result.items)} депозитов из {result.total}")
        return result
        
    except Exception as e:
        logger.error(f"❌ ADMIN API: Ошибка при получении всех депозитов: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ошибка получения депозитов: {str(e)}")

@router.get("/balance/admin/history/deposits", response_model=HistoryResponse)
async def get_deposits_history_for_admin(
    user_id: Optional[int] = Query(None, description="Фильтр по ID пользователя"),
    search: Optional[str] = Query(None, description="Поиск по email пользователя"),
    status: Optional[str] = Query(None, description="Фильтр по статусу"),
    currency: Optional[str] = Query(None, description="Фильтр по валюте"),
    from_date: Optional[datetime] = Query(None, description="Начальная дата"),
    to_date: Optional[datetime] = Query(None, description="Конечная дата"),
    page: int = Query(1, ge=1, description="Номер страницы"),
    limit: int = Query(20, ge=1, le=1000, description="Размер страницы"),
    db: Session = Depends(get_db)
):
    """
    АЛЬТЕРНАТИВНЫЙ АДМИНСКИЙ РОУТ: История депозитов
    
    То же самое что и /balance/admin/deposits/all, но с другим путем
    для совместимости с существующим фронтендом
    """
    logger.info(f"🎯 ADMIN API: Запрос истории депозитов (альтернативный роут)")
    
    try:
        result = await BalanceService.get_all_deposits_for_admin(
            db=db,
            user_id=user_id,
            search=search,
            status=status,
            currency=currency,
            from_date=from_date,
            to_date=to_date,
            page=page,
            limit=limit
        )
        
        logger.info(f"✅ ADMIN API: Найдено {len(result.items)} депозитов из {result.total}")
        return result
        
    except Exception as e:
        logger.error(f"❌ ADMIN API: Ошибка при получении истории депозитов: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ошибка получения истории депозитов: {str(e)}")

# ==============================================
# ОСНОВНЫЕ РОУТЫ ДЛЯ ПОЛЬЗОВАТЕЛЕЙ
# ==============================================

@router.get("/balance", response_model=BalanceResponse)
async def get_balance(
    user_id: int = Query(..., description="ID пользователя"),
    currency: Optional[str] = Query(None, description="Фильтр по валюте"),
    db: Session = Depends(get_db)
):
    """Получение текущего баланса пользователя"""
    logger.info(f"🎯 API: Запрос баланса пользователя {user_id}")
    return await BalanceService.get_user_balances(db, user_id, currency)

@router.get("/balance/history", response_model=HistoryResponse)
async def get_balance_history(
    user_id: int = Query(..., description="ID пользователя"),
    currency: Optional[str] = Query(None, description="Фильтр по валюте"),
    from_date: Optional[datetime] = Query(None, description="Начальная дата"),
    to_date: Optional[datetime] = Query(None, description="Конечная дата"),
    page: int = Query(1, ge=1, description="Номер страницы"),
    limit: int = Query(10, ge=1, le=100, description="Размер страницы"),
    db: Session = Depends(get_db)
):
    """Получение истории изменений баланса пользователя"""
    return BalanceService.get_balance_history(
        db=db, user_id=user_id, currency=currency, from_date=from_date,
        to_date=to_date, page=page, limit=limit
    )

@router.get("/balance/summary", response_model=SummaryResponse)
async def get_balance_summary(
    user_id: int = Query(..., description="ID пользователя"),
    period: Optional[PeriodEnum] = Query(None, description="Период для статистики"),
    db: Session = Depends(get_db)
):
    """Получение суммарной статистики по балансу"""
    return BalanceService.get_summary_statistics(db=db, user_id=user_id, period=period)

@router.get("/balance/fixed", response_model=FixedBalanceResponse)
async def get_fixed_balance(
    user_id: int = Query(..., description="ID пользователя"),
    db: Session = Depends(get_db)
):
    """Получение фиксированного USD баланса пользователя"""
    logger.info(f"🎯 API: Запрос фиксированного баланса для пользователя {user_id}")
    
    try:
        fixed_balance = await BalanceService.get_user_fixed_balance(db, user_id)
        logger.info(f"✅ API: Фиксированный баланс получен: {fixed_balance.fixed_usd_balance}")
        
        # Получаем также обычные балансы для обратной совместимости
        balances_response = await BalanceService.get_user_balances(db, user_id)
        
        result = {
            "fixed_usd_balance": fixed_balance.fixed_usd_balance,
            "locked_usd_balance": fixed_balance.locked_usd_balance,
            "available_usd_balance": fixed_balance.available_usd_balance,
            "balances": balances_response.balances,
            "total_equivalent_usd": balances_response.total_equivalent_usd
        }
        
        logger.info(f"🎉 API: Отправляем результат")
        return result
        
    except Exception as e:
        logger.error(f"❌ API: Ошибка при получении фиксированного баланса для {user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Ошибка получения баланса: {str(e)}")

@router.get("/balance/history/by-type", response_model=HistoryResponse)
async def get_history_by_type(
    user_id: int = Query(..., description="ID пользователя"),
    type: str = Query(..., description="Тип транзакций (deposit или withdrawal)"),
    currency: Optional[str] = Query(None, description="Фильтр по валюте"),
    from_date: Optional[datetime] = Query(None, description="Начальная дата"),
    to_date: Optional[datetime] = Query(None, description="Конечная дата"),
    page: int = Query(1, ge=1, description="Номер страницы"),
    limit: int = Query(10, ge=1, le=100, description="Размер страницы"),
    db: Session = Depends(get_db)
):
    """Получение истории депозитов или выводов пользователя"""
    return BalanceService.get_balance_history(
        db=db, user_id=user_id, currency=currency, from_date=from_date,
        to_date=to_date, page=page, limit=limit, event_type=type
    )

# ==============================================
# РОУТ ДЛЯ ПРОВЕРКИ РАБОТОСПОСОБНОСТИ
# ==============================================

@router.get("/balance/health")
async def health_check():
    """Проверка работоспособности API балансов"""
    return {"status": "OK", "service": "balance", "timestamp": datetime.utcnow().isoformat()}