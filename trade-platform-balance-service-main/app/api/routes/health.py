from fastapi import APIRouter, Depends
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session

from app.db.session import get_db

router = APIRouter(tags=["health"])


@router.get("/health")
async def health_check(db: Session = Depends(get_db)):
    try:
        db.execute("SELECT 1").fetchone()  # Добавить .fetchone()
        return {"status": "ok", "message": "Сервис работает нормально"}
    except Exception as e:
        return JSONResponse(status_code=500, content={"status": "error", "message": str(e)})