from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
from typing import Dict, Any, Optional
import logging
import json

from app.db.session import get_db
from app.core.services.balance_service import BalanceService
from app.api.dependencies import get_user_id

# Настройка логирования
logger = logging.getLogger(__name__)

router = APIRouter(prefix="/kafka", tags=["kafka"])

# Обновленная модель, которая соответствует данным с фронтенда
class KafkaMessageRequest(BaseModel):
    """Модель запроса для отправки сообщения в Kafka"""
    topic: str
    key: Optional[str] = None
    value: Dict[str, Any]

@router.post("/send", status_code=status.HTTP_202_ACCEPTED)
async def send_kafka_message(
    request: KafkaMessageRequest,
    db: Session = Depends(get_db)
):
    """
    Отправка сообщения в Kafka
    
    Используется для отправки событий от фронтенда в топики Kafka
    """
    try:
        logger.info(f"Получен запрос на отправку сообщения в Kafka: {request}")
        
        # Получаем user_id из value
        user_id = request.value.get("user_id")
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id отсутствует в данных"
            )
        
        # Преобразуем user_id в int
        try:
            user_id = int(user_id)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="user_id должен быть числом"
            )
        
        # Проверяем тип сообщения и обрабатываем соответствующе
        event_type = request.value.get("event_type")
        operation_type = request.value.get("operation_type")
        
        if event_type == "trade_operation":
            # Для всех торговых операций (open_position, close_position, cancel_order) вызываем один обработчик
            from app.kafka.event_handlers import handle_trade_operation
            await handle_trade_operation(request.value)
            
            return {"status": "accepted", "message": "Сообщение успешно обработано"}
        else:
            logger.info(f"Обработка сообщения для топика {request.topic} не реализована")
            return {"status": "accepted", "message": "Сообщение получено, но обработка не реализована"}
            
    except HTTPException as e:
        raise e
    except Exception as e:
        logger.error(f"Ошибка при отправке сообщения в Kafka: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Ошибка при обработке сообщения: {str(e)}"
        )
