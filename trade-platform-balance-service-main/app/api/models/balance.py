from datetime import datetime
from decimal import Decimal
from enum import Enum
from typing import List, Optional, Dict
from pydantic import BaseModel, Field, validator


class EventType(str, Enum):
    """Типы событий для истории баланса"""
    DEPOSIT = "deposit"
    WITHDRAWAL = "withdrawal"
    TRANSFER = "transfer"


class BalanceItem(BaseModel):
    """Модель для отображения баланса по валюте"""
    currency: str
    amount: str
    equivalent_usd: Optional[str] = None
    
    @validator('amount', 'equivalent_usd', pre=True)
    def format_decimal(cls, v, values):
        """Преобразование Decimal в строку с фиксированной точностью"""
        if isinstance(v, Decimal):
            # Для BTC, ETH используем 8 десятичных знаков, для остальных 2
            if 'currency' in values and values['currency'] in ["BTC", "ETH"]:
                return f"{v:.8f}"
            return f"{v:.2f}"
        return v
    
    class Config:
        orm_mode = True


class BalanceResponse(BaseModel):
    """Ответ на запрос баланса"""
    balances: List[BalanceItem]
    total_equivalent_usd: Optional[str] = None
    
    class Config:
        orm_mode = True


# ⭐ ИСПРАВЛЕННАЯ МОДЕЛЬ - ДОБАВЛЕНЫ НЕДОСТАЮЩИЕ ПОЛЯ
class HistoryItem(BaseModel):
    """Модель для отображения истории изменений баланса"""
    id: str                                    # ⭐ ДОБАВЛЕНО
    userId: int                                # ⭐ ДОБАВЛЕНО
    currency: str
    amount_change: str
    transaction_hash: Optional[str] = None
    event_type: EventType
    timestamp: datetime
    is_processed: bool                         # ⭐ ДОБАВЛЕНО
    
    @validator('amount_change', pre=True)
    def format_decimal(cls, v):
        """Преобразование Decimal в строку с фиксированной точностью"""
        if isinstance(v, Decimal):
            # Для BTC, ETH используем 8 десятичных знаков, для остальных 2
            if hasattr(cls, 'currency') and cls.currency in ["BTC", "ETH"]:
                return f"{v:.8f}"
            return f"{v:.2f}"
        return v
    
    class Config:
        orm_mode = True


class HistoryResponse(BaseModel):
    """Ответ на запрос истории баланса"""
    items: List[HistoryItem]
    total: int
    page: int
    limit: int
    pages: int


# ОСТАЛЬНОЙ КОД БЕЗ ИЗМЕНЕНИЙ...

class PeriodEnum(str, Enum):
    """Перечисление периодов для статистики"""
    DAY = "day"
    WEEK = "week"
    MONTH = "month"
    YEAR = "year"


class SummaryItem(BaseModel):
    """Модель для отображения суммарной статистики по валюте"""
    currency: str
    amount: str
    equivalent_usd: Optional[str] = None


class SummaryResponse(BaseModel):
    """Ответ на запрос суммарной статистики"""
    total_deposits: Dict[str, str]
    total_withdrawals: Dict[str, str]
    net_change: Dict[str, str]
    
    @validator('total_deposits', 'total_withdrawals', 'net_change', pre=True)
    def format_amounts(cls, v):
        """Преобразование Decimal в строку с фиксированной точностью"""
        result = {}
        for currency, amount in v.items():
            if isinstance(amount, Decimal):
                # Для BTC, ETH используем 8 десятичных знаков, для остальных 2
                if currency in ["BTC", "ETH"]:
                    result[currency] = f"{amount:.8f}"
                else:
                    result[currency] = f"{amount:.2f}"
            else:
                result[currency] = amount
        return result


class BalanceHistoryRequest(BaseModel):
    """Запрос истории баланса"""
    currency: Optional[str] = None
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None
    page: int = Field(default=1, ge=1)
    limit: int = Field(default=10, ge=1, le=100)


class TransactionEventSchema(BaseModel):
    """Схема события о транзакции из Kafka"""
    event_type: str = "transaction_update"
    user_id: int
    amount: Decimal
    currency: str
    tx_hash: str
    status: str
    timestamp: datetime


class DepositRequestModel(BaseModel):
    """Модель запроса на создание депозита"""
    currency: str
    amount: Decimal
    temporary_id: Optional[str] = None

class WithdrawalRequestModel(BaseModel):
    """Модель запроса на создание вывода"""
    currency: str
    amount: Decimal
    address: str
    temporary_id: Optional[str] = None

class DepositAddressResponse(BaseModel):
    """Ответ с адресом для депозита"""
    address: str
    currency: str
    network: str
    min_amount: Decimal
    temporary_id: str
    
class TransactionRequestResponse(BaseModel):
    """Ответ на запрос создания транзакции"""
    id: str
    user_id: int
    currency: str
    amount: str
    transaction_hash: Optional[str] = None
    event_type: EventType
    is_processed: bool
    timestamp: datetime
    temporary_id: Optional[str] = None
    
    class Config:
        orm_mode = True


class FixedBalanceResponse(BaseModel):
    """Ответ на запрос фиксированного баланса"""
    fixed_usd_balance: str
    locked_usd_balance: str
    available_usd_balance: str
    
    class Config:
        orm_mode = True