from fastapi import Query
from typing import Dict, Any

async def get_user_id(user_id: int = Query(..., description="ID пользователя")) -> Dict[str, Any]:
    """
    Простая функция для получения ID пользователя из параметров запроса
    
    Args:
        user_id: ID пользователя из параметра запроса
        
    Returns:
        Dict[str, Any]: Словарь с данными пользователя
    """
    return {"id": user_id}