import json
import os
from pydantic_settings import BaseSettings
from typing import List
from pydantic import validator


class Settings(BaseSettings):
    """Настройки приложения"""
    
    # Основные настройки приложения
    APP_NAME: str = "balance-service"
    DEBUG: bool = False  # Включаем режим отладки
    API_PREFIX: str = "/api"
    PORT: int = int(os.getenv("PORT", "8005"))
    LOG_LEVEL: str = "INFO"  # Подробное логирование
    
    # CORS настройки
    CORS_ORIGINS: List[str] = ["*"]
    CORS_ALLOW_CREDENTIALS: bool = True
    CORS_ALLOW_METHODS: List[str] = ["*"]
    CORS_ALLOW_HEADERS: List[str] = ["*"]

    
    # Настройки базы данных
    DATABASE_URL: str
    
    # Настройки Kafka
    KAFKA_BOOTSTRAP_SERVERS: str
    KAFKA_TRANSACTION_TOPIC: str = "transaction-events"
    KAFKA_BALANCE_TOPIC: str = "balance-operations" 
    KAFKA_GROUP_ID: str = "balance-service-group"
    
    # Настройки криптовалют
    SUPPORTED_CURRENCIES: List[str] = ["BTC", "ETH", "USDT", "USDC", "TRX"]

    # Настройки API цен криптовалют
    CRYPTO_PRICE_API_URL: str = "https://api.binance.com/api/v3"
    CRYPTO_PRICE_API_KEY: str = ""
    
    # # Настройки блокчейн API
    # BTC_API_URL: str = "https://api.binance.com/api/v3"
    # BTC_API_KEY: str = ""
    
    # ETH_API_URL: str = "https://api.binance.com/api/v3"
    # ETH_API_KEY: str = ""
    
    # TRON_API_URL: str = "https://api.binance.com/api/v3"
    # TRON_API_KEY: str = ""
    
    # Настройки кэширования цен
    PRICE_CACHE_TTL: int = 300  # время жизни кэша цен в секундах (5 минут)


      # Валютные пары для Binance API
    BINANCE_PAIRS: dict = {
        "BTC": "BTCUSDT", 
        "ETH": "ETHUSDT",
        "USDT": "USDTUSDC",  # или можно использовать фиксированное соотношение 1:1
        "USDC": "USDCUSDT",  # может быть обратной парой
        "TRX": "TRXUSDT"     # добавлен TRX
    }

    @validator('CORS_ORIGINS', 'CORS_ALLOW_METHODS', 'CORS_ALLOW_HEADERS', 'SUPPORTED_CURRENCIES', pre=True)
    def parse_json_string(cls, v):
        if isinstance(v, str):
            try:
                # Пытаемся распарсить как JSON
                return json.loads(v)
            except json.JSONDecodeError:
                # Если не JSON, разбиваем по запятой
                return [item.strip() for item in v.split(',')]
        return v
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"


# Создание экземпляра настроек
settings = Settings()