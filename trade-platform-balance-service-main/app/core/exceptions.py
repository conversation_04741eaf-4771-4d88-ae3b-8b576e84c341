from fastapi import HTTPException, status


class BalanceException(Exception):
    """Базовый класс для исключений сервиса балансов"""
    pass


class InsufficientFundsException(BalanceException):
    """Исключение при недостаточных средствах"""
    
    def __init__(self, user_id: int, currency: str, required: float, available: float):
        self.user_id = user_id
        self.currency = currency
        self.required = required
        self.available = available
        self.message = (
            f"Недостаточно средств. Пользователь: {user_id}, "
            f"Валюта: {currency}, Требуется: {required}, Доступно: {available}"
        )
        super().__init__(self.message)


class TransactionAlreadyProcessedException(BalanceException):
    """Исключение при попытке повторной обработки транзакции"""
    
    def __init__(self, tx_hash: str):
        self.tx_hash = tx_hash
        self.message = f"Транзакция {tx_hash} уже обработана"
        super().__init__(self.message)


class BalanceNotFoundException(BalanceException):
    """Исключение, когда баланс пользователя не найден"""
    
    def __init__(self, user_id: int, currency: str):
        self.user_id = user_id
        self.currency = currency
        self.message = f"Баланс не найден. Пользователь: {user_id}, Валюта: {currency}"
        super().__init__(self.message)


def handle_balance_exception(exc: BalanceException):
    """Преобразование исключений сервиса в HTTP исключения для API"""
    
    if isinstance(exc, InsufficientFundsException):
        return HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=exc.message
        )
    elif isinstance(exc, TransactionAlreadyProcessedException):
        return HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=exc.message
        )
    elif isinstance(exc, BalanceNotFoundException):
        return HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=exc.message
        )
    else:
        return HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(exc)
        )