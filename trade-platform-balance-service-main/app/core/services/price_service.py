import logging
import aiohttp
from decimal import Decimal
from typing import Dict, Optional
from app.config import settings

logger = logging.getLogger(__name__)

class PriceService:
    """Сервис для получения курсов криптовалют в USD"""
    
    # Кэш для хранения курсов валют
    _price_cache = {}
    _last_update = {}
    
    @staticmethod
    async def get_currency_price(currency: str) -> Optional[Decimal]:
        """
        Получение текущего курса валюты в USD
        
        Args:
            currency: Код валюты (BTC, ETH, USDT, etc.)
            
        Returns:
            Optional[Decimal]: Текущий курс валюты в USD или None в случае ошибки
        """
        import time
        
        # Для стейблкоинов возвращаем 1:1
        if currency in ["USDT", "USDC"]:
            return Decimal('1.0')
        
        # Проверяем кэш
        current_time = time.time()
        if (currency in PriceService._price_cache and 
            current_time - PriceService._last_update.get(currency, 0) < settings.PRICE_CACHE_TTL):
            logger.debug(f"Используем кэшированный курс для {currency}: {PriceService._price_cache[currency]}")
            return PriceService._price_cache[currency]
        
        # Получаем курс из соответствующего API
        try:
            price = None
            
            if currency == "BTC":
                price = await PriceService._get_btc_price()
            elif currency == "ETH":
                price = await PriceService._get_eth_price()
            elif currency == "TRX":
                price = await PriceService._get_tron_price()
            else:
                # Для других валют можно использовать публичные API или отдельный сервис котировок
                logger.warning(f"Нет метода получения курса для валюты {currency}")
                return None
            
            if price:
                # Обновляем кэш
                PriceService._price_cache[currency] = price
                PriceService._last_update[currency] = current_time
                logger.info(f"Обновлен курс для {currency}: {price} USD")
                return price
            
            return None
        except Exception as e:
            logger.error(f"Ошибка при получении курса для {currency}: {str(e)}")
            # Если есть кэшированная цена, возвращаем её даже если она устарела
            if currency in PriceService._price_cache:
                logger.warning(f"Используем устаревший кэшированный курс для {currency}")
                return PriceService._price_cache[currency]
            return None
    
    @staticmethod
    async def _get_btc_price() -> Optional[Decimal]:
        """
        Получение курса BTC/USD с Binance
        
        Returns:
            Optional[Decimal]: Текущий курс BTC в USD или None в случае ошибки
        """
        try:
            async with aiohttp.ClientSession() as session:
                pair = settings.BINANCE_PAIRS.get("BTC")
                url = f"{settings.CRYPTO_PRICE_API_URL}/ticker/price?symbol={pair}"
                
                logger.info(f"Запрос цены для BTC, URL: {url}")
                
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        price = Decimal(str(data.get("price", 0)))
                        return price
                    else:
                        logger.error(f"Ошибка при запросе курса BTC. Статус: {response.status}")
                        # Если не удалось получить данные, возвращаем дефолтную цену
                        return Decimal('85000')  # Примерная текущая цена BTC
        except Exception as e:
            logger.error(f"Исключение при получении курса BTC: {str(e)}")
            return Decimal('85000')  # Примерная текущая цена BTC
    
    @staticmethod
    async def _get_eth_price() -> Optional[Decimal]:
        """
        Получение курса ETH/USD с Binance
        
        Returns:
            Optional[Decimal]: Текущий курс ETH в USD или None в случае ошибки
        """
        try:
            async with aiohttp.ClientSession() as session:
                pair = settings.BINANCE_PAIRS.get("ETH")
                url = f"{settings.CRYPTO_PRICE_API_URL}/ticker/price?symbol={pair}"
                
                logger.info(f"Запрос цены для ETH, URL: {url}")
                
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        price = Decimal(str(data.get("price", 0)))
                        return price
                    else:
                        logger.error(f"Ошибка при запросе курса ETH. Статус: {response.status}")
                        # Если не удалось получить данные, возвращаем дефолтную цену
                        return Decimal('3800')  # Примерная текущая цена ETH
        except Exception as e:
            logger.error(f"Исключение при получении курса ETH: {str(e)}")
            return Decimal('3800')  # Примерная текущая цена ETH
    
    @staticmethod
    async def _get_tron_price() -> Optional[Decimal]:
        """
        Получение курса TRX/USD с Binance
        
        Returns:
            Optional[Decimal]: Текущий курс TRX в USD или None в случае ошибки
        """
        try:
            async with aiohttp.ClientSession() as session:
                pair = settings.BINANCE_PAIRS.get("TRX")
                url = f"{settings.CRYPTO_PRICE_API_URL}/ticker/price?symbol={pair}"
                
                logger.info(f"Запрос цены для TRX, URL: {url}")
                
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        price = Decimal(str(data.get("price", 0)))
                        return price
                    else:
                        logger.error(f"Ошибка при запросе курса TRX. Статус: {response.status}")
                        return Decimal('0.23')  # Примерная текущая цена TRX
        except Exception as e:
            logger.error(f"Исключение при получении курса TRX: {str(e)}")
            return Decimal('0.23')  # Примерная текущая цена TRX
    
    @staticmethod
    async def get_all_prices() -> Dict[str, Decimal]:
        """
        Получение курсов всех поддерживаемых валют
        
        Returns:
            Dict[str, Decimal]: Словарь с курсами валют {код_валюты: курс_в_usd}
        """
        # Список поддерживаемых валют
        currencies = settings.SUPPORTED_CURRENCIES
        
        result = {}
        for currency in currencies:
            price = await PriceService.get_currency_price(currency)
            if price:
                result[currency] = price
        
        return result