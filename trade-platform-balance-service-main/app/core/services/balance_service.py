import asyncio
import logging
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.db.repositories.balance_repository import BalanceRepository
from app.db.models.balance import BalanceHistory, UserBalance, UserFixedBalance
from app.api.models.balance import BalanceItem, BalanceResponse, FixedBalanceResponse, HistoryResponse, SummaryResponse, PeriodEnum
from app.core.services.price_service import PriceService  # Импортируем новый сервис
from sqlalchemy import text
from decimal import Decimal

logger = logging.getLogger(__name__)


class BalanceService:
    """Сервис для управления балансами пользователей"""
    
    @staticmethod
    async def get_user_balances(db: Session, user_id: int, currency: Optional[str] = None) -> BalanceResponse:
        """
        Получение балансов пользователя с актуальными USD эквивалентами
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты (опционально)
            
        Returns:
            BalanceResponse: Ответ с балансами пользователя
        """
        # Получаем балансы из БД
        db_balances = BalanceRepository.get_user_balances(db, user_id, currency)
        
        # Получаем текущие курсы всех валют для актуализации данных
        current_prices = await PriceService.get_all_prices()
        
        # Преобразуем в DTO модели
        balances = []
        total_usd_value = Decimal('0')
        
        for balance in db_balances:
            # Получаем текущий курс валюты
            current_price = current_prices.get(balance.currency)
            
            # Если цена получена, обновляем эквивалент в USD и курс в базе
            if current_price and (
                balance.price_usd is None or 
                abs(balance.price_usd - current_price) / current_price > Decimal('0.005')  # Изменение более 0.5%
            ):
                equivalent_usd = balance.amount * current_price
                
                # Обновляем запись в базе
                balance.equivalent_usd = equivalent_usd
                balance.price_usd = current_price
                balance.last_updated = datetime.utcnow()
                db.commit()
            elif balance.equivalent_usd is None and balance.currency in ["USDT", "USDC"]:
                # Для стейблкоинов, если эквивалент не задан, используем 1:1
                equivalent_usd = balance.amount
                balance.equivalent_usd = equivalent_usd
                balance.price_usd = Decimal('1')
                balance.last_updated = datetime.utcnow()
                db.commit()
            else:
                # Используем сохраненный эквивалент
                equivalent_usd = balance.equivalent_usd
            
            # Добавляем к общей сумме в USD
            if equivalent_usd:
                total_usd_value += equivalent_usd
            
            balances.append(
                BalanceItem(
                    currency=balance.currency,
                    amount=balance.amount,
                    equivalent_usd=str(equivalent_usd) if equivalent_usd else None
                )
            )
        
        # Логирование для отладки
        logger.info(f"Получены балансы для пользователя {user_id} с общей суммой {total_usd_value} USD")
        
        return BalanceResponse(
            balances=balances,
            total_equivalent_usd=f"{total_usd_value:.2f}" if total_usd_value > 0 else None
        )
    
    @staticmethod
    def get_balance_history(
        db: Session,
        user_id: int,
        currency: Optional[str] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
        page: int = 1,
        limit: int = 10,
        event_type: Optional[str] = None
    ) -> HistoryResponse:
        """
        Получение истории изменений баланса пользователя с фильтрацией по типу события
        """
        logger.info(f"Запрос истории для пользователя {user_id}, тип события: {event_type}")
        
        # Получаем данные из репозитория с дополнительным фильтром по типу события
        history_data = BalanceRepository.get_balance_history(
            db, user_id, currency, from_date, to_date, page, limit, event_type
        )
        
        logger.info(f"Получено записей из репозитория: {len(history_data['items'])}")
        
        # Преобразуем элементы истории в DTO с правильными типами данных
        items = []
        for item in history_data["items"]:
            # Создаем правильный объект с нужными полями
            history_item = {
                "id": str(item.id),  # UUID преобразуем в строку
                "currency": item.currency,
                "amount_change": str(item.amount_change),  # Decimal в строку
                "transaction_hash": item.transaction_hash,
                "event_type": item.event_type,
                "timestamp": item.timestamp.isoformat() if hasattr(item.timestamp, 'isoformat') else str(item.timestamp),
                "is_processed": bool(item.is_processed),  # ✅ КРИТИЧНО: преобразуем в boolean
                "userId": user_id  # Добавляем userId для фронтенда
            }
            items.append(history_item)
            
            # Логируем первую запись для отладки
            if len(items) == 1:
                logger.info(f"Первая запись после обработки: {history_item}")
        
        logger.info(f"Отправляем {len(items)} записей истории для пользователя {user_id}")
        
        return HistoryResponse(
            items=items,
            total=history_data["total"],
            page=history_data["page"],
            limit=history_data["limit"],
            pages=history_data["pages"]
        )
    
    @staticmethod
    def get_summary_statistics(
        db: Session,
        user_id: int,
        period: Optional[PeriodEnum] = None
    ) -> SummaryResponse:
        """
        Получение суммарной статистики по изменениям баланса
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            period: Период (опционально)
            
        Returns:
            SummaryResponse: Ответ со статистикой
        """
        # Определяем временной интервал на основе периода
        from_date = None
        to_date = datetime.utcnow()
        
        if period:
            if period == PeriodEnum.DAY:
                from_date = to_date - timedelta(days=1)
            elif period == PeriodEnum.WEEK:
                from_date = to_date - timedelta(weeks=1)
            elif period == PeriodEnum.MONTH:
                from_date = to_date - timedelta(days=30)
            elif period == PeriodEnum.YEAR:
                from_date = to_date - timedelta(days=365)
        
        # Получаем статистику из репозитория
        summary_data = BalanceRepository.get_summary_statistics(db, user_id, from_date, to_date)
        
        return SummaryResponse(
            total_deposits=summary_data["total_deposits"],
            total_withdrawals=summary_data["total_withdrawals"],
            net_change=summary_data["net_change"]
        )
    
    @staticmethod
    async def process_transaction_event(
        db: Session,
        user_id: int,
        amount: Decimal,
        currency: str,
        tx_hash: str,
        status: str
    ) -> bool:
        """
        Обработка события о транзакции с обновлением USD эквивалента
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            amount: Сумма транзакции
            currency: Код валюты
            tx_hash: Хеш транзакции
            status: Статус транзакции
            
        Returns:
            bool: True, если обработка успешна
        """
        try:
            # Проверяем статус транзакции
            if status != "confirmed":
                logger.info(f"Транзакция {tx_hash} еще не подтверждена. Статус: {status}")
                return False
            
            # Проверяем, не обработана ли уже эта транзакция
            existing_history = db.query(
                BalanceHistory
            ).filter_by(
                transaction_hash=tx_hash
            ).first()
            
            if existing_history and existing_history.is_processed:
                logger.info(f"Транзакция {tx_hash} уже обработана. Пропускаем.")
                return False
            
            # Положительная сумма - это депозит, отрицательная - вывод средств
            event_type = "deposit" if amount > 0 else "withdrawal"
            
            # Получаем текущий курс валюты в USD
            current_price = await PriceService.get_currency_price(currency)
            
            if current_price is None and currency not in ["USDT", "USDC"]:
                logger.warning(f"Не удалось получить курс для валюты {currency}.")
                # Для не-стейблкоинов без курса пропускаем обновление USD эквивалента
                # но продолжаем обработку транзакции
            
            # Создаем запись в истории изменений баланса
            BalanceRepository.add_balance_history(
                db=db,
                user_id=user_id,
                currency=currency,
                amount_change=amount,
                transaction_hash=tx_hash,
                event_type=event_type
            )
            
            # Обновляем баланс пользователя
            updated_balance = BalanceRepository.create_or_update_balance(
                db=db,
                user_id=user_id,
                currency=currency,
                amount_change=amount
            )
            
            # Обновляем USD эквивалент в балансе
            if current_price is not None:
                # Рассчитываем эквивалент в USD
                if currency in ["USDT", "USDC"]:
                    equivalent_usd = updated_balance.amount  # Для стейблкоинов 1:1
                    updated_balance.price_usd = Decimal('1')
                else:
                    equivalent_usd = updated_balance.amount * current_price
                    updated_balance.price_usd = current_price
                
                updated_balance.equivalent_usd = equivalent_usd
                db.commit()
            
            logger.info(
                f"Успешно обработана транзакция {tx_hash}. "
                f"Пользователь: {user_id}, Сумма: {amount} {currency}, Тип: {event_type}"
            )
            
            # Получаем обновленные балансы с учетом USD эквивалентов
            balances_response = await BalanceService.get_user_balances(db, user_id)
            
            # Импортируем send_balance_update из websocket модуля
            from app.websocket import send_balance_update
            
            # Подготовка данных для отправки через WebSocket
            logger.info("Подготовка данных для отправки через WebSocket")
            
            # Формируем список балансов с USD эквивалентами
            balances_list = []
            for b in balances_response.balances:
                balance_item = {
                    "currency": b.currency,
                    "amount": str(b.amount)
                }
                # Добавляем equivalent_usd, только если он есть
                if hasattr(b, 'equivalent_usd') and b.equivalent_usd is not None:
                    balance_item["equivalent_usd"] = str(b.equivalent_usd)
                balances_list.append(balance_item)
            
            # Логируем список балансов
            logger.info(f"Список балансов для отправки: {balances_list}")
            
            # Формируем итоговые данные для WebSocket
            websocket_data = {
                "balances": balances_list
            }
            
            # Добавляем total_equivalent_usd, только если он есть
            if hasattr(balances_response, 'total_equivalent_usd') and balances_response.total_equivalent_usd is not None:
                websocket_data["total_equivalent_usd"] = balances_response.total_equivalent_usd
                logger.info(f"Добавлен total_equivalent_usd: {balances_response.total_equivalent_usd}")
            else:
                logger.warning("total_equivalent_usd отсутствует или равен None")
            
            # Логируем итоговые данные
            logger.info(f"Данные для отправки в WebSocket: {websocket_data}")
            
            # Отправляем обновление баланса через WebSocket
            asyncio.create_task(send_balance_update(user_id, websocket_data))
            
            return True
        except Exception as e:
            logger.error(f"Ошибка при обработке транзакции {tx_hash}: {str(e)}")
            db.rollback()
            return False
        

    @staticmethod
    async def get_user_fixed_balance(db: Session, user_id: int) -> FixedBalanceResponse:
        """
        Получение фиксированного USD баланса пользователя
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
                
        Returns:
            FixedBalanceResponse: Ответ с фиксированным балансом
        """
        # Получаем баланс
        balance = BalanceRepository.get_user_fixed_balance(db, user_id)
        
        # Если баланса нет, инициализируем его
        if not balance:
            logger.info(f"Фиксированный баланс не найден для пользователя {user_id}, инициализируем")
            balance = await BalanceService.initialize_fixed_balance(db, user_id)
        
        # Расчет доступного баланса
        available_balance = balance.fixed_usd_balance - balance.locked_usd_balance
        
        return FixedBalanceResponse(
            fixed_usd_balance=f"{balance.fixed_usd_balance:.2f}",
            locked_usd_balance=f"{balance.locked_usd_balance:.2f}",
            available_usd_balance=f"{available_balance:.2f}"
        )
    
    @staticmethod
    async def process_transaction_event(
        db: Session,
        user_id: int,
        amount: Decimal,
        currency: str,
        tx_hash: str,
        status: str
    ) -> bool:
        """
        Обработка события о транзакции с обновлением фиксированного USD баланса
        """
        try:
            # Проверяем статус транзакции
            if status != "confirmed":
                logger.info(f"Транзакция {tx_hash} еще не подтверждена. Статус: {status}")
                return False
            
            # Проверяем, не обработана ли уже эта транзакция
            existing_history = db.query(
                BalanceHistory
            ).filter_by(
                transaction_hash=tx_hash
            ).first()
            
            if existing_history and existing_history.is_processed:
                logger.info(f"Транзакция {tx_hash} уже обработана. Пропускаем.")
                return False
            
            # Положительная сумма - это депозит, отрицательная - вывод средств
            event_type = "deposit" if amount > 0 else "withdrawal"
            
            # Получаем текущий курс валюты в USD
            current_price = await PriceService.get_currency_price(currency)
            
            # Для USDT и USDC курс 1:1
            if currency in ["USDT", "USDC"]:
                current_price = Decimal('1.0')
            
            if current_price is None and currency not in ["USDT", "USDC"]:
                logger.warning(f"Не удалось получить курс для валюты {currency}.")
                # Для валют без курса прерываем обработку
                return False
            
            # Создаем запись в истории изменений баланса
            BalanceRepository.add_balance_history(
                db=db,
                user_id=user_id,
                currency=currency,
                amount_change=amount,
                transaction_hash=tx_hash,
                event_type=event_type
            )
            
            # Обновляем баланс пользователя
            updated_balance = BalanceRepository.create_or_update_balance(
                db=db,
                user_id=user_id,
                currency=currency,
                amount_change=amount
            )
            
            # Обновляем USD эквивалент в обычном балансе
            equivalent_usd = updated_balance.amount * current_price
            updated_balance.equivalent_usd = equivalent_usd
            updated_balance.price_usd = current_price
            db.commit()
            
            # Обновляем фиксированный USD баланс
            if event_type == "deposit":
                # При депозите добавляем USD эквивалент к фиксированному балансу
                usd_amount = amount * current_price
                BalanceRepository.update_fixed_balance(
                    db=db,
                    user_id=user_id,
                    amount_change=usd_amount,
                    is_locked=False
                )
            elif event_type == "withdrawal":
                # При выводе вычитаем USD эквивалент из фиксированного баланса
                usd_amount = abs(amount * current_price)
                BalanceRepository.update_fixed_balance(
                    db=db,
                    user_id=user_id,
                    amount_change=-usd_amount,
                    is_locked=False
                )
            
            logger.info(
                f"Успешно обработана транзакция {tx_hash}. "
                f"Пользователь: {user_id}, Сумма: {amount} {currency}, Тип: {event_type}"
            )
            
            # Получаем фиксированный баланс для отправки на фронтенд
            fixed_balance_response = await BalanceService.get_user_fixed_balance(db, user_id)
            
            # Импортируем send_balance_update из websocket модуля
            from app.websocket import send_balance_update
            
            # Данные для отправки через WebSocket
            websocket_data = {
                "fixed_usd_balance": fixed_balance_response.fixed_usd_balance,
                "locked_usd_balance": fixed_balance_response.locked_usd_balance,
                "available_usd_balance": fixed_balance_response.available_usd_balance
            }
            
            # Отправляем обновление баланса через WebSocket
            asyncio.create_task(send_balance_update(user_id, websocket_data))
            
            return True
        except Exception as e:
            logger.error(f"Ошибка при обработке транзакции {tx_hash}: {str(e)}")
            db.rollback()
            return False
        
    
    @staticmethod
    async def initialize_fixed_balance(db: Session, user_id: int) -> UserFixedBalance:
        """
        Инициализирует фиксированный USD баланс пользователя, если он не существует.
        Также пересчитывает его на основе имеющихся криптовалютных балансов.
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            
        Returns:
            UserFixedBalance: Фиксированный баланс пользователя
        """
        # Проверяем существование фиксированного баланса
        fixed_balance = BalanceRepository.get_user_fixed_balance(db, user_id)
        
        if fixed_balance:
            return fixed_balance
        
        logger.info(f"Инициализация фиксированного USD баланса для пользователя {user_id}")
        
        # Получаем текущие балансы пользователя
        balances = BalanceRepository.get_user_balances(db, user_id)
        
        # Получаем текущие курсы валют
        current_prices = await PriceService.get_all_prices()
        
        # Рассчитываем общий USD эквивалент
        total_usd = Decimal('0')
        
        for balance in balances:
            if balance.currency in ["USDT", "USDC"]:
                # Для стейблкоинов 1:1
                equivalent_usd = balance.amount
            elif balance.currency in current_prices:
                # Для остальных валют используем текущий курс
                price = current_prices[balance.currency]
                equivalent_usd = balance.amount * price
            else:
                # Если не удалось получить курс, используем сохраненный эквивалент
                equivalent_usd = balance.equivalent_usd or Decimal('0')
            
            # Добавляем к общей сумме
            total_usd += equivalent_usd
            
            # Обновляем USD эквивалент в балансе, если необходимо
            if equivalent_usd != balance.equivalent_usd or balance.price_usd is None:
                balance.equivalent_usd = equivalent_usd
                balance.price_usd = current_prices.get(balance.currency, Decimal('1'))
                balance.last_updated = datetime.utcnow()
        
        # Фиксируем изменения балансов
        db.commit()
        
        # Создаем запись фиксированного баланса
        fixed_balance = UserFixedBalance(
            user_id=user_id,
            fixed_usd_balance=total_usd,
            locked_usd_balance=Decimal('0'),
            last_updated=datetime.utcnow()
        )
        
        db.add(fixed_balance)
        db.commit()
        db.refresh(fixed_balance)
        
        logger.info(f"Создан фиксированный USD баланс для пользователя {user_id}: {total_usd}")
        
        return fixed_balance
    

    @staticmethod
    async def get_user_available_balance_only(db: Session, user_id: int) -> float:
        """
        ПОЛУЧЕНИЕ ТОЛЬКО ДОСТУПНОГО БАЛАНСА ПОЛЬЗОВАТЕЛЯ
        Быстрая функция для отображения в таблице
        """
        logger.info(f"📊 Получение доступного баланса для пользователя {user_id}")
        
        try:
            # Пытаемся получить из фиксированного баланса
            fixed_balance = BalanceRepository.get_user_fixed_balance(db, user_id)
            
            if fixed_balance:
                available = float(fixed_balance.fixed_usd_balance - fixed_balance.locked_usd_balance)
                logger.info(f"✅ Найден фиксированный баланс: {available}")
                return available
            else:
                # Если нет фиксированного, создаем с нулевым балансом
                logger.info(f"⚡ Создаем новый фиксированный баланс для пользователя {user_id}")
                new_balance = UserFixedBalance(
                    user_id=user_id,
                    fixed_usd_balance=Decimal('0'),
                    locked_usd_balance=Decimal('0'),
                    last_updated=datetime.utcnow()
                )
                db.add(new_balance)
                db.commit()
                return 0.0
                
        except Exception as e:
            logger.error(f"❌ Ошибка при получении баланса для пользователя {user_id}: {str(e)}")
            return 0.0

    @staticmethod
    async def get_bulk_available_balances(db: Session, user_ids: List[int]) -> Dict[int, float]:
        """
        МАССОВОЕ ПОЛУЧЕНИЕ ДОСТУПНЫХ БАЛАНСОВ
        Одним запросом получаем балансы всех пользователей
        """
        logger.info(f"📊 Массовое получение балансов для {len(user_ids)} пользователей")
        
        try:
            if not user_ids:
                return {}
            
            # SQL запрос для получения всех балансов одним запросом
            query = text("""
                SELECT 
                    user_id,
                    COALESCE(fixed_usd_balance - locked_usd_balance, 0) as available_balance
                FROM user_fixed_balances 
                WHERE user_id = ANY(:user_ids)
            """)
            
            result = db.execute(query, {"user_ids": user_ids}).fetchall()
            logger.info(f"📋 Получено из БД: {len(result)} записей")
            
            # Создаем словарь результатов
            balances = {}
            found_user_ids = set()
            
            for row in result:
                balances[row.user_id] = float(row.available_balance)
                found_user_ids.add(row.user_id)
            
            # Для пользователей без записей создаем нулевые балансы
            missing_user_ids = set(user_ids) - found_user_ids
            if missing_user_ids:
                logger.info(f"⚡ Создаем нулевые балансы для {len(missing_user_ids)} пользователей")
                for user_id in missing_user_ids:
                    balances[user_id] = 0.0
                    # Создаем запись в БД
                    try:
                        new_balance = UserFixedBalance(
                            user_id=user_id,
                            fixed_usd_balance=Decimal('0'),
                            locked_usd_balance=Decimal('0'),
                            last_updated=datetime.utcnow()
                        )
                        db.add(new_balance)
                    except Exception:
                        pass  # Игнорируем ошибки дублирования
                
                try:
                    db.commit()
                except Exception:
                    db.rollback()
            
            logger.info(f"✅ Возвращаем {len(balances)} балансов")
            return balances
            
        except Exception as e:
            logger.error(f"❌ Ошибка при массовом получении балансов: {str(e)}")
            # Возвращаем нулевые балансы для всех пользователей
            return {user_id: 0.0 for user_id in user_ids}

    @staticmethod
    async def update_user_fixed_balance(
        db: Session, 
        user_id: int, 
        new_balance: float
    ):
        """
        ОБНОВЛЕНИЕ ФИКСИРОВАННОГО БАЛАНСА ПОЛЬЗОВАТЕЛЯ
        Устанавливает новое значение без записи в историю
        """
        logger.info(f"💰 Обновление баланса пользователя {user_id}: {new_balance}")
        
        try:
            # Получаем или создаем запись
            fixed_balance = db.query(UserFixedBalance).filter_by(user_id=user_id).first()
            
            if not fixed_balance:
                logger.info(f"⚡ Создаем новую запись баланса для пользователя {user_id}")
                fixed_balance = UserFixedBalance(
                    user_id=user_id,
                    fixed_usd_balance=Decimal(str(new_balance)),
                    locked_usd_balance=Decimal('0'),
                    last_updated=datetime.utcnow()
                )
                db.add(fixed_balance)
            else:
                logger.info(f"📝 Обновляем существующую запись: {fixed_balance.fixed_usd_balance} -> {new_balance}")
                fixed_balance.fixed_usd_balance = Decimal(str(new_balance))
                fixed_balance.last_updated = datetime.utcnow()
                
                # ИСТОРИЯ ОТКЛЮЧЕНА - БЕЗ ЗАПИСИ В BALANCE_HISTORY
                logger.info(f"📝 Изменение баланса: {new_balance} (без записи в историю)")
            
            db.commit()
            db.refresh(fixed_balance)
            logger.info(f"✅ Баланс успешно обновлен для пользователя {user_id}")
            
        except Exception as e:
            db.rollback()
            logger.error(f"❌ Ошибка при обновлении баланса пользователя {user_id}: {str(e)}")
            raise


    @staticmethod
    def get_available_balance(db: Session, user_id: int) -> Decimal:
        """
        Получение доступного (незаблокированного) фиксированного USD баланса
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            
        Returns:
            Decimal: Доступный баланс в USD
        """
        balance = BalanceRepository.get_user_fixed_balance(db, user_id)
        
        if not balance:
            return Decimal('0')
        
        return balance.fixed_usd_balance - balance.locked_usd_balance
    

    @staticmethod
    def get_all_deposits_for_admin(
        db: Session,
        user_id: Optional[int] = None,
        search: Optional[str] = None,
        status: Optional[str] = None,
        currency: Optional[str] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
        page: int = 1,
        limit: int = 20
    ) -> HistoryResponse:
        
        logger.info(f"🎯 ADMIN: Запрос всех депозитов")
        
        try:
            query = db.query(BalanceHistory)
            
            # ✅ ИСПРАВЛЕНО: точное сравнение вместо LIKE
            query = query.filter(BalanceHistory.event_type == "deposit")
            
            # Опциональные фильтры
            if user_id:
                query = query.filter(BalanceHistory.user_id == user_id)
            if status == "processed":
                query = query.filter(BalanceHistory.is_processed == True)
            elif status == "pending":
                query = query.filter(BalanceHistory.is_processed == False)
            if currency:
                query = query.filter(BalanceHistory.currency == currency)
            if from_date:
                query = query.filter(BalanceHistory.timestamp >= from_date)
            if to_date:
                query = query.filter(BalanceHistory.timestamp <= to_date)
            
            query = query.order_by(BalanceHistory.timestamp.desc())
            
            total = query.count()
            offset = (page - 1) * limit
            items = query.offset(offset).limit(limit).all()
            
            history_items = []
            for item in items:
                history_items.append({
                    "id": str(item.id),
                    "userId": item.user_id,
                    "currency": item.currency,
                    "amount_change": str(item.amount_change),
                    "transaction_hash": item.transaction_hash,
                    "event_type": item.event_type,
                    "timestamp": item.timestamp.isoformat(),
                    "is_processed": bool(item.is_processed)
                })
            
            pages = (total + limit - 1) // limit
            
            logger.info(f"✅ ADMIN: Найдено {len(history_items)} депозитов из {total}")
            
            return HistoryResponse(
                items=history_items,
                total=total,
                page=page,
                limit=limit,
                pages=pages
            )
            
        except Exception as e:
            logger.error(f"❌ ADMIN: Ошибка: {str(e)}")
            return HistoryResponse(items=[], total=0, page=page, limit=limit, pages=0)