import logging
import json
from datetime import datetime
from typing import Dict, Any, Optional

from app.config import settings


class JSONFormatter(logging.Formatter):
    """
    Форматирование логов в структурированный JSON
    """
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Форматирование записи лога в JSON
        
        Args:
            record: Запись лога
            
        Returns:
            str: Форматированная запись лога в JSON
        """
        log_data = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "name": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Добавляем исключение, если оно есть
        if record.exc_info:
            log_data["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": self.formatException(record.exc_info),
            }
        
        # Добавляем дополнительные данные из extra
        if hasattr(record, "data") and isinstance(record.data, dict):
            log_data.update(record.data)
        
        return json.dumps(log_data)


def setup_logging(name: Optional[str] = None, level: Optional[str] = None) -> logging.Logger:
    """
    Настройка логирования
    
    Args:
        name: Имя логгера
        level: Уровень логирования
        
    Returns:
        logging.Logger: Настроенный логгер
    """
    log_level = getattr(logging, level or settings.LOG_LEVEL)
    logger_name = name or settings.APP_NAME
    
    # Создаем логгер
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)
    
    # Очищаем обработчики, если они уже существуют
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)
    
    # Создаем обработчик для вывода в консоль
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    
    # Устанавливаем форматтер для JSON
    formatter = JSONFormatter()
    console_handler.setFormatter(formatter)
    
    # Добавляем обработчик в логгер
    logger.addHandler(console_handler)
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """
    Получение логгера с заданным именем
    
    Args:
        name: Имя логгера
        
    Returns:
        logging.Logger: Логгер
    """
    return logging.getLogger(name)