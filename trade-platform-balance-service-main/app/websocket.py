import logging
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Query, status
from typing import Dict, List, Any, Optional
import json
import asyncio

from app.db.session import SessionLocal, get_db
from app.core.services.balance_service import BalanceService

logger = logging.getLogger(__name__)

# Хранение активных соединений: {user_id: WebSocket}
active_connections: Dict[int, WebSocket] = {}

async def setup_websocket_routes(app: FastAPI) -> None:
    @app.websocket("/ws/balance")
    async def websocket_endpoint(websocket: WebSocket, user_id: int = Query(...)):
        # Расширенное логирование для отладки
        client = f"{websocket.client.host}:{websocket.client.port}"
        logger.info(f"Получен запрос на WebSocket подключение от {client} с user_id={user_id}")
        
        # Логирование заголовков для отладки
        headers = dict(websocket.headers.items())
        origin = headers.get("origin", "Unknown")
        logger.info(f"Origin запроса: {origin}")
        
        # Принимаем соединение
        await websocket.accept()
        logger.info(f"WebSocket соединение принято для user_id={user_id}")
        
        # 🔥 ИСПРАВЛЕНИЕ: Инициализируем переменную db вне try блока
        db = None
        
        try:
            # Регистрируем соединение
            active_connections[user_id] = websocket
            logger.info(f"Пользователь {user_id} подключился к WebSocket")
            
            # Отправляем текущий баланс
            try:
                # 🔥 ИСПРАВЛЕНИЕ: Правильное получение и закрытие соединения БД
                logger.info("🗄️ DATABASE - Создание сессии базы данных для WebSocket")
                db = SessionLocal()
                
                # Получаем фиксированный баланс
                fixed_balance = await BalanceService.get_user_fixed_balance(db, user_id)
                
                # Получаем обычные балансы
                balances = await BalanceService.get_user_balances(db, user_id)
                
                # Подготовка данных для отправки
                balances_list = []
                for b in balances.balances:
                    balance_item = {
                        "currency": b.currency,
                        "amount": str(b.amount)
                    }
                    # Добавляем equivalent_usd, только если он есть
                    if hasattr(b, 'equivalent_usd') and b.equivalent_usd is not None:
                        balance_item["equivalent_usd"] = str(b.equivalent_usd)
                    balances_list.append(balance_item)
                
                # Создаем сообщение с обоими типами данных
                message_data = {
                    "type": "balance_update",
                    "balances": balances_list,
                    "fixed_usd_balance": fixed_balance.fixed_usd_balance,
                    "locked_usd_balance": fixed_balance.locked_usd_balance,
                    "available_usd_balance": fixed_balance.available_usd_balance
                }
                
                # Добавляем total_equivalent_usd, только если он есть
                if hasattr(balances, 'total_equivalent_usd') and balances.total_equivalent_usd is not None:
                    message_data["total_equivalent_usd"] = balances.total_equivalent_usd
                
                logger.info(f"Отправка начальных данных баланса: {message_data}")
                
                await websocket.send_text(json.dumps(message_data))
                logger.info(f"Отправлены начальные данные баланса пользователю {user_id}")
                
            except Exception as e:
                logger.error(f"Ошибка при отправке начальных данных баланса: {str(e)}")
                import traceback
                logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
            finally:
                # 🔥 ИСПРАВЛЕНИЕ: Обязательно закрываем соединение БД после получения данных
                if db:
                    db.close()
                    logger.info("🗄️ DATABASE - Сессия базы данных закрыта после получения начальных данных")
                    db = None  # Сбрасываем переменную
            
            # Держим соединение открытым до отключения клиента
            while True:
                # Ожидаем сообщения от клиента или отправляем пинг
                try:
                    data = await asyncio.wait_for(
                        websocket.receive_text(),
                        timeout=30.0  # таймаут 30 секунд
                    )
                    message = json.loads(data)
                    logger.debug(f"Получено сообщение от пользователя {user_id}: {message}")
                    
                    # Можно обработать сообщения от клиента, если нужно
                    if message.get("type") == "ping":
                        await websocket.send_text(json.dumps({"type": "pong"}))
                except asyncio.TimeoutError:
                    # Если клиент не отправил сообщение за 30 секунд, отправляем пинг
                    await websocket.send_text(json.dumps({"type": "ping"}))
                except WebSocketDisconnect:
                    break
                except Exception as e:
                    logger.error(f"Ошибка при обработке сообщения WebSocket: {str(e)}")
                    # Если произошла ошибка, прекращаем цикл
                    break
                
        except WebSocketDisconnect:
            # Удаляем соединение при отключении
            if user_id in active_connections:
                del active_connections[user_id]
                logger.info(f"Пользователь {user_id} отключился от WebSocket")
        except Exception as e:
            logger.error(f"Ошибка WebSocket: {str(e)}")
            import traceback
            logger.error(f"📍 Полная трассировка ошибки WebSocket:\n{traceback.format_exc()}")
            if user_id in active_connections:
                del active_connections[user_id]
                logger.info(f"Пользователь {user_id} удален из активных соединений из-за ошибки")
        finally:
            # 🔥 ИСПРАВЛЕНИЕ: Дополнительная проверка и закрытие соединения БД в finally
            if db:
                try:
                    db.close()
                    logger.info("🗄️ DATABASE - Сессия базы данных закрыта в finally блоке WebSocket")
                except Exception as e:
                    logger.error(f"❌ DATABASE - Ошибка при закрытии сессии БД в WebSocket: {str(e)}")
            
            # Убираем пользователя из активных соединений
            if user_id in active_connections:
                del active_connections[user_id]
                logger.info(f"🔌 WebSocket соединение окончательно очищено для пользователя {user_id}")


# Функция для отправки обновления баланса через WebSocket
async def send_balance_update(user_id: int, balances_data: Any):
    """Отправляет обновление баланса пользователю через WebSocket"""
    if user_id in active_connections:
        try:
            websocket = active_connections[user_id]
            
            # Преобразуем в JSON и отправляем
            message_data = {"type": "balance_update"}
            
            # Добавляем данные о фиксированном балансе
            if isinstance(balances_data, dict):
                if "fixed_usd_balance" in balances_data:
                    message_data["fixed_usd_balance"] = balances_data["fixed_usd_balance"]
                    message_data["locked_usd_balance"] = balances_data["locked_usd_balance"]
                    message_data["available_usd_balance"] = balances_data["available_usd_balance"]
                
                if "balances" in balances_data:
                    message_data["balances"] = balances_data["balances"]
                
                if "total_equivalent_usd" in balances_data:
                    message_data["total_equivalent_usd"] = balances_data["total_equivalent_usd"]
            
            json_data = json.dumps(message_data)
            logger.info(f"Отправляемые данные JSON: {json_data}")
            
            await websocket.send_text(json_data)
            logger.info(f"Отправлено обновление баланса пользователю {user_id}")
        except Exception as e:
            logger.error(f"Ошибка при отправке обновления баланса: {str(e)}")
            if user_id in active_connections:
                del active_connections[user_id]
    else:
        logger.debug(f"Пользователь {user_id} не подключен к WebSocket")