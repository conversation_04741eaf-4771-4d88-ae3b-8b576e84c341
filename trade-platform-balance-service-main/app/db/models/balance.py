import uuid
from datetime import datetime
from sqlalchemy import Column, Integer, String, Numeric, DateTime, ForeignKey, Enum, Boolean, Index
from sqlalchemy.dialects.postgresql import UUID

# Вместо создания нового Base, импортируем из session
from app.db.session import Base


class UserBalance(Base):
    """Модель для хранения текущих балансов пользователей"""
    
    __tablename__ = "user_balances"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, nullable=False, index=True)
    currency = Column(String(10), nullable=False)
    amount = Column(Numeric(precision=36, scale=18), nullable=False, default=0)
    equivalent_usd = Column(Numeric(precision=36, scale=2), nullable=True)  # USD эквивалент
    price_usd = Column(Numeric(precision=36, scale=18), nullable=True)      # Текущий курс валюты
    last_updated = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Создаем составной индекс для быстрого поиска по user_id и currency
    __table_args__ = (
        Index('idx_user_currency', 'user_id', 'currency', unique=True),
    )
    
    def __repr__(self):
        return f"<UserBalance(user_id={self.user_id}, currency='{self.currency}', amount={self.amount}, equivalent_usd={self.equivalent_usd})>"



class BalanceHistory(Base):
    """Модель для хранения истории изменений балансов"""
    
    __tablename__ = "balance_history"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, nullable=False, index=True)
    currency = Column(String(10), nullable=False, index=True)
    amount_change = Column(Numeric(precision=36, scale=18), nullable=False)
    transaction_hash = Column(String(100), nullable=True, unique=True)
    event_type = Column(Enum('deposit', 'withdrawal', 'transfer', name='event_type'), nullable=False)
    is_processed = Column(Boolean, default=False, nullable=False)
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Индексы для оптимизации запросов
    __table_args__ = (
        Index('idx_user_timestamp', 'user_id', 'timestamp'),
        Index('idx_transaction_hash', 'transaction_hash'),
    )
    
    def __repr__(self):
        return (
            f"<BalanceHistory(user_id={self.user_id}, currency='{self.currency}', "
            f"amount_change={self.amount_change}, event_type='{self.event_type}')>"
        )


class BalanceLock(Base):
    """Модель для блокировки средств (например, при открытых ордерах)"""
    
    __tablename__ = "balance_locks"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, nullable=False, index=True)
    currency = Column(String(10), nullable=False)
    amount = Column(Numeric(precision=36, scale=18), nullable=False)
    reason = Column(String(100), nullable=False)
    created_at = Column(DateTime, nullable=False, default=datetime.utcnow)
    expires_at = Column(DateTime, nullable=True)
    is_released = Column(Boolean, default=False, nullable=False)
    
    # Составной индекс для оптимизации запросов
    __table_args__ = (
        Index('idx_user_currency_lock', 'user_id', 'currency', 'is_released'),
    )
    
    def __repr__(self):
        return (
            f"<BalanceLock(user_id={self.user_id}, currency='{self.currency}', "
            f"amount={self.amount}, is_released={self.is_released})>"
        )
    

class UserFixedBalance(Base):
    """Модель для хранения фиксированного баланса в USD"""
    
    __tablename__ = "user_fixed_balances"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(Integer, nullable=False, index=True, unique=True)
    fixed_usd_balance = Column(Numeric(precision=36, scale=2), nullable=False, default=0)
    locked_usd_balance = Column(Numeric(precision=36, scale=2), nullable=False, default=0)  # Заблокированные средства для открытых позиций
    last_updated = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_fixed_balance_user_id', 'user_id', unique=True),
    )
    
    def __repr__(self):
        return f"<UserFixedBalance(user_id={self.user_id}, fixed_usd_balance={self.fixed_usd_balance}, locked_usd_balance={self.locked_usd_balance})>"


  