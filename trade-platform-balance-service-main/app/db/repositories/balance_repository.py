from asyncio.log import logger
from datetime import datetime
from decimal import Decimal
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_

from app.api.models.balance import HistoryResponse
from app.db.models.balance import UserBalance, BalanceHistory, BalanceLock, UserFixedBalance


class BalanceRepository:
    """Репозиторий для работы с балансами пользователей"""
    
    @staticmethod
    def get_user_balances(db: Session, user_id: int, currency: Optional[str] = None) -> List[UserBalance]:
        """
        Получение баланса пользователя по всем или конкретной валюте
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты (опционально)
            
        Returns:
            List[UserBalance]: Список балансов пользователя
        """
        query = db.query(UserBalance).filter(UserBalance.user_id == user_id)
        
        if currency:
            query = query.filter(UserBalance.currency == currency)
            
        return query.all()
    
    @staticmethod
    def get_user_balance(db: Session, user_id: int, currency: str) -> Optional[UserBalance]:
        """
        Получение баланса пользователя по конкретной валюте
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты
            
        Returns:
            Optional[UserBalance]: Баланс пользователя или None
        """
        return db.query(UserBalance).filter(
            UserBalance.user_id == user_id,
            UserBalance.currency == currency
        ).first()
    
    @staticmethod
    def create_or_update_balance(
        db: Session,
        user_id: int,
        currency: str,
        amount_change: Decimal,
        price_usd: Optional[Decimal] = None,
        equivalent_usd: Optional[Decimal] = None
    ) -> UserBalance:
        """
        Создание или обновление баланса пользователя с поддержкой USD эквивалента
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты
            amount_change: Изменение суммы
            price_usd: Текущий курс в USD (опционально)
            equivalent_usd: Эквивалент в USD (опционально)
            
        Returns:
            UserBalance: Обновленный баланс пользователя
        """
        # Ищем существующий баланс
        balance = BalanceRepository.get_user_balance(db, user_id, currency)
        
        if balance:
            # Обновляем существующий баланс
            balance.amount += amount_change
            balance.last_updated = datetime.utcnow()
            
            # Обновляем USD эквивалент если предоставлен
            if price_usd is not None:
                balance.price_usd = price_usd
            
            # Обновляем эквивалент USD если предоставлен или можем рассчитать
            if equivalent_usd is not None:
                balance.equivalent_usd = equivalent_usd
            elif price_usd is not None:
                balance.equivalent_usd = balance.amount * price_usd
        else:
            # Создаем новый баланс
            balance_data = {
                "user_id": user_id,
                "currency": currency,
                "amount": amount_change,
                "last_updated": datetime.utcnow()
            }
            
            # Добавляем USD информацию если доступна
            if price_usd is not None:
                balance_data["price_usd"] = price_usd
            
            if equivalent_usd is not None:
                balance_data["equivalent_usd"] = equivalent_usd
            elif price_usd is not None:
                balance_data["equivalent_usd"] = amount_change * price_usd
                
            balance = UserBalance(**balance_data)
            db.add(balance)
        
        db.commit()
        db.refresh(balance)
        return balance
    
    @staticmethod
    def update_balance_usd_equivalent(
        db: Session,
        user_id: int,
        currency: str,
        price_usd: Decimal
    ) -> Optional[UserBalance]:
        """
        Обновление USD эквивалента для баланса пользователя
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты
            price_usd: Текущий курс в USD
            
        Returns:
            Optional[UserBalance]: Обновленный баланс или None, если баланс не найден
        """
        balance = BalanceRepository.get_user_balance(db, user_id, currency)
        
        if not balance:
            return None
        
        # Обновляем курс и эквивалент
        balance.price_usd = price_usd
        balance.equivalent_usd = balance.amount * price_usd
        balance.last_updated = datetime.utcnow()
        
        db.commit()
        db.refresh(balance)
        return balance
    
    @staticmethod
    def add_balance_history(
        db: Session,
        user_id: int,
        currency: str,
        amount_change: Decimal,
        transaction_hash: Optional[str],
        event_type: str
    ) -> BalanceHistory:
        """
        Добавление записи в историю изменений баланса
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты
            amount_change: Изменение суммы
            transaction_hash: Хеш транзакции (опционально)
            event_type: Тип события
            
        Returns:
            BalanceHistory: Запись в истории баланса
        """
        history_entry = BalanceHistory(
            user_id=user_id,
            currency=currency,
            amount_change=amount_change,
            transaction_hash=transaction_hash,
            event_type=event_type,
            is_processed=True,
            timestamp=datetime.utcnow()
        )
        
        db.add(history_entry)
        db.commit()
        db.refresh(history_entry)
        return history_entry
    
    @staticmethod
    def get_balance_history(
        db: Session,
        user_id: int,
        currency: Optional[str] = None,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None,
        page: int = 1,
        limit: int = 10,
        event_type: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Получение истории изменений баланса пользователя с дополнительным фильтром по типу события
        """
        query = db.query(BalanceHistory).filter(BalanceHistory.user_id == user_id)
        
        if currency:
            query = query.filter(BalanceHistory.currency == currency)
        
        if from_date:
            query = query.filter(BalanceHistory.timestamp >= from_date)
        
        if to_date:
            query = query.filter(BalanceHistory.timestamp <= to_date)
        
        if event_type:  # Добавлен фильтр по типу события
            query = query.filter(BalanceHistory.event_type == event_type)
        
        total = query.count()
        pages = (total + limit - 1) // limit
        
        items = query.order_by(BalanceHistory.timestamp.desc()) \
                    .offset((page - 1) * limit) \
                    .limit(limit) \
                    .all()
        
        return {
            "items": items,
            "total": total,
            "page": page,
            "limit": limit,
            "pages": pages
        }
    
    @staticmethod
    def get_summary_statistics(
        db: Session,
        user_id: int,
        from_date: Optional[datetime] = None,
        to_date: Optional[datetime] = None
    ) -> Dict[str, Dict[str, Decimal]]:
        """
        Получение суммарной статистики по изменениям баланса пользователя
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            from_date: Начальная дата (опционально)
            to_date: Конечная дата (опционально)
            
        Returns:
            Dict[str, Dict[str, Decimal]]: Словарь со статистикой по типу и валюте
        """
        # Базовый запрос
        query = db.query(
            BalanceHistory.currency,
            BalanceHistory.event_type,
            func.sum(BalanceHistory.amount_change).label('total')
        ).filter(BalanceHistory.user_id == user_id)
        
        # Применяем фильтры по датам
        if from_date:
            query = query.filter(BalanceHistory.timestamp >= from_date)
        
        if to_date:
            query = query.filter(BalanceHistory.timestamp <= to_date)
        
        # Группируем результаты
        query = query.group_by(BalanceHistory.currency, BalanceHistory.event_type)
        
        # Формируем результат
        result = {
            "total_deposits": {},
            "total_withdrawals": {},
            "net_change": {}
        }
        
        for row in query.all():
            if row.event_type == 'deposit':
                result["total_deposits"][row.currency] = row.total
            elif row.event_type == 'withdrawal':
                result["total_withdrawals"][row.currency] = abs(row.total)
        
        # Расчет net_change для каждой валюты
        for currency in set(list(result["total_deposits"].keys()) + list(result["total_withdrawals"].keys())):
            deposits = result["total_deposits"].get(currency, Decimal('0'))
            withdrawals = result["total_withdrawals"].get(currency, Decimal('0'))
            result["net_change"][currency] = deposits - withdrawals
        
        return result
    
    @staticmethod
    def get_total_usd_equivalent(
        db: Session,
        user_id: int
    ) -> Decimal:
        """
        Получение общей суммы всех балансов пользователя в USD
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            
        Returns:
            Decimal: Общая сумма в USD
        """
        result = db.query(
            func.sum(UserBalance.equivalent_usd).label('total_usd')
        ).filter(
            UserBalance.user_id == user_id,
            UserBalance.equivalent_usd.isnot(None)  # Учитываем только записи с эквивалентом
        ).scalar()
        
        return result or Decimal('0')
    
    @staticmethod
    def lock_balance(
        db: Session,
        user_id: int,
        currency: str,
        amount: Decimal,
        reason: str,
        expires_at: Optional[datetime] = None
    ) -> Optional[BalanceLock]:
        """
        Блокировка части баланса пользователя
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты
            amount: Сумма для блокировки
            reason: Причина блокировки
            expires_at: Время истечения блокировки
            
        Returns:
            Optional[BalanceLock]: Запись о блокировке или None, если недостаточно средств
        """
        # Проверяем доступный баланс
        balance = BalanceRepository.get_user_balance(db, user_id, currency)
        
        if not balance or balance.amount < amount:
            return None
        
        # Создаем запись о блокировке
        lock = BalanceLock(
            user_id=user_id,
            currency=currency,
            amount=amount,
            reason=reason,
            created_at=datetime.utcnow(),
            expires_at=expires_at,
            is_released=False
        )
        
        db.add(lock)
        db.commit()
        db.refresh(lock)
        return lock
    
    @staticmethod
    def get_locked_balance(db: Session, user_id: int, currency: str) -> Decimal:
        """
        Получение суммы заблокированных средств пользователя
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            currency: Код валюты
            
        Returns:
            Decimal: Сумма заблокированных средств
        """
        result = db.query(
            func.sum(BalanceLock.amount).label('locked_amount')
        ).filter(
            BalanceLock.user_id == user_id,
            BalanceLock.currency == currency,
            BalanceLock.is_released == False
        ).scalar()
        
        return result or Decimal('0')
    
    @staticmethod
    def release_lock(db: Session, lock_id: str) -> bool:
        """
        Освобождение заблокированных средств
        
        Args:
            db: Сессия базы данных
            lock_id: ID блокировки
            
        Returns:
            bool: True, если блокировка успешно снята
        """
        lock = db.query(BalanceLock).filter(BalanceLock.id == lock_id).first()
        
        if not lock or lock.is_released:
            return False
        
        lock.is_released = True
        db.commit()
        return True
    

    @staticmethod
    def get_user_fixed_balance(db: Session, user_id: int) -> Optional[UserFixedBalance]:
        """
        Получение фиксированного USD баланса пользователя
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            
        Returns:
            Optional[UserFixedBalance]: Фиксированный баланс пользователя или None
        """
        return db.query(UserFixedBalance).filter(
            UserFixedBalance.user_id == user_id
        ).first()
    
    @staticmethod
    def update_fixed_balance(db: Session, user_id: int, amount_change: Decimal, is_locked: bool = False) -> UserFixedBalance:
        """
        Обновление фиксированного USD баланса пользователя
        
        Args:
            db: Сессия базы данных
            user_id: ID пользователя
            amount_change: Изменение суммы в USD
            is_locked: Флаг, указывающий на обновление заблокированных средств
            
        Returns:
            UserFixedBalance: Обновленный фиксированный баланс
        """
        balance = BalanceRepository.get_user_fixed_balance(db, user_id)
        
        if balance:
            # Обновляем существующий баланс
            if is_locked:
                balance.locked_usd_balance += amount_change
            else:
                balance.fixed_usd_balance += amount_change
            balance.last_updated = datetime.utcnow()
        else:
            # Создаем новый баланс
            balance_data = {
                "user_id": user_id,
                "fixed_usd_balance": amount_change if not is_locked else Decimal('0'),
                "locked_usd_balance": amount_change if is_locked else Decimal('0'),
                "last_updated": datetime.utcnow()
            }
            
            balance = UserFixedBalance(**balance_data)
            db.add(balance)
        
        db.commit()
        db.refresh(balance)
        return balance