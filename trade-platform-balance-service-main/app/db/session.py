from contextlib import asynccontextmanager
from sqlalchemy import create_engine, pool
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import logging

from app.config import settings

logger = logging.getLogger(__name__)

# Создание движка SQLAlchemy
engine = create_engine(
    settings.DATABASE_URL,
    pool_size=25,           # Увеличиваем основной пул
    max_overflow=25,        # Увеличиваем overflow
    pool_timeout=30,        # Таймаут получения соединения
    pool_recycle=3600,      # Переиспользование соединений каждый час
    pool_pre_ping=True,     # Проверка соединений перед использованием
    echo=False
)

# Создание фабрики сессий
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Базовый класс для моделей SQLAlchemy
Base = declarative_base()


# 🔥 ЭТА ФУНКЦИЯ ОТСУТСТВОВАЛА - ДОБАВЛЯЕМ ЕЕ!
def get_db():
    """
    Генератор для получения сессии базы данных
    
    Используется как dependency в FastAPI эндпоинтах
    Автоматически закрывает соединение после использования
    """
    db = SessionLocal()
    try:
        logger.debug("🗄️ DATABASE - Создана сессия базы данных")
        yield db
    except Exception as e:
        logger.error(f"❌ DATABASE - Ошибка в сессии БД: {str(e)}")
        db.rollback()
        raise
    finally:
        db.close()
        logger.debug("🗄️ DATABASE - Сессия базы данных закрыта")


@asynccontextmanager
async def get_db_context():
    """
    Асинхронный context manager для безопасной работы с базой данных
    """
    db = SessionLocal()
    try:
        logger.debug("🗄️ DATABASE - Создана сессия базы данных (async context)")
        yield db
        logger.debug("🗄️ DATABASE - Операция с БД завершена успешно")
    except Exception as e:
        logger.error(f"❌ DATABASE - Ошибка в операции с БД: {str(e)}")
        try:
            db.rollback()
            logger.info("🔄 DATABASE - Выполнен rollback")
        except Exception as rollback_error:
            logger.error(f"❌ DATABASE - Ошибка при rollback: {str(rollback_error)}")
        raise
    finally:
        try:
            db.close()
            logger.debug("🗄️ DATABASE - Сессия базы данных закрыта (async context)")
        except Exception as close_error:
            logger.error(f"❌ DATABASE - Ошибка при закрытии сессии: {str(close_error)}")


def get_engine_info():
    """
    Получение информации о движке БД для отладки
    """
    try:
        pool = engine.pool
        return {
            "pool_size": pool.size(),
            "checked_in": pool.checkedin(),
            "checked_out": pool.checkedout(),
            "overflow": pool.overflow(),
            "invalid": pool.invalid()
        }
    except Exception as e:
        logger.warning(f"⚠️ DATABASE - Не удалось получить статус пула: {str(e)}")
        return {"error": str(e)}


def log_pool_status():
    """
    Логирование состояния пула соединений
    """
    try:
        info = get_engine_info()
        if "error" not in info:
            logger.info("📊 DATABASE POOL STATUS:")
            logger.info(f"   📍 Pool size: {info['pool_size']}")
            logger.info(f"   📍 Checked in: {info['checked_in']}")
            logger.info(f"   📍 Checked out: {info['checked_out']}")
            logger.info(f"   📍 Overflow: {info['overflow']}")
            logger.info(f"   📍 Invalid: {info['invalid']}")
        else:
            logger.warning(f"⚠️ DATABASE POOL - Ошибка получения статуса: {info['error']}")
    except Exception as e:
        logger.warning(f"⚠️ DATABASE - Не удалось залогировать статус пула: {str(e)}")


def close_db_connections():
    """
    Закрытие всех соединений с базой данных
    Используется при остановке приложения
    """
    try:
        logger.info("🛑 DATABASE - Закрытие всех соединений с базой данных")
        engine.dispose()
        logger.info("✅ DATABASE - Все соединения закрыты")
    except Exception as e:
        logger.error(f"❌ DATABASE - Ошибка при закрытии соединений: {str(e)}")