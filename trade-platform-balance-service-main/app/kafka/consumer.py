import asyncio
import json
import logging
from typing import Dict, Any, Callable
from aiokafka import AIOKafkaConsumer
from datetime import datetime
from decimal import Decimal

from app.config import settings
from app.kafka.event_handlers import initialize_kafka_producer

logger = logging.getLogger(__name__)


class KafkaConsumer:
    """
    Kafka потребитель для обработки сообщений из Kafka
    """
    
    def __init__(self, topics: list, group_id: str, bootstrap_servers: str, handler_map: Dict[str, Callable]):
        """
        Инициализация Kafka потребителя
        
        Args:
            topics: Список топиков для подписки
            group_id: ID группы потребителей
            bootstrap_servers: Адреса Kafka серверов
            handler_map: Словарь с обработчиками сообщений по типу события
        """
        self.topics = topics
        self.group_id = group_id
        self.bootstrap_servers = bootstrap_servers
        self.handler_map = handler_map
        self.consumer = None
        self.running = False
    
    async def start(self):
        """Запуск Kafka потребителя"""
        logger.info(f"📨 KAFKA CONSUMER - Запуск потребителя для топиков: {self.topics}")
        logger.info(f"📨 KAFKA CONSUMER - Группа: {self.group_id}")
        logger.info(f"📨 KAFKA CONSUMER - Серверы: {self.bootstrap_servers}")
        
        try:
            # Инициализируем Kafka продюсер для ответов
            await initialize_kafka_producer()
            logger.info("✅ KAFKA PRODUCER - Продюсер для ответов инициализирован")
            
            self.consumer = AIOKafkaConsumer(
                *self.topics,
                bootstrap_servers=self.bootstrap_servers,
                group_id=self.group_id,
                auto_offset_reset="earliest",
                enable_auto_commit=False,
                value_deserializer=lambda m: json.loads(m.decode('utf-8'))
            )
            
            await self.consumer.start()
            self.running = True
            
            asyncio.create_task(self._consume())
            logger.info("✅ KAFKA CONSUMER - Потребитель успешно запущен")
            
        except Exception as e:
            logger.error(f"❌ KAFKA CONSUMER - Ошибка при запуске: {str(e)}")
            import traceback
            logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
            if self.consumer:
                await self.consumer.stop()
    
    async def stop(self):
        """Остановка Kafka потребителя"""
        logger.info("🛑 KAFKA CONSUMER - Остановка потребителя")
        self.running = False
        if self.consumer:
            await self.consumer.stop()
            logger.info("✅ KAFKA CONSUMER - Потребитель остановлен")
    
    async def _consume(self):
        """Основной цикл обработки сообщений"""
        logger.info("🔄 KAFKA CONSUMER - Запуск цикла обработки сообщений")
        
        try:
            async for message in self.consumer:
                if not self.running:
                    logger.info("🛑 KAFKA CONSUMER - Остановка цикла обработки")
                    break
                
                try:
                    logger.info("=" * 60)
                    logger.info(f"📨 KAFKA MESSAGE - Получено новое сообщение")
                    logger.info(f"   📍 Топик: {message.topic}")
                    logger.info(f"   📍 Партиция: {message.partition}")
                    logger.info(f"   📍 Офсет: {message.offset}")
                    logger.info(f"   📍 Время: {datetime.fromtimestamp(message.timestamp/1000)}")
                    logger.info(f"   📍 Размер: {len(str(message.value))} символов")
                    
                    # Обработка сообщения
                    await self._process_message(message.topic, message.value)
                    
                    # Подтверждение обработки
                    await self.consumer.commit()
                    logger.info("✅ KAFKA MESSAGE - Сообщение успешно обработано и подтверждено")
                    logger.info("=" * 60)
                    
                except Exception as e:
                    logger.error("❌ KAFKA MESSAGE - Ошибка при обработке сообщения")
                    logger.error(f"   📍 Топик: {message.topic}")
                    logger.error(f"   📍 Ошибка: {str(e)}")
                    import traceback
                    logger.error(f"   📍 Трассировка:\n{traceback.format_exc()}")
                    logger.error("=" * 60)
                    
        except Exception as e:
            logger.error(f"❌ KAFKA CONSUMER - Критическая ошибка в цикле обработки: {str(e)}")
            import traceback
            logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
            
            if self.running:
                logger.info("🔄 KAFKA CONSUMER - Попытка перезапуска через 5 секунд...")
                await self.stop()
                await asyncio.sleep(5)
                await self.start()
    
    
    
    async def _process_message(self, topic: str, message: Dict[str, Any]):
        """
        Обработка сообщения из Kafka
        
        Args:
            topic: Топик сообщения
            message: Сообщение
        """
        logger.info(f"🔍 MESSAGE PROCESSING - Начало обработки")
        logger.info(f"   📍 Топик: {topic}")
        logger.info(f"   📍 Содержимое: {json.dumps(message, indent=2, ensure_ascii=False)}")
        
        # Специальная обработка для топика check.locked.balance
        if topic == "check.locked.balance":
            logger.info(f"🔐 LOCKED BALANCE CHECK - Получен запрос на проверку заблокированного баланса")
            logger.info(f"   📍 User ID: {message.get('userId', 'Unknown')}")
            logger.info(f"   📍 Correlation ID: {message.get('correlationId', 'Unknown')}")
            
            handler = self.handler_map.get("check_locked_balance")
            if handler:
                logger.info("✅ HANDLER - Найден обработчик check_locked_balance")
                await handler(message)
                return
            else:
                logger.error("❌ HANDLER - Нет обработчика для check_locked_balance")
                return
        
        # Специальная обработка для топика deduct.fixed.balance
        if topic == "deduct.fixed.balance":
            logger.info(f"💰 DEDUCT BALANCE - Получен запрос на уменьшение фиксированного баланса")
            logger.info(f"   📍 User ID: {message.get('userId', 'Unknown')}")
            logger.info(f"   📍 Amount: {message.get('amount', 'Unknown')}")
            logger.info(f"   📍 Transaction Ref: {message.get('transactionReference', 'Unknown')}")
            
            handler = self.handler_map.get("deduct_fixed_balance")
            if handler:
                logger.info("✅ HANDLER - Найден обработчик deduct_fixed_balance")
                await handler(message)
                return
            else:
                logger.error("❌ HANDLER - Нет обработчика для deduct_fixed_balance")
                return
        
        # Специальная обработка для топика balance.add.referal.reward
        if topic == "balance.add.referal.reward":
            logger.info(f"🎁 REFERAL REWARD - Получен запрос на добавление реферальной суммы")
            logger.info(f"   📍 User ID: {message.get('userId', 'Unknown')}")
            logger.info(f"   📍 Amount: {message.get('amount', 'Unknown')}")
            logger.info(f"   📍 Period: {message.get('referalPeriod', 'Unknown')}")
            
            handler = self.handler_map.get("add_referal_reward")
            if handler:
                logger.info("✅ HANDLER - Найден обработчик add_referal_reward")
                await handler(message)
                return
            else:
                logger.error("❌ HANDLER - Нет обработчика для add_referal_reward")
                return

        # 🔥 НОВАЯ ОБРАБОТКА для топика balance.add.funds (ВОЗВРАТ СРЕДСТВ)
        if topic == "balance.add.funds":
            logger.info(f"💰 ADD FUNDS - Получен запрос на возврат средств")
            logger.info(f"   📍 User ID: {message.get('userId', 'Unknown')}")
            logger.info(f"   📍 Amount: {message.get('amount', 'Unknown')}")
            logger.info(f"   📍 Currency: {message.get('currency', 'Unknown')}")
            logger.info(f"   📍 Transaction Ref: {message.get('transactionReference', 'Unknown')}")
            logger.info(f"   📍 Reason: {message.get('reason', 'Unknown')}")
            
            handler = self.handler_map.get("add_funds")
            if handler:
                logger.info("✅ HANDLER - Найден обработчик add_funds")
                await handler(message)
                return
            else:
                logger.error("❌ HANDLER - Нет обработчика для add_funds")
                return
        
        # НОВАЯ ОБРАБОТКА для топика balance-operations (от Trade Service)
        if topic == "balance-operations":
            logger.info(f"📈 TRADE BALANCE OPERATION - Получена торговая операция от Trade Service")
            logger.info(f"   📍 Event Type: {message.get('event_type', 'Unknown')}")
            logger.info(f"   📍 Operation Type: {message.get('operation_type', 'Unknown')}")
            logger.info(f"   📍 User ID: {message.get('user_id', 'Unknown')}")
            logger.info(f"   📍 Margin USD: {message.get('margin_usd', 'Unknown')}")
            logger.info(f"   📍 PnL USD: {message.get('pnl_usd', 'Unknown')}")
            
            # Для торговых операций принудительно используем обработчик trade_operation
            handler = self.handler_map.get("trade_operation")
            if handler:
                logger.info("✅ HANDLER - Найден обработчик trade_operation")
                
                # Преобразуем строковые представления чисел в Decimal
                if "margin_usd" in message and isinstance(message["margin_usd"], (str, float)):
                    old_margin = message["margin_usd"]
                    message["margin_usd"] = Decimal(str(message["margin_usd"]))
                    logger.info(f"🔢 TYPE CONVERSION - margin_usd: {old_margin} -> {message['margin_usd']}")
                    
                if "pnl_usd" in message and isinstance(message["pnl_usd"], (str, float)):
                    old_pnl = message["pnl_usd"]
                    message["pnl_usd"] = Decimal(str(message["pnl_usd"]))
                    logger.info(f"🔢 TYPE CONVERSION - pnl_usd: {old_pnl} -> {message['pnl_usd']}")
                    
                if "amount" in message and isinstance(message["amount"], (str, float)):
                    old_amount = message["amount"]
                    message["amount"] = Decimal(str(message["amount"]))
                    logger.info(f"🔢 TYPE CONVERSION - amount: {old_amount} -> {message['amount']}")
                    
                if "return_amount" in message and isinstance(message["return_amount"], (str, float)):
                    old_return = message["return_amount"]
                    message["return_amount"] = Decimal(str(message["return_amount"]))
                    logger.info(f"🔢 TYPE CONVERSION - return_amount: {old_return} -> {message['return_amount']}")
                
                # Преобразуем строковые представления дат в datetime
                if "timestamp" in message and isinstance(message["timestamp"], str):
                    old_timestamp = message["timestamp"]
                    message["timestamp"] = datetime.fromisoformat(message["timestamp"].replace("Z", "+00:00"))
                    logger.info(f"📅 TYPE CONVERSION - timestamp: {old_timestamp} -> {message['timestamp']}")
                
                await handler(message)
                return
            else:
                logger.error("❌ HANDLER - Нет обработчика для trade_operation")
                return
        
        # Обработка для топика transaction-events
        if topic == settings.KAFKA_TRANSACTION_TOPIC or topic == "transaction-events":
            logger.info(f"💳 TRANSACTION EVENT - Получено событие транзакции")
            logger.info(f"   📍 Event Type: {message.get('event_type', 'Unknown')}")
            logger.info(f"   📍 User ID: {message.get('user_id', 'Unknown')}")
            logger.info(f"   📍 Amount: {message.get('amount', 'Unknown')}")
            logger.info(f"   📍 Currency: {message.get('currency', 'Unknown')}")
            logger.info(f"   📍 Status: {message.get('status', 'Unknown')}")
            
            # Получаем тип события из сообщения
            event_type = message.get("event_type")
            
            if not event_type:
                logger.warning(f"⚠️ EVENT TYPE - Отсутствует event_type в сообщении транзакции")
                return
            
            # Ищем обработчик для типа события
            handler = self.handler_map.get(event_type)
            
            if not handler:
                logger.error(f"❌ HANDLER - Нет обработчика для события типа '{event_type}'")
                logger.error(f"   📍 Доступные обработчики: {list(self.handler_map.keys())}")
                return
            
            logger.info(f"✅ HANDLER - Найден обработчик для '{event_type}'")
            
            # Преобразуем строковые представления чисел в Decimal
            if "amount" in message and isinstance(message["amount"], (str, float)):
                old_amount = message["amount"]
                message["amount"] = Decimal(str(message["amount"]))
                logger.info(f"🔢 TYPE CONVERSION - amount: {old_amount} -> {message['amount']}")
            
            # Преобразуем строковые представления дат в datetime
            if "timestamp" in message and isinstance(message["timestamp"], str):
                old_timestamp = message["timestamp"]
                message["timestamp"] = datetime.fromisoformat(message["timestamp"].replace("Z", "+00:00"))
                logger.info(f"📅 TYPE CONVERSION - timestamp: {old_timestamp} -> {message['timestamp']}")
            
            await handler(message)
            logger.info(f"✅ HANDLER - Обработчик '{event_type}' завершил работу")
            return
        
        # Для остальных топиков получаем event_type
        event_type = message.get("event_type")
        
        if not event_type:
            logger.warning(f"⚠️ EVENT TYPE - Пропуск сообщения без типа события")
            logger.warning(f"   📍 Топик: {topic}")
            logger.warning(f"   📍 Сообщение: {message}")
            return
        
        logger.info(f"🎯 EVENT TYPE - Обработка события типа '{event_type}'")
        
        # Ищем обработчик для типа события
        handler = self.handler_map.get(event_type)
        
        if not handler:
            logger.error(f"❌ HANDLER - Нет обработчика для события типа '{event_type}'")
            logger.error(f"   📍 Доступные обработчики: {list(self.handler_map.keys())}")
            return
        
        logger.info(f"✅ HANDLER - Найден обработчик для '{event_type}'")
        
        # Вызываем обработчик с сообщением
        try:
            # Преобразуем строковые представления чисел в Decimal
            if "amount" in message and isinstance(message["amount"], (str, float)):
                old_amount = message["amount"]
                message["amount"] = Decimal(str(message["amount"]))
                logger.info(f"🔢 TYPE CONVERSION - amount: {old_amount} -> {message['amount']}")
            
            # Преобразуем строковые представления дат в datetime
            if "timestamp" in message and isinstance(message["timestamp"], str):
                old_timestamp = message["timestamp"]
                message["timestamp"] = datetime.fromisoformat(message["timestamp"].replace("Z", "+00:00"))
                logger.info(f"📅 TYPE CONVERSION - timestamp: {old_timestamp} -> {message['timestamp']}")
            
            await handler(message)
            logger.info(f"✅ HANDLER - Событие '{event_type}' успешно обработано")
            
        except Exception as e:
            logger.error(f"❌ HANDLER - Ошибка при обработке события '{event_type}': {str(e)}")
            import traceback
            logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
            raise