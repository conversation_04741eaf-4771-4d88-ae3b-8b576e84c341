from datetime import datetime
from decimal import Decimal
import logging
import asyncio
import json
from typing import Dict, Any
from sqlalchemy.orm import Session
from aiokafka import AIOKafkaProducer

from app.core.services.balance_service import BalanceService
from app.db.repositories.balance_repository import BalanceRepository
from app.db.session import SessionLocal
from app.config import settings

logger = logging.getLogger(__name__)

# Глобальный продюсер Kafka для ответов
kafka_producer = None

async def initialize_kafka_producer():
    """Инициализация Kafka продюсера для ответов"""
    global kafka_producer
    try:
        logger.info("🚀 KAFKA PRODUCER - Инициализация продюсера для ответов")
        producer = AIOKafkaProducer(
            bootstrap_servers=settings.KAFKA_BOOTSTRAP_SERVERS,
            value_serializer=lambda v: json.dumps(v).encode('utf-8')
        )
        await producer.start()
        kafka_producer = producer
        logger.info("✅ KAFKA PRODUCER - Продюсер для ответов успешно инициализирован")
    except Exception as e:
        logger.error(f"❌ KAFKA PRODUCER - Ошибка при инициализации: {str(e)}")


async def handle_transaction_update(message: Dict[str, Any]):
    """
    Обработка события обновления транзакции - ИСПРАВЛЕННАЯ ВЕРСИЯ
    
    Args:
        message: Сообщение с данными о транзакции
            {
                "event_type": "transaction_update",
                "user_id": 123,
                "amount": 0.5,
                "currency": "BTC",
                "tx_hash": "0xabc...",
                "status": "confirmed",
                "timestamp": "2023-05-15T12:34:56Z"
            }
    """
    # 🔥 ИСПРАВЛЕНИЕ: Инициализируем db вне try блока
    db = None
    
    try:
        logger.info("💳 TRANSACTION UPDATE - Начало обработки события обновления транзакции")
        logger.info(f"   📍 Полное сообщение: {json.dumps(message, indent=2, ensure_ascii=False, default=str)}")
        
        # Проверка наличия необходимых полей
        required_fields = ["user_id", "amount", "currency", "tx_hash", "status"]
        missing_fields = []
        
        for field in required_fields:
            if field not in message:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"❌ VALIDATION - Отсутствуют обязательные поля: {missing_fields}")
            return
        
        # Получаем значения полей
        user_id = message["user_id"]
        amount = message["amount"]
        currency = message["currency"]
        tx_hash = message["tx_hash"]
        status = message["status"]
        
        logger.info("📋 TRANSACTION DETAILS:")
        logger.info(f"   📍 User ID: {user_id}")
        logger.info(f"   📍 Amount: {amount}")
        logger.info(f"   📍 Currency: {currency}")
        logger.info(f"   📍 TX Hash: {tx_hash}")
        logger.info(f"   📍 Status: {status}")
        
        # 🔥 ИСПРАВЛЕНИЕ: Создаем новую сессию БД с правильным exception handling
        logger.info("🗄️ DATABASE - Создание сессии базы данных")
        db = SessionLocal()
        
        # 🔥 ИСПРАВЛЕНИЕ: Добавляем таймаут для операций БД
        try:
            # Обрабатываем транзакцию с таймаутом
            logger.info("⚙️ PROCESSING - Вызов BalanceService.process_transaction_event")
            
            # Используем asyncio.wait_for для добавления таймаута к операции
            success = await asyncio.wait_for(
                BalanceService.process_transaction_event(
                    db=db,
                    user_id=user_id,
                    amount=amount,
                    currency=currency,
                    tx_hash=tx_hash,
                    status=status
                ),
                timeout=30.0  # 🔥 Таймаут 30 секунд для операции
            )
            
            if success:
                logger.info(f"✅ TRANSACTION UPDATE - Транзакция {tx_hash} успешно обработана")
            else:
                logger.warning(f"⚠️ TRANSACTION UPDATE - Транзакция {tx_hash} не обработана (возможно, уже существует или неподтвержденная)")
                
        except asyncio.TimeoutError:
            # 🔥 ИСПРАВЛЕНИЕ: Обработка таймаута
            logger.error(f"❌ TRANSACTION UPDATE - Таймаут при обработке транзакции {tx_hash}")
            # При таймауте делаем rollback
            try:
                db.rollback()
                logger.info("🔄 DATABASE - Выполнен rollback из-за таймаута")
            except Exception as rollback_error:
                logger.error(f"❌ DATABASE - Ошибка при rollback: {str(rollback_error)}")
                
        except Exception as e:
            logger.error(f"❌ TRANSACTION UPDATE - Ошибка при обработке транзакции: {str(e)}")
            import traceback
            logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
            
            # 🔥 ИСПРАВЛЕНИЕ: Выполняем rollback при ошибке
            try:
                db.rollback()
                logger.info("🔄 DATABASE - Выполнен rollback из-за ошибки")
            except Exception as rollback_error:
                logger.error(f"❌ DATABASE - Ошибка при rollback: {str(rollback_error)}")
                
    except Exception as e:
        logger.error(f"❌ TRANSACTION UPDATE - Критическая ошибка: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
        
    finally:
        # 🔥 ИСПРАВЛЕНИЕ: Обязательно закрываем соединение БД в finally блоке
        if db:
            try:
                db.close()
                logger.info("🗄️ DATABASE - Сессия базы данных закрыта")
            except Exception as close_error:
                logger.error(f"❌ DATABASE - Ошибка при закрытии сессии БД: {str(close_error)}")


# 🔥 ДОПОЛНИТЕЛЬНО: Context Manager для безопасной работы с БД
from contextlib import asynccontextmanager

@asynccontextmanager
async def get_db_context():
    """
    Безопасный context manager для работы с базой данных
    Автоматически закрывает соединение при выходе из контекста
    """
    db = SessionLocal()
    try:
        logger.debug("🗄️ DATABASE - Создана сессия базы данных")
        yield db
        logger.debug("🗄️ DATABASE - Операция с БД завершена успешно")
    except Exception as e:
        logger.error(f"❌ DATABASE - Ошибка в операции с БД: {str(e)}")
        try:
            db.rollback()
            logger.info("🔄 DATABASE - Выполнен rollback")
        except Exception as rollback_error:
            logger.error(f"❌ DATABASE - Ошибка при rollback: {str(rollback_error)}")
        raise
    finally:
        try:
            db.close()
            logger.debug("🗄️ DATABASE - Сессия базы данных закрыта")
        except Exception as close_error:
            logger.error(f"❌ DATABASE - Ошибка при закрытии сессии: {str(close_error)}")


# 🔥 АЛЬТЕРНАТИВНАЯ ВЕРСИЯ с использованием context manager
async def handle_transaction_update_v2(message: Dict[str, Any]):
    """
    Альтернативная версия с использованием context manager
    """
    try:
        logger.info("💳 TRANSACTION UPDATE V2 - Начало обработки события обновления транзакции")
        logger.info(f"   📍 Полное сообщение: {json.dumps(message, indent=2, ensure_ascii=False, default=str)}")
        
        # Проверка наличия необходимых полей
        required_fields = ["user_id", "amount", "currency", "tx_hash", "status"]
        missing_fields = [field for field in required_fields if field not in message]
        
        if missing_fields:
            logger.error(f"❌ VALIDATION - Отсутствуют обязательные поля: {missing_fields}")
            return
        
        # Получаем значения полей
        user_id = message["user_id"]
        amount = message["amount"]
        currency = message["currency"]
        tx_hash = message["tx_hash"]
        status = message["status"]
        
        logger.info("📋 TRANSACTION DETAILS:")
        logger.info(f"   📍 User ID: {user_id}")
        logger.info(f"   📍 Amount: {amount}")
        logger.info(f"   📍 Currency: {currency}")
        logger.info(f"   📍 TX Hash: {tx_hash}")
        logger.info(f"   📍 Status: {status}")
        
        # 🔥 ИСПОЛЬЗОВАНИЕ CONTEXT MANAGER для автоматического управления соединением
        async with get_db_context() as db:
            # Обрабатываем транзакцию с таймаутом
            logger.info("⚙️ PROCESSING - Вызов BalanceService.process_transaction_event")
            
            success = await asyncio.wait_for(
                BalanceService.process_transaction_event(
                    db=db,
                    user_id=user_id,
                    amount=amount,
                    currency=currency,
                    tx_hash=tx_hash,
                    status=status
                ),
                timeout=30.0  # Таймаут 30 секунд
            )
            
            if success:
                logger.info(f"✅ TRANSACTION UPDATE V2 - Транзакция {tx_hash} успешно обработана")
            else:
                logger.warning(f"⚠️ TRANSACTION UPDATE V2 - Транзакция {tx_hash} не обработана")
                
    except asyncio.TimeoutError:
        logger.error(f"❌ TRANSACTION UPDATE V2 - Таймаут при обработке транзакции {message.get('tx_hash', 'unknown')}")
    except Exception as e:
        logger.error(f"❌ TRANSACTION UPDATE V2 - Критическая ошибка: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")


async def handle_trade_operation(message: Dict[str, Any]):
    """
    Обработка события торговой операции - ИСПРАВЛЕННАЯ ВЕРСИЯ с обработкой системных сообщений
    """
    try:
        logger.info("📈 TRADE OPERATION - Начало обработки события торговой операции")
        logger.info(f"   📍 Полное сообщение: {json.dumps(message, indent=2, ensure_ascii=False, default=str)}")
        
        # Проверка наличия необходимых полей
        required_fields = ["operation_type", "user_id"]
        missing_fields = []
        
        for field in required_fields:
            if field not in message:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"❌ VALIDATION - Отсутствуют обязательные поля: {missing_fields}")
            return
        
        # Получаем значения полей
        operation_type = message["operation_type"]
        user_id = message["user_id"]
        
        # 🔥 ИСПРАВЛЕНИЕ: Обработка системных/тестовых сообщений
        if operation_type == "test_connection" or user_id == "system":
            logger.info("🔧 SYSTEM MESSAGE - Получено системное/тестовое сообщение")
            logger.info(f"   📍 Operation Type: {operation_type}")
            logger.info(f"   📍 User ID: {user_id}")
            logger.info("✅ SYSTEM MESSAGE - Системное сообщение обработано, пропускаем основную логику")
            
            # Отправляем подтверждение для системных сообщений
            if kafka_producer:
                response = {
                    "success": True,
                    "user_id": str(user_id),
                    "operation_type": operation_type,
                    "message": "Системное сообщение успешно обработано"
                }
                try:
                    await kafka_producer.send_and_wait(
                        topic="trade.operations.response",
                        key=str(user_id).encode('utf-8'),
                        value=response
                    )
                    logger.info("✅ SYSTEM RESPONSE - Подтверждение системного сообщения отправлено")
                except Exception as e:
                    logger.warning(f"⚠️ SYSTEM RESPONSE - Не удалось отправить подтверждение: {str(e)}")
            
            return
        
        # 🔥 ИСПРАВЛЕНИЕ: Безопасное преобразование user_id в int
        try:
            if isinstance(user_id, str):
                # Проверяем, является ли строка числом
                if user_id.isdigit():
                    user_id = int(user_id)
                else:
                    logger.error(f"❌ VALIDATION - user_id '{user_id}' не является числом")
                    return
            elif not isinstance(user_id, int):
                logger.error(f"❌ VALIDATION - user_id должен быть числом или строкой с числом, получен: {type(user_id)}")
                return
        except ValueError as e:
            logger.error(f"❌ VALIDATION - Ошибка преобразования user_id '{user_id}' в число: {str(e)}")
            return
        
        # Опциональные поля
        position_id = message.get("position_id", "")
        margin_usd = message.get("margin_usd", Decimal('0'))
        pnl_usd = message.get("pnl_usd", Decimal('0'))
        return_amount = message.get("return_amount", Decimal('0'))
        currency = message.get("currency", "")
        
        # Преобразуем числовые значения, если они пришли как строки
        if isinstance(margin_usd, (str, int, float)):
            margin_usd = Decimal(str(margin_usd))
        if isinstance(pnl_usd, (str, int, float)):
            pnl_usd = Decimal(str(pnl_usd))
        if isinstance(return_amount, (str, int, float)):
            return_amount = Decimal(str(return_amount))
        
        logger.info("📋 TRADE OPERATION DETAILS:")
        logger.info(f"   📍 Operation Type: {operation_type}")
        logger.info(f"   📍 User ID: {user_id}")
        logger.info(f"   📍 Position ID: {position_id}")
        logger.info(f"   📍 Margin USD: {margin_usd}")
        logger.info(f"   📍 PnL USD: {pnl_usd}")
        logger.info(f"   📍 Return Amount: {return_amount}")
        logger.info(f"   📍 Currency: {currency}")
        
        # Создаем новую сессию БД
        logger.info("🗄️ DATABASE - Создание сессии базы данных")
        db = SessionLocal()
        try:
            if operation_type == "open_position":
                logger.info("🔓 OPEN POSITION - Обработка открытия позиции")
                
                # При открытии позиции блокируем реальный вклад пользователя
                actual_user_investment = message.get("amount", Decimal('0'))
                if isinstance(actual_user_investment, (str, int, float)):
                    actual_user_investment = Decimal(str(actual_user_investment))
                
                if actual_user_investment <= 0:
                    logger.error("❌ VALIDATION - Реальный вклад должен быть положительным для открытия позиции")
                    
                    # Отправляем ошибку валидации в Trade Service
                    if kafka_producer:
                        error_response = {
                            "success": False,
                            "user_id": str(user_id),
                            "operation_type": operation_type,
                            "position_id": position_id,
                            "error": "invalid_margin",
                            "message": "Реальный вклад должен быть положительным для открытия позиции"
                        }
                        try:
                            await kafka_producer.send_and_wait(
                                topic="trade.operations.response",
                                key=str(user_id).encode('utf-8'),
                                value=error_response
                            )
                            logger.info("✅ VALIDATION ERROR - Уведомление об ошибке валидации отправлено")
                        except Exception as e:
                            logger.warning(f"⚠️ VALIDATION ERROR - Не удалось отправить уведомление: {str(e)}")
                    
                    # Отправляем WebSocket уведомление даже при ошибке валидации
                    logger.info("📡 WEBSOCKET - Отправка текущего баланса после ошибки валидации")
                    await send_balance_update_after_trade(db, user_id)
                    return
                
                # Проверяем наличие доступного баланса для реального вклада
                logger.info("💰 BALANCE CHECK - Проверка доступного баланса")
                current_balance = BalanceRepository.get_user_fixed_balance(db, user_id)
                if current_balance:
                    available_balance = current_balance.fixed_usd_balance - current_balance.locked_usd_balance
                else:
                    available_balance = Decimal('0')
                logger.info(f"   📍 Доступный баланс: {available_balance} USD")
                logger.info(f"   📍 Требуется (реальный вклад): {actual_user_investment} USD")
                logger.info(f"   📍 Размер позиции с плечом: {margin_usd} USD")
                
                if available_balance < actual_user_investment:
                    logger.error(f"❌ INSUFFICIENT FUNDS - Недостаточно средств")
                    logger.error(f"   📍 Доступно: {available_balance} USD")
                    logger.error(f"   📍 Требуется: {actual_user_investment} USD")
                    
                    # Отправляем ошибку в Trade Service
                    if kafka_producer:
                        error_response = {
                            "success": False,
                            "user_id": str(user_id),
                            "operation_type": operation_type,
                            "position_id": position_id,
                            "error": "insufficient_funds",
                            "message": f"Недостаточно средств. Доступно: {available_balance} USD, требуется: {actual_user_investment} USD",
                            "available_balance": float(available_balance),
                            "required_margin": float(actual_user_investment)
                        }
                        try:
                            await kafka_producer.send_and_wait(
                                topic="trade.operations.response",
                                key=str(user_id).encode('utf-8'),
                                value=error_response
                            )
                            logger.info("✅ ERROR NOTIFICATION - Уведомление об ошибке отправлено в Trade Service")
                        except Exception as e:
                            logger.warning(f"⚠️ ERROR NOTIFICATION - Не удалось отправить уведомление об ошибке: {str(e)}")
                    
                    # Отправляем WebSocket уведомление даже при ошибке
                    logger.info("📡 WEBSOCKET - Отправка текущего баланса после ошибки")
                    await send_balance_update_after_trade(db, user_id)
                    return
                
                # Вычитаем маржу из фиксированного баланса пользователя
                logger.info("💰 OPEN POSITION - Вычитание маржи из баланса")
                logger.info(f"   📍 Маржа (реальный вклад): {actual_user_investment} USD")

                # Вычитаем маржу из fixed_usd_balance
                logger.info(f"📉 DEDUCT MARGIN - Вычитание маржи из фиксированного баланса")
                BalanceRepository.update_fixed_balance(db, user_id, -actual_user_investment, is_locked=False)
                logger.info(f"✅ DEDUCT MARGIN - Вычтено {actual_user_investment} USD из баланса для позиции {position_id}")

                # Проверяем результат операции
                updated_balance = BalanceRepository.get_user_fixed_balance(db, user_id)
                if updated_balance:
                    new_fixed = updated_balance.fixed_usd_balance
                    new_locked = updated_balance.locked_usd_balance
                    new_available = new_fixed - new_locked
                    
                    logger.info(f"💰 BALANCES AFTER OPEN:")
                    logger.info(f"   📍 Fixed: {new_fixed} USD")
                    logger.info(f"   📍 Locked: {new_locked} USD (не используется)")
                    logger.info(f"   📍 Available: {new_fixed} USD")
                    
                    logger.info(f"✅ OPEN POSITION - Маржа успешно вычтена из баланса")
                else:
                    logger.error("❌ BALANCE CHECK - Не удалось получить обновленный баланс!")
                
            elif operation_type == "close_position":
                logger.info("🔐 CLOSE POSITION - Обработка закрытия позиции")
                
                logger.info(f"🔐 CLOSE POSITION - Детали закрытия позиции:")
                logger.info(f"   📍 Position ID: {position_id}")
                logger.info(f"   📍 Return Amount (что нужно вернуть): {return_amount} USD")
                logger.info(f"   📍 PnL USD: {pnl_usd} USD")

                # Простая логика закрытия позиции: добавляем return_amount к балансу
                # return_amount = маржа + PnL (может быть положительным или отрицательным)
                
                logger.info(f"💰 CLOSE POSITION - Добавление результата к балансу")
                logger.info(f"   📍 Return Amount: {return_amount} USD")
                
                # Добавляем весь return_amount к fixed_balance
                BalanceRepository.update_fixed_balance(db, user_id, return_amount, is_locked=False)
                
                if return_amount > 0:
                    logger.info(f"✅ CLOSE POSITION - Добавлено {return_amount} USD к балансу (прибыль)")
                else:
                    logger.info(f"📉 CLOSE POSITION - Вычтено {abs(return_amount)} USD из баланса (убыток)")

                # 4. Возвращаем PnL в криптовалюте (если есть)
                pnl_in_currency = Decimal(str(message.get("amount", 0)))
                if currency and pnl_in_currency != 0:
                    logger.info(f"💰 CRYPTO PNL - Возврат PnL в криптовалюте")
                    logger.info(f"   📍 Сумма: {pnl_in_currency} {currency}")
                    
                    BalanceRepository.create_or_update_balance(db, user_id, currency, pnl_in_currency)
                    BalanceRepository.add_balance_history(
                        db, user_id, currency, pnl_in_currency, None, "trading_pnl"
                    )
                    logger.info(f"✅ CRYPTO PNL - Добавлено {pnl_in_currency} {currency}")
                else:
                    logger.info("ℹ️ CRYPTO PNL - Нет PnL в криптовалюте")
                
            elif operation_type == "cancel_order":
                logger.info("❌ CANCEL ORDER - Обработка отмены ордера")
                
                # При отмене ордера получаем return_amount (равен изначальной марже)
                cancel_return_amount = message.get("return_amount", Decimal('0'))
                if isinstance(cancel_return_amount, (str, int, float)):
                    cancel_return_amount = Decimal(str(cancel_return_amount))
                
                logger.info(f"❌ CANCEL ORDER - Возврат маржи:")
                logger.info(f"   📍 Return Amount: {cancel_return_amount} USD")
                
                # Возвращаем маржу обратно в fixed_balance
                BalanceRepository.update_fixed_balance(db, user_id, cancel_return_amount, is_locked=False)
                logger.info(f"✅ CANCEL ORDER - Возвращено {cancel_return_amount} USD в баланс")
            
            else:
                logger.warning(f"⚠️ UNKNOWN OPERATION - Неизвестный тип операции: {operation_type}")
                return
            
            # Отправляем обновление баланса через WebSocket
            logger.info("📡 WEBSOCKET - Отправка обновления баланса")
            await send_balance_update_after_trade(db, user_id)
            logger.info("✅ TRADE OPERATION - Торговая операция успешно обработана")
            
            # Отправляем подтверждение обработки обратно в Trade Service
            if kafka_producer:
                response = {
                    "success": True,
                    "user_id": str(user_id),
                    "operation_type": operation_type,
                    "position_id": position_id,
                    "message": f"Операция {operation_type} успешно обработана"
                }
                try:
                    await kafka_producer.send_and_wait(
                        topic="trade.operations.response",
                        key=str(user_id).encode('utf-8'),
                        value=response
                    )
                    logger.info("✅ CONFIRMATION - Подтверждение отправлено в Trade Service")
                except Exception as e:
                    logger.warning(f"⚠️ CONFIRMATION - Не удалось отправить подтверждение: {str(e)}")
            
        finally:
            db.close()
            logger.info("🗄️ DATABASE - Сессия базы данных закрыта")
            
    except Exception as e:
        logger.error(f"❌ TRADE OPERATION - Критическая ошибка: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")

async def handle_check_locked_balance(message: Dict[str, Any]):
    """
    Обработчик для проверки locked_usd_balance пользователя
    """
    try:
        logger.info("🔐 LOCKED BALANCE CHECK - Начало обработки запроса")
        logger.info(f"   📍 Полное сообщение: {json.dumps(message, indent=2, ensure_ascii=False)}")
        
        # Проверка наличия необходимых полей
        if "userId" not in message or "correlationId" not in message:
            logger.error(f"❌ VALIDATION - Отсутствуют обязательные поля")
            logger.error(f"   📍 Требуются: userId, correlationId")
            logger.error(f"   📍 Получены: {list(message.keys())}")
            return
        
        # Получаем user_id и correlation_id
        user_id = message["userId"]
        correlation_id = message["correlationId"]
        
        logger.info("📋 REQUEST DETAILS:")
        logger.info(f"   📍 User ID (raw): {user_id}")
        logger.info(f"   📍 Correlation ID: {correlation_id}")
        
        # Преобразуем user_id в int, если это строка
        if isinstance(user_id, str):
            try:
                user_id = int(user_id)
                logger.info(f"   📍 User ID (converted): {user_id}")
            except ValueError:
                logger.error(f"❌ CONVERSION - Не удалось преобразовать user_id '{user_id}' в число")
                return
        
        # Создаем новую сессию БД
        logger.info("🗄️ DATABASE - Создание сессии базы данных")
        db = SessionLocal()
        try:
            # Получаем фиксированный баланс пользователя
            logger.info("💰 BALANCE QUERY - Получение фиксированного баланса")
            fixed_balance = await BalanceService.get_user_fixed_balance(db, user_id)
            
            # Определяем, есть ли заблокированные средства
            locked_balance = Decimal(fixed_balance.locked_usd_balance)
            has_locked_balance = locked_balance > 0
            
            logger.info("📊 BALANCE RESULT:")
            logger.info(f"   📍 Fixed Balance: {fixed_balance.fixed_usd_balance}")
            logger.info(f"   📍 Locked Balance: {locked_balance}")
            logger.info(f"   📍 Available Balance: {fixed_balance.available_usd_balance}")
            logger.info(f"   📍 Has Locked Balance: {has_locked_balance}")
            
            # Формируем ответ
            response = {
                "userId": str(user_id),
                "hasLockedBalance": has_locked_balance,
                "lockedUsdBalance": float(locked_balance),
                "correlationId": correlation_id
            }
            
            logger.info("📤 RESPONSE - Подготовка ответа")
            logger.info(f"   📍 Response: {json.dumps(response, indent=2)}")
            
            # Отправляем ответ через Kafka
            if kafka_producer:
                logger.info("📨 KAFKA SEND - Отправка ответа через Kafka")
                await kafka_producer.send_and_wait(
                    topic="check.locked.balance.response", 
                    value=response,
                    key=str(correlation_id).encode('utf-8')
                )
                logger.info("✅ LOCKED BALANCE CHECK - Ответ успешно отправлен")
            else:
                logger.error("❌ KAFKA ERROR - Продюсер не инициализирован, невозможно отправить ответ")
                
        finally:
            db.close()
            logger.info("🗄️ DATABASE - Сессия базы данных закрыта")
            
    except Exception as e:
        logger.error(f"❌ LOCKED BALANCE CHECK - Критическая ошибка: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")


async def handle_deduct_fixed_balance(message: Dict[str, Any]):
    """
    Обработчик для уменьшения фиксированного баланса пользователя
    """
    try:
        logger.info("💸 DEDUCT BALANCE - Начало обработки запроса на уменьшение баланса")
        logger.info(f"   📍 Полное сообщение: {json.dumps(message, indent=2, ensure_ascii=False)}")
        
        # Проверка наличия необходимых полей
        required_fields = ["userId", "amount", "correlationId", "transactionReference"]
        missing_fields = []
        
        for field in required_fields:
            if field not in message:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"❌ VALIDATION - Отсутствуют обязательные поля: {missing_fields}")
            
            # Отправляем ответ об ошибке
            if "correlationId" in message and kafka_producer:
                error_response = {
                    "success": False,
                    "message": f"Отсутствуют обязательные поля: {missing_fields}",
                    "correlationId": message["correlationId"]
                }
                logger.info("📤 ERROR RESPONSE - Отправка ответа об ошибке валидации")
                await kafka_producer.send_and_wait(
                    topic="deduct.fixed.balance.response", 
                    value=error_response
                )
            return
        
        # Получаем параметры из сообщения
        user_id = message["userId"]
        amount = message["amount"]
        correlation_id = message["correlationId"]
        transaction_reference = message["transactionReference"]
        
        logger.info("📋 DEDUCT REQUEST DETAILS:")
        logger.info(f"   📍 User ID (raw): {user_id}")
        logger.info(f"   📍 Amount (raw): {amount}")
        logger.info(f"   📍 Transaction Ref: {transaction_reference}")
        logger.info(f"   📍 Correlation ID: {correlation_id}")
        
        # Преобразуем user_id в int, если это строка
        if isinstance(user_id, str):
            user_id = int(user_id)
            logger.info(f"   📍 User ID (converted): {user_id}")
        
        # Преобразуем amount в Decimal, если это строка или float
        if isinstance(amount, (str, float)):
            amount = Decimal(str(amount))
            logger.info(f"   📍 Amount (converted): {amount}")
        
        # Создаем новую сессию БД
        logger.info("🗄️ DATABASE - Создание сессии базы данных")
        db = SessionLocal()
        try:
            # Получаем фиксированный баланс пользователя
            logger.info("💰 BALANCE CHECK - Проверка фиксированного баланса")
            fixed_balance = BalanceRepository.get_user_fixed_balance(db, user_id)
            
            if not fixed_balance:
                logger.error("❌ BALANCE NOT FOUND - Фиксированный баланс не найден")
                current_balance = Decimal('0')
            else:
                current_balance = fixed_balance.fixed_usd_balance
                logger.info(f"   📍 Текущий баланс: {current_balance} USD")
            
            # Проверяем достаточно ли средств
            if current_balance < amount:
                logger.error("❌ INSUFFICIENT FUNDS - Недостаточно средств")
                logger.error(f"   📍 Доступно: {current_balance} USD")
                logger.error(f"   📍 Требуется: {amount} USD")
                
                # Отправляем ответ об ошибке
                if kafka_producer:
                    error_response = {
                        "success": False,
                        "message": f"Недостаточно средств. Доступно: {current_balance}, требуется: {amount}",
                        "correlationId": correlation_id
                    }
                    logger.info("📤 ERROR RESPONSE - Отправка ответа о недостатке средств")
                    await kafka_producer.send_and_wait(
                        topic="deduct.fixed.balance.response", 
                        value=error_response
                    )
                return
            
            # Уменьшаем фиксированный баланс на сумму вывода
            logger.info(f"💸 DEDUCT OPERATION - Списание {amount} USD с фиксированного баланса")
            BalanceRepository.update_fixed_balance(db, user_id, -amount, is_locked=False)
            
            # Добавляем запись в историю баланса
            logger.info("📝 HISTORY - Добавление записи в историю баланса")
            BalanceRepository.add_balance_history(
                db, 
                user_id, 
                "USD", 
                -amount, 
                transaction_reference, 
                "withdrawal"
            )
            
            logger.info(f"✅ DEDUCT OPERATION - Баланс успешно уменьшен на {amount} USD")
            
            # Отправляем ответ об успешном уменьшении баланса
            if kafka_producer:
                success_response = {
                    "success": True,
                    "message": f"Фиксированный баланс успешно уменьшен на {amount} USD",
                    "newBalance": float(current_balance - amount),
                    "correlationId": correlation_id
                }
                logger.info("📤 SUCCESS RESPONSE - Отправка подтверждения успешной операции")
                logger.info(f"   📍 Новый баланс: {current_balance - amount} USD")
                await kafka_producer.send_and_wait(
                    topic="deduct.fixed.balance.response", 
                    value=success_response
                )
            
            # Отправляем обновление баланса через WebSocket
            logger.info("📡 WEBSOCKET - Отправка обновления баланса")
            await send_balance_update_after_trade(db, user_id)
            logger.info("✅ DEDUCT BALANCE - Операция полностью завершена")
            
        finally:
            db.close()
            logger.info("🗄️ DATABASE - Сессия базы данных закрыта")
            
    except Exception as e:
        logger.error(f"❌ DEDUCT BALANCE - Критическая ошибка: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
        
        # Отправляем ответ об ошибке
        if "correlationId" in message and kafka_producer:
            error_response = {
                "success": False,
                "message": f"Критическая ошибка при уменьшении баланса: {str(e)}",
                "correlationId": message["correlationId"]
            }
            logger.info("📤 CRITICAL ERROR RESPONSE - Отправка ответа о критической ошибке")
            await kafka_producer.send_and_wait(
                topic="deduct.fixed.balance.response", 
                value=error_response
            )


async def handle_add_referal_reward(message: Dict[str, Any]):
    """
    Обработка события добавления реферальной суммы к балансу пользователя
    """
    try:
        logger.info("🎁 REFERAL REWARD - Начало обработки реферального вознаграждения")
        logger.info(f"   📍 Полное сообщение: {json.dumps(message, indent=2, ensure_ascii=False)}")
        
        # Проверка наличия необходимых полей
        required_fields = ["userId", "amount", "referalPeriod"]
        missing_fields = []
        
        for field in required_fields:
            if field not in message:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"❌ VALIDATION - Отсутствуют обязательные поля: {missing_fields}")
            return
        
        # Получаем значения полей
        user_id = message["userId"]
        amount = message["amount"]
        referal_period = message["referalPeriod"]
        
        logger.info("📋 REFERAL REWARD DETAILS:")
        logger.info(f"   📍 User ID (raw): {user_id}")
        logger.info(f"   📍 Amount (raw): {amount}")
        logger.info(f"   📍 Period: {referal_period}")
        
        # Преобразуем user_id в int, если это строка
        if isinstance(user_id, str):
            user_id = int(user_id)
            logger.info(f"   📍 User ID (converted): {user_id}")
        
        # Преобразуем amount в Decimal, если это строка или float
        if isinstance(amount, (str, float)):
            amount = Decimal(str(amount))
            logger.info(f"   📍 Amount (converted): {amount}")
        
        # Создаем новую сессию БД
        logger.info("🗄️ DATABASE - Создание сессии базы данных")
        db = SessionLocal()
        try:
            # Добавляем сумму к фиксированному балансу пользователя
            logger.info(f"💰 ADD REWARD - Добавление {amount} USD к фиксированному балансу")
            BalanceRepository.update_fixed_balance(db, user_id, amount, is_locked=False)
            logger.info(f"✅ ADD REWARD - Сумма успешно добавлена к балансу")
            
            # Добавляем запись в историю баланса
            transaction_reference = f"REF-{user_id}-{referal_period.replace('/', '')}"
            logger.info("📝 HISTORY - Добавление записи в историю баланса")
            logger.info(f"   📍 Transaction Ref: {transaction_reference}")
            
            BalanceRepository.add_balance_history(
                db, 
                user_id, 
                "USD", 
                amount, 
                transaction_reference, 
                "deposit"  # Используем тип deposit для пополнения
            )
            logger.info("✅ HISTORY - Запись в историю добавлена")
            
            # Отправляем WebSocket-уведомление с обновленным балансом
            logger.info("📡 WEBSOCKET - Отправка обновления баланса")
            await send_balance_update_after_trade(db, user_id)
            logger.info("✅ WEBSOCKET - Обновление баланса отправлено")
            
            # Отправляем подтверждение в Kafka, если необходимо
            if kafka_producer:
                logger.info("📤 KAFKA RESPONSE - Отправка подтверждения")
                response_data = {
                    "success": True,
                    "userId": user_id,
                    "amount": float(amount),
                    "referalPeriod": referal_period,
                    "timestamp": datetime.utcnow().isoformat(),
                    "transactionReference": transaction_reference,
                    "message": f"Реферальная сумма {amount} USD успешно добавлена к балансу"
                }
                
                await kafka_producer.send_and_wait(
                    topic="balance.add.referal.reward.response", 
                    value=response_data
                )
                logger.info("✅ REFERAL REWARD - Подтверждение отправлено")
            
            logger.info("✅ REFERAL REWARD - Операция полностью завершена")
            
        finally:
            db.close()
            logger.info("🗄️ DATABASE - Сессия базы данных закрыта")
            
    except Exception as e:
        logger.error(f"❌ REFERAL REWARD - Критическая ошибка: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
        
        # Отправляем сообщение об ошибке в Kafka
        if kafka_producer:
            error_response = {
                "success": False,
                "userId": message.get("userId"),
                "amount": message.get("amount"),
                "referalPeriod": message.get("referalPeriod"),
                "timestamp": datetime.utcnow().isoformat(),
                "message": f"Ошибка при добавлении реферальной суммы: {str(e)}"
            }
            logger.info("📤 ERROR RESPONSE - Отправка ответа об ошибке")
            await kafka_producer.send_and_wait(
                topic="balance.add.referal.reward.response", 
                value=error_response
            )


# 🔥 НОВЫЙ ОБРАБОТЧИК: Возврат средств на баланс при отклонении вывода
async def handle_add_funds(message: Dict[str, Any]):
    """
    Обработчик для возврата средств на фиксированный баланс пользователя
    Используется при отклонении заявок на вывод средств
    """
    try:
        logger.info("💰 ADD FUNDS - Начало обработки возврата средств на баланс")
        logger.info(f"   📍 Полное сообщение: {json.dumps(message, indent=2, ensure_ascii=False)}")
        
        # Проверка наличия необходимых полей
        required_fields = ["userId", "amount", "currency", "transactionReference"]
        missing_fields = []
        
        for field in required_fields:
            if field not in message:
                missing_fields.append(field)
        
        if missing_fields:
            logger.error(f"❌ VALIDATION - Отсутствуют обязательные поля: {missing_fields}")
            
            # Отправляем ответ об ошибке
            if "correlationId" in message and kafka_producer:
                error_response = {
                    "success": False,
                    "message": f"Отсутствуют обязательные поля: {missing_fields}",
                    "correlationId": message.get("correlationId"),
                    "timestamp": datetime.utcnow().isoformat()
                }
                logger.info("📤 ERROR RESPONSE - Отправка ответа об ошибке валидации")
                await kafka_producer.send_and_wait(
                    topic="balance.add.funds.response", 
                    value=error_response
                )
            return
        
        # Получаем параметры из сообщения
        user_id = message["userId"]
        amount = message["amount"]
        currency = message["currency"]
        transaction_reference = message["transactionReference"]
        correlation_id = message.get("correlationId", "")
        reason = message.get("reason", "funds_return")
        operation_type = message.get("operation_type", "add_funds")
        
        logger.info("📋 ADD FUNDS REQUEST DETAILS:")
        logger.info(f"   📍 User ID (raw): {user_id}")
        logger.info(f"   📍 Amount (raw): {amount}")
        logger.info(f"   📍 Currency: {currency}")
        logger.info(f"   📍 Transaction Ref: {transaction_reference}")
        logger.info(f"   📍 Correlation ID: {correlation_id}")
        logger.info(f"   📍 Reason: {reason}")
        logger.info(f"   📍 Operation Type: {operation_type}")
        
        # Преобразуем user_id в int, если это строка
        if isinstance(user_id, str):
            user_id = int(user_id)
            logger.info(f"   📍 User ID (converted): {user_id}")
        
        # Преобразуем amount в Decimal, если это строка или float
        if isinstance(amount, (str, float)):
            amount = Decimal(str(amount))
            logger.info(f"   📍 Amount (converted): {amount}")
        
        # Создаем новую сессию БД
        logger.info("🗄️ DATABASE - Создание сессии базы данных")
        db = SessionLocal()
        try:
            # Получаем текущий фиксированный баланс пользователя
            logger.info("💰 BALANCE CHECK - Проверка текущего фиксированного баланса")
            current_balance = BalanceRepository.get_user_fixed_balance(db, user_id)
            
            if current_balance:
                current_fixed = current_balance.fixed_usd_balance
                current_locked = current_balance.locked_usd_balance
                current_available = current_fixed - current_locked
                logger.info(f"   📍 Текущий Fixed: {current_fixed} USD")
                logger.info(f"   📍 Текущий Locked: {current_locked} USD")
                logger.info(f"   📍 Текущий Available: {current_available} USD")
            else:
                logger.info("   📍 Фиксированный баланс не найден, будет создан")
            
            # Возвращаем средства на фиксированный баланс
            logger.info(f"💰 ADD FUNDS - Возврат {amount} {currency} на фиксированный баланс")
            
            # Если валюта USD, добавляем к фиксированному балансу
            if currency.upper() == "USD" or currency.upper() == "USDT":
                BalanceRepository.update_fixed_balance(db, user_id, amount, is_locked=False)
                logger.info(f"✅ ADD FUNDS - Добавлено {amount} USD к фиксированному балансу")
                
                # Добавляем запись в историю баланса
                logger.info("📝 HISTORY - Добавление записи в историю баланса")
                BalanceRepository.add_balance_history(
                    db, 
                    user_id, 
                    "USD", 
                    amount, 
                    transaction_reference, 
                    "refund"  # Используем тип refund для возврата средств
                )
                logger.info("✅ HISTORY - Запись в историю добавлена")
                
            else:
                # Для других валют добавляем к обычным балансам
                logger.info(f"💰 CRYPTO BALANCE - Добавление {amount} {currency} к криптобалансу")
                BalanceRepository.create_or_update_balance(db, user_id, currency, amount)
                BalanceRepository.add_balance_history(
                    db, user_id, currency, amount, transaction_reference, "refund"
                )
                logger.info(f"✅ CRYPTO BALANCE - Добавлено {amount} {currency}")
            
            # Отправляем обновление баланса через WebSocket
            logger.info("📡 WEBSOCKET - Отправка обновления баланса")
            await send_balance_update_after_trade(db, user_id)
            logger.info("✅ WEBSOCKET - Обновление баланса отправлено")
            
            # Отправляем подтверждение в Kafka
            if kafka_producer:
                success_response = {
                    "success": True,
                    "userId": user_id,
                    "amount": float(amount),
                    "currency": currency,
                    "transactionReference": transaction_reference,
                    "correlationId": correlation_id,
                    "reason": reason,
                    "operation_type": operation_type,
                    "timestamp": datetime.utcnow().isoformat(),
                    "message": f"Средства {amount} {currency} успешно возвращены на баланс"
                }
                logger.info("📤 SUCCESS RESPONSE - Отправка подтверждения успешной операции")
                await kafka_producer.send_and_wait(
                    topic="balance.add.funds.response", 
                    value=success_response
                )
                logger.info("✅ ADD FUNDS - Подтверждение отправлено")
            
            logger.info("✅ ADD FUNDS - Операция возврата средств полностью завершена")
            
        finally:
            db.close()
            logger.info("🗄️ DATABASE - Сессия базы данных закрыта")
            
    except Exception as e:
        logger.error(f"❌ ADD FUNDS - Критическая ошибка: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")
        
        # Отправляем ответ об ошибке
        if kafka_producer:
            error_response = {
                "success": False,
                "userId": message.get("userId"),
                "amount": message.get("amount"),
                "currency": message.get("currency"),
                "transactionReference": message.get("transactionReference"),
                "correlationId": message.get("correlationId"),
                "reason": message.get("reason"),
                "timestamp": datetime.utcnow().isoformat(),
                "message": f"Критическая ошибка при возврате средств: {str(e)}"
            }
            logger.info("📤 CRITICAL ERROR RESPONSE - Отправка ответа о критической ошибке")
            await kafka_producer.send_and_wait(
                topic="balance.add.funds.response", 
                value=error_response
            )


async def send_balance_update_after_trade(db: Session, user_id: int):
    """Отправка обновления баланса через WebSocket после торговой операции"""
    try:
        logger.info(f"📡 WEBSOCKET UPDATE - Подготовка обновления для пользователя {user_id}")
        
        # Получаем фиксированный баланс пользователя
        logger.info("💰 BALANCE QUERY - Получение фиксированного баланса")
        fixed_balance = await BalanceService.get_user_fixed_balance(db, user_id)
        
        # Получаем обычные балансы
        logger.info("💰 BALANCE QUERY - Получение обычных балансов")
        balances = await BalanceService.get_user_balances(db, user_id)
        
        # Импортируем функцию отправки WebSocket сообщений
        from app.websocket import send_balance_update
        
        # Данные для отправки через WebSocket
        websocket_data = {
            "fixed_usd_balance": fixed_balance.fixed_usd_balance,
            "locked_usd_balance": fixed_balance.locked_usd_balance,
            "available_usd_balance": fixed_balance.available_usd_balance,
            "balances": [
                {
                    "currency": b.currency,
                    "amount": b.amount,
                    "equivalent_usd": b.equivalent_usd
                } for b in balances.balances
            ],
            "total_equivalent_usd": balances.total_equivalent_usd
        }
        
        logger.info("📊 WEBSOCKET DATA:")
        logger.info(f"   📍 Fixed USD: {fixed_balance.fixed_usd_balance}")
        logger.info(f"   📍 Locked USD: {fixed_balance.locked_usd_balance}")
        logger.info(f"   📍 Available USD: {fixed_balance.available_usd_balance}")
        logger.info(f"   📍 Balances count: {len(balances.balances)}")
        logger.info(f"   📍 Total equivalent: {balances.total_equivalent_usd}")
        
        # Отправляем обновление баланса
        logger.info("📨 WEBSOCKET SEND - Отправка данных через WebSocket")
        await send_balance_update(user_id, websocket_data)
        logger.info("✅ WEBSOCKET UPDATE - Обновление успешно отправлено")
        
    except Exception as e:
        logger.error(f"❌ WEBSOCKET UPDATE - Ошибка при отправке: {str(e)}")
        import traceback
        logger.error(f"📍 Полная трассировка ошибки:\n{traceback.format_exc()}")


# 🔥 ОБНОВЛЕННЫЙ Словарь типов событий и их обработчиков
EVENT_HANDLERS = {
   "transaction_update": handle_transaction_update,
   "trade_operation": handle_trade_operation,
   "check_locked_balance": handle_check_locked_balance,
   "deduct_fixed_balance": handle_deduct_fixed_balance,
   "add_referal_reward": handle_add_referal_reward,
   "add_funds": handle_add_funds,  # 🔥 НОВЫЙ обработчик для возврата средств
}