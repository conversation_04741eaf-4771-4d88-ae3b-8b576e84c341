#!/usr/bin/env python
"""
Скрипт для инициализации фиксированных USD балансов для существующих пользователей
"""

import asyncio
import logging
import sys
import os

# Добавляем родительский каталог в путь, чтобы импортировать app
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from sqlalchemy.orm import Session
from sqlalchemy import distinct
from app.db.session import SessionLocal
from app.db.models.balance import UserBalance, UserFixedBalance
from app.core.services.balance_service import BalanceService

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


async def initialize_all_fixed_balances():
    """
    Инициализирует фиксированные USD балансы для всех пользователей,
    у которых есть обычные балансы, но нет фиксированных
    """
    db = SessionLocal()
    try:
        # Получаем всех пользователей с обычными балансами
        user_ids = db.query(distinct(UserBalance.user_id)).all()
        user_ids = [user_id[0] for user_id in user_ids]
        
        logger.info(f"Найдено {len(user_ids)} пользователей с балансами")
        
        # Получаем всех пользователей с фиксированными балансами
        fixed_user_ids = db.query(distinct(UserFixedBalance.user_id)).all()
        fixed_user_ids = [user_id[0] for user_id in fixed_user_ids]
        
        logger.info(f"Найдено {len(fixed_user_ids)} пользователей с фиксированными балансами")
        
        # Находим пользователей, у которых нет фиксированных балансов
        users_to_initialize = [user_id for user_id in user_ids if user_id not in fixed_user_ids]
        
        logger.info(f"Требуется инициализировать {len(users_to_initialize)} пользователей")
        
        # Инициализируем фиксированные балансы
        for user_id in users_to_initialize:
            logger.info(f"Инициализация фиксированного баланса для пользователя {user_id}")
            await BalanceService.initialize_fixed_balance(db, user_id)
        
        logger.info("Инициализация фиксированных балансов завершена")
        
    except Exception as e:
        logger.error(f"Ошибка при инициализации фиксированных балансов: {str(e)}")
    finally:
        db.close()


if __name__ == "__main__":
    # Запускаем асинхронную функцию
    asyncio.run(initialize_all_fixed_balances())