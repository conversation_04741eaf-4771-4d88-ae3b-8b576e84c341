#!/usr/bin/env python3
"""
Скрипт для тестирования обновления баланса и передачи USD-эквивалентов через WebSocket.
Запустите этот скрипт, чтобы вручную добавить баланс с USD-эквивалентами и проверить WebSocket.

Использование:
python3 test_balance_usd.py
"""

import asyncio
import sys
import json
import logging
from decimal import Decimal
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import uvicorn
import os
import requests
from datetime import datetime, timedelta

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Добавляем текущую директорию в путь
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Импортируем классы проекта
from app.db.models.balance import UserBalance, BalanceHistory
from app.db.session import Base, get_db
from app.config import settings
from app.core.services.price_service import PriceService

# Создаем соединение с базой данных
engine = create_engine(settings.DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


async def test_update_user_balance():
    """Тестирование обновления баланса с USD-эквивалентами"""
    try:
        # Получаем текущие курсы валют
        logger.info("Получение текущих курсов валют...")
        prices = await PriceService.get_all_prices()
        logger.info(f"Получены курсы: {prices}")
        
        # Создаем сессию БД
        db = SessionLocal()
        
        try:
            # Тестовый пользователь
            user_id = 6  # Укажите ID существующего пользователя
            
            # Получаем текущие балансы пользователя
            user_balances = db.query(UserBalance).filter(UserBalance.user_id == user_id).all()
            
            if not user_balances:
                logger.warning(f"Балансы для пользователя {user_id} не найдены. Добавляем тестовые балансы.")
                
                # Добавляем тестовые балансы
                currencies = ["BTC", "ETH", "USDT", "USDC"]
                amounts = {"BTC": "0.00123456", "ETH": "0.05", "USDT": "100.00", "USDC": "150.00"}
                
                for currency in currencies:
                    amount = Decimal(amounts[currency])
                    
                    # Получаем курс валюты
                    price_usd = prices.get(currency, Decimal('1.0'))
                    
                    # Рассчитываем эквивалент в USD
                    equivalent_usd = amount * price_usd
                    
                    # Создаем запись баланса
                    balance = UserBalance(
                        user_id=user_id,
                        currency=currency,
                        amount=amount,
                        equivalent_usd=equivalent_usd,
                        price_usd=price_usd,
                        last_updated=datetime.utcnow()
                    )
                    
                    db.add(balance)
                    
                    logger.info(f"Добавлен баланс: {currency}, {amount}, {equivalent_usd:.2f} USD")
                
                # Сохраняем изменения
                db.commit()
                
                logger.info("Тестовые балансы добавлены успешно.")
            else:
                # Обновляем существующие балансы
                logger.info(f"Найдены существующие балансы для пользователя {user_id}. Обновляем USD-эквиваленты.")
                
                for balance in user_balances:
                    # Получаем курс валюты
                    price_usd = prices.get(balance.currency, Decimal('1.0'))
                    
                    # Рассчитываем эквивалент в USD
                    equivalent_usd = balance.amount * price_usd
                    
                    # Обновляем эквивалент и курс
                    balance.equivalent_usd = equivalent_usd
                    balance.price_usd = price_usd
                    balance.last_updated = datetime.utcnow()
                    
                    logger.info(f"Обновлен баланс: {balance.currency}, {balance.amount}, {equivalent_usd:.2f} USD")
                
                # Сохраняем изменения
                db.commit()
                
                logger.info("USD-эквиваленты обновлены успешно.")
            
            # Получаем обновленные балансы для проверки
            updated_balances = db.query(UserBalance).filter(UserBalance.user_id == user_id).all()
            
            # Проверяем, что USD-эквиваленты установлены
            for balance in updated_balances:
                logger.info(f"Проверка баланса: {balance.currency}, {balance.amount}, {balance.equivalent_usd} USD, цена: {balance.price_usd}")
                
                if balance.equivalent_usd is None:
                    logger.warning(f"USD-эквивалент для {balance.currency} не установлен!")
            
            logger.info("Проверка завершена.")
            
            # Теперь отправляем запрос через API для проверки отображения эквивалентов в ответе API
            try:
                api_url = f"http://localhost:{settings.PORT}/api/balance?user_id={user_id}"
                logger.info(f"Отправка запроса к API: {api_url}")
                
                response = requests.get(api_url)
                
                if response.status_code == 200:
                    data = response.json()
                    logger.info(f"Ответ API: {json.dumps(data, indent=2)}")
                    
                    # Проверяем, есть ли эквиваленты в ответе
                    for balance in data.get("balances", []):
                        if "equivalent_usd" not in balance:
                            logger.warning(f"В ответе API нет equivalent_usd для {balance['currency']}!")
                    
                    if "total_equivalent_usd" not in data:
                        logger.warning("В ответе API нет total_equivalent_usd!")
                else:
                    logger.error(f"Ошибка API: {response.status_code}, {response.text}")
            except Exception as e:
                logger.error(f"Ошибка при проверке API: {str(e)}")
            
        except Exception as e:
            logger.error(f"Ошибка при обновлении балансов: {str(e)}")
            db.rollback()
        finally:
            db.close()
    
    except Exception as e:
        logger.error(f"Общая ошибка: {str(e)}")


if __name__ == "__main__":
    logger.info("Запуск тестового скрипта для проверки USD-эквивалентов")
    
    # Запускаем асинхронную функцию
    asyncio.run(test_update_user_balance())
    
    logger.info("Тестовый скрипт завершен. Проверьте логи для анализа результатов.")